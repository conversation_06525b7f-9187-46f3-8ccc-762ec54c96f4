{"version": 3, "names": ["_toNodeDescription", "require", "UnparenthesizedPipeBodyDescriptions", "Set", "exports", "_default", "PipeBodyIsTighter", "PipeTopicRequiresHackPipes", "PipeTopicUnbound", "PipeTopicUnconfiguredToken", "token", "PipeTopicUnused", "PipeUnparenthesizedBody", "type", "toNodeDescription", "PipelineBodyNoArrow", "PipelineBodySequenceExpression", "PipelineHeadSequenceExpression", "PipelineTopicUnused", "PrimaryTopicNotAllowed", "PrimaryTopicRequiresSmartPipeline", "default"], "sources": ["../../src/parse-error/pipeline-operator-errors.ts"], "sourcesContent": ["import toNodeDescription from \"./to-node-description\";\n\nexport const UnparenthesizedPipeBodyDescriptions = new Set([\n  \"ArrowFunctionExpression\",\n  \"AssignmentExpression\",\n  \"ConditionalExpression\",\n  \"YieldExpression\",\n] as const);\n\ntype GetSetMemberType<T extends Set<any>> = T extends Set<infer M>\n  ? M\n  : unknown;\n\ntype UnparenthesizedPipeBodyTypes = GetSetMemberType<\n  typeof UnparenthesizedPipeBodyDescriptions\n>;\n\nexport default {\n  // This error is only used by the smart-mix proposal\n  PipeBodyIsTighter:\n    \"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.\",\n  PipeTopicRequiresHackPipes:\n    'Topic reference is used, but the pipelineOperator plugin was not passed a \"proposal\": \"hack\" or \"smart\" option.',\n  PipeTopicUnbound:\n    \"Topic reference is unbound; it must be inside a pipe body.\",\n  PipeTopicUnconfiguredToken: ({ token }: { token: string }) =>\n    `Invalid topic token ${token}. In order to use ${token} as a topic reference, the pipelineOperator plugin must be configured with { \"proposal\": \"hack\", \"topicToken\": \"${token}\" }.`,\n  PipeTopicUnused:\n    \"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.\",\n  PipeUnparenthesizedBody: ({ type }: { type: UnparenthesizedPipeBodyTypes }) =>\n    `Hack-style pipe body cannot be an unparenthesized ${toNodeDescription({\n      type,\n    })}; please wrap it in parentheses.`,\n\n  // Messages whose codes start with “Pipeline” or “PrimaryTopic”\n  // are retained for backwards compatibility\n  // with the deprecated smart-mix pipe operator proposal plugin.\n  // They are subject to removal in a future major version.\n  PipelineBodyNoArrow:\n    'Unexpected arrow \"=>\" after pipeline body; arrow function in pipeline body must be parenthesized.',\n  PipelineBodySequenceExpression:\n    \"Pipeline body may not be a comma-separated sequence expression.\",\n  PipelineHeadSequenceExpression:\n    \"Pipeline head should not be a comma-separated sequence expression.\",\n  PipelineTopicUnused:\n    \"Pipeline is in topic style but does not use topic reference.\",\n  PrimaryTopicNotAllowed:\n    \"Topic reference was used in a lexical context without topic binding.\",\n  PrimaryTopicRequiresSmartPipeline:\n    'Topic reference is used, but the pipelineOperator plugin was not passed a \"proposal\": \"hack\" or \"smart\" option.',\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEO,MAAMC,mCAAmC,GAAG,IAAIC,GAAG,CAAC,CACzD,yBAAyB,EACzB,sBAAsB,EACtB,uBAAuB,EACvB,iBAAiB,CACT,CAAC;AAACC,OAAA,CAAAF,mCAAA,GAAAA,mCAAA;AAAA,IAAAG,QAAA,GAUG;EAEbC,iBAAiB,EACf,uJAAuJ;EACzJC,0BAA0B,EACxB,iHAAiH;EACnHC,gBAAgB,EACd,4DAA4D;EAC9DC,0BAA0B,EAAEA,CAAC;IAAEC;EAAyB,CAAC,KACtD,uBAAsBA,KAAM,qBAAoBA,KAAM,mHAAkHA,KAAM,MAAK;EACtLC,eAAe,EACb,yGAAyG;EAC3GC,uBAAuB,EAAEA,CAAC;IAAEC;EAA6C,CAAC,KACvE,qDAAoD,IAAAC,0BAAiB,EAAC;IACrED;EACF,CAAC,CAAE,kCAAiC;EAMtCE,mBAAmB,EACjB,mGAAmG;EACrGC,8BAA8B,EAC5B,iEAAiE;EACnEC,8BAA8B,EAC5B,oEAAoE;EACtEC,mBAAmB,EACjB,8DAA8D;EAChEC,sBAAsB,EACpB,sEAAsE;EACxEC,iCAAiC,EAC/B;AACJ,CAAC;AAAAhB,OAAA,CAAAiB,OAAA,GAAAhB,QAAA"}