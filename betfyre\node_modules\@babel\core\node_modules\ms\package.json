{"_from": "ms@^2.1.1", "_id": "ms@2.1.2", "_inBundle": false, "_integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "_location": "/@babel/core/ms", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ms@^2.1.1", "name": "ms", "escapedName": "ms", "rawSpec": "^2.1.1", "saveSpec": null, "fetchSpec": "^2.1.1"}, "_requiredBy": ["/@babel/core/debug"], "_resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "_shasum": "d09d1f357b443f493382a8eb3ccd183872ae6009", "_spec": "ms@^2.1.1", "_where": "/var/www/html/node_modules/@babel/core/node_modules/debug", "bugs": {"url": "https://github.com/zeit/ms/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tiny millisecond conversion utility", "devDependencies": {"eslint": "4.12.1", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "files": ["index.js"], "homepage": "https://github.com/zeit/ms#readme", "license": "MIT", "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "main": "./index", "name": "ms", "repository": {"type": "git", "url": "git+https://github.com/zeit/ms.git"}, "scripts": {"lint": "eslint lib/* bin/*", "precommit": "lint-staged", "test": "mocha tests.js"}, "version": "2.1.2"}