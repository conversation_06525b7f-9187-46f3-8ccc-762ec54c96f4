{"version": 3, "names": ["TSTypeAnnotation", "node", "token", "space", "optional", "print", "typeAnnotation", "TSTypeParameterInstantiation", "parent", "printList", "params", "type", "length", "TSTypeParameter", "in", "word", "out", "name", "constraint", "default", "TSParameterProperty", "accessibility", "readonly", "_param", "parameter", "TSDeclareFunction", "declare", "_functionHead", "TSDeclareMethod", "_classMethodHead", "TSQualifiedName", "left", "right", "TSCallSignatureDeclaration", "tsPrintSignatureDeclarationBase", "TSConstructSignatureDeclaration", "TSPropertySignature", "initializer", "tsPrintPropertyOrMethodName", "computed", "key", "TSMethodSignature", "kind", "TSIndexSignature", "static", "isStatic", "_parameters", "parameters", "TSAnyKeyword", "TSBigIntKeyword", "TSUnknownKeyword", "TSNumberKeyword", "TSObjectKeyword", "TSBooleanKeyword", "TSStringKeyword", "TSSymbolKeyword", "TSVoidKeyword", "TSUndefinedKeyword", "TSNullKeyword", "TSNeverKeyword", "TSIntrinsicKeyword", "TSThisType", "TSFunctionType", "tsPrintFunctionOrConstructorType", "TSConstructorType", "abstract", "typeParameters", "returnType", "TSTypeReference", "typeName", "TSTypePredicate", "asserts", "parameterName", "TSTypeQuery", "exprName", "TSTypeLiteral", "tsPrintTypeLiteralOrInterfaceBody", "members", "tsPrintBraced", "printer", "indent", "newline", "member", "dedent", "rightBrace", "TSArrayType", "elementType", "TSTupleType", "elementTypes", "TSOptionalType", "TSRestType", "TSNamedTupleMember", "label", "TSUnionType", "tsPrintUnionOrIntersectionType", "TSIntersectionType", "sep", "printJoin", "types", "separator", "TSConditionalType", "checkType", "extendsType", "trueType", "falseType", "TSInferType", "typeParameter", "TSParenthesizedType", "TSTypeOperator", "operator", "TSIndexedAccessType", "objectType", "indexType", "TSMappedType", "nameType", "tokenIfPlusMinus", "self", "tok", "TSLiteralType", "literal", "TSExpressionWithTypeArguments", "expression", "TSInterfaceDeclaration", "id", "extends", "extendz", "body", "TSInterfaceBody", "TSTypeAliasDeclaration", "TSTypeExpression", "_expression$trailingC", "forceParens", "trailingComments", "undefined", "TSTypeAssertion", "TSInstantiationExpression", "TSEnumDeclaration", "const", "isConst", "TSEnumMember", "TSModuleDeclaration", "global", "TSModuleBlock", "TSImportType", "argument", "qualifier", "TSImportEqualsDeclaration", "isExport", "moduleReference", "TSExternalModuleReference", "TSNonNullExpression", "TSExportAssignment", "TSNamespaceExportDeclaration", "tsPrintClassMemberModifiers", "isField", "override"], "sources": ["../../src/generators/typescript.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport function TSTypeAnnotation(this: Printer, node: t.TSTypeAnnotation) {\n  this.token(\":\");\n  this.space();\n  // @ts-expect-error todo(flow->ts) can this be removed? `.optional` looks to be not existing property\n  if (node.optional) this.token(\"?\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSTypeParameterInstantiation(\n  this: Printer,\n  node: t.TSTypeParameterInstantiation,\n  parent: t.Node,\n): void {\n  this.token(\"<\");\n  this.printList(node.params, node, {});\n  if (parent.type === \"ArrowFunctionExpression\" && node.params.length === 1) {\n    this.token(\",\");\n  }\n  this.token(\">\");\n}\n\nexport { TSTypeParameterInstantiation as TSTypeParameterDeclaration };\n\nexport function TSTypeParameter(this: Printer, node: t.TSTypeParameter) {\n  if (node.in) {\n    this.word(\"in\");\n    this.space();\n  }\n\n  if (node.out) {\n    this.word(\"out\");\n    this.space();\n  }\n\n  this.word(\n    !process.env.BABEL_8_BREAKING\n      ? (node.name as unknown as string)\n      : (node.name as unknown as t.Identifier).name,\n  );\n\n  if (node.constraint) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.constraint, node);\n  }\n\n  if (node.default) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.default, node);\n  }\n}\n\nexport function TSParameterProperty(\n  this: Printer,\n  node: t.TSParameterProperty,\n) {\n  if (node.accessibility) {\n    this.word(node.accessibility);\n    this.space();\n  }\n\n  if (node.readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this._param(node.parameter);\n}\n\nexport function TSDeclareFunction(\n  this: Printer,\n  node: t.TSDeclareFunction,\n  parent: NodePath<t.TSDeclareFunction>[\"parent\"],\n) {\n  if (node.declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this._functionHead(node, parent);\n  this.token(\";\");\n}\n\nexport function TSDeclareMethod(this: Printer, node: t.TSDeclareMethod) {\n  this._classMethodHead(node);\n  this.token(\";\");\n}\n\nexport function TSQualifiedName(this: Printer, node: t.TSQualifiedName) {\n  this.print(node.left, node);\n  this.token(\".\");\n  this.print(node.right, node);\n}\n\nexport function TSCallSignatureDeclaration(\n  this: Printer,\n  node: t.TSCallSignatureDeclaration,\n) {\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSConstructSignatureDeclaration(\n  this: Printer,\n  node: t.TSConstructSignatureDeclaration,\n) {\n  this.word(\"new\");\n  this.space();\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSPropertySignature(\n  this: Printer,\n  node: t.TSPropertySignature,\n) {\n  const { readonly, initializer } = node;\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.print(node.typeAnnotation, node);\n  if (initializer) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(initializer, node);\n  }\n  this.token(\";\");\n}\n\nexport function tsPrintPropertyOrMethodName(\n  this: Printer,\n  node: t.TSPropertySignature | t.TSMethodSignature,\n) {\n  if (node.computed) {\n    this.token(\"[\");\n  }\n  this.print(node.key, node);\n  if (node.computed) {\n    this.token(\"]\");\n  }\n  if (node.optional) {\n    this.token(\"?\");\n  }\n}\n\nexport function TSMethodSignature(this: Printer, node: t.TSMethodSignature) {\n  const { kind } = node;\n  if (kind === \"set\" || kind === \"get\") {\n    this.word(kind);\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSIndexSignature(this: Printer, node: t.TSIndexSignature) {\n  const { readonly, static: isStatic } = node;\n  if (isStatic) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.token(\"[\");\n  this._parameters(node.parameters, node);\n  this.token(\"]\");\n  this.print(node.typeAnnotation, node);\n  this.token(\";\");\n}\n\nexport function TSAnyKeyword(this: Printer) {\n  this.word(\"any\");\n}\nexport function TSBigIntKeyword(this: Printer) {\n  this.word(\"bigint\");\n}\nexport function TSUnknownKeyword(this: Printer) {\n  this.word(\"unknown\");\n}\nexport function TSNumberKeyword(this: Printer) {\n  this.word(\"number\");\n}\nexport function TSObjectKeyword(this: Printer) {\n  this.word(\"object\");\n}\nexport function TSBooleanKeyword(this: Printer) {\n  this.word(\"boolean\");\n}\nexport function TSStringKeyword(this: Printer) {\n  this.word(\"string\");\n}\nexport function TSSymbolKeyword(this: Printer) {\n  this.word(\"symbol\");\n}\nexport function TSVoidKeyword(this: Printer) {\n  this.word(\"void\");\n}\nexport function TSUndefinedKeyword(this: Printer) {\n  this.word(\"undefined\");\n}\nexport function TSNullKeyword(this: Printer) {\n  this.word(\"null\");\n}\nexport function TSNeverKeyword(this: Printer) {\n  this.word(\"never\");\n}\nexport function TSIntrinsicKeyword(this: Printer) {\n  this.word(\"intrinsic\");\n}\n\nexport function TSThisType(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function TSFunctionType(this: Printer, node: t.TSFunctionType) {\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function TSConstructorType(this: Printer, node: t.TSConstructorType) {\n  if (node.abstract) {\n    this.word(\"abstract\");\n    this.space();\n  }\n  this.word(\"new\");\n  this.space();\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function tsPrintFunctionOrConstructorType(\n  this: Printer,\n  node: t.TSFunctionType | t.TSConstructorType,\n) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.params\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.parameters;\n  this.print(typeParameters, node);\n  this.token(\"(\");\n  this._parameters(parameters, node);\n  this.token(\")\");\n  this.space();\n  this.token(\"=>\");\n  this.space();\n  const returnType = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.returnType\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.typeAnnotation;\n  this.print(returnType.typeAnnotation, node);\n}\n\nexport function TSTypeReference(this: Printer, node: t.TSTypeReference) {\n  this.print(node.typeName, node, true);\n  this.print(node.typeParameters, node, true);\n}\n\nexport function TSTypePredicate(this: Printer, node: t.TSTypePredicate) {\n  if (node.asserts) {\n    this.word(\"asserts\");\n    this.space();\n  }\n  this.print(node.parameterName);\n  if (node.typeAnnotation) {\n    this.space();\n    this.word(\"is\");\n    this.space();\n    this.print(node.typeAnnotation.typeAnnotation);\n  }\n}\n\nexport function TSTypeQuery(this: Printer, node: t.TSTypeQuery) {\n  this.word(\"typeof\");\n  this.space();\n  this.print(node.exprName);\n\n  if (node.typeParameters) {\n    this.print(node.typeParameters, node);\n  }\n}\n\nexport function TSTypeLiteral(this: Printer, node: t.TSTypeLiteral) {\n  this.tsPrintTypeLiteralOrInterfaceBody(node.members, node);\n}\n\nexport function tsPrintTypeLiteralOrInterfaceBody(\n  this: Printer,\n  members: t.TSTypeElement[],\n  node: t.TSType | t.TSInterfaceBody,\n) {\n  tsPrintBraced(this, members, node);\n}\n\nfunction tsPrintBraced(printer: Printer, members: t.Node[], node: t.Node) {\n  printer.token(\"{\");\n  if (members.length) {\n    printer.indent();\n    printer.newline();\n    for (const member of members) {\n      printer.print(member, node);\n      //this.token(sep);\n      printer.newline();\n    }\n    printer.dedent();\n  }\n\n  printer.rightBrace(node);\n}\n\nexport function TSArrayType(this: Printer, node: t.TSArrayType) {\n  this.print(node.elementType, node, true);\n\n  this.token(\"[]\");\n}\n\nexport function TSTupleType(this: Printer, node: t.TSTupleType) {\n  this.token(\"[\");\n  this.printList(node.elementTypes, node);\n  this.token(\"]\");\n}\n\nexport function TSOptionalType(this: Printer, node: t.TSOptionalType) {\n  this.print(node.typeAnnotation, node);\n  this.token(\"?\");\n}\n\nexport function TSRestType(this: Printer, node: t.TSRestType) {\n  this.token(\"...\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSNamedTupleMember(this: Printer, node: t.TSNamedTupleMember) {\n  this.print(node.label, node);\n  if (node.optional) this.token(\"?\");\n  this.token(\":\");\n  this.space();\n  this.print(node.elementType, node);\n}\n\nexport function TSUnionType(this: Printer, node: t.TSUnionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"|\");\n}\n\nexport function TSIntersectionType(this: Printer, node: t.TSIntersectionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"&\");\n}\n\nfunction tsPrintUnionOrIntersectionType(\n  printer: Printer,\n  node: t.TSUnionType | t.TSIntersectionType,\n  sep: \"|\" | \"&\",\n) {\n  printer.printJoin(node.types, node, {\n    separator() {\n      this.space();\n      this.token(sep);\n      this.space();\n    },\n  });\n}\n\nexport function TSConditionalType(this: Printer, node: t.TSConditionalType) {\n  this.print(node.checkType);\n  this.space();\n  this.word(\"extends\");\n  this.space();\n  this.print(node.extendsType);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.trueType);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.falseType);\n}\n\nexport function TSInferType(this: Printer, node: t.TSInferType) {\n  this.token(\"infer\");\n  this.space();\n  this.print(node.typeParameter);\n}\n\nexport function TSParenthesizedType(\n  this: Printer,\n  node: t.TSParenthesizedType,\n) {\n  this.token(\"(\");\n  this.print(node.typeAnnotation, node);\n  this.token(\")\");\n}\n\nexport function TSTypeOperator(this: Printer, node: t.TSTypeOperator) {\n  this.word(node.operator);\n  this.space();\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSIndexedAccessType(\n  this: Printer,\n  node: t.TSIndexedAccessType,\n) {\n  this.print(node.objectType, node, true);\n  this.token(\"[\");\n  this.print(node.indexType, node);\n  this.token(\"]\");\n}\n\nexport function TSMappedType(this: Printer, node: t.TSMappedType) {\n  const { nameType, optional, readonly, typeParameter } = node;\n  this.token(\"{\");\n  this.space();\n  if (readonly) {\n    tokenIfPlusMinus(this, readonly);\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this.token(\"[\");\n  this.word(\n    !process.env.BABEL_8_BREAKING\n      ? (typeParameter.name as unknown as string)\n      : (typeParameter.name as unknown as t.Identifier).name,\n  );\n  this.space();\n  this.word(\"in\");\n  this.space();\n  this.print(typeParameter.constraint, typeParameter);\n\n  if (nameType) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(nameType, node);\n  }\n\n  this.token(\"]\");\n\n  if (optional) {\n    tokenIfPlusMinus(this, optional);\n    this.token(\"?\");\n  }\n  this.token(\":\");\n  this.space();\n  this.print(node.typeAnnotation, node);\n  this.space();\n  this.token(\"}\");\n}\n\nfunction tokenIfPlusMinus(self: Printer, tok: true | \"+\" | \"-\") {\n  if (tok !== true) {\n    self.token(tok);\n  }\n}\n\nexport function TSLiteralType(this: Printer, node: t.TSLiteralType) {\n  this.print(node.literal, node);\n}\n\nexport function TSExpressionWithTypeArguments(\n  this: Printer,\n  node: t.TSExpressionWithTypeArguments,\n) {\n  this.print(node.expression, node);\n  this.print(node.typeParameters, node);\n}\n\nexport function TSInterfaceDeclaration(\n  this: Printer,\n  node: t.TSInterfaceDeclaration,\n) {\n  const { declare, id, typeParameters, extends: extendz, body } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"interface\");\n  this.space();\n  this.print(id, node);\n  this.print(typeParameters, node);\n  if (extendz?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(extendz, node);\n  }\n  this.space();\n  this.print(body, node);\n}\n\nexport function TSInterfaceBody(this: Printer, node: t.TSInterfaceBody) {\n  this.tsPrintTypeLiteralOrInterfaceBody(node.body, node);\n}\n\nexport function TSTypeAliasDeclaration(\n  this: Printer,\n  node: t.TSTypeAliasDeclaration,\n) {\n  const { declare, id, typeParameters, typeAnnotation } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"type\");\n  this.space();\n  this.print(id, node);\n  this.print(typeParameters, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(typeAnnotation, node);\n  this.token(\";\");\n}\n\nfunction TSTypeExpression(\n  this: Printer,\n  node: t.TSAsExpression | t.TSSatisfiesExpression,\n) {\n  const { type, expression, typeAnnotation } = node;\n  const forceParens = !!expression.trailingComments?.length;\n  this.print(expression, node, true, undefined, forceParens);\n  this.space();\n  this.word(type === \"TSAsExpression\" ? \"as\" : \"satisfies\");\n  this.space();\n  this.print(typeAnnotation, node);\n}\n\nexport {\n  TSTypeExpression as TSAsExpression,\n  TSTypeExpression as TSSatisfiesExpression,\n};\n\nexport function TSTypeAssertion(this: Printer, node: t.TSTypeAssertion) {\n  const { typeAnnotation, expression } = node;\n  this.token(\"<\");\n  this.print(typeAnnotation, node);\n  this.token(\">\");\n  this.space();\n  this.print(expression, node);\n}\n\nexport function TSInstantiationExpression(\n  this: Printer,\n  node: t.TSInstantiationExpression,\n) {\n  this.print(node.expression, node);\n  this.print(node.typeParameters, node);\n}\n\nexport function TSEnumDeclaration(this: Printer, node: t.TSEnumDeclaration) {\n  const { declare, const: isConst, id, members } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  if (isConst) {\n    this.word(\"const\");\n    this.space();\n  }\n  this.word(\"enum\");\n  this.space();\n  this.print(id, node);\n  this.space();\n  tsPrintBraced(this, members, node);\n}\n\nexport function TSEnumMember(this: Printer, node: t.TSEnumMember) {\n  const { id, initializer } = node;\n  this.print(id, node);\n  if (initializer) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(initializer, node);\n  }\n  this.token(\",\");\n}\n\nexport function TSModuleDeclaration(\n  this: Printer,\n  node: t.TSModuleDeclaration,\n) {\n  const { declare, id } = node;\n\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (!node.global) {\n    this.word(id.type === \"Identifier\" ? \"namespace\" : \"module\");\n    this.space();\n  }\n  this.print(id, node);\n\n  if (!node.body) {\n    this.token(\";\");\n    return;\n  }\n\n  let body = node.body;\n  while (body.type === \"TSModuleDeclaration\") {\n    this.token(\".\");\n    this.print(body.id, body);\n    body = body.body;\n  }\n\n  this.space();\n  this.print(body, node);\n}\n\nexport function TSModuleBlock(this: Printer, node: t.TSModuleBlock) {\n  tsPrintBraced(this, node.body, node);\n}\n\nexport function TSImportType(this: Printer, node: t.TSImportType) {\n  const { argument, qualifier, typeParameters } = node;\n  this.word(\"import\");\n  this.token(\"(\");\n  this.print(argument, node);\n  this.token(\")\");\n  if (qualifier) {\n    this.token(\".\");\n    this.print(qualifier, node);\n  }\n  if (typeParameters) {\n    this.print(typeParameters, node);\n  }\n}\n\nexport function TSImportEqualsDeclaration(\n  this: Printer,\n  node: t.TSImportEqualsDeclaration,\n) {\n  const { isExport, id, moduleReference } = node;\n  if (isExport) {\n    this.word(\"export\");\n    this.space();\n  }\n  this.word(\"import\");\n  this.space();\n  this.print(id, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(moduleReference, node);\n  this.token(\";\");\n}\n\nexport function TSExternalModuleReference(\n  this: Printer,\n  node: t.TSExternalModuleReference,\n) {\n  this.token(\"require(\");\n  this.print(node.expression, node);\n  this.token(\")\");\n}\n\nexport function TSNonNullExpression(\n  this: Printer,\n  node: t.TSNonNullExpression,\n) {\n  this.print(node.expression, node);\n  this.token(\"!\");\n}\n\nexport function TSExportAssignment(this: Printer, node: t.TSExportAssignment) {\n  this.word(\"export\");\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.expression, node);\n  this.token(\";\");\n}\n\nexport function TSNamespaceExportDeclaration(\n  this: Printer,\n  node: t.TSNamespaceExportDeclaration,\n) {\n  this.word(\"export\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.word(\"namespace\");\n  this.space();\n  this.print(node.id, node);\n}\n\nexport function tsPrintSignatureDeclarationBase(this: Printer, node: any) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? node.params\n    : node.parameters;\n  this.print(typeParameters, node);\n  this.token(\"(\");\n  this._parameters(parameters, node);\n  this.token(\")\");\n  const returnType = process.env.BABEL_8_BREAKING\n    ? node.returnType\n    : node.typeAnnotation;\n  this.print(returnType, node);\n}\n\nexport function tsPrintClassMemberModifiers(\n  this: Printer,\n  node:\n    | t.ClassProperty\n    | t.ClassAccessorProperty\n    | t.ClassMethod\n    | t.ClassPrivateMethod\n    | t.TSDeclareMethod,\n) {\n  const isField =\n    node.type === \"ClassAccessorProperty\" || node.type === \"ClassProperty\";\n  if (isField && node.declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  if (node.accessibility) {\n    this.word(node.accessibility);\n    this.space();\n  }\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (node.override) {\n    this.word(\"override\");\n    this.space();\n  }\n  if (node.abstract) {\n    this.word(\"abstract\");\n    this.space();\n  }\n  if (isField && node.readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,SAASA,gBAAgBA,CAAgBC,IAAwB,EAAE;EACxE,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EAEZ,IAAIF,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACF,SAAK,GAAI,CAAC;EAClC,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;AACvC;AAEO,SAASM,4BAA4BA,CAE1CN,IAAoC,EACpCO,MAAc,EACR;EACN,IAAI,CAACN,SAAK,GAAI,CAAC;EACf,IAAI,CAACO,SAAS,CAACR,IAAI,CAACS,MAAM,EAAET,IAAI,EAAE,CAAC,CAAC,CAAC;EACrC,IAAIO,MAAM,CAACG,IAAI,KAAK,yBAAyB,IAAIV,IAAI,CAACS,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;IACzE,IAAI,CAACV,SAAK,GAAI,CAAC;EACjB;EACA,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB;AAIO,SAASW,eAAeA,CAAgBZ,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACa,EAAE,EAAE;IACX,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EAEA,IAAIF,IAAI,CAACe,GAAG,EAAE;IACZ,IAAI,CAACD,IAAI,CAAC,KAAK,CAAC;IAChB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACY,IAAI,CAEFd,IAAI,CAACgB,IAEZ,CAAC;EAED,IAAIhB,IAAI,CAACiB,UAAU,EAAE;IACnB,IAAI,CAACf,KAAK,CAAC,CAAC;IACZ,IAAI,CAACY,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACiB,UAAU,EAAEjB,IAAI,CAAC;EACnC;EAEA,IAAIA,IAAI,CAACkB,OAAO,EAAE;IAChB,IAAI,CAAChB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACkB,OAAO,EAAElB,IAAI,CAAC;EAChC;AACF;AAEO,SAASmB,mBAAmBA,CAEjCnB,IAA2B,EAC3B;EACA,IAAIA,IAAI,CAACoB,aAAa,EAAE;IACtB,IAAI,CAACN,IAAI,CAACd,IAAI,CAACoB,aAAa,CAAC;IAC7B,IAAI,CAAClB,KAAK,CAAC,CAAC;EACd;EAEA,IAAIF,IAAI,CAACqB,QAAQ,EAAE;IACjB,IAAI,CAACP,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACoB,MAAM,CAACtB,IAAI,CAACuB,SAAS,CAAC;AAC7B;AAEO,SAASC,iBAAiBA,CAE/BxB,IAAyB,EACzBO,MAA+C,EAC/C;EACA,IAAIP,IAAI,CAACyB,OAAO,EAAE;IAChB,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACwB,aAAa,CAAC1B,IAAI,EAAEO,MAAM,CAAC;EAChC,IAAI,CAACN,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS0B,eAAeA,CAAgB3B,IAAuB,EAAE;EACtE,IAAI,CAAC4B,gBAAgB,CAAC5B,IAAI,CAAC;EAC3B,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS4B,eAAeA,CAAgB7B,IAAuB,EAAE;EACtE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC8B,IAAI,EAAE9B,IAAI,CAAC;EAC3B,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAACJ,IAAI,CAAC+B,KAAK,EAAE/B,IAAI,CAAC;AAC9B;AAEO,SAASgC,0BAA0BA,CAExChC,IAAkC,EAClC;EACA,IAAI,CAACiC,+BAA+B,CAACjC,IAAI,CAAC;EAC1C,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASiC,+BAA+BA,CAE7ClC,IAAuC,EACvC;EACA,IAAI,CAACc,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAAC+B,+BAA+B,CAACjC,IAAI,CAAC;EAC1C,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASkC,mBAAmBA,CAEjCnC,IAA2B,EAC3B;EACA,MAAM;IAAEqB,QAAQ;IAAEe;EAAY,CAAC,GAAGpC,IAAI;EACtC,IAAIqB,QAAQ,EAAE;IACZ,IAAI,CAACP,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACmC,2BAA2B,CAACrC,IAAI,CAAC;EACtC,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;EACrC,IAAIoC,WAAW,EAAE;IACf,IAAI,CAAClC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACgC,WAAW,EAAEpC,IAAI,CAAC;EAC/B;EACA,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASoC,2BAA2BA,CAEzCrC,IAAiD,EACjD;EACA,IAAIA,IAAI,CAACsC,QAAQ,EAAE;IACjB,IAAI,CAACrC,SAAK,GAAI,CAAC;EACjB;EACA,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACuC,GAAG,EAAEvC,IAAI,CAAC;EAC1B,IAAIA,IAAI,CAACsC,QAAQ,EAAE;IACjB,IAAI,CAACrC,SAAK,GAAI,CAAC;EACjB;EACA,IAAID,IAAI,CAACG,QAAQ,EAAE;IACjB,IAAI,CAACF,SAAK,GAAI,CAAC;EACjB;AACF;AAEO,SAASuC,iBAAiBA,CAAgBxC,IAAyB,EAAE;EAC1E,MAAM;IAAEyC;EAAK,CAAC,GAAGzC,IAAI;EACrB,IAAIyC,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;IACpC,IAAI,CAAC3B,IAAI,CAAC2B,IAAI,CAAC;IACf,IAAI,CAACvC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACmC,2BAA2B,CAACrC,IAAI,CAAC;EACtC,IAAI,CAACiC,+BAA+B,CAACjC,IAAI,CAAC;EAC1C,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASyC,gBAAgBA,CAAgB1C,IAAwB,EAAE;EACxE,MAAM;IAAEqB,QAAQ;IAAEsB,MAAM,EAAEC;EAAS,CAAC,GAAG5C,IAAI;EAC3C,IAAI4C,QAAQ,EAAE;IACZ,IAAI,CAAC9B,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAImB,QAAQ,EAAE;IACZ,IAAI,CAACP,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACD,SAAK,GAAI,CAAC;EACf,IAAI,CAAC4C,WAAW,CAAC7C,IAAI,CAAC8C,UAAU,EAAE9C,IAAI,CAAC;EACvC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;EACrC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS8C,YAAYA,CAAA,EAAgB;EAC1C,IAAI,CAACjC,IAAI,CAAC,KAAK,CAAC;AAClB;AACO,SAASkC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAAClC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAASmC,gBAAgBA,CAAA,EAAgB;EAC9C,IAAI,CAACnC,IAAI,CAAC,SAAS,CAAC;AACtB;AACO,SAASoC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAACpC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAASqC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAACrC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAASsC,gBAAgBA,CAAA,EAAgB;EAC9C,IAAI,CAACtC,IAAI,CAAC,SAAS,CAAC;AACtB;AACO,SAASuC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAACvC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAASwC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAACxC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAASyC,aAAaA,CAAA,EAAgB;EAC3C,IAAI,CAACzC,IAAI,CAAC,MAAM,CAAC;AACnB;AACO,SAAS0C,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAAC1C,IAAI,CAAC,WAAW,CAAC;AACxB;AACO,SAAS2C,aAAaA,CAAA,EAAgB;EAC3C,IAAI,CAAC3C,IAAI,CAAC,MAAM,CAAC;AACnB;AACO,SAAS4C,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAAC5C,IAAI,CAAC,OAAO,CAAC;AACpB;AACO,SAAS6C,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAAC7C,IAAI,CAAC,WAAW,CAAC;AACxB;AAEO,SAAS8C,UAAUA,CAAA,EAAgB;EACxC,IAAI,CAAC9C,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAAS+C,cAAcA,CAAgB7D,IAAsB,EAAE;EACpE,IAAI,CAAC8D,gCAAgC,CAAC9D,IAAI,CAAC;AAC7C;AAEO,SAAS+D,iBAAiBA,CAAgB/D,IAAyB,EAAE;EAC1E,IAAIA,IAAI,CAACgE,QAAQ,EAAE;IACjB,IAAI,CAAClD,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACY,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAAC4D,gCAAgC,CAAC9D,IAAI,CAAC;AAC7C;AAEO,SAAS8D,gCAAgCA,CAE9C9D,IAA4C,EAC5C;EACA,MAAM;IAAEiE;EAAe,CAAC,GAAGjE,IAAI;EAC/B,MAAM8C,UAAU,GAIZ9C,IAAI,CAAC8C,UAAU;EACnB,IAAI,CAAC1C,KAAK,CAAC6D,cAAc,EAAEjE,IAAI,CAAC;EAChC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAAC4C,WAAW,CAACC,UAAU,EAAE9C,IAAI,CAAC;EAClC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,MAAMgE,UAAU,GAIZlE,IAAI,CAACK,cAAc;EACvB,IAAI,CAACD,KAAK,CAAC8D,UAAU,CAAC7D,cAAc,EAAEL,IAAI,CAAC;AAC7C;AAEO,SAASmE,eAAeA,CAAgBnE,IAAuB,EAAE;EACtE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACoE,QAAQ,EAAEpE,IAAI,EAAE,IAAI,CAAC;EACrC,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACiE,cAAc,EAAEjE,IAAI,EAAE,IAAI,CAAC;AAC7C;AAEO,SAASqE,eAAeA,CAAgBrE,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACsE,OAAO,EAAE;IAChB,IAAI,CAACxD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACuE,aAAa,CAAC;EAC9B,IAAIvE,IAAI,CAACK,cAAc,EAAE;IACvB,IAAI,CAACH,KAAK,CAAC,CAAC;IACZ,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACZ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACK,cAAc,CAACA,cAAc,CAAC;EAChD;AACF;AAEO,SAASmE,WAAWA,CAAgBxE,IAAmB,EAAE;EAC9D,IAAI,CAACc,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACyE,QAAQ,CAAC;EAEzB,IAAIzE,IAAI,CAACiE,cAAc,EAAE;IACvB,IAAI,CAAC7D,KAAK,CAACJ,IAAI,CAACiE,cAAc,EAAEjE,IAAI,CAAC;EACvC;AACF;AAEO,SAAS0E,aAAaA,CAAgB1E,IAAqB,EAAE;EAClE,IAAI,CAAC2E,iCAAiC,CAAC3E,IAAI,CAAC4E,OAAO,EAAE5E,IAAI,CAAC;AAC5D;AAEO,SAAS2E,iCAAiCA,CAE/CC,OAA0B,EAC1B5E,IAAkC,EAClC;EACA6E,aAAa,CAAC,IAAI,EAAED,OAAO,EAAE5E,IAAI,CAAC;AACpC;AAEA,SAAS6E,aAAaA,CAACC,OAAgB,EAAEF,OAAiB,EAAE5E,IAAY,EAAE;EACxE8E,OAAO,CAAC7E,KAAK,CAAC,GAAG,CAAC;EAClB,IAAI2E,OAAO,CAACjE,MAAM,EAAE;IAClBmE,OAAO,CAACC,MAAM,CAAC,CAAC;IAChBD,OAAO,CAACE,OAAO,CAAC,CAAC;IACjB,KAAK,MAAMC,MAAM,IAAIL,OAAO,EAAE;MAC5BE,OAAO,CAAC1E,KAAK,CAAC6E,MAAM,EAAEjF,IAAI,CAAC;MAE3B8E,OAAO,CAACE,OAAO,CAAC,CAAC;IACnB;IACAF,OAAO,CAACI,MAAM,CAAC,CAAC;EAClB;EAEAJ,OAAO,CAACK,UAAU,CAACnF,IAAI,CAAC;AAC1B;AAEO,SAASoF,WAAWA,CAAgBpF,IAAmB,EAAE;EAC9D,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACqF,WAAW,EAAErF,IAAI,EAAE,IAAI,CAAC;EAExC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC;AAClB;AAEO,SAASqF,WAAWA,CAAgBtF,IAAmB,EAAE;EAC9D,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACO,SAAS,CAACR,IAAI,CAACuF,YAAY,EAAEvF,IAAI,CAAC;EACvC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASuF,cAAcA,CAAgBxF,IAAsB,EAAE;EACpE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;EACrC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASwF,UAAUA,CAAgBzF,IAAkB,EAAE;EAC5D,IAAI,CAACC,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;AACvC;AAEO,SAAS0F,kBAAkBA,CAAgB1F,IAA0B,EAAE;EAC5E,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC2F,KAAK,EAAE3F,IAAI,CAAC;EAC5B,IAAIA,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACF,SAAK,GAAI,CAAC;EAClC,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACqF,WAAW,EAAErF,IAAI,CAAC;AACpC;AAEO,SAAS4F,WAAWA,CAAgB5F,IAAmB,EAAE;EAC9D6F,8BAA8B,CAAC,IAAI,EAAE7F,IAAI,EAAE,GAAG,CAAC;AACjD;AAEO,SAAS8F,kBAAkBA,CAAgB9F,IAA0B,EAAE;EAC5E6F,8BAA8B,CAAC,IAAI,EAAE7F,IAAI,EAAE,GAAG,CAAC;AACjD;AAEA,SAAS6F,8BAA8BA,CACrCf,OAAgB,EAChB9E,IAA0C,EAC1C+F,GAAc,EACd;EACAjB,OAAO,CAACkB,SAAS,CAAChG,IAAI,CAACiG,KAAK,EAAEjG,IAAI,EAAE;IAClCkG,SAASA,CAAA,EAAG;MACV,IAAI,CAAChG,KAAK,CAAC,CAAC;MACZ,IAAI,CAACD,KAAK,CAAC8F,GAAG,CAAC;MACf,IAAI,CAAC7F,KAAK,CAAC,CAAC;IACd;EACF,CAAC,CAAC;AACJ;AAEO,SAASiG,iBAAiBA,CAAgBnG,IAAyB,EAAE;EAC1E,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACoG,SAAS,CAAC;EAC1B,IAAI,CAAClG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACY,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACqG,WAAW,CAAC;EAC5B,IAAI,CAACnG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACsG,QAAQ,CAAC;EACzB,IAAI,CAACpG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACuG,SAAS,CAAC;AAC5B;AAEO,SAASC,WAAWA,CAAgBxG,IAAmB,EAAE;EAC9D,IAAI,CAACC,KAAK,CAAC,OAAO,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACyG,aAAa,CAAC;AAChC;AAEO,SAASC,mBAAmBA,CAEjC1G,IAA2B,EAC3B;EACA,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;EACrC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS0G,cAAcA,CAAgB3G,IAAsB,EAAE;EACpE,IAAI,CAACc,IAAI,CAACd,IAAI,CAAC4G,QAAQ,CAAC;EACxB,IAAI,CAAC1G,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;AACvC;AAEO,SAAS6G,mBAAmBA,CAEjC7G,IAA2B,EAC3B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC8G,UAAU,EAAE9G,IAAI,EAAE,IAAI,CAAC;EACvC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAACJ,IAAI,CAAC+G,SAAS,EAAE/G,IAAI,CAAC;EAChC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS+G,YAAYA,CAAgBhH,IAAoB,EAAE;EAChE,MAAM;IAAEiH,QAAQ;IAAE9G,QAAQ;IAAEkB,QAAQ;IAAEoF;EAAc,CAAC,GAAGzG,IAAI;EAC5D,IAAI,CAACC,SAAK,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAImB,QAAQ,EAAE;IACZ6F,gBAAgB,CAAC,IAAI,EAAE7F,QAAQ,CAAC;IAChC,IAAI,CAACP,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACD,SAAK,GAAI,CAAC;EACf,IAAI,CAACa,IAAI,CAEF2F,aAAa,CAACzF,IAErB,CAAC;EACD,IAAI,CAACd,KAAK,CAAC,CAAC;EACZ,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACqG,aAAa,CAACxF,UAAU,EAAEwF,aAAa,CAAC;EAEnD,IAAIQ,QAAQ,EAAE;IACZ,IAAI,CAAC/G,KAAK,CAAC,CAAC;IACZ,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACZ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAAC6G,QAAQ,EAAEjH,IAAI,CAAC;EAC5B;EAEA,IAAI,CAACC,SAAK,GAAI,CAAC;EAEf,IAAIE,QAAQ,EAAE;IACZ+G,gBAAgB,CAAC,IAAI,EAAE/G,QAAQ,CAAC;IAChC,IAAI,CAACF,SAAK,GAAI,CAAC;EACjB;EACA,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACK,cAAc,EAAEL,IAAI,CAAC;EACrC,IAAI,CAACE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,SAAK,IAAI,CAAC;AACjB;AAEA,SAASiH,gBAAgBA,CAACC,IAAa,EAAEC,GAAqB,EAAE;EAC9D,IAAIA,GAAG,KAAK,IAAI,EAAE;IAChBD,IAAI,CAAClH,KAAK,CAACmH,GAAG,CAAC;EACjB;AACF;AAEO,SAASC,aAAaA,CAAgBrH,IAAqB,EAAE;EAClE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACsH,OAAO,EAAEtH,IAAI,CAAC;AAChC;AAEO,SAASuH,6BAA6BA,CAE3CvH,IAAqC,EACrC;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACwH,UAAU,EAAExH,IAAI,CAAC;EACjC,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACiE,cAAc,EAAEjE,IAAI,CAAC;AACvC;AAEO,SAASyH,sBAAsBA,CAEpCzH,IAA8B,EAC9B;EACA,MAAM;IAAEyB,OAAO;IAAEiG,EAAE;IAAEzD,cAAc;IAAE0D,OAAO,EAAEC,OAAO;IAAEC;EAAK,CAAC,GAAG7H,IAAI;EACpE,IAAIyB,OAAO,EAAE;IACX,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACY,IAAI,CAAC,WAAW,CAAC;EACtB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACsH,EAAE,EAAE1H,IAAI,CAAC;EACpB,IAAI,CAACI,KAAK,CAAC6D,cAAc,EAAEjE,IAAI,CAAC;EAChC,IAAI4H,OAAO,YAAPA,OAAO,CAAEjH,MAAM,EAAE;IACnB,IAAI,CAACT,KAAK,CAAC,CAAC;IACZ,IAAI,CAACY,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACM,SAAS,CAACoH,OAAO,EAAE5H,IAAI,CAAC;EAC/B;EACA,IAAI,CAACE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACyH,IAAI,EAAE7H,IAAI,CAAC;AACxB;AAEO,SAAS8H,eAAeA,CAAgB9H,IAAuB,EAAE;EACtE,IAAI,CAAC2E,iCAAiC,CAAC3E,IAAI,CAAC6H,IAAI,EAAE7H,IAAI,CAAC;AACzD;AAEO,SAAS+H,sBAAsBA,CAEpC/H,IAA8B,EAC9B;EACA,MAAM;IAAEyB,OAAO;IAAEiG,EAAE;IAAEzD,cAAc;IAAE5D;EAAe,CAAC,GAAGL,IAAI;EAC5D,IAAIyB,OAAO,EAAE;IACX,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACY,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACsH,EAAE,EAAE1H,IAAI,CAAC;EACpB,IAAI,CAACI,KAAK,CAAC6D,cAAc,EAAEjE,IAAI,CAAC;EAChC,IAAI,CAACE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACC,cAAc,EAAEL,IAAI,CAAC;EAChC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEA,SAAS+H,gBAAgBA,CAEvBhI,IAAgD,EAChD;EAAA,IAAAiI,qBAAA;EACA,MAAM;IAAEvH,IAAI;IAAE8G,UAAU;IAAEnH;EAAe,CAAC,GAAGL,IAAI;EACjD,MAAMkI,WAAW,GAAG,CAAC,GAAAD,qBAAA,GAACT,UAAU,CAACW,gBAAgB,aAA3BF,qBAAA,CAA6BtH,MAAM;EACzD,IAAI,CAACP,KAAK,CAACoH,UAAU,EAAExH,IAAI,EAAE,IAAI,EAAEoI,SAAS,EAAEF,WAAW,CAAC;EAC1D,IAAI,CAAChI,KAAK,CAAC,CAAC;EACZ,IAAI,CAACY,IAAI,CAACJ,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,WAAW,CAAC;EACzD,IAAI,CAACR,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACC,cAAc,EAAEL,IAAI,CAAC;AAClC;AAOO,SAASqI,eAAeA,CAAgBrI,IAAuB,EAAE;EACtE,MAAM;IAAEK,cAAc;IAAEmH;EAAW,CAAC,GAAGxH,IAAI;EAC3C,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAACC,cAAc,EAAEL,IAAI,CAAC;EAChC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACoH,UAAU,EAAExH,IAAI,CAAC;AAC9B;AAEO,SAASsI,yBAAyBA,CAEvCtI,IAAiC,EACjC;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACwH,UAAU,EAAExH,IAAI,CAAC;EACjC,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACiE,cAAc,EAAEjE,IAAI,CAAC;AACvC;AAEO,SAASuI,iBAAiBA,CAAgBvI,IAAyB,EAAE;EAC1E,MAAM;IAAEyB,OAAO;IAAE+G,KAAK,EAAEC,OAAO;IAAEf,EAAE;IAAE9C;EAAQ,CAAC,GAAG5E,IAAI;EACrD,IAAIyB,OAAO,EAAE;IACX,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAIuI,OAAO,EAAE;IACX,IAAI,CAAC3H,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACY,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACsH,EAAE,EAAE1H,IAAI,CAAC;EACpB,IAAI,CAACE,KAAK,CAAC,CAAC;EACZ2E,aAAa,CAAC,IAAI,EAAED,OAAO,EAAE5E,IAAI,CAAC;AACpC;AAEO,SAAS0I,YAAYA,CAAgB1I,IAAoB,EAAE;EAChE,MAAM;IAAE0H,EAAE;IAAEtF;EAAY,CAAC,GAAGpC,IAAI;EAChC,IAAI,CAACI,KAAK,CAACsH,EAAE,EAAE1H,IAAI,CAAC;EACpB,IAAIoC,WAAW,EAAE;IACf,IAAI,CAAClC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACgC,WAAW,EAAEpC,IAAI,CAAC;EAC/B;EACA,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS0I,mBAAmBA,CAEjC3I,IAA2B,EAC3B;EACA,MAAM;IAAEyB,OAAO;IAAEiG;EAAG,CAAC,GAAG1H,IAAI;EAE5B,IAAIyB,OAAO,EAAE;IACX,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACF,IAAI,CAAC4I,MAAM,EAAE;IAChB,IAAI,CAAC9H,IAAI,CAAC4G,EAAE,CAAChH,IAAI,KAAK,YAAY,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC5D,IAAI,CAACR,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACE,KAAK,CAACsH,EAAE,EAAE1H,IAAI,CAAC;EAEpB,IAAI,CAACA,IAAI,CAAC6H,IAAI,EAAE;IACd,IAAI,CAAC5H,SAAK,GAAI,CAAC;IACf;EACF;EAEA,IAAI4H,IAAI,GAAG7H,IAAI,CAAC6H,IAAI;EACpB,OAAOA,IAAI,CAACnH,IAAI,KAAK,qBAAqB,EAAE;IAC1C,IAAI,CAACT,SAAK,GAAI,CAAC;IACf,IAAI,CAACG,KAAK,CAACyH,IAAI,CAACH,EAAE,EAAEG,IAAI,CAAC;IACzBA,IAAI,GAAGA,IAAI,CAACA,IAAI;EAClB;EAEA,IAAI,CAAC3H,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACyH,IAAI,EAAE7H,IAAI,CAAC;AACxB;AAEO,SAAS6I,aAAaA,CAAgB7I,IAAqB,EAAE;EAClE6E,aAAa,CAAC,IAAI,EAAE7E,IAAI,CAAC6H,IAAI,EAAE7H,IAAI,CAAC;AACtC;AAEO,SAAS8I,YAAYA,CAAgB9I,IAAoB,EAAE;EAChE,MAAM;IAAE+I,QAAQ;IAAEC,SAAS;IAAE/E;EAAe,CAAC,GAAGjE,IAAI;EACpD,IAAI,CAACc,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACb,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC2I,QAAQ,EAAE/I,IAAI,CAAC;EAC1B,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI+I,SAAS,EAAE;IACb,IAAI,CAAC/I,SAAK,GAAI,CAAC;IACf,IAAI,CAACG,KAAK,CAAC4I,SAAS,EAAEhJ,IAAI,CAAC;EAC7B;EACA,IAAIiE,cAAc,EAAE;IAClB,IAAI,CAAC7D,KAAK,CAAC6D,cAAc,EAAEjE,IAAI,CAAC;EAClC;AACF;AAEO,SAASiJ,yBAAyBA,CAEvCjJ,IAAiC,EACjC;EACA,MAAM;IAAEkJ,QAAQ;IAAExB,EAAE;IAAEyB;EAAgB,CAAC,GAAGnJ,IAAI;EAC9C,IAAIkJ,QAAQ,EAAE;IACZ,IAAI,CAACpI,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACY,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACsH,EAAE,EAAE1H,IAAI,CAAC;EACpB,IAAI,CAACE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAAC+I,eAAe,EAAEnJ,IAAI,CAAC;EACjC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASmJ,yBAAyBA,CAEvCpJ,IAAiC,EACjC;EACA,IAAI,CAACC,KAAK,CAAC,UAAU,CAAC;EACtB,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACwH,UAAU,EAAExH,IAAI,CAAC;EACjC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASoJ,mBAAmBA,CAEjCrJ,IAA2B,EAC3B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACwH,UAAU,EAAExH,IAAI,CAAC;EACjC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASqJ,kBAAkBA,CAAgBtJ,IAA0B,EAAE;EAC5E,IAAI,CAACc,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACD,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACwH,UAAU,EAAExH,IAAI,CAAC;EACjC,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASsJ,4BAA4BA,CAE1CvJ,IAAoC,EACpC;EACA,IAAI,CAACc,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACY,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACY,IAAI,CAAC,WAAW,CAAC;EACtB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAAC0H,EAAE,EAAE1H,IAAI,CAAC;AAC3B;AAEO,SAASiC,+BAA+BA,CAAgBjC,IAAS,EAAE;EACxE,MAAM;IAAEiE;EAAe,CAAC,GAAGjE,IAAI;EAC/B,MAAM8C,UAAU,GAEZ9C,IAAI,CAAC8C,UAAU;EACnB,IAAI,CAAC1C,KAAK,CAAC6D,cAAc,EAAEjE,IAAI,CAAC;EAChC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAAC4C,WAAW,CAACC,UAAU,EAAE9C,IAAI,CAAC;EAClC,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,MAAMiE,UAAU,GAEZlE,IAAI,CAACK,cAAc;EACvB,IAAI,CAACD,KAAK,CAAC8D,UAAU,EAAElE,IAAI,CAAC;AAC9B;AAEO,SAASwJ,2BAA2BA,CAEzCxJ,IAKqB,EACrB;EACA,MAAMyJ,OAAO,GACXzJ,IAAI,CAACU,IAAI,KAAK,uBAAuB,IAAIV,IAAI,CAACU,IAAI,KAAK,eAAe;EACxE,IAAI+I,OAAO,IAAIzJ,IAAI,CAACyB,OAAO,EAAE;IAC3B,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAIF,IAAI,CAACoB,aAAa,EAAE;IACtB,IAAI,CAACN,IAAI,CAACd,IAAI,CAACoB,aAAa,CAAC;IAC7B,IAAI,CAAClB,KAAK,CAAC,CAAC;EACd;EACA,IAAIF,IAAI,CAAC2C,MAAM,EAAE;IACf,IAAI,CAAC7B,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAIF,IAAI,CAAC0J,QAAQ,EAAE;IACjB,IAAI,CAAC5I,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAIF,IAAI,CAACgE,QAAQ,EAAE;IACjB,IAAI,CAAClD,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;EACA,IAAIuJ,OAAO,IAAIzJ,IAAI,CAACqB,QAAQ,EAAE;IAC5B,IAAI,CAACP,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACZ,KAAK,CAAC,CAAC;EACd;AACF"}