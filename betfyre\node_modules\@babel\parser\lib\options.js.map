{"version": 3, "names": ["defaultOptions", "sourceType", "sourceFilename", "undefined", "startColumn", "startLine", "allowAwaitOutsideFunction", "allowReturnOutsideFunction", "allowNewTargetOutsideFunction", "allowImportExportEverywhere", "allowSuperOutsideMethod", "allowUndeclaredExports", "plugins", "strictMode", "ranges", "tokens", "createParenthesizedExpressions", "errorRecovery", "attachComment", "annexB", "exports", "getOptions", "opts", "Error", "options", "key", "Object", "keys"], "sources": ["../src/options.ts"], "sourcesContent": ["import type { PluginList } from \"./plugin-utils\";\n\n// A second optional argument can be given to further configure\n// the parser process. These options are recognized:\n\nexport type SourceType = \"script\" | \"module\" | \"unambiguous\";\n\nexport type Options = {\n  sourceType: SourceType;\n  sourceFilename?: string;\n  startColumn: number;\n  startLine: number;\n  allowAwaitOutsideFunction: boolean;\n  allowReturnOutsideFunction: boolean;\n  allowNewTargetOutsideFunction: boolean;\n  allowImportExportEverywhere: boolean;\n  allowSuperOutsideMethod: boolean;\n  allowUndeclaredExports: boolean;\n  plugins: PluginList;\n  strictMode: boolean | undefined | null;\n  ranges: boolean;\n  tokens: boolean;\n  createParenthesizedExpressions: boolean;\n  errorRecovery: boolean;\n  attachComment: boolean;\n  annexB: boolean;\n};\n\nexport const defaultOptions: Options = {\n  // Source type (\"script\" or \"module\") for different semantics\n  sourceType: \"script\",\n  // Source filename.\n  sourceFilename: undefined,\n  // Column (0-based) from which to start counting source. Useful for\n  // integration with other tools.\n  startColumn: 0,\n  // Line (1-based) from which to start counting source. Useful for\n  // integration with other tools.\n  startLine: 1,\n  // When enabled, await at the top level is not considered an\n  // error.\n  allowAwaitOutsideFunction: false,\n  // When enabled, a return at the top level is not considered an\n  // error.\n  allowReturnOutsideFunction: false,\n  // When enabled, new.target outside a function or class is not\n  // considered an error.\n  allowNewTargetOutsideFunction: false,\n  // When enabled, import/export statements are not constrained to\n  // appearing at the top of the program.\n  allowImportExportEverywhere: false,\n  // TODO\n  allowSuperOutsideMethod: false,\n  // When enabled, export statements can reference undeclared variables.\n  allowUndeclaredExports: false,\n  // An array of plugins to enable\n  plugins: [],\n  // TODO\n  strictMode: null,\n  // Nodes have their start and end characters offsets recorded in\n  // `start` and `end` properties (directly on the node, rather than\n  // the `loc` object, which holds line/column data. To also add a\n  // [semi-standardized][range] `range` property holding a `[start,\n  // end]` array with the same numbers, set the `ranges` option to\n  // `true`.\n  //\n  // [range]: https://bugzilla.mozilla.org/show_bug.cgi?id=745678\n  ranges: false,\n  // Adds all parsed tokens to a `tokens` property on the `File` node\n  tokens: false,\n  // Whether to create ParenthesizedExpression AST nodes (if false\n  // the parser sets extra.parenthesized on the expression nodes instead).\n  createParenthesizedExpressions: false,\n  // When enabled, errors are attached to the AST instead of being directly thrown.\n  // Some errors will still throw, because @babel/parser can't always recover.\n  errorRecovery: false,\n  // When enabled, comments will be attached to adjacent AST nodes as one of\n  // `leadingComments`, `trailingComments` and `innerComments`. The comment attachment\n  // is vital to preserve comments after transform. If you don't print AST back,\n  // consider set this option to `false` for performance\n  attachComment: true,\n  // When enabled, the parser will support Annex B syntax.\n  // https://tc39.es/ecma262/#sec-additional-ecmascript-features-for-web-browsers\n  annexB: true,\n};\n\n// Interpret and default an options object\n\nexport function getOptions(opts?: Options | null): Options {\n  if (opts && opts.annexB != null && opts.annexB !== false) {\n    throw new Error(\"The `annexB` option can only be set to `false`.\");\n  }\n\n  const options: any = {};\n  for (const key of Object.keys(defaultOptions)) {\n    // @ts-expect-error key may not exist in opts\n    options[key] = opts && opts[key] != null ? opts[key] : defaultOptions[key];\n  }\n  return options;\n}\n"], "mappings": ";;;;;;;AA4BO,MAAMA,cAAuB,GAAG;EAErCC,UAAU,EAAE,QAAQ;EAEpBC,cAAc,EAAEC,SAAS;EAGzBC,WAAW,EAAE,CAAC;EAGdC,SAAS,EAAE,CAAC;EAGZC,yBAAyB,EAAE,KAAK;EAGhCC,0BAA0B,EAAE,KAAK;EAGjCC,6BAA6B,EAAE,KAAK;EAGpCC,2BAA2B,EAAE,KAAK;EAElCC,uBAAuB,EAAE,KAAK;EAE9BC,sBAAsB,EAAE,KAAK;EAE7BC,OAAO,EAAE,EAAE;EAEXC,UAAU,EAAE,IAAI;EAShBC,MAAM,EAAE,KAAK;EAEbC,MAAM,EAAE,KAAK;EAGbC,8BAA8B,EAAE,KAAK;EAGrCC,aAAa,EAAE,KAAK;EAKpBC,aAAa,EAAE,IAAI;EAGnBC,MAAM,EAAE;AACV,CAAC;AAACC,OAAA,CAAApB,cAAA,GAAAA,cAAA;AAIK,SAASqB,UAAUA,CAACC,IAAqB,EAAW;EACzD,IAAIA,IAAI,IAAIA,IAAI,CAACH,MAAM,IAAI,IAAI,IAAIG,IAAI,CAACH,MAAM,KAAK,KAAK,EAAE;IACxD,MAAM,IAAII,KAAK,CAAC,iDAAiD,CAAC;EACpE;EAEA,MAAMC,OAAY,GAAG,CAAC,CAAC;EACvB,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAAC3B,cAAc,CAAC,EAAE;IAE7CwB,OAAO,CAACC,GAAG,CAAC,GAAGH,IAAI,IAAIA,IAAI,CAACG,GAAG,CAAC,IAAI,IAAI,GAAGH,IAAI,CAACG,GAAG,CAAC,GAAGzB,cAAc,CAACyB,GAAG,CAAC;EAC5E;EACA,OAAOD,OAAO;AAChB"}