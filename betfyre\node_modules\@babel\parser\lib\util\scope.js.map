{"version": 3, "names": ["_scopeflags", "require", "_parseError", "<PERSON><PERSON>", "constructor", "flags", "var", "Set", "lexical", "functions", "exports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parser", "inModule", "scopeStack", "undefinedExports", "Map", "inTopLevel", "currentScope", "SCOPE_PROGRAM", "inFunction", "currentVarScopeFlags", "SCOPE_FUNCTION", "allowSuper", "currentThisScopeFlags", "SCOPE_SUPER", "allowDirectSuper", "SCOPE_DIRECT_SUPER", "inClass", "SCOPE_CLASS", "inClassAndNotInNonArrowFunction", "inStaticBlock", "i", "length", "SCOPE_STATIC_BLOCK", "SCOPE_VAR", "inNonArrowFunction", "treatFunctionsAsVar", "treatFunctionsAsVarInScope", "createScope", "enter", "push", "exit", "scope", "pop", "declareName", "name", "bindingType", "loc", "BIND_SCOPE_LEXICAL", "BIND_SCOPE_FUNCTION", "checkRedeclarationInScope", "add", "maybeExportDefined", "BIND_SCOPE_VAR", "delete", "isRedeclaredInScope", "raise", "Errors", "VarRedeclaration", "at", "identifierName", "BIND_KIND_VALUE", "has", "SCOPE_SIMPLE_CATCH", "values", "next", "value", "checkLocalExport", "id", "topLevelScope", "set", "start", "SCOPE_ARROW", "default"], "sources": ["../../src/util/scope.ts"], "sourcesContent": ["import {\n  SCOPE_ARROW,\n  SCOPE_DIRECT_SUPER,\n  SCOPE_FUNCTION,\n  SCOPE_SIMPLE_CATCH,\n  SCOPE_SUPER,\n  SCOPE_PROGRAM,\n  SCOPE_VAR,\n  SCOPE_CLASS,\n  SCOPE_STATIC_BLOCK,\n  BIND_SCOPE_FUNCTION,\n  BIND_SCOPE_VAR,\n  BIND_SCOPE_LEXICAL,\n  BIND_KIND_VALUE,\n  type ScopeFlags,\n  type BindingTypes,\n} from \"./scopeflags\";\nimport type { Position } from \"./location\";\nimport type * as N from \"../types\";\nimport { Errors } from \"../parse-error\";\nimport type Tokenizer from \"../tokenizer\";\n\n// Start an AST node, attaching a start offset.\nexport class Scope {\n  declare flags: ScopeFlags;\n  // A set of var-declared names in the current lexical scope\n  var: Set<string> = new Set();\n  // A set of lexically-declared names in the current lexical scope\n  lexical: Set<string> = new Set();\n  // A set of lexically-declared FunctionDeclaration names in the current lexical scope\n  functions: Set<string> = new Set();\n\n  constructor(flags: ScopeFlags) {\n    this.flags = flags;\n  }\n}\n\n// The functions in this module keep track of declared variables in the\n// current scope in order to detect duplicate variable names.\nexport default class ScopeHandler<IScope extends Scope = Scope> {\n  parser: Tokenizer;\n  scopeStack: Array<IScope> = [];\n  inModule: boolean;\n  undefinedExports: Map<string, Position> = new Map();\n\n  constructor(parser: Tokenizer, inModule: boolean) {\n    this.parser = parser;\n    this.inModule = inModule;\n  }\n\n  get inTopLevel() {\n    return (this.currentScope().flags & SCOPE_PROGRAM) > 0;\n  }\n  get inFunction() {\n    return (this.currentVarScopeFlags() & SCOPE_FUNCTION) > 0;\n  }\n  get allowSuper() {\n    return (this.currentThisScopeFlags() & SCOPE_SUPER) > 0;\n  }\n  get allowDirectSuper() {\n    return (this.currentThisScopeFlags() & SCOPE_DIRECT_SUPER) > 0;\n  }\n  get inClass() {\n    return (this.currentThisScopeFlags() & SCOPE_CLASS) > 0;\n  }\n  get inClassAndNotInNonArrowFunction() {\n    const flags = this.currentThisScopeFlags();\n    return (flags & SCOPE_CLASS) > 0 && (flags & SCOPE_FUNCTION) === 0;\n  }\n  get inStaticBlock() {\n    for (let i = this.scopeStack.length - 1; ; i--) {\n      const { flags } = this.scopeStack[i];\n      if (flags & SCOPE_STATIC_BLOCK) {\n        return true;\n      }\n      if (flags & (SCOPE_VAR | SCOPE_CLASS)) {\n        // function body, module body, class property initializers\n        return false;\n      }\n    }\n  }\n  get inNonArrowFunction() {\n    return (this.currentThisScopeFlags() & SCOPE_FUNCTION) > 0;\n  }\n  get treatFunctionsAsVar() {\n    return this.treatFunctionsAsVarInScope(this.currentScope());\n  }\n\n  createScope(flags: ScopeFlags): Scope {\n    return new Scope(flags);\n  }\n\n  enter(flags: ScopeFlags) {\n    /*:: +createScope: (flags: ScopeFlags) => IScope; */\n    // @ts-expect-error This method will be overwritten by subclasses\n    this.scopeStack.push(this.createScope(flags));\n  }\n\n  exit(): ScopeFlags {\n    const scope = this.scopeStack.pop();\n    return scope.flags;\n  }\n\n  // The spec says:\n  // > At the top level of a function, or script, function declarations are\n  // > treated like var declarations rather than like lexical declarations.\n  treatFunctionsAsVarInScope(scope: IScope): boolean {\n    return !!(\n      scope.flags & (SCOPE_FUNCTION | SCOPE_STATIC_BLOCK) ||\n      (!this.parser.inModule && scope.flags & SCOPE_PROGRAM)\n    );\n  }\n\n  declareName(name: string, bindingType: BindingTypes, loc: Position) {\n    let scope = this.currentScope();\n    if (bindingType & BIND_SCOPE_LEXICAL || bindingType & BIND_SCOPE_FUNCTION) {\n      this.checkRedeclarationInScope(scope, name, bindingType, loc);\n\n      if (bindingType & BIND_SCOPE_FUNCTION) {\n        scope.functions.add(name);\n      } else {\n        scope.lexical.add(name);\n      }\n\n      if (bindingType & BIND_SCOPE_LEXICAL) {\n        this.maybeExportDefined(scope, name);\n      }\n    } else if (bindingType & BIND_SCOPE_VAR) {\n      for (let i = this.scopeStack.length - 1; i >= 0; --i) {\n        scope = this.scopeStack[i];\n        this.checkRedeclarationInScope(scope, name, bindingType, loc);\n        scope.var.add(name);\n        this.maybeExportDefined(scope, name);\n\n        if (scope.flags & SCOPE_VAR) break;\n      }\n    }\n    if (this.parser.inModule && scope.flags & SCOPE_PROGRAM) {\n      this.undefinedExports.delete(name);\n    }\n  }\n\n  maybeExportDefined(scope: IScope, name: string) {\n    if (this.parser.inModule && scope.flags & SCOPE_PROGRAM) {\n      this.undefinedExports.delete(name);\n    }\n  }\n\n  checkRedeclarationInScope(\n    scope: IScope,\n    name: string,\n    bindingType: BindingTypes,\n    loc: Position,\n  ) {\n    if (this.isRedeclaredInScope(scope, name, bindingType)) {\n      this.parser.raise(Errors.VarRedeclaration, {\n        at: loc,\n        identifierName: name,\n      });\n    }\n  }\n\n  isRedeclaredInScope(\n    scope: IScope,\n    name: string,\n    bindingType: BindingTypes,\n  ): boolean {\n    if (!(bindingType & BIND_KIND_VALUE)) return false;\n\n    if (bindingType & BIND_SCOPE_LEXICAL) {\n      return (\n        scope.lexical.has(name) ||\n        scope.functions.has(name) ||\n        scope.var.has(name)\n      );\n    }\n\n    if (bindingType & BIND_SCOPE_FUNCTION) {\n      return (\n        scope.lexical.has(name) ||\n        (!this.treatFunctionsAsVarInScope(scope) && scope.var.has(name))\n      );\n    }\n\n    return (\n      (scope.lexical.has(name) &&\n        // Annex B.3.4\n        // https://tc39.es/ecma262/#sec-variablestatements-in-catch-blocks\n        !(\n          scope.flags & SCOPE_SIMPLE_CATCH &&\n          scope.lexical.values().next().value === name\n        )) ||\n      (!this.treatFunctionsAsVarInScope(scope) && scope.functions.has(name))\n    );\n  }\n\n  checkLocalExport(id: N.Identifier) {\n    const { name } = id;\n    const topLevelScope = this.scopeStack[0];\n    if (\n      !topLevelScope.lexical.has(name) &&\n      !topLevelScope.var.has(name) &&\n      // In strict mode, scope.functions will always be empty.\n      // Modules are strict by default, but the `scriptMode` option\n      // can overwrite this behavior.\n      !topLevelScope.functions.has(name)\n    ) {\n      this.undefinedExports.set(name, id.loc.start);\n    }\n  }\n\n  currentScope(): IScope {\n    return this.scopeStack[this.scopeStack.length - 1];\n  }\n\n  currentVarScopeFlags(): ScopeFlags {\n    for (let i = this.scopeStack.length - 1; ; i--) {\n      const { flags } = this.scopeStack[i];\n      if (flags & SCOPE_VAR) {\n        return flags;\n      }\n    }\n  }\n\n  // Could be useful for `arguments`, `this`, `new.target`, `super()`, `super.property`, and `super[property]`.\n  currentThisScopeFlags(): ScopeFlags {\n    for (let i = this.scopeStack.length - 1; ; i--) {\n      const { flags } = this.scopeStack[i];\n      if (flags & (SCOPE_VAR | SCOPE_CLASS) && !(flags & SCOPE_ARROW)) {\n        return flags;\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AAmBA,IAAAC,WAAA,GAAAD,OAAA;AAIO,MAAME,KAAK,CAAC;EASjBC,WAAWA,CAACC,KAAiB,EAAE;IAAA,KAN/BC,GAAG,GAAgB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAE5BC,OAAO,GAAgB,IAAID,GAAG,CAAC,CAAC;IAAA,KAEhCE,SAAS,GAAgB,IAAIF,GAAG,CAAC,CAAC;IAGhC,IAAI,CAACF,KAAK,GAAGA,KAAK;EACpB;AACF;AAACK,OAAA,CAAAP,KAAA,GAAAA,KAAA;AAIc,MAAMQ,YAAY,CAA+B;EAM9DP,WAAWA,CAACQ,MAAiB,EAAEC,QAAiB,EAAE;IAAA,KALlDD,MAAM;IAAA,KACNE,UAAU,GAAkB,EAAE;IAAA,KAC9BD,QAAQ;IAAA,KACRE,gBAAgB,GAA0B,IAAIC,GAAG,CAAC,CAAC;IAGjD,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAII,UAAUA,CAAA,EAAG;IACf,OAAO,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,CAACb,KAAK,GAAGc,yBAAa,IAAI,CAAC;EACxD;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,CAAC,IAAI,CAACC,oBAAoB,CAAC,CAAC,GAAGC,0BAAc,IAAI,CAAC;EAC3D;EACA,IAAIC,UAAUA,CAAA,EAAG;IACf,OAAO,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,GAAGC,uBAAW,IAAI,CAAC;EACzD;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACrB,OAAO,CAAC,IAAI,CAACF,qBAAqB,CAAC,CAAC,GAAGG,8BAAkB,IAAI,CAAC;EAChE;EACA,IAAIC,OAAOA,CAAA,EAAG;IACZ,OAAO,CAAC,IAAI,CAACJ,qBAAqB,CAAC,CAAC,GAAGK,uBAAW,IAAI,CAAC;EACzD;EACA,IAAIC,+BAA+BA,CAAA,EAAG;IACpC,MAAMzB,KAAK,GAAG,IAAI,CAACmB,qBAAqB,CAAC,CAAC;IAC1C,OAAO,CAACnB,KAAK,GAAGwB,uBAAW,IAAI,CAAC,IAAI,CAACxB,KAAK,GAAGiB,0BAAc,MAAM,CAAC;EACpE;EACA,IAAIS,aAAaA,CAAA,EAAG;IAClB,KAAK,IAAIC,CAAC,GAAG,IAAI,CAAClB,UAAU,CAACmB,MAAM,GAAG,CAAC,GAAID,CAAC,EAAE,EAAE;MAC9C,MAAM;QAAE3B;MAAM,CAAC,GAAG,IAAI,CAACS,UAAU,CAACkB,CAAC,CAAC;MACpC,IAAI3B,KAAK,GAAG6B,8BAAkB,EAAE;QAC9B,OAAO,IAAI;MACb;MACA,IAAI7B,KAAK,IAAI8B,qBAAS,GAAGN,uBAAW,CAAC,EAAE;QAErC,OAAO,KAAK;MACd;IACF;EACF;EACA,IAAIO,kBAAkBA,CAAA,EAAG;IACvB,OAAO,CAAC,IAAI,CAACZ,qBAAqB,CAAC,CAAC,GAAGF,0BAAc,IAAI,CAAC;EAC5D;EACA,IAAIe,mBAAmBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAACpB,YAAY,CAAC,CAAC,CAAC;EAC7D;EAEAqB,WAAWA,CAAClC,KAAiB,EAAS;IACpC,OAAO,IAAIF,KAAK,CAACE,KAAK,CAAC;EACzB;EAEAmC,KAAKA,CAACnC,KAAiB,EAAE;IAGvB,IAAI,CAACS,UAAU,CAAC2B,IAAI,CAAC,IAAI,CAACF,WAAW,CAAClC,KAAK,CAAC,CAAC;EAC/C;EAEAqC,IAAIA,CAAA,EAAe;IACjB,MAAMC,KAAK,GAAG,IAAI,CAAC7B,UAAU,CAAC8B,GAAG,CAAC,CAAC;IACnC,OAAOD,KAAK,CAACtC,KAAK;EACpB;EAKAiC,0BAA0BA,CAACK,KAAa,EAAW;IACjD,OAAO,CAAC,EACNA,KAAK,CAACtC,KAAK,IAAIiB,0BAAc,GAAGY,8BAAkB,CAAC,IAClD,CAAC,IAAI,CAACtB,MAAM,CAACC,QAAQ,IAAI8B,KAAK,CAACtC,KAAK,GAAGc,yBAAc,CACvD;EACH;EAEA0B,WAAWA,CAACC,IAAY,EAAEC,WAAyB,EAAEC,GAAa,EAAE;IAClE,IAAIL,KAAK,GAAG,IAAI,CAACzB,YAAY,CAAC,CAAC;IAC/B,IAAI6B,WAAW,GAAGE,8BAAkB,IAAIF,WAAW,GAAGG,+BAAmB,EAAE;MACzE,IAAI,CAACC,yBAAyB,CAACR,KAAK,EAAEG,IAAI,EAAEC,WAAW,EAAEC,GAAG,CAAC;MAE7D,IAAID,WAAW,GAAGG,+BAAmB,EAAE;QACrCP,KAAK,CAAClC,SAAS,CAAC2C,GAAG,CAACN,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLH,KAAK,CAACnC,OAAO,CAAC4C,GAAG,CAACN,IAAI,CAAC;MACzB;MAEA,IAAIC,WAAW,GAAGE,8BAAkB,EAAE;QACpC,IAAI,CAACI,kBAAkB,CAACV,KAAK,EAAEG,IAAI,CAAC;MACtC;IACF,CAAC,MAAM,IAAIC,WAAW,GAAGO,0BAAc,EAAE;MACvC,KAAK,IAAItB,CAAC,GAAG,IAAI,CAAClB,UAAU,CAACmB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACpDW,KAAK,GAAG,IAAI,CAAC7B,UAAU,CAACkB,CAAC,CAAC;QAC1B,IAAI,CAACmB,yBAAyB,CAACR,KAAK,EAAEG,IAAI,EAAEC,WAAW,EAAEC,GAAG,CAAC;QAC7DL,KAAK,CAACrC,GAAG,CAAC8C,GAAG,CAACN,IAAI,CAAC;QACnB,IAAI,CAACO,kBAAkB,CAACV,KAAK,EAAEG,IAAI,CAAC;QAEpC,IAAIH,KAAK,CAACtC,KAAK,GAAG8B,qBAAS,EAAE;MAC/B;IACF;IACA,IAAI,IAAI,CAACvB,MAAM,CAACC,QAAQ,IAAI8B,KAAK,CAACtC,KAAK,GAAGc,yBAAa,EAAE;MACvD,IAAI,CAACJ,gBAAgB,CAACwC,MAAM,CAACT,IAAI,CAAC;IACpC;EACF;EAEAO,kBAAkBA,CAACV,KAAa,EAAEG,IAAY,EAAE;IAC9C,IAAI,IAAI,CAAClC,MAAM,CAACC,QAAQ,IAAI8B,KAAK,CAACtC,KAAK,GAAGc,yBAAa,EAAE;MACvD,IAAI,CAACJ,gBAAgB,CAACwC,MAAM,CAACT,IAAI,CAAC;IACpC;EACF;EAEAK,yBAAyBA,CACvBR,KAAa,EACbG,IAAY,EACZC,WAAyB,EACzBC,GAAa,EACb;IACA,IAAI,IAAI,CAACQ,mBAAmB,CAACb,KAAK,EAAEG,IAAI,EAAEC,WAAW,CAAC,EAAE;MACtD,IAAI,CAACnC,MAAM,CAAC6C,KAAK,CAACC,kBAAM,CAACC,gBAAgB,EAAE;QACzCC,EAAE,EAAEZ,GAAG;QACPa,cAAc,EAAEf;MAClB,CAAC,CAAC;IACJ;EACF;EAEAU,mBAAmBA,CACjBb,KAAa,EACbG,IAAY,EACZC,WAAyB,EAChB;IACT,IAAI,EAAEA,WAAW,GAAGe,2BAAe,CAAC,EAAE,OAAO,KAAK;IAElD,IAAIf,WAAW,GAAGE,8BAAkB,EAAE;MACpC,OACEN,KAAK,CAACnC,OAAO,CAACuD,GAAG,CAACjB,IAAI,CAAC,IACvBH,KAAK,CAAClC,SAAS,CAACsD,GAAG,CAACjB,IAAI,CAAC,IACzBH,KAAK,CAACrC,GAAG,CAACyD,GAAG,CAACjB,IAAI,CAAC;IAEvB;IAEA,IAAIC,WAAW,GAAGG,+BAAmB,EAAE;MACrC,OACEP,KAAK,CAACnC,OAAO,CAACuD,GAAG,CAACjB,IAAI,CAAC,IACtB,CAAC,IAAI,CAACR,0BAA0B,CAACK,KAAK,CAAC,IAAIA,KAAK,CAACrC,GAAG,CAACyD,GAAG,CAACjB,IAAI,CAAE;IAEpE;IAEA,OACGH,KAAK,CAACnC,OAAO,CAACuD,GAAG,CAACjB,IAAI,CAAC,IAGtB,EACEH,KAAK,CAACtC,KAAK,GAAG2D,8BAAkB,IAChCrB,KAAK,CAACnC,OAAO,CAACyD,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK,KAAKrB,IAAI,CAC7C,IACF,CAAC,IAAI,CAACR,0BAA0B,CAACK,KAAK,CAAC,IAAIA,KAAK,CAAClC,SAAS,CAACsD,GAAG,CAACjB,IAAI,CAAE;EAE1E;EAEAsB,gBAAgBA,CAACC,EAAgB,EAAE;IACjC,MAAM;MAAEvB;IAAK,CAAC,GAAGuB,EAAE;IACnB,MAAMC,aAAa,GAAG,IAAI,CAACxD,UAAU,CAAC,CAAC,CAAC;IACxC,IACE,CAACwD,aAAa,CAAC9D,OAAO,CAACuD,GAAG,CAACjB,IAAI,CAAC,IAChC,CAACwB,aAAa,CAAChE,GAAG,CAACyD,GAAG,CAACjB,IAAI,CAAC,IAI5B,CAACwB,aAAa,CAAC7D,SAAS,CAACsD,GAAG,CAACjB,IAAI,CAAC,EAClC;MACA,IAAI,CAAC/B,gBAAgB,CAACwD,GAAG,CAACzB,IAAI,EAAEuB,EAAE,CAACrB,GAAG,CAACwB,KAAK,CAAC;IAC/C;EACF;EAEAtD,YAAYA,CAAA,EAAW;IACrB,OAAO,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACA,UAAU,CAACmB,MAAM,GAAG,CAAC,CAAC;EACpD;EAEAZ,oBAAoBA,CAAA,EAAe;IACjC,KAAK,IAAIW,CAAC,GAAG,IAAI,CAAClB,UAAU,CAACmB,MAAM,GAAG,CAAC,GAAID,CAAC,EAAE,EAAE;MAC9C,MAAM;QAAE3B;MAAM,CAAC,GAAG,IAAI,CAACS,UAAU,CAACkB,CAAC,CAAC;MACpC,IAAI3B,KAAK,GAAG8B,qBAAS,EAAE;QACrB,OAAO9B,KAAK;MACd;IACF;EACF;EAGAmB,qBAAqBA,CAAA,EAAe;IAClC,KAAK,IAAIQ,CAAC,GAAG,IAAI,CAAClB,UAAU,CAACmB,MAAM,GAAG,CAAC,GAAID,CAAC,EAAE,EAAE;MAC9C,MAAM;QAAE3B;MAAM,CAAC,GAAG,IAAI,CAACS,UAAU,CAACkB,CAAC,CAAC;MACpC,IAAI3B,KAAK,IAAI8B,qBAAS,GAAGN,uBAAW,CAAC,IAAI,EAAExB,KAAK,GAAGoE,uBAAW,CAAC,EAAE;QAC/D,OAAOpE,KAAK;MACd;IACF;EACF;AACF;AAACK,OAAA,CAAAgE,OAAA,GAAA/D,YAAA"}