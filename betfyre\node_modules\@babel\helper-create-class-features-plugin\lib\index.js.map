{"version": 3, "names": ["_core", "require", "_helperFunctionName", "_helperSplitExportDeclaration", "_semver", "_fields", "_decorators", "_misc", "_features", "_typescript", "version<PERSON>ey", "createClassFeaturePlugin", "name", "feature", "loose", "manipulateOptions", "api", "inherits", "_api", "assumption", "setPublicClassFields", "privateFieldsAsSymbols", "privateFieldsAsProperties", "constant<PERSON>uper", "noDocumentAll", "Error", "privateFieldsAsSymbolsOrProperties", "explicit", "undefined", "push", "length", "console", "warn", "join", "pre", "file", "enableFeature", "get", "set", "semver", "lt", "visitor", "Class", "path", "shouldTransform", "isClassDeclaration", "assertFieldTransformed", "isLoose", "constructor", "isDecorated", "hasDecorators", "node", "props", "elements", "computedPaths", "privateNames", "Set", "body", "isClassProperty", "isClassMethod", "computed", "isPrivate", "key", "id", "getName", "setName", "isClassPrivateMethod", "kind", "has", "buildCodeFrameError", "add", "isProperty", "isStaticBlock", "innerBinding", "ref", "isClassExpression", "nameFunction", "scope", "generateUidIdentifier", "t", "cloneNode", "privateNamesMap", "buildPrivateNamesMap", "privateNamesNodes", "buildPrivateNamesNodes", "transformPrivateNamesUsage", "keysNodes", "staticNodes", "instanceNodes", "pureStaticNodes", "wrapClass", "buildDecoratedClass", "extractComputedKeys", "buildFieldsInitNodes", "superClass", "injectInitialization", "referenceVisitor", "state", "prop", "static", "traverse", "<PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "find", "parent", "isStatement", "isDeclaration", "ExportDefaultDeclaration", "decl", "splitExportDeclaration", "type"], "sources": ["../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { PluginAPI, PluginObject } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\n\nimport semver from \"semver\";\n\nimport {\n  buildPrivateNamesNodes,\n  buildPrivateNamesMap,\n  transformPrivateNamesUsage,\n  buildFieldsInitNodes,\n  buildCheckInRHS,\n} from \"./fields\";\nimport type { PropPath } from \"./fields\";\nimport { buildDecoratedClass, hasDecorators } from \"./decorators\";\nimport { injectInitialization, extractComputedKeys } from \"./misc\";\nimport { enableFeature, FEATURES, isLoose, shouldTransform } from \"./features\";\nimport { assertFieldTransformed } from \"./typescript\";\n\nexport { FEATURES, enableFeature, injectInitialization, buildCheckInRHS };\n\nconst versionKey = \"@babel/plugin-class-features/version\";\n\ninterface Options {\n  name: string;\n  feature: number;\n  loose?: boolean;\n  inherits?: PluginObject[\"inherits\"];\n  manipulateOptions?: PluginObject[\"manipulateOptions\"];\n  api?: PluginAPI;\n}\n\nexport function createClassFeaturePlugin({\n  name,\n  feature,\n  loose,\n  manipulateOptions,\n  api,\n  inherits,\n}: Options): PluginObject {\n  if (!process.env.BABEL_8_BREAKING) {\n    api ??= { assumption: () => void 0 as any } as any;\n  }\n  const setPublicClassFields = api.assumption(\"setPublicClassFields\");\n  const privateFieldsAsSymbols = api.assumption(\"privateFieldsAsSymbols\");\n  const privateFieldsAsProperties = api.assumption(\"privateFieldsAsProperties\");\n  const constantSuper = api.assumption(\"constantSuper\");\n  const noDocumentAll = api.assumption(\"noDocumentAll\");\n\n  if (privateFieldsAsProperties && privateFieldsAsSymbols) {\n    throw new Error(\n      `Cannot enable both the \"privateFieldsAsProperties\" and ` +\n        `\"privateFieldsAsSymbols\" assumptions as the same time.`,\n    );\n  }\n  const privateFieldsAsSymbolsOrProperties =\n    privateFieldsAsProperties || privateFieldsAsSymbols;\n\n  if (loose === true) {\n    type AssumptionName = Parameters<PluginAPI[\"assumption\"]>[0];\n    const explicit: `\"${AssumptionName}\"`[] = [];\n\n    if (setPublicClassFields !== undefined) {\n      explicit.push(`\"setPublicClassFields\"`);\n    }\n    if (privateFieldsAsProperties !== undefined) {\n      explicit.push(`\"privateFieldsAsProperties\"`);\n    }\n    if (privateFieldsAsSymbols !== undefined) {\n      explicit.push(`\"privateFieldsAsSymbols\"`);\n    }\n    if (explicit.length !== 0) {\n      console.warn(\n        `[${name}]: You are using the \"loose: true\" option and you are` +\n          ` explicitly setting a value for the ${explicit.join(\" and \")}` +\n          ` assumption${explicit.length > 1 ? \"s\" : \"\"}. The \"loose\" option` +\n          ` can cause incompatibilities with the other class features` +\n          ` plugins, so it's recommended that you replace it with the` +\n          ` following top-level option:\\n` +\n          `\\t\"assumptions\": {\\n` +\n          `\\t\\t\"setPublicClassFields\": true,\\n` +\n          `\\t\\t\"privateFieldsAsSymbols\": true\\n` +\n          `\\t}`,\n      );\n    }\n  }\n\n  return {\n    name,\n    manipulateOptions,\n    inherits,\n\n    pre(file) {\n      enableFeature(file, feature, loose);\n\n      if (!process.env.BABEL_8_BREAKING) {\n        // Until 7.21.4, we used to encode the version as a number.\n        // If file.get(versionKey) is a number, it has thus been\n        // set by an older version of this plugin.\n        if (typeof file.get(versionKey) === \"number\") {\n          file.set(versionKey, PACKAGE_JSON.version);\n          return;\n        }\n      }\n      if (\n        !file.get(versionKey) ||\n        semver.lt(file.get(versionKey), PACKAGE_JSON.version)\n      ) {\n        file.set(versionKey, PACKAGE_JSON.version);\n      }\n    },\n\n    visitor: {\n      Class(path, { file }) {\n        if (file.get(versionKey) !== PACKAGE_JSON.version) return;\n\n        if (!shouldTransform(path, file)) return;\n\n        if (path.isClassDeclaration()) assertFieldTransformed(path);\n\n        const loose = isLoose(file, feature);\n\n        let constructor: NodePath<t.ClassMethod>;\n        const isDecorated = hasDecorators(path.node);\n        const props: PropPath[] = [];\n        const elements = [];\n        const computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[] = [];\n        const privateNames = new Set<string>();\n        const body = path.get(\"body\");\n\n        for (const path of body.get(\"body\")) {\n          if (\n            // check path.node.computed is enough, but ts will complain\n            (path.isClassProperty() || path.isClassMethod()) &&\n            path.node.computed\n          ) {\n            computedPaths.push(path);\n          }\n\n          if (path.isPrivate()) {\n            const { name } = path.node.key.id;\n            const getName = `get ${name}`;\n            const setName = `set ${name}`;\n\n            if (path.isClassPrivateMethod()) {\n              if (path.node.kind === \"get\") {\n                if (\n                  privateNames.has(getName) ||\n                  (privateNames.has(name) && !privateNames.has(setName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(getName).add(name);\n              } else if (path.node.kind === \"set\") {\n                if (\n                  privateNames.has(setName) ||\n                  (privateNames.has(name) && !privateNames.has(getName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(setName).add(name);\n              }\n            } else {\n              if (\n                (privateNames.has(name) &&\n                  !privateNames.has(getName) &&\n                  !privateNames.has(setName)) ||\n                (privateNames.has(name) &&\n                  (privateNames.has(getName) || privateNames.has(setName)))\n              ) {\n                throw path.buildCodeFrameError(\"Duplicate private field\");\n              }\n\n              privateNames.add(name);\n            }\n          }\n\n          if (path.isClassMethod({ kind: \"constructor\" })) {\n            constructor = path;\n          } else {\n            elements.push(path);\n            if (\n              path.isProperty() ||\n              path.isPrivate() ||\n              path.isStaticBlock?.()\n            ) {\n              props.push(path as PropPath);\n            }\n          }\n        }\n\n        if (process.env.BABEL_8_BREAKING) {\n          if (!props.length) return;\n        } else {\n          if (!props.length && !isDecorated) return;\n        }\n\n        const innerBinding = path.node.id;\n        let ref: t.Identifier;\n        if (!innerBinding || path.isClassExpression()) {\n          nameFunction(path);\n          ref = path.scope.generateUidIdentifier(\"class\");\n        } else {\n          ref = t.cloneNode(path.node.id);\n        }\n\n        // NODE: These three functions don't support decorators yet,\n        //       but verifyUsedFeatures throws if there are both\n        //       decorators and private fields.\n        const privateNamesMap = buildPrivateNamesMap(props);\n        const privateNamesNodes = buildPrivateNamesNodes(\n          privateNamesMap,\n          privateFieldsAsProperties ?? loose,\n          privateFieldsAsSymbols ?? false,\n          file,\n        );\n\n        transformPrivateNamesUsage(\n          ref,\n          path,\n          privateNamesMap,\n          {\n            privateFieldsAsProperties:\n              privateFieldsAsSymbolsOrProperties ?? loose,\n            noDocumentAll,\n            innerBinding,\n          },\n          file,\n        );\n\n        let keysNodes: t.Statement[],\n          staticNodes: t.Statement[],\n          instanceNodes: t.Statement[],\n          pureStaticNodes: t.FunctionDeclaration[],\n          wrapClass: (path: NodePath<t.Class>) => NodePath;\n\n        if (!process.env.BABEL_8_BREAKING) {\n          if (isDecorated) {\n            staticNodes = pureStaticNodes = keysNodes = [];\n            ({ instanceNodes, wrapClass } = buildDecoratedClass(\n              ref,\n              path,\n              elements,\n              file,\n            ));\n          } else {\n            keysNodes = extractComputedKeys(path, computedPaths, file);\n            ({ staticNodes, pureStaticNodes, instanceNodes, wrapClass } =\n              buildFieldsInitNodes(\n                ref,\n                path.node.superClass,\n                props,\n                privateNamesMap,\n                file,\n                setPublicClassFields ?? loose,\n                privateFieldsAsSymbolsOrProperties ?? loose,\n                constantSuper ?? loose,\n                innerBinding,\n              ));\n          }\n        } else {\n          keysNodes = extractComputedKeys(path, computedPaths, file);\n          ({ staticNodes, pureStaticNodes, instanceNodes, wrapClass } =\n            buildFieldsInitNodes(\n              ref,\n              path.node.superClass,\n              props,\n              privateNamesMap,\n              file,\n              setPublicClassFields ?? loose,\n              privateFieldsAsSymbolsOrProperties ?? loose,\n              constantSuper ?? loose,\n              innerBinding,\n            ));\n        }\n\n        if (instanceNodes.length > 0) {\n          injectInitialization(\n            path,\n            constructor,\n            instanceNodes,\n            (referenceVisitor, state) => {\n              if (!process.env.BABEL_8_BREAKING) {\n                if (isDecorated) return;\n              }\n              for (const prop of props) {\n                // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n                if (t.isStaticBlock?.(prop.node) || prop.node.static) continue;\n                prop.traverse(referenceVisitor, state);\n              }\n            },\n          );\n        }\n\n        // rename to make ts happy\n        const wrappedPath = wrapClass(path);\n        wrappedPath.insertBefore([...privateNamesNodes, ...keysNodes]);\n        if (staticNodes.length > 0) {\n          wrappedPath.insertAfter(staticNodes);\n        }\n        if (pureStaticNodes.length > 0) {\n          wrappedPath\n            .find(parent => parent.isStatement() || parent.isDeclaration())\n            .insertAfter(pureStaticNodes);\n        }\n      },\n\n      ExportDefaultDeclaration(path, { file }) {\n        if (!process.env.BABEL_8_BREAKING) {\n          if (file.get(versionKey) !== PACKAGE_JSON.version) return;\n\n          const decl = path.get(\"declaration\");\n\n          if (decl.isClassDeclaration() && hasDecorators(decl.node)) {\n            if (decl.node.id) {\n              // export default class Foo {}\n              //   -->\n              // class Foo {} export { Foo as default }\n              splitExportDeclaration(path);\n            } else {\n              // @ts-expect-error Anonymous class declarations can be\n              // transformed as if they were expressions\n              decl.node.type = \"ClassExpression\";\n            }\n          }\n        }\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,6BAAA,GAAAF,OAAA;AAEA,IAAAG,OAAA,GAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAJ,OAAA;AAQA,IAAAK,WAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,SAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAR,OAAA;AAIA,MAAMS,UAAU,GAAG,sCAAsC;AAWlD,SAASC,wBAAwBA,CAAC;EACvCC,IAAI;EACJC,OAAO;EACPC,KAAK;EACLC,iBAAiB;EACjBC,GAAG;EACHC;AACO,CAAC,EAAgB;EACW;IAAA,IAAAC,IAAA;IACjC,CAAAA,IAAA,GAAAF,GAAG,YAAAE,IAAA,GAAHF,GAAG,GAAK;MAAEG,UAAU,EAAEA,CAAA,KAAM,KAAK;IAAS,CAAC;EAC7C;EACA,MAAMC,oBAAoB,GAAGJ,GAAG,CAACG,UAAU,CAAC,sBAAsB,CAAC;EACnE,MAAME,sBAAsB,GAAGL,GAAG,CAACG,UAAU,CAAC,wBAAwB,CAAC;EACvE,MAAMG,yBAAyB,GAAGN,GAAG,CAACG,UAAU,CAAC,2BAA2B,CAAC;EAC7E,MAAMI,aAAa,GAAGP,GAAG,CAACG,UAAU,CAAC,eAAe,CAAC;EACrD,MAAMK,aAAa,GAAGR,GAAG,CAACG,UAAU,CAAC,eAAe,CAAC;EAErD,IAAIG,yBAAyB,IAAID,sBAAsB,EAAE;IACvD,MAAM,IAAII,KAAK,CACZ,yDAAwD,GACtD,wDACL,CAAC;EACH;EACA,MAAMC,kCAAkC,GACtCJ,yBAAyB,IAAID,sBAAsB;EAErD,IAAIP,KAAK,KAAK,IAAI,EAAE;IAElB,MAAMa,QAAiC,GAAG,EAAE;IAE5C,IAAIP,oBAAoB,KAAKQ,SAAS,EAAE;MACtCD,QAAQ,CAACE,IAAI,CAAE,wBAAuB,CAAC;IACzC;IACA,IAAIP,yBAAyB,KAAKM,SAAS,EAAE;MAC3CD,QAAQ,CAACE,IAAI,CAAE,6BAA4B,CAAC;IAC9C;IACA,IAAIR,sBAAsB,KAAKO,SAAS,EAAE;MACxCD,QAAQ,CAACE,IAAI,CAAE,0BAAyB,CAAC;IAC3C;IACA,IAAIF,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;MACzBC,OAAO,CAACC,IAAI,CACT,IAAGpB,IAAK,uDAAsD,GAC5D,uCAAsCe,QAAQ,CAACM,IAAI,CAAC,OAAO,CAAE,EAAC,GAC9D,cAAaN,QAAQ,CAACG,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG,sBAAqB,GACjE,4DAA2D,GAC3D,4DAA2D,GAC3D,gCAA+B,GAC/B,sBAAqB,GACrB,qCAAoC,GACpC,sCAAqC,GACrC,KACL,CAAC;IACH;EACF;EAEA,OAAO;IACLlB,IAAI;IACJG,iBAAiB;IACjBE,QAAQ;IAERiB,GAAGA,CAACC,IAAI,EAAE;MACR,IAAAC,uBAAa,EAACD,IAAI,EAAEtB,OAAO,EAAEC,KAAK,CAAC;MAEA;QAIjC,IAAI,OAAOqB,IAAI,CAACE,GAAG,CAAC3B,UAAU,CAAC,KAAK,QAAQ,EAAE;UAC5CyB,IAAI,CAACG,GAAG,CAAC5B,UAAU,UAAsB,CAAC;UAC1C;QACF;MACF;MACA,IACE,CAACyB,IAAI,CAACE,GAAG,CAAC3B,UAAU,CAAC,IACrB6B,OAAM,CAACC,EAAE,CAACL,IAAI,CAACE,GAAG,CAAC3B,UAAU,CAAC,UAAsB,CAAC,EACrD;QACAyB,IAAI,CAACG,GAAG,CAAC5B,UAAU,UAAsB,CAAC;MAC5C;IACF,CAAC;IAED+B,OAAO,EAAE;MACPC,KAAKA,CAACC,IAAI,EAAE;QAAER;MAAK,CAAC,EAAE;QACpB,IAAIA,IAAI,CAACE,GAAG,CAAC3B,UAAU,CAAC,aAAyB,EAAE;QAEnD,IAAI,CAAC,IAAAkC,yBAAe,EAACD,IAAI,EAAER,IAAI,CAAC,EAAE;QAElC,IAAIQ,IAAI,CAACE,kBAAkB,CAAC,CAAC,EAAE,IAAAC,kCAAsB,EAACH,IAAI,CAAC;QAE3D,MAAM7B,KAAK,GAAG,IAAAiC,iBAAO,EAACZ,IAAI,EAAEtB,OAAO,CAAC;QAEpC,IAAImC,WAAoC;QACxC,MAAMC,WAAW,GAAG,IAAAC,yBAAa,EAACP,IAAI,CAACQ,IAAI,CAAC;QAC5C,MAAMC,KAAiB,GAAG,EAAE;QAC5B,MAAMC,QAAQ,GAAG,EAAE;QACnB,MAAMC,aAA0D,GAAG,EAAE;QACrE,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;QACtC,MAAMC,IAAI,GAAGd,IAAI,CAACN,GAAG,CAAC,MAAM,CAAC;QAE7B,KAAK,MAAMM,IAAI,IAAIc,IAAI,CAACpB,GAAG,CAAC,MAAM,CAAC,EAAE;UACnC,IAEE,CAACM,IAAI,CAACe,eAAe,CAAC,CAAC,IAAIf,IAAI,CAACgB,aAAa,CAAC,CAAC,KAC/ChB,IAAI,CAACQ,IAAI,CAACS,QAAQ,EAClB;YACAN,aAAa,CAACzB,IAAI,CAACc,IAAI,CAAC;UAC1B;UAEA,IAAIA,IAAI,CAACkB,SAAS,CAAC,CAAC,EAAE;YACpB,MAAM;cAAEjD;YAAK,CAAC,GAAG+B,IAAI,CAACQ,IAAI,CAACW,GAAG,CAACC,EAAE;YACjC,MAAMC,OAAO,GAAI,OAAMpD,IAAK,EAAC;YAC7B,MAAMqD,OAAO,GAAI,OAAMrD,IAAK,EAAC;YAE7B,IAAI+B,IAAI,CAACuB,oBAAoB,CAAC,CAAC,EAAE;cAC/B,IAAIvB,IAAI,CAACQ,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;gBAC5B,IACEZ,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IACxBT,YAAY,CAACa,GAAG,CAACxD,IAAI,CAAC,IAAI,CAAC2C,YAAY,CAACa,GAAG,CAACH,OAAO,CAAE,EACtD;kBACA,MAAMtB,IAAI,CAAC0B,mBAAmB,CAAC,yBAAyB,CAAC;gBAC3D;gBACAd,YAAY,CAACe,GAAG,CAACN,OAAO,CAAC,CAACM,GAAG,CAAC1D,IAAI,CAAC;cACrC,CAAC,MAAM,IAAI+B,IAAI,CAACQ,IAAI,CAACgB,IAAI,KAAK,KAAK,EAAE;gBACnC,IACEZ,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,IACxBV,YAAY,CAACa,GAAG,CAACxD,IAAI,CAAC,IAAI,CAAC2C,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAE,EACtD;kBACA,MAAMrB,IAAI,CAAC0B,mBAAmB,CAAC,yBAAyB,CAAC;gBAC3D;gBACAd,YAAY,CAACe,GAAG,CAACL,OAAO,CAAC,CAACK,GAAG,CAAC1D,IAAI,CAAC;cACrC;YACF,CAAC,MAAM;cACL,IACG2C,YAAY,CAACa,GAAG,CAACxD,IAAI,CAAC,IACrB,CAAC2C,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IAC1B,CAACT,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,IAC3BV,YAAY,CAACa,GAAG,CAACxD,IAAI,CAAC,KACpB2C,YAAY,CAACa,GAAG,CAACJ,OAAO,CAAC,IAAIT,YAAY,CAACa,GAAG,CAACH,OAAO,CAAC,CAAE,EAC3D;gBACA,MAAMtB,IAAI,CAAC0B,mBAAmB,CAAC,yBAAyB,CAAC;cAC3D;cAEAd,YAAY,CAACe,GAAG,CAAC1D,IAAI,CAAC;YACxB;UACF;UAEA,IAAI+B,IAAI,CAACgB,aAAa,CAAC;YAAEQ,IAAI,EAAE;UAAc,CAAC,CAAC,EAAE;YAC/CnB,WAAW,GAAGL,IAAI;UACpB,CAAC,MAAM;YACLU,QAAQ,CAACxB,IAAI,CAACc,IAAI,CAAC;YACnB,IACEA,IAAI,CAAC4B,UAAU,CAAC,CAAC,IACjB5B,IAAI,CAACkB,SAAS,CAAC,CAAC,IAChBlB,IAAI,CAAC6B,aAAa,YAAlB7B,IAAI,CAAC6B,aAAa,CAAG,CAAC,EACtB;cACApB,KAAK,CAACvB,IAAI,CAACc,IAAgB,CAAC;YAC9B;UACF;QACF;QAIO;UACL,IAAI,CAACS,KAAK,CAACtB,MAAM,IAAI,CAACmB,WAAW,EAAE;QACrC;QAEA,MAAMwB,YAAY,GAAG9B,IAAI,CAACQ,IAAI,CAACY,EAAE;QACjC,IAAIW,GAAiB;QACrB,IAAI,CAACD,YAAY,IAAI9B,IAAI,CAACgC,iBAAiB,CAAC,CAAC,EAAE;UAC7C,IAAAC,2BAAY,EAACjC,IAAI,CAAC;UAClB+B,GAAG,GAAG/B,IAAI,CAACkC,KAAK,CAACC,qBAAqB,CAAC,OAAO,CAAC;QACjD,CAAC,MAAM;UACLJ,GAAG,GAAGK,WAAC,CAACC,SAAS,CAACrC,IAAI,CAACQ,IAAI,CAACY,EAAE,CAAC;QACjC;QAKA,MAAMkB,eAAe,GAAG,IAAAC,4BAAoB,EAAC9B,KAAK,CAAC;QACnD,MAAM+B,iBAAiB,GAAG,IAAAC,8BAAsB,EAC9CH,eAAe,EACf3D,yBAAyB,WAAzBA,yBAAyB,GAAIR,KAAK,EAClCO,sBAAsB,WAAtBA,sBAAsB,GAAI,KAAK,EAC/Bc,IACF,CAAC;QAED,IAAAkD,kCAA0B,EACxBX,GAAG,EACH/B,IAAI,EACJsC,eAAe,EACf;UACE3D,yBAAyB,EACvBI,kCAAkC,WAAlCA,kCAAkC,GAAIZ,KAAK;UAC7CU,aAAa;UACbiD;QACF,CAAC,EACDtC,IACF,CAAC;QAED,IAAImD,SAAwB,EAC1BC,WAA0B,EAC1BC,aAA4B,EAC5BC,eAAwC,EACxCC,SAAgD;QAEf;UACjC,IAAIzC,WAAW,EAAE;YACfsC,WAAW,GAAGE,eAAe,GAAGH,SAAS,GAAG,EAAE;YAC9C,CAAC;cAAEE,aAAa;cAAEE;YAAU,CAAC,GAAG,IAAAC,+BAAmB,EACjDjB,GAAG,EACH/B,IAAI,EACJU,QAAQ,EACRlB,IACF,CAAC;UACH,CAAC,MAAM;YACLmD,SAAS,GAAG,IAAAM,yBAAmB,EAACjD,IAAI,EAAEW,aAAa,EAAEnB,IAAI,CAAC;YAC1D,CAAC;cAAEoD,WAAW;cAAEE,eAAe;cAAED,aAAa;cAAEE;YAAU,CAAC,GACzD,IAAAG,4BAAoB,EAClBnB,GAAG,EACH/B,IAAI,CAACQ,IAAI,CAAC2C,UAAU,EACpB1C,KAAK,EACL6B,eAAe,EACf9C,IAAI,EACJf,oBAAoB,WAApBA,oBAAoB,GAAIN,KAAK,EAC7BY,kCAAkC,WAAlCA,kCAAkC,GAAIZ,KAAK,EAC3CS,aAAa,WAAbA,aAAa,GAAIT,KAAK,EACtB2D,YACF,CAAC;UACL;QACF;QAgBA,IAAIe,aAAa,CAAC1D,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAAiE,0BAAoB,EAClBpD,IAAI,EACJK,WAAW,EACXwC,aAAa,EACb,CAACQ,gBAAgB,EAAEC,KAAK,KAAK;YACQ;cACjC,IAAIhD,WAAW,EAAE;YACnB;YACA,KAAK,MAAMiD,IAAI,IAAI9C,KAAK,EAAE;cAExB,IAAI2B,WAAC,CAACP,aAAa,YAAfO,WAAC,CAACP,aAAa,CAAG0B,IAAI,CAAC/C,IAAI,CAAC,IAAI+C,IAAI,CAAC/C,IAAI,CAACgD,MAAM,EAAE;cACtDD,IAAI,CAACE,QAAQ,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;YACxC;UACF,CACF,CAAC;QACH;QAGA,MAAMI,WAAW,GAAGX,SAAS,CAAC/C,IAAI,CAAC;QACnC0D,WAAW,CAACC,YAAY,CAAC,CAAC,GAAGnB,iBAAiB,EAAE,GAAGG,SAAS,CAAC,CAAC;QAC9D,IAAIC,WAAW,CAACzD,MAAM,GAAG,CAAC,EAAE;UAC1BuE,WAAW,CAACE,WAAW,CAAChB,WAAW,CAAC;QACtC;QACA,IAAIE,eAAe,CAAC3D,MAAM,GAAG,CAAC,EAAE;UAC9BuE,WAAW,CACRG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,WAAW,CAAC,CAAC,IAAID,MAAM,CAACE,aAAa,CAAC,CAAC,CAAC,CAC9DJ,WAAW,CAACd,eAAe,CAAC;QACjC;MACF,CAAC;MAEDmB,wBAAwBA,CAACjE,IAAI,EAAE;QAAER;MAAK,CAAC,EAAE;QACJ;UACjC,IAAIA,IAAI,CAACE,GAAG,CAAC3B,UAAU,CAAC,aAAyB,EAAE;UAEnD,MAAMmG,IAAI,GAAGlE,IAAI,CAACN,GAAG,CAAC,aAAa,CAAC;UAEpC,IAAIwE,IAAI,CAAChE,kBAAkB,CAAC,CAAC,IAAI,IAAAK,yBAAa,EAAC2D,IAAI,CAAC1D,IAAI,CAAC,EAAE;YACzD,IAAI0D,IAAI,CAAC1D,IAAI,CAACY,EAAE,EAAE;cAIhB,IAAA+C,qCAAsB,EAACnE,IAAI,CAAC;YAC9B,CAAC,MAAM;cAGLkE,IAAI,CAAC1D,IAAI,CAAC4D,IAAI,GAAG,iBAAiB;YACpC;UACF;QACF;MACF;IACF;EACF,CAAC;AACH"}