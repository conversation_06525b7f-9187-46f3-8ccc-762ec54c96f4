const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const port = 8000;

// Serve static files from public directory
app.use(express.static('public'));

// Serve uploaded files
app.use('/uploaded_file', express.static('public/uploaded_file'));

// Basic route for testing - serve a simple HTML page since we can't run PHP
app.get('/', (req, res) => {
    // Create a simple HTML page that shows the site structure
    const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>BetFyre Casino - Local Development</title>
        <link rel="stylesheet" href="/css/app.css">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: white; }
            .container { max-width: 800px; margin: 0 auto; }
            .warning { background: #ff6b6b; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .info { background: #4ecdc4; padding: 20px; border-radius: 8px; margin: 20px 0; color: black; }
            .file-list { background: #333; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .file-list a { color: #4ecdc4; text-decoration: none; display: block; margin: 5px 0; }
            .file-list a:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎰 BetFyre Casino - Local Development Server</h1>

            <div class="warning">
                <h3>⚠️ Limited Functionality</h3>
                <p>This is a static file server. The full Laravel application requires PHP, MySQL, and Redis to function properly.</p>
            </div>

            <div class="info">
                <h3>✅ What's Working</h3>
                <ul>
                    <li>Static assets (CSS, JS, images)</li>
                    <li>Frontend files are accessible</li>
                    <li>Webpack build completed successfully</li>
                </ul>
            </div>

            <div class="file-list">
                <h3>📁 Available Static Files</h3>
                <a href="/css/app.css">CSS Styles</a>
                <a href="/js/app.js">JavaScript Bundle</a>
                <a href="/js/main.js">Main JavaScript</a>
                <a href="/images/">Images Directory</a>
                <a href="/fonts/">Fonts Directory</a>
            </div>

            <div class="info">
                <h3>🔧 To Enable Full Functionality</h3>
                <ol>
                    <li>Install PHP 7.4+</li>
                    <li>Install MySQL/MariaDB</li>
                    <li>Install Redis</li>
                    <li>Run: <code>php artisan serve</code></li>
                    <li>Start the Node.js socket server</li>
                </ol>
            </div>
        </div>

        <script src="/js/app.js"></script>
    </body>
    </html>
    `;
    res.send(html);
});

// Handle static file requests
app.get('*', (req, res, next) => {
    const filePath = path.join(__dirname, 'public', req.path);

    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // File doesn't exist, send 404
            res.status(404).send(`
                <h1>404 - File Not Found</h1>
                <p>The requested file <code>${req.path}</code> was not found.</p>
                <p><a href="/">← Back to Home</a></p>
            `);
        } else {
            // File exists, let express.static handle it
            next();
        }
    });
});

app.listen(port, () => {
    console.log(`🚀 Server running at http://localhost:${port}`);
    console.log('📁 Serving static files from public/ directory');
    console.log('⚠️  Note: This is a static server. PHP functionality requires proper Laravel setup.');
});
