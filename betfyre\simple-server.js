const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const port = 8000;

// Serve static files from public directory
app.use(express.static('public'));

// Serve uploaded files
app.use('/uploaded_file', express.static('public/uploaded_file'));

// Serve a casino-like interface to preview the design
app.get('/', (req, res) => {
    const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>BetFyre Casino</title>
        <link rel="stylesheet" href="/css/app.css">
        <link rel="stylesheet" href="/css/home.css">
        <style>
            body { margin: 0; background: #0a0a0a; color: white; font-family: 'Arial', sans-serif; }
            .header { background: linear-gradient(45deg, #ff6b35, #f7931e); padding: 15px 0; }
            .nav { display: flex; justify-content: space-between; align-items: center; max-width: 1200px; margin: 0 auto; padding: 0 20px; }
            .logo { font-size: 24px; font-weight: bold; color: white; }
            .nav-links { display: flex; gap: 30px; }
            .nav-links a { color: white; text-decoration: none; font-weight: 500; }
            .hero { background: linear-gradient(135deg, #1a1a1a, #2d2d2d); padding: 60px 20px; text-align: center; }
            .hero h1 { font-size: 48px; margin-bottom: 20px; background: linear-gradient(45deg, #ff6b35, #f7931e); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
            .games-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; max-width: 1200px; margin: 40px auto; padding: 0 20px; }
            .game-card { background: #1a1a1a; border-radius: 10px; padding: 20px; text-align: center; border: 2px solid #333; transition: all 0.3s; }
            .game-card:hover { border-color: #ff6b35; transform: translateY(-5px); }
            .game-card img { width: 80px; height: 80px; margin-bottom: 15px; }
            .btn { background: linear-gradient(45deg, #ff6b35, #f7931e); color: white; padding: 12px 30px; border: none; border-radius: 25px; font-weight: bold; cursor: pointer; text-decoration: none; display: inline-block; }
            .setup-info { background: #1a1a1a; margin: 40px auto; max-width: 800px; padding: 30px; border-radius: 10px; border-left: 4px solid #ff6b35; }
        </style>
    </head>
    <body>
        <header class="header">
            <nav class="nav">
                <div class="logo">🎰 BetFyre Casino</div>
                <div class="nav-links">
                    <a href="#games">Games</a>
                    <a href="#promotions">Promotions</a>
                    <a href="#support">Support</a>
                    <a href="#login">Login</a>
                </div>
            </nav>
        </header>

        <section class="hero">
            <h1>Welcome to BetFyre Casino</h1>
            <p style="font-size: 18px; margin-bottom: 30px;">Experience the thrill of online gaming with our exciting casino games</p>
            <a href="#games" class="btn">Play Now</a>
        </section>

        <section id="games" style="padding: 60px 0;">
            <h2 style="text-align: center; margin-bottom: 40px; font-size: 36px;">Our Games</h2>
            <div class="games-grid">
                <div class="game-card">
                    <img src="/img/Crash.png" alt="Crash" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiByeD0iMTAiIGZpbGw9IiNmZjZiMzUiLz4KPHRleHQgeD0iNDAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5DUkFTSDwvdGV4dD4KPC9zdmc+'">
                    <h3>Crash</h3>
                    <p>Watch the multiplier rise and cash out before it crashes!</p>
                    <button class="btn" onclick="alert('PHP backend required for games')">Play Crash</button>
                </div>
                <div class="game-card">
                    <img src="/img/Dice.png" alt="Dice" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiByeD0iMTAiIGZpbGw9IiM0ZWNkYzQiLz4KPHRleHQgeD0iNDAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ESUNFPC90ZXh0Pgo8L3N2Zz4='">
                    <h3>Dice</h3>
                    <p>Roll the dice and predict the outcome to win big!</p>
                    <button class="btn" onclick="alert('PHP backend required for games')">Play Dice</button>
                </div>
                <div class="game-card">
                    <img src="/img/Double.png" alt="Double" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiByeD0iMTAiIGZpbGw9IiNlNzRjM2MiLz4KPHRleHQgeD0iNDAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ET1VCTEU8L3RleHQ+Cjwvc3ZnPg=='">
                    <h3>Double</h3>
                    <p>Choose red, black, or green and double your money!</p>
                    <button class="btn" onclick="alert('PHP backend required for games')">Play Double</button>
                </div>
                <div class="game-card">
                    <img src="/img/FortuneTiger.png" alt="Fortune Tiger" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiByeD0iMTAiIGZpbGw9IiNmZmQ3MDAiLz4KPHRleHQgeD0iNDAiIHk9IjQ1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5USUdFUjwvdGV4dD4KPC9zdmc+'">
                    <h3>Fortune Tiger</h3>
                    <p>Spin the reels and unleash the power of the tiger!</p>
                    <button class="btn" onclick="alert('PHP backend required for games')">Play Slots</button>
                </div>
            </div>
        </section>

        <div class="setup-info">
            <h3>🔧 Setup Status</h3>
            <p><strong>Current Status:</strong> Static Preview Mode</p>
            <p><strong>To enable full functionality:</strong></p>
            <ol>
                <li>Install XAMPP (PHP + MySQL)</li>
                <li>Install Composer</li>
                <li>Run the setup script: <code>setup-casino.bat</code></li>
                <li>Import database: <code>betfyre/betfyre.sql</code></li>
                <li>Start Laravel: <code>php artisan serve</code></li>
            </ol>
            <p><strong>Setup Guide:</strong> See <code>SETUP-GUIDE.md</code> for detailed instructions</p>
        </div>

        <script>
            console.log('BetFyre Casino - Static Preview');
            console.log('For full functionality, complete the PHP/Laravel setup');
        </script>
    </body>
    </html>
    `;
    res.send(html);
});

// Handle static file requests
app.get('*', (req, res, next) => {
    const filePath = path.join(__dirname, 'public', req.path);

    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // File doesn't exist, send 404
            res.status(404).send(`
                <h1>404 - File Not Found</h1>
                <p>The requested file <code>${req.path}</code> was not found.</p>
                <p><a href="/">← Back to Home</a></p>
            `);
        } else {
            // File exists, let express.static handle it
            next();
        }
    });
});

app.listen(port, () => {
    console.log(`🚀 Server running at http://localhost:${port}`);
    console.log('📁 Serving static files from public/ directory');
    console.log('⚠️  Note: This is a static server. PHP functionality requires proper Laravel setup.');
});
