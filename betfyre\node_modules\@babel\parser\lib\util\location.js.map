{"version": 3, "names": ["Position", "constructor", "line", "col", "index", "column", "exports", "SourceLocation", "start", "end", "filename", "identifierName", "createPositionWithColumnOffset", "position", "columnOffset"], "sources": ["../../src/util/location.ts"], "sourcesContent": ["export type Pos = {\n  start: number;\n};\n\n// These are used when `options.locations` is on, for the\n// `startLoc` and `endLoc` properties.\n\nexport class Position {\n  line: number;\n  column: number;\n  index: number;\n\n  constructor(line: number, col: number, index: number) {\n    this.line = line;\n    this.column = col;\n    this.index = index;\n  }\n}\n\nexport class SourceLocation {\n  start: Position;\n  end: Position;\n  filename: string;\n  identifierName: string | undefined | null;\n\n  constructor(start: Position, end?: Position) {\n    this.start = start;\n    // (may start as null, but initialized later)\n    this.end = end;\n  }\n}\n\n/**\n * creates a new position with a non-zero column offset from the given position.\n * This function should be only be used when we create AST node out of the token\n * boundaries, such as TemplateElement ends before tt.templateNonTail. This\n * function does not skip whitespaces.\n */\nexport function createPositionWithColumnOffset(\n  position: Position,\n  columnOffset: number,\n) {\n  const { line, column, index } = position;\n  return new Position(line, column + columnOffset, index + columnOffset);\n}\n"], "mappings": ";;;;;;;AAOO,MAAMA,QAAQ,CAAC;EAKpBC,WAAWA,CAACC,IAAY,EAAEC,GAAW,EAAEC,KAAa,EAAE;IAAA,KAJtDF,IAAI;IAAA,KACJG,MAAM;IAAA,KACND,KAAK;IAGH,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,MAAM,GAAGF,GAAG;IACjB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;AACF;AAACE,OAAA,CAAAN,QAAA,GAAAA,QAAA;AAEM,MAAMO,cAAc,CAAC;EAM1BN,WAAWA,CAACO,KAAe,EAAEC,GAAc,EAAE;IAAA,KAL7CD,KAAK;IAAA,KACLC,GAAG;IAAA,KACHC,QAAQ;IAAA,KACRC,cAAc;IAGZ,IAAI,CAACH,KAAK,GAAGA,KAAK;IAElB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAChB;AACF;AAACH,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAQM,SAASK,8BAA8BA,CAC5CC,QAAkB,EAClBC,YAAoB,EACpB;EACA,MAAM;IAAEZ,IAAI;IAAEG,MAAM;IAAED;EAAM,CAAC,GAAGS,QAAQ;EACxC,OAAO,IAAIb,QAAQ,CAACE,IAAI,EAAEG,MAAM,GAAGS,YAAY,EAAEV,KAAK,GAAGU,YAAY,CAAC;AACxE"}