<!doctype html>
<html lang="bn">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Navbar + Hero</title>
  <style>
    *{box-sizing:border-box}
    body{margin:0;font-family:system-ui,<PERSON><PERSON>}
    .nav{display:flex;justify-content:space-between;align-items:center;padding:12px 16px;border-bottom:1px solid #eee}
    .brand{font-weight:700}
    .links a{margin-left:12px}
    .hero{padding:56px 16px;text-align:center}
    .btn{display:inline-block;padding:10px 16px;border:1px solid #333;border-radius:6px}
    @media (max-width:600px){ .links{display:none} }
  </style>
</head>
<body>
  <div class="nav">
    <div class="brand">MySite</div>
    <div class="links">
      <a href="#">Home</a>
      <a href="#">Blog</a>
      <a href="#">Contact</a>
    </div>
  </div>
  <section class="hero">
    <h1>Get Started</a>
  </section>
</body>
</html>

<!doctype html>
<html lang="bn">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>GET API Example</title>
<style>
  body{font-family:system-ui,Arial;margin:20px}
  .card{border:1px solid #eee;border-radius:8px;padding:12px;margin:8px 0}
</style>
</head>
<body>
  <h2>Recent Posts (GET)</h2>
  <button id="loadBtn">Load Posts</button>
  <div id="list"></div>

  <script>
    const list = document.getElementById('list');
    document.getElementById('loadBtn').addEventListener('click', loadPosts);

    async function loadPosts(){
      list.textContent = 'Loading...';
      try{
        const res = await fetch('https://jsonplaceholder.typicode.com/posts?_limit=5');
        if(!res.ok) throw new Error('Network response was not ok');
        const data = await res.json();
        list.innerHTML = data.map(p => `
          <article class="card">
            <h3>${p.title}</h3>
            <p>${p.body}</p>
          </article>
        `).join('');
      }catch(err){
        list.textContent = 'Error: ' + err.message;
      }
    }
  </script>
</body>
</html>


[
  {
    "userId": 1,
    "id": 1,
    "title": "sunt aut facere repellat provident occaecati excepturi optio reprehenderit",
    "body": "quia et suscipit\nsuscipit recusandae consequuntur expedita et cum\nreprehenderit molestiae ut ut quas totam\nnostrum rerum est autem sunt rem eveniet architecto"
  },
  {
    "userId": 1,
    "id": 2,
    "title": "qui est esse",
    "body": "est rerum tempore vitae\nsequi sint nihil reprehenderit dolor beatae ea dolores neque\nfugiat blanditiis voluptate porro vel nihil molestiae ut reiciendis\nqui aperiam non debitis possimus qui neque nisi nulla"
  },
  {
    "userId": 1,
    "id": 3,
    "title": "ea molestias quasi exercitationem repellat qui ipsa sit aut",
    "body": "et iusto sed quo iure\nvoluptatem occaecati omnis eligendi aut ad\nvoluptatem doloribus vel accusantium quis pariatur\nmolestiae porro eius odio et labore et velit aut"
  },
  {
    "userId": 1,
    "id": 4,
    "title": "eum et est occaecati",
    "body": "ullam et saepe reiciendis voluptatem adipisci\nsit amet autem assumenda provident rerum culpa\nquis hic commodi nesciunt rem tenetur doloremque ipsam iure\nquis sunt voluptatem rerum illo velit"
  },
  {
    "userId": 1,
    "id": 5,
    "title": "nesciunt quas odio",
    "body": "repudiandae veniam quaerat sunt sed\nalias aut fugiat sit autem sed est\nvoluptatem omnis possimus esse voluptatibus quis\nest aut tenetur dolor neque"
  }
]
