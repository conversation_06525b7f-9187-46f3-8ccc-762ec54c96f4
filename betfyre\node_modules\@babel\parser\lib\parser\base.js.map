{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "sawUnambiguousESM", "ambiguousScriptDifferentAst", "hasPlugin", "pluginConfig", "plugins", "has", "pluginName", "pluginOptions", "actualOptions", "get", "key", "Object", "keys", "getPluginOption", "plugin", "name", "_this$plugins$get", "exports", "default"], "sources": ["../../src/parser/base.ts"], "sourcesContent": ["import type { Options } from \"../options\";\nimport type State from \"../tokenizer/state\";\nimport type { PluginsMap } from \"./index\";\nimport type <PERSON><PERSON><PERSON><PERSON><PERSON> from \"../util/scope\";\nimport type Expression<PERSON>copeHandler from \"../util/expression-scope\";\nimport type <PERSON><PERSON><PERSON>Handler from \"../util/class-scope\";\nimport type <PERSON>ParameterHandler from \"../util/production-parameter\";\nimport type {\n  ParserPluginWithOptions,\n  PluginConfig,\n  PluginOptions,\n} from \"../typings\";\n\nexport default class BaseParser {\n  // Properties set by constructor in index.js\n  declare options: Options;\n  declare inModule: boolean;\n  declare scope: ScopeHandler<any>;\n  declare classScope: ClassScopeHandler;\n  declare prodParam: ProductionParameterHandler;\n  declare expressionScope: ExpressionScopeHandler;\n  declare plugins: PluginsMap;\n  declare filename: string | undefined | null;\n  // Names of exports store. `default` is stored as a name for both\n  // `export default foo;` and `export { foo as default };`.\n  declare exportedIdentifiers: Set<string>;\n  sawUnambiguousESM: boolean = false;\n  ambiguousScriptDifferentAst: boolean = false;\n\n  // Initialized by Tokenizer\n  declare state: State;\n  // input and length are not in state as they are constant and we do\n  // not want to ever copy them, which happens if state gets cloned\n  declare input: string;\n  declare length: number;\n\n  // This method accepts either a string (plugin name) or an array pair\n  // (plugin name and options object). If an options object is given,\n  // then each value is non-recursively checked for identity with that\n  // plugin’s actual option value.\n  hasPlugin(pluginConfig: PluginConfig): boolean {\n    if (typeof pluginConfig === \"string\") {\n      return this.plugins.has(pluginConfig);\n    } else {\n      const [pluginName, pluginOptions] = pluginConfig;\n      if (!this.hasPlugin(pluginName)) {\n        return false;\n      }\n      const actualOptions = this.plugins.get(pluginName);\n      for (const key of Object.keys(\n        pluginOptions,\n      ) as (keyof typeof pluginOptions)[]) {\n        if (actualOptions?.[key] !== pluginOptions[key]) {\n          return false;\n        }\n      }\n      return true;\n    }\n  }\n\n  getPluginOption<\n    PluginName extends ParserPluginWithOptions[0],\n    OptionName extends keyof PluginOptions<PluginName>,\n  >(plugin: PluginName, name: OptionName) {\n    return (this.plugins.get(plugin) as null | PluginOptions<PluginName>)?.[\n      name\n    ];\n  }\n}\n"], "mappings": ";;;;;;AAae,MAAMA,UAAU,CAAC;EAAAC,YAAA;IAAA,KAa9BC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,2BAA2B,GAAY,KAAK;EAAA;EAa5CC,SAASA,CAACC,YAA0B,EAAW;IAC7C,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpC,OAAO,IAAI,CAACC,OAAO,CAACC,GAAG,CAACF,YAAY,CAAC;IACvC,CAAC,MAAM;MACL,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGJ,YAAY;MAChD,IAAI,CAAC,IAAI,CAACD,SAAS,CAACI,UAAU,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,MAAME,aAAa,GAAG,IAAI,CAACJ,OAAO,CAACK,GAAG,CAACH,UAAU,CAAC;MAClD,KAAK,MAAMI,GAAG,IAAIC,MAAM,CAACC,IAAI,CAC3BL,aACF,CAAC,EAAoC;QACnC,IAAI,CAAAC,aAAa,oBAAbA,aAAa,CAAGE,GAAG,CAAC,MAAKH,aAAa,CAACG,GAAG,CAAC,EAAE;UAC/C,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;EACF;EAEAG,eAAeA,CAGbC,MAAkB,EAAEC,IAAgB,EAAE;IAAA,IAAAC,iBAAA;IACtC,QAAAA,iBAAA,GAAQ,IAAI,CAACZ,OAAO,CAACK,GAAG,CAACK,MAAM,CAAC,qBAAzBE,iBAAA,CACLD,IAAI,CACL;EACH;AACF;AAACE,OAAA,CAAAC,OAAA,GAAApB,UAAA"}