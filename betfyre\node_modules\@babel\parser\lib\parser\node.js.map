{"version": 3, "names": ["_util", "require", "_location", "Node", "constructor", "parser", "pos", "loc", "type", "start", "end", "SourceLocation", "options", "ranges", "range", "filename", "NodePrototype", "prototype", "__clone", "newNode", "undefined", "keys", "Object", "i", "length", "key", "clonePlaceholder", "node", "cloneIdentifier", "extra", "name", "cloned", "create", "expectedNode", "cloneStringLiteral", "raw", "value", "NodeUtils", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startNode", "state", "startLoc", "startNodeAt", "index", "startNodeAtNode", "finishNode", "finishNodeAt", "lastTokEndLoc", "endLoc", "process", "env", "NODE_ENV", "Error", "attachComment", "processComment", "resetStartLocation", "resetEndLocation", "resetStartLocationFromNode", "locationNode", "exports"], "sources": ["../../src/parser/node.ts"], "sourcesContent": ["import type Parser from \"./index\";\nimport UtilParser from \"./util\";\nimport { SourceLocation, type Position } from \"../util/location\";\nimport type { Comment, Node as NodeType, NodeBase } from \"../types\";\n\n// Start an AST node, attaching a start offset.\n\nclass Node implements NodeBase {\n  constructor(parser: Parser, pos: number, loc: Position) {\n    this.start = pos;\n    this.end = 0;\n    this.loc = new SourceLocation(loc);\n    if (parser?.options.ranges) this.range = [pos, 0];\n    if (parser?.filename) this.loc.filename = parser.filename;\n  }\n\n  type: string = \"\";\n  declare start: number;\n  declare end: number;\n  declare loc: SourceLocation;\n  declare range: [number, number];\n  declare leadingComments: Array<Comment>;\n  declare trailingComments: Array<Comment>;\n  declare innerComments: Array<Comment>;\n  declare extra: {\n    [key: string]: any;\n  };\n}\nconst NodePrototype = Node.prototype;\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-expect-error __clone is not defined in Node prototype\n  NodePrototype.__clone = function (): Node {\n    const newNode = new Node(undefined, this.start, this.loc.start);\n    const keys = Object.keys(this) as (keyof Node)[];\n    for (let i = 0, length = keys.length; i < length; i++) {\n      const key = keys[i];\n      // Do not clone comments that are already attached to the node\n      if (\n        key !== \"leadingComments\" &&\n        key !== \"trailingComments\" &&\n        key !== \"innerComments\"\n      ) {\n        // @ts-expect-error cloning this to newNode\n        newNode[key] = this[key];\n      }\n    }\n\n    return newNode;\n  };\n}\n\nfunction clonePlaceholder(node: any): any {\n  return cloneIdentifier(node);\n}\n\nexport function cloneIdentifier(node: any): any {\n  // We don't need to clone `typeAnnotations` and `optional`: because\n  // cloneIdentifier is only used in object shorthand and named import/export.\n  // Neither of them allow type annotations after the identifier or optional identifier\n  const { type, start, end, loc, range, extra, name } = node;\n  const cloned = Object.create(NodePrototype);\n  cloned.type = type;\n  cloned.start = start;\n  cloned.end = end;\n  cloned.loc = loc;\n  cloned.range = range;\n  cloned.extra = extra;\n  cloned.name = name;\n  if (type === \"Placeholder\") {\n    cloned.expectedNode = node.expectedNode;\n  }\n  return cloned;\n}\n\nexport function cloneStringLiteral(node: any): any {\n  const { type, start, end, loc, range, extra } = node;\n  if (type === \"Placeholder\") {\n    return clonePlaceholder(node);\n  }\n  const cloned = Object.create(NodePrototype);\n  cloned.type = type;\n  cloned.start = start;\n  cloned.end = end;\n  cloned.loc = loc;\n  cloned.range = range;\n  if (node.raw !== undefined) {\n    // estree set node.raw instead of node.extra\n    cloned.raw = node.raw;\n  } else {\n    cloned.extra = extra;\n  }\n  cloned.value = node.value;\n  return cloned;\n}\n\nexport type Undone<T extends NodeType> = Omit<T, \"type\">;\n\nexport abstract class NodeUtils extends UtilParser {\n  startNode<T extends NodeType>(): Undone<T> {\n    // @ts-expect-error cast Node as Undone<T>\n    return new Node(this, this.state.start, this.state.startLoc);\n  }\n\n  startNodeAt<T extends NodeType>(loc: Position): Undone<T> {\n    // @ts-expect-error cast Node as Undone<T>\n    return new Node(this, loc.index, loc);\n  }\n\n  /** Start a new node with a previous node's location. */\n  startNodeAtNode<T extends NodeType>(type: Undone<NodeType>): Undone<T> {\n    return this.startNodeAt(type.loc.start);\n  }\n\n  // Finish an AST node, adding `type` and `end` properties.\n\n  finishNode<T extends NodeType>(node: Undone<T>, type: T[\"type\"]): T {\n    return this.finishNodeAt(node, type, this.state.lastTokEndLoc);\n  }\n\n  // Finish node at given position\n\n  finishNodeAt<T extends NodeType>(\n    node: Omit<T, \"type\">,\n    type: T[\"type\"],\n    endLoc: Position,\n  ): T {\n    if (process.env.NODE_ENV !== \"production\" && node.end > 0) {\n      throw new Error(\n        \"Do not call finishNode*() twice on the same node.\" +\n          \" Instead use resetEndLocation() or change type directly.\",\n      );\n    }\n    // @ts-expect-error migrate to Babel types AST typings\n    node.type = type;\n    // @ts-expect-error migrate to Babel types AST typings\n    node.end = endLoc.index;\n    node.loc.end = endLoc;\n    if (this.options.ranges) node.range[1] = endLoc.index;\n    if (this.options.attachComment) this.processComment(node as T);\n    return node as T;\n  }\n\n  resetStartLocation(node: NodeBase, startLoc: Position): void {\n    node.start = startLoc.index;\n    node.loc.start = startLoc;\n    if (this.options.ranges) node.range[0] = startLoc.index;\n  }\n\n  resetEndLocation(\n    node: NodeBase,\n    endLoc: Position = this.state.lastTokEndLoc,\n  ): void {\n    node.end = endLoc.index;\n    node.loc.end = endLoc;\n    if (this.options.ranges) node.range[1] = endLoc.index;\n  }\n\n  /**\n   * Reset the start location of node to the start location of locationNode\n   */\n  resetStartLocationFromNode(node: NodeBase, locationNode: NodeBase): void {\n    this.resetStartLocation(node, locationNode.loc.start);\n  }\n}\n"], "mappings": ";;;;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAKA,MAAME,IAAI,CAAqB;EAC7BC,WAAWA,CAACC,MAAc,EAAEC,GAAW,EAAEC,GAAa,EAAE;IAAA,KAQxDC,IAAI,GAAW,EAAE;IAPf,IAAI,CAACC,KAAK,GAAGH,GAAG;IAChB,IAAI,CAACI,GAAG,GAAG,CAAC;IACZ,IAAI,CAACH,GAAG,GAAG,IAAII,wBAAc,CAACJ,GAAG,CAAC;IAClC,IAAIF,MAAM,YAANA,MAAM,CAAEO,OAAO,CAACC,MAAM,EAAE,IAAI,CAACC,KAAK,GAAG,CAACR,GAAG,EAAE,CAAC,CAAC;IACjD,IAAID,MAAM,YAANA,MAAM,CAAEU,QAAQ,EAAE,IAAI,CAACR,GAAG,CAACQ,QAAQ,GAAGV,MAAM,CAACU,QAAQ;EAC3D;AAaF;AACA,MAAMC,aAAa,GAAGb,IAAI,CAACc,SAAS;AAED;EAEjCD,aAAa,CAACE,OAAO,GAAG,YAAkB;IACxC,MAAMC,OAAO,GAAG,IAAIhB,IAAI,CAACiB,SAAS,EAAE,IAAI,CAACX,KAAK,EAAE,IAAI,CAACF,GAAG,CAACE,KAAK,CAAC;IAC/D,MAAMY,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC,IAAI,CAAmB;IAChD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;MACrD,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;MAEnB,IACEE,GAAG,KAAK,iBAAiB,IACzBA,GAAG,KAAK,kBAAkB,IAC1BA,GAAG,KAAK,eAAe,EACvB;QAEAN,OAAO,CAACM,GAAG,CAAC,GAAG,IAAI,CAACA,GAAG,CAAC;MAC1B;IACF;IAEA,OAAON,OAAO;EAChB,CAAC;AACH;AAEA,SAASO,gBAAgBA,CAACC,IAAS,EAAO;EACxC,OAAOC,eAAe,CAACD,IAAI,CAAC;AAC9B;AAEO,SAASC,eAAeA,CAACD,IAAS,EAAO;EAI9C,MAAM;IAAEnB,IAAI;IAAEC,KAAK;IAAEC,GAAG;IAAEH,GAAG;IAAEO,KAAK;IAAEe,KAAK;IAAEC;EAAK,CAAC,GAAGH,IAAI;EAC1D,MAAMI,MAAM,GAAGT,MAAM,CAACU,MAAM,CAAChB,aAAa,CAAC;EAC3Ce,MAAM,CAACvB,IAAI,GAAGA,IAAI;EAClBuB,MAAM,CAACtB,KAAK,GAAGA,KAAK;EACpBsB,MAAM,CAACrB,GAAG,GAAGA,GAAG;EAChBqB,MAAM,CAACxB,GAAG,GAAGA,GAAG;EAChBwB,MAAM,CAACjB,KAAK,GAAGA,KAAK;EACpBiB,MAAM,CAACF,KAAK,GAAGA,KAAK;EACpBE,MAAM,CAACD,IAAI,GAAGA,IAAI;EAClB,IAAItB,IAAI,KAAK,aAAa,EAAE;IAC1BuB,MAAM,CAACE,YAAY,GAAGN,IAAI,CAACM,YAAY;EACzC;EACA,OAAOF,MAAM;AACf;AAEO,SAASG,kBAAkBA,CAACP,IAAS,EAAO;EACjD,MAAM;IAAEnB,IAAI;IAAEC,KAAK;IAAEC,GAAG;IAAEH,GAAG;IAAEO,KAAK;IAAEe;EAAM,CAAC,GAAGF,IAAI;EACpD,IAAInB,IAAI,KAAK,aAAa,EAAE;IAC1B,OAAOkB,gBAAgB,CAACC,IAAI,CAAC;EAC/B;EACA,MAAMI,MAAM,GAAGT,MAAM,CAACU,MAAM,CAAChB,aAAa,CAAC;EAC3Ce,MAAM,CAACvB,IAAI,GAAGA,IAAI;EAClBuB,MAAM,CAACtB,KAAK,GAAGA,KAAK;EACpBsB,MAAM,CAACrB,GAAG,GAAGA,GAAG;EAChBqB,MAAM,CAACxB,GAAG,GAAGA,GAAG;EAChBwB,MAAM,CAACjB,KAAK,GAAGA,KAAK;EACpB,IAAIa,IAAI,CAACQ,GAAG,KAAKf,SAAS,EAAE;IAE1BW,MAAM,CAACI,GAAG,GAAGR,IAAI,CAACQ,GAAG;EACvB,CAAC,MAAM;IACLJ,MAAM,CAACF,KAAK,GAAGA,KAAK;EACtB;EACAE,MAAM,CAACK,KAAK,GAAGT,IAAI,CAACS,KAAK;EACzB,OAAOL,MAAM;AACf;AAIO,MAAeM,SAAS,SAASC,aAAU,CAAC;EACjDC,SAASA,CAAA,EAAkC;IAEzC,OAAO,IAAIpC,IAAI,CAAC,IAAI,EAAE,IAAI,CAACqC,KAAK,CAAC/B,KAAK,EAAE,IAAI,CAAC+B,KAAK,CAACC,QAAQ,CAAC;EAC9D;EAEAC,WAAWA,CAAqBnC,GAAa,EAAa;IAExD,OAAO,IAAIJ,IAAI,CAAC,IAAI,EAAEI,GAAG,CAACoC,KAAK,EAAEpC,GAAG,CAAC;EACvC;EAGAqC,eAAeA,CAAqBpC,IAAsB,EAAa;IACrE,OAAO,IAAI,CAACkC,WAAW,CAAClC,IAAI,CAACD,GAAG,CAACE,KAAK,CAAC;EACzC;EAIAoC,UAAUA,CAAqBlB,IAAe,EAAEnB,IAAe,EAAK;IAClE,OAAO,IAAI,CAACsC,YAAY,CAACnB,IAAI,EAAEnB,IAAI,EAAE,IAAI,CAACgC,KAAK,CAACO,aAAa,CAAC;EAChE;EAIAD,YAAYA,CACVnB,IAAqB,EACrBnB,IAAe,EACfwC,MAAgB,EACb;IACH,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIxB,IAAI,CAACjB,GAAG,GAAG,CAAC,EAAE;MACzD,MAAM,IAAI0C,KAAK,CACb,mDAAmD,GACjD,0DACJ,CAAC;IACH;IAEAzB,IAAI,CAACnB,IAAI,GAAGA,IAAI;IAEhBmB,IAAI,CAACjB,GAAG,GAAGsC,MAAM,CAACL,KAAK;IACvBhB,IAAI,CAACpB,GAAG,CAACG,GAAG,GAAGsC,MAAM;IACrB,IAAI,IAAI,CAACpC,OAAO,CAACC,MAAM,EAAEc,IAAI,CAACb,KAAK,CAAC,CAAC,CAAC,GAAGkC,MAAM,CAACL,KAAK;IACrD,IAAI,IAAI,CAAC/B,OAAO,CAACyC,aAAa,EAAE,IAAI,CAACC,cAAc,CAAC3B,IAAS,CAAC;IAC9D,OAAOA,IAAI;EACb;EAEA4B,kBAAkBA,CAAC5B,IAAc,EAAEc,QAAkB,EAAQ;IAC3Dd,IAAI,CAAClB,KAAK,GAAGgC,QAAQ,CAACE,KAAK;IAC3BhB,IAAI,CAACpB,GAAG,CAACE,KAAK,GAAGgC,QAAQ;IACzB,IAAI,IAAI,CAAC7B,OAAO,CAACC,MAAM,EAAEc,IAAI,CAACb,KAAK,CAAC,CAAC,CAAC,GAAG2B,QAAQ,CAACE,KAAK;EACzD;EAEAa,gBAAgBA,CACd7B,IAAc,EACdqB,MAAgB,GAAG,IAAI,CAACR,KAAK,CAACO,aAAa,EACrC;IACNpB,IAAI,CAACjB,GAAG,GAAGsC,MAAM,CAACL,KAAK;IACvBhB,IAAI,CAACpB,GAAG,CAACG,GAAG,GAAGsC,MAAM;IACrB,IAAI,IAAI,CAACpC,OAAO,CAACC,MAAM,EAAEc,IAAI,CAACb,KAAK,CAAC,CAAC,CAAC,GAAGkC,MAAM,CAACL,KAAK;EACvD;EAKAc,0BAA0BA,CAAC9B,IAAc,EAAE+B,YAAsB,EAAQ;IACvE,IAAI,CAACH,kBAAkB,CAAC5B,IAAI,EAAE+B,YAAY,CAACnD,GAAG,CAACE,KAAK,CAAC;EACvD;AACF;AAACkD,OAAA,CAAAtB,SAAA,GAAAA,SAAA"}