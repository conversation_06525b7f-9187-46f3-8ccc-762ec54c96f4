@echo off
echo ========================================
echo    BETFYRE CASINO SETUP SCRIPT
echo ========================================
echo.

echo [1/6] Checking PHP installation...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install XAMPP or PHP first
    pause
    exit /b 1
)

echo [2/6] Checking Composer installation...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer is not installed
    echo Please install Composer from https://getcomposer.org/
    pause
    exit /b 1
)

echo [3/6] Installing PHP dependencies...
cd betfyre
composer install --no-dev --optimize-autoloader

echo [4/6] Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo Created .env file from example
)

echo [5/6] Generating application key...
php artisan key:generate

echo [6/6] Setting up database...
echo Please make sure MySQL is running and create a database named 'casino'
echo Then import the betfyre.sql file into your database

echo.
echo ========================================
echo    SETUP COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. Start MySQL and import betfyre/betfyre.sql
echo 2. Update .env file with your database credentials
echo 3. Run: php artisan serve
echo 4. Visit: http://localhost:8000
echo.
pause
