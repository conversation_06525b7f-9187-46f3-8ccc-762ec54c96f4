{"version": 3, "names": ["dispose_SuppressedError", "suppressed", "error", "SuppressedError", "stack", "Error", "prototype", "Object", "create", "constructor", "value", "writable", "configurable", "_dispose", "<PERSON><PERSON><PERSON><PERSON>", "next", "length", "r", "pop", "a", "Promise", "resolve", "d", "call", "v", "then", "err", "e"], "sources": ["../../src/helpers/dispose.js"], "sourcesContent": ["/* @minVersion 7.22.0 */\nfunction dispose_SuppressedError(suppressed, error) {\n  if (typeof SuppressedError !== \"undefined\") {\n    // eslint-disable-next-line no-undef\n    dispose_SuppressedError = SuppressedError;\n  } else {\n    dispose_SuppressedError = function SuppressedError(suppressed, error) {\n      this.suppressed = suppressed;\n      this.error = error;\n      this.stack = new Error().stack;\n    };\n    dispose_SuppressedError.prototype = Object.create(Error.prototype, {\n      constructor: {\n        value: dispose_SuppressedError,\n        writable: true,\n        configurable: true,\n      },\n    });\n  }\n  return new dispose_SuppressedError(suppressed, error);\n}\n\nexport default function _dispose(stack, error, hasError) {\n  function next() {\n    if (stack.length === 0) {\n      if (hasError) throw error;\n      return;\n    }\n\n    var r = stack.pop();\n    if (r.a) {\n      return Promise.resolve(r.d.call(r.v)).then(next, err);\n    }\n    try {\n      r.d.call(r.v);\n    } catch (e) {\n      return err(e);\n    }\n    return next();\n  }\n\n  function err(e) {\n    error = hasError ? new dispose_SuppressedError(e, error) : e;\n    hasError = true;\n\n    return next();\n  }\n\n  return next();\n}\n"], "mappings": ";;;;;;AACA,SAASA,uBAAuBA,CAACC,UAAU,EAAEC,KAAK,EAAE;EAClD,IAAI,OAAOC,eAAe,KAAK,WAAW,EAAE;IAE1CH,uBAAuB,GAAGG,eAAe;EAC3C,CAAC,MAAM;IACLH,uBAAuB,GAAG,SAASG,eAAeA,CAACF,UAAU,EAAEC,KAAK,EAAE;MACpE,IAAI,CAACD,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACE,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC,CAACD,KAAK;IAChC,CAAC;IACDJ,uBAAuB,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,KAAK,CAACC,SAAS,EAAE;MACjEG,WAAW,EAAE;QACXC,KAAK,EAAEV,uBAAuB;QAC9BW,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,uBAAuB,CAACC,UAAU,EAAEC,KAAK,CAAC;AACvD;AAEe,SAASW,QAAQA,CAACT,KAAK,EAAEF,KAAK,EAAEY,QAAQ,EAAE;EACvD,SAASC,IAAIA,CAAA,EAAG;IACd,IAAIX,KAAK,CAACY,MAAM,KAAK,CAAC,EAAE;MACtB,IAAIF,QAAQ,EAAE,MAAMZ,KAAK;MACzB;IACF;IAEA,IAAIe,CAAC,GAAGb,KAAK,CAACc,GAAG,CAAC,CAAC;IACnB,IAAID,CAAC,CAACE,CAAC,EAAE;MACP,OAAOC,OAAO,CAACC,OAAO,CAACJ,CAAC,CAACK,CAAC,CAACC,IAAI,CAACN,CAAC,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAACV,IAAI,EAAEW,GAAG,CAAC;IACvD;IACA,IAAI;MACFT,CAAC,CAACK,CAAC,CAACC,IAAI,CAACN,CAAC,CAACO,CAAC,CAAC;IACf,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV,OAAOD,GAAG,CAACC,CAAC,CAAC;IACf;IACA,OAAOZ,IAAI,CAAC,CAAC;EACf;EAEA,SAASW,GAAGA,CAACC,CAAC,EAAE;IACdzB,KAAK,GAAGY,QAAQ,GAAG,IAAId,uBAAuB,CAAC2B,CAAC,EAAEzB,KAAK,CAAC,GAAGyB,CAAC;IAC5Db,QAAQ,GAAG,IAAI;IAEf,OAAOC,IAAI,CAAC,CAAC;EACf;EAEA,OAAOA,IAAI,CAAC,CAAC;AACf"}