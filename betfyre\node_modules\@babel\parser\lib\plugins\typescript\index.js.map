{"version": 3, "names": ["_types", "require", "_context", "_location", "_scopeflags", "_scope", "_productionParameter", "_parseError", "_node", "_lval", "getOwn", "object", "key", "Object", "hasOwnProperty", "call", "nonNull", "x", "Error", "assert", "TSErrors", "ParseErrorEnum", "AbstractMethodHasImplementation", "methodName", "AbstractPropertyHasInitializer", "propertyName", "AccesorCannotDeclareThisParameter", "AccesorCannotHaveTypeParameters", "AccessorCannotBeOptional", "ClassMethodHasDeclare", "ClassMethodHasReadonly", "ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference", "ConstructorHasTypeParameters", "<PERSON>lareAccessor", "kind", "DeclareClassFieldHasInitializer", "DeclareFunctionHasImplementation", "DuplicateAccessibilityModifier", "modifier", "DuplicateModifier", "EmptyHeritageClauseType", "token", "EmptyTypeArguments", "EmptyTypeParameters", "ExpectedAmbientAfterExportDeclare", "ImportAliasHasImportType", "ImportReflectionHasImportType", "IncompatibleModifiers", "modifiers", "IndexSignatureHasAbstract", "IndexSignatureHasAccessibility", "IndexSignatureHasDeclare", "IndexSignatureHasOverride", "IndexSignatureHasStatic", "InitializerNotAllowedInAmbientContext", "InvalidModifierOnTypeMember", "InvalidModifierOnTypeParameter", "InvalidModifierOnTypeParameterPositions", "InvalidModifiersOrder", "orderedModifiers", "InvalidPropertyAccessAfterInstantiationExpression", "InvalidTupleMemberLabel", "MissingInterfaceName", "MixedLabeledAndUnlabeledElements", "NonAbstractClassHasAbstractMethod", "NonClassMethodPropertyHasAbstractModifer", "OptionalTypeBeforeRequired", "OverrideNotInSubClass", "PatternIsOptional", "PrivateElementHasAbstract", "PrivateElementHasAccessibility", "ReadonlyForMethodSignature", "ReservedArrowTypeParam", "ReservedTypeAssertion", "SetAccesorCannotHaveOptionalParameter", "SetAccesorCannotHaveRestParameter", "SetAccesorCannotHaveReturnType", "SingleTypeParameterWithoutTrailingComma", "typeParameterName", "StaticBlockCannotHaveModifier", "TupleOptionalAfterType", "TypeAnnotationAfterAssign", "TypeImportCannotSpecifyDefaultAndNamed", "TypeModifierIsUsedInTypeExports", "TypeModifierIsUsedInTypeImports", "UnexpectedParameterModifier", "Unexpected<PERSON><PERSON><PERSON>ly", "UnexpectedTypeAnnotation", "UnexpectedTypeCastInParameter", "UnsupportedImportTypeArgument", "UnsupportedParameterPropertyKind", "UnsupportedSignatureParameterKind", "type", "keywordTypeFromName", "value", "undefined", "tsIsAccessModifier", "tsIsVarianceAnnotations", "_default", "superClass", "TypeScriptParserMixin", "constructor", "args", "tsParseInOutModifiers", "tsParseModifiers", "bind", "allowedModifiers", "disallowedModifiers", "errorTemplate", "tsParseConstModifier", "tsParseInOutConstModifiers", "getScopeHandler", "TypeScriptScopeHandler", "tsIsIdentifier", "tokenIsIdentifier", "state", "tsTokenCanFollowModifier", "match", "isLiteralPropertyName", "hasPrecedingLineBreak", "tsNextTokenCanFollowModifier", "next", "tsParseModifier", "stopOnStartOfClassStaticBlock", "indexOf", "tsIsStartOfStaticBlocks", "tsTryParse", "modified", "enforceOrder", "loc", "before", "after", "raise", "at", "incompatible", "mod1", "mod2", "startLoc", "concat", "accessibility", "includes", "tsIsListTerminator", "tsParseList", "parseElement", "result", "push", "tsParseDelimitedList", "refTrailingCommaPos", "tsParseDelimitedListWorker", "expectSuccess", "trailingCommaPos", "element", "eat", "lastTokStart", "expect", "tsParseBracketedList", "bracket", "skipFirstToken", "tsParseImportType", "node", "startNode", "argument", "parseExprAtom", "qualifier", "tsParseEntityName", "typeParameters", "tsParseTypeArguments", "finishNode", "allowReservedWords", "entity", "parseIdentifier", "startNodeAtNode", "left", "right", "tsParseTypeReference", "typeName", "tsParseThisTypePredicate", "lhs", "parameterName", "typeAnnotation", "tsParseTypeAnnotation", "asserts", "tsParseThisTypeNode", "tsParseTypeQuery", "exprName", "tsParseTypeParameter", "parseModifiers", "name", "tsParseTypeParameterName", "constraint", "tsEatThenParseType", "default", "tsTryParseTypeParameters", "tsParseTypeParameters", "unexpected", "params", "length", "addExtra", "tsFillSignature", "returnToken", "signature", "returnTokenRequired", "params<PERSON><PERSON>", "returnTypeKey", "tsParseBindingListForSignature", "tsParseTypeOrTypePredicateAnnotation", "list", "parseBindingList", "ParseBindingListFlags", "IS_FUNCTION_PARAMS", "pattern", "tsParseTypeMemberSemicolon", "isLineTerminator", "tsParseSignatureMember", "tsIsUnambiguouslyIndexSignature", "tsTryParseIndexSignature", "tsLookAhead", "id", "resetEndLocation", "parameters", "tsTryParseTypeAnnotation", "tsParsePropertyOrMethodSignature", "readonly", "optional", "nodeAny", "method", "curPosition", "Errors", "BadGetterArity", "isThisParam", "BadSetterArity", "firstParameter", "property", "tsParseTypeMember", "createIdentifier", "idx", "parsePropertyName", "computed", "tsParseTypeLiteral", "members", "tsParseObjectTypeMembers", "tsIsStartOfMappedType", "isContextual", "tsParseMappedTypeParameter", "tsExpectThenParseType", "tsParseMappedType", "expectContextual", "eatContextual", "typeParameter", "nameType", "tsParseType", "tsTryParseType", "semicolon", "tsParseTupleType", "elementTypes", "tsParseTupleElementType", "seenOptionalElement", "labeledElements", "for<PERSON>ach", "elementNode", "_labeledElements", "checkType", "isLabeled", "rest", "labeled", "label", "isWord", "tokenIsKeywordOrIdentifier", "chAfterWord", "lookaheadCharCode", "wordName", "typeOrLabel", "tsParseNonArrayType", "startNodeAt", "labeledNode", "elementType", "lastTokStartLoc", "optionalTypeNode", "restNode", "tsParseParenthesizedType", "tsParseFunctionOrConstructorType", "abstract", "tsInAllowConditionalTypesContext", "tsParseLiteralTypeNode", "literal", "tsParseTemplateLiteralType", "parseTemplate", "parseTemplateSubstitution", "inType", "tsParseThisTypeOrThisTypePredicate", "thisKeyword", "nextToken", "<PERSON><PERSON><PERSON>", "parseMaybeUnary", "nodeType", "tsParseArrayType<PERSON><PERSON><PERSON><PERSON><PERSON>", "objectType", "indexType", "tsParseTypeOperator", "operator", "tsParseTypeOperator<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsCheckTypeAnnotationForReadOnly", "tsParseInferType", "tsParseConstraintForInferType", "tsInDisallowConditionalTypesContext", "inDisallowConditionalTypesContext", "isTypeOperator", "tokenIsTSTypeOperator", "containsEsc", "tsParseUnionOrIntersectionType", "parseConstituentType", "hasLeadingOperator", "types", "tsParseIntersectionType<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsParseUnionType<PERSON>r<PERSON><PERSON>er", "tsIsStartOfFunctionType", "tsIsUnambiguouslyStartOfFunctionType", "tsSkipParameterStart", "errors", "previousErrorCount", "parseObjectLike", "_unused", "ALLOW_EMPTY", "_unused2", "tsInType", "t", "tsParseTypePredicateAsserts", "thisTypePredicate", "resetStartLocationFromNode", "typePredicateVariable", "tsParseTypePredicatePrefix", "tsTryParseTypeOrTypePredicateAnnotation", "InvalidEscapedReservedWord", "reservedWord", "eatColon", "tsParseNonConditionalType", "extendsType", "trueType", "falseType", "isAbstractConstructorSignature", "tsParseTypeAssertion", "getPluginOption", "expression", "tsParseHeritageClause", "originalStartLoc", "delimitedList", "tsParseInterfaceDeclaration", "properties", "hasFollowingLineBreak", "declare", "checkIdentifier", "BIND_TS_INTERFACE", "extends", "body", "tsParseTypeAliasDeclaration", "BIND_TS_TYPE", "tsInNoContext", "cb", "oldContext", "context", "oldInType", "oldInDisallowConditionalTypesContext", "tsNextThenParseType", "tsParseEnumMember", "parseStringLiteral", "initializer", "parseMaybeAssignAllowIn", "tsParseEnumDeclaration", "const", "BIND_TS_CONST_ENUM", "BIND_TS_ENUM", "tsParseModuleBlock", "scope", "enter", "SCOPE_OTHER", "parseBlockOrModuleBlockBody", "exit", "tsParseModuleOrNamespaceDeclaration", "nested", "BIND_TS_NAMESPACE", "inner", "SCOPE_TS_MODULE", "prodParam", "PARAM", "tsParseAmbientExternalModuleDeclaration", "global", "tsParseImportEqualsDeclaration", "maybeDefaultIdentifier", "isExport", "BIND_FLAGS_TS_IMPORT", "moduleReference", "tsParseModuleReference", "importKind", "tsIsExternalModuleReference", "tsParseExternalModuleReference", "sawUnambiguousESM", "f", "clone", "res", "tsTryParseAndCatch", "try<PERSON><PERSON><PERSON>", "abort", "aborted", "error", "failState", "tsTryParseDeclare", "nany", "startType", "tsInAmbientContext", "parseFunctionStatement", "parseClass", "isLookaheadContextual", "parseVarStatement", "tsParseDeclaration", "tsTryParseExportDeclaration", "tsParseExpressionStatement", "expr", "decorators", "declaration", "mod", "tsCheckLineTerminator", "tsParseAbstractDeclaration", "tsTryParseGenericAsyncArrowFunction", "oldMaybeInArrowParameters", "maybeInArrowParameters", "parseFunctionParams", "returnType", "parseArrowExpression", "tsParseTypeArgumentsInExpression", "reScan_lt", "cur<PERSON><PERSON><PERSON><PERSON>", "tc", "brace", "reScan_lt_gt", "tsIsDeclarationStart", "tokenIsTSDeclarationStart", "isExportDefaultSpecifier", "parseAssignableListItem", "flags", "override", "IS_CONSTRUCTOR_PARAMS", "parseMaybeDefault", "parseAssignableListItemTypes", "elt", "start", "pp", "parameter", "isSimpleParameter", "tsDisallowOptionalPattern", "param", "isAmbientContext", "setArrowFunctionParameters", "trailingCommaLoc", "parseFunctionBodyAndFinish", "isMethod", "bodilessType", "registerFunctionStatementId", "BIND_TS_AMBIENT", "tsCheckForInvalidTypeCasts", "items", "toReferencedList", "exprList", "isInParens", "parseArrayLike", "close", "canBePattern", "isTuple", "refExpressionErrors", "elements", "parseSubscript", "base", "noCalls", "canStartJSXElement", "nonNullExpression", "isOptionalCall", "stop", "optionalChainMember", "missingParenErrorLoc", "atPossibleAsyncArrow", "asyncArrowFn", "typeArguments", "tokenIsTemplate", "parseTaggedTemplateExpression", "callee", "arguments", "parseCallExpressionArguments", "finishCallExpression", "tokenType", "tokenCanStartExpression", "parseNewCallee", "_callee$extra", "extra", "parenthesized", "parseExprOp", "leftStartLoc", "minPrec", "isSatisfies", "tokenOperatorPrecedence", "UnexpectedKeyword", "keyword", "checkReservedWord", "word", "checkKeywords", "isBinding", "checkImportReflection", "module", "specifiers", "checkDuplicateExports", "isPotentialImportPhase", "ch", "applyImportPhase", "phase", "exportKind", "parseImport", "importNode", "parseMaybeImportPhase", "parseImportSpecifiersAndAfter", "parseExport", "assign", "parseExpression", "decl", "isAbstractClass", "parseExportDefaultExpression", "cls", "allowMissingInitializer", "init", "declarations", "isValidAmbientConstInitializer", "hasPlugin", "parseStatementContent", "parseAccessModifier", "tsHasSomeModifiers", "member", "some", "parseClassMember", "classBody", "callParseClassMemberWithIsStatic", "parseClassStaticBlock", "parseClassMemberWithIsStatic", "static", "isStatic", "inAbstractClass", "hadSuperClass", "parsePostMemberNameModifiers", "methodOrProp", "parseExpressionStatement", "shouldParseExportDeclaration", "parseConditional", "setOptionalParametersError", "parseParenItem", "typeCastNode", "parseExportDeclaration", "isDeclare", "isIdentifier", "resetStartLocation", "parseClassId", "isStatement", "optionalId", "bindingType", "BIND_CLASS", "parseClassPropertyAnnotation", "definite", "parseClassProperty", "input", "slice", "end", "parseClassPrivateProperty", "parseClassAccessorProperty", "pushClassMethod", "isGenerator", "isAsync", "isConstructor", "allowsDirectSuper", "pushClassPrivateMethod", "declareClassPrivateMethodInScope", "parseClassSuper", "superTypeParameters", "implements", "parseObjPropValue", "prop", "isPattern", "isAccessor", "parseVarId", "parseAsyncArrowFromCallExpression", "parseMaybeAssign", "afterLeftParse", "_jsx", "_jsx2", "_typeCast", "_jsx3", "_typeCast2", "jsx", "typeCast", "currentContext", "j_oTag", "j_expr", "pop", "arrow", "_expr$extra", "_typeParameters", "reportReservedArrowTypeParam", "_node$extra", "trailingComma", "sawUnary", "parseArrow", "canInsertSemicolon", "thrown", "isAssignable", "toAssignable", "isLHS", "toAssignableParenthesizedExpression", "expressionScope", "recordArrowParameterBindingError", "typeCastToParameter", "checkToRestConversion", "allowPattern", "isValidLVal", "isUnparenthesizedInAssign", "binding", "TSTypeCastExpression", "TSParameterProperty", "TSNonNullExpression", "TSAsExpression", "BIND_NONE", "TSSatisfiesExpression", "TSTypeAssertion", "parseBindingAtom", "parseMaybeDecoratorArguments", "checkCommaAfterRest", "isClassMethod", "isClassProperty", "getTokenFromCode", "code", "finishOp", "pos", "readToken_lt", "readToken_gt", "toAssignableList", "i", "shouldParseArrow", "every", "shouldParseAsyncArrow", "canHaveLeadingDecorator", "jsxParseOpeningElementAfterName", "getGetterSetterExpectedParamCount", "baseCount", "getObjectOrClassMethodParams", "firstParam", "hasContextParam", "parseCatchClauseParam", "oldIsAmbientContext", "oldInAbstractClass", "maybeTakeDecorators", "parseMethod", "allowDirectSuper", "inClassScope", "hasBody", "shouldParseAsAmbientContext", "parse", "getExpression", "parseExportSpecifier", "isString", "isInTypeExport", "isMaybeTypeOnly", "parseTypeOnlyImportExportSpecifier", "parseImportSpecifier", "specifier", "importedIsString", "isInTypeOnlyImport", "BIND_TS_TYPE_IMPORT", "isImport", "isInTypeOnlyImportExport", "leftOfAsKey", "rightOfAsKey", "leftOfAs", "rightOfAs", "hasTypeSpecifier", "canParseAsKeyword", "firstAs", "secondAs", "parseModuleExportName", "<PERSON><PERSON><PERSON>", "cloneIdentifier", "exports", "isPossiblyLiteralEnum", "expressions", "isUncomputedMemberExpressionChain", "estree", "_expression$extra", "isNumber", "isNegativeNumber"], "sources": ["../../../src/plugins/typescript/index.ts"], "sourcesContent": ["/*:: declare var invariant; */\n\nimport type State from \"../../tokenizer/state\";\nimport {\n  tokenIsIdentifier,\n  tokenIsTSDeclarationStart,\n  tokenIsTSTypeOperator,\n  tokenOperatorPrecedence,\n  tokenIsKeywordOrIdentifier,\n  tt,\n  type TokenType,\n  tokenIsTemplate,\n  tokenCanStartExpression,\n} from \"../../tokenizer/types\";\nimport { types as tc } from \"../../tokenizer/context\";\nimport type * as N from \"../../types\";\nimport type { Position } from \"../../util/location\";\nimport { createPositionWithColumnOffset } from \"../../util/location\";\nimport type Parser from \"../../parser\";\nimport {\n  type BindingTypes,\n  SCOPE_TS_MODULE,\n  SCOPE_OTHER,\n  BIND_TS_ENUM,\n  BIND_TS_CONST_ENUM,\n  BIND_TS_TYPE,\n  BIND_TS_INTERFACE,\n  BIND_TS_AMBIENT,\n  BIND_TS_NAMESPACE,\n  BIND_TS_TYPE_IMPORT,\n  BIND_CLASS,\n  BIND_NONE,\n  BIND_FLAGS_TS_IMPORT,\n} from \"../../util/scopeflags\";\nimport TypeScriptScopeHandler from \"./scope\";\nimport * as charCodes from \"charcodes\";\nimport type { ExpressionErrors } from \"../../parser/util\";\nimport type { ParseStatementFlag } from \"../../parser/statement\";\nimport { PARAM } from \"../../util/production-parameter\";\nimport { Errors, ParseErrorEnum } from \"../../parse-error\";\nimport { cloneIdentifier, type Undone } from \"../../parser/node\";\nimport type { Pattern } from \"../../types\";\nimport type { Expression } from \"../../types\";\nimport type { IJSXParserMixin } from \"../jsx\";\nimport { ParseBindingListFlags } from \"../../parser/lval\";\n\nconst getOwn = <T extends {}>(object: T, key: keyof T) =>\n  Object.hasOwnProperty.call(object, key) && object[key];\n\ntype TsModifier =\n  | \"readonly\"\n  | \"abstract\"\n  | \"declare\"\n  | \"static\"\n  | \"override\"\n  | \"const\"\n  | N.Accessibility\n  | N.VarianceAnnotations;\n\nfunction nonNull<T>(x?: T | null): T {\n  if (x == null) {\n    throw new Error(`Unexpected ${x} value.`);\n  }\n  return x;\n}\n\nfunction assert(x: boolean): void {\n  if (!x) {\n    throw new Error(\"Assert fail\");\n  }\n}\n\ntype ParsingContext =\n  | \"EnumMembers\"\n  | \"HeritageClauseElement\"\n  | \"TupleElementTypes\"\n  | \"TypeMembers\"\n  | \"TypeParametersOrArguments\";\n\ntype ModifierBase = {\n  accessibility?: N.Accessibility;\n} & {\n  [key in TsModifier]?: boolean | undefined | null;\n};\n\n/* eslint sort-keys: \"error\" */\nconst TSErrors = ParseErrorEnum`typescript`({\n  AbstractMethodHasImplementation: ({ methodName }: { methodName: string }) =>\n    `Method '${methodName}' cannot have an implementation because it is marked abstract.`,\n  AbstractPropertyHasInitializer: ({\n    propertyName,\n  }: {\n    propertyName: string;\n  }) =>\n    `Property '${propertyName}' cannot have an initializer because it is marked abstract.`,\n  AccesorCannotDeclareThisParameter:\n    \"'get' and 'set' accessors cannot declare 'this' parameters.\",\n  AccesorCannotHaveTypeParameters: \"An accessor cannot have type parameters.\",\n  AccessorCannotBeOptional:\n    \"An 'accessor' property cannot be declared optional.\",\n  ClassMethodHasDeclare: \"Class methods cannot have the 'declare' modifier.\",\n  ClassMethodHasReadonly: \"Class methods cannot have the 'readonly' modifier.\",\n  ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference:\n    \"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.\",\n  ConstructorHasTypeParameters:\n    \"Type parameters cannot appear on a constructor declaration.\",\n  DeclareAccessor: ({ kind }: { kind: \"get\" | \"set\" }) =>\n    `'declare' is not allowed in ${kind}ters.`,\n  DeclareClassFieldHasInitializer:\n    \"Initializers are not allowed in ambient contexts.\",\n  DeclareFunctionHasImplementation:\n    \"An implementation cannot be declared in ambient contexts.\",\n  DuplicateAccessibilityModifier:\n    // `Accessibility modifier already seen: ${modifier}` would be more helpful.\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    ({ modifier }: { modifier: N.Accessibility }) =>\n      `Accessibility modifier already seen.`,\n  DuplicateModifier: ({ modifier }: { modifier: TsModifier }) =>\n    `Duplicate modifier: '${modifier}'.`,\n  // `token` matches the terminology used by typescript:\n  // https://github.com/microsoft/TypeScript/blob/main/src/compiler/types.ts#L2915\n  EmptyHeritageClauseType: ({ token }: { token: \"extends\" | \"implements\" }) =>\n    `'${token}' list cannot be empty.`,\n  EmptyTypeArguments: \"Type argument list cannot be empty.\",\n  EmptyTypeParameters: \"Type parameter list cannot be empty.\",\n  ExpectedAmbientAfterExportDeclare:\n    \"'export declare' must be followed by an ambient declaration.\",\n  ImportAliasHasImportType: \"An import alias can not use 'import type'.\",\n  ImportReflectionHasImportType:\n    \"An `import module` declaration can not use `type` modifier\",\n  IncompatibleModifiers: ({\n    modifiers,\n  }: {\n    modifiers: [TsModifier, TsModifier];\n  }) =>\n    `'${modifiers[0]}' modifier cannot be used with '${modifiers[1]}' modifier.`,\n  IndexSignatureHasAbstract:\n    \"Index signatures cannot have the 'abstract' modifier.\",\n  IndexSignatureHasAccessibility: ({\n    modifier,\n  }: {\n    modifier: N.Accessibility;\n  }) =>\n    `Index signatures cannot have an accessibility modifier ('${modifier}').`,\n  IndexSignatureHasDeclare:\n    \"Index signatures cannot have the 'declare' modifier.\",\n  IndexSignatureHasOverride:\n    \"'override' modifier cannot appear on an index signature.\",\n  IndexSignatureHasStatic:\n    \"Index signatures cannot have the 'static' modifier.\",\n  InitializerNotAllowedInAmbientContext:\n    \"Initializers are not allowed in ambient contexts.\",\n  InvalidModifierOnTypeMember: ({ modifier }: { modifier: TsModifier }) =>\n    `'${modifier}' modifier cannot appear on a type member.`,\n  InvalidModifierOnTypeParameter: ({ modifier }: { modifier: TsModifier }) =>\n    `'${modifier}' modifier cannot appear on a type parameter.`,\n  InvalidModifierOnTypeParameterPositions: ({\n    modifier,\n  }: {\n    modifier: TsModifier;\n  }) =>\n    `'${modifier}' modifier can only appear on a type parameter of a class, interface or type alias.`,\n  InvalidModifiersOrder: ({\n    orderedModifiers,\n  }: {\n    orderedModifiers: [TsModifier, TsModifier];\n  }) =>\n    `'${orderedModifiers[0]}' modifier must precede '${orderedModifiers[1]}' modifier.`,\n  InvalidPropertyAccessAfterInstantiationExpression:\n    \"Invalid property access after an instantiation expression. \" +\n    \"You can either wrap the instantiation expression in parentheses, or delete the type arguments.\",\n  InvalidTupleMemberLabel:\n    \"Tuple members must be labeled with a simple identifier.\",\n  MissingInterfaceName:\n    \"'interface' declarations must be followed by an identifier.\",\n  MixedLabeledAndUnlabeledElements:\n    \"Tuple members must all have names or all not have names.\",\n  NonAbstractClassHasAbstractMethod:\n    \"Abstract methods can only appear within an abstract class.\",\n  NonClassMethodPropertyHasAbstractModifer:\n    \"'abstract' modifier can only appear on a class, method, or property declaration.\",\n  OptionalTypeBeforeRequired:\n    \"A required element cannot follow an optional element.\",\n  OverrideNotInSubClass:\n    \"This member cannot have an 'override' modifier because its containing class does not extend another class.\",\n  PatternIsOptional:\n    \"A binding pattern parameter cannot be optional in an implementation signature.\",\n  PrivateElementHasAbstract:\n    \"Private elements cannot have the 'abstract' modifier.\",\n  PrivateElementHasAccessibility: ({\n    modifier,\n  }: {\n    modifier: N.Accessibility;\n  }) =>\n    `Private elements cannot have an accessibility modifier ('${modifier}').`,\n  ReadonlyForMethodSignature:\n    \"'readonly' modifier can only appear on a property declaration or index signature.\",\n  ReservedArrowTypeParam:\n    \"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.\",\n  ReservedTypeAssertion:\n    \"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.\",\n  // TODO: Accesor -> Accessor\n  SetAccesorCannotHaveOptionalParameter:\n    \"A 'set' accessor cannot have an optional parameter.\",\n  SetAccesorCannotHaveRestParameter:\n    \"A 'set' accessor cannot have rest parameter.\",\n  SetAccesorCannotHaveReturnType:\n    \"A 'set' accessor cannot have a return type annotation.\",\n  SingleTypeParameterWithoutTrailingComma: ({\n    typeParameterName,\n  }: {\n    typeParameterName: string;\n  }) =>\n    `Single type parameter ${typeParameterName} should have a trailing comma. Example usage: <${typeParameterName},>.`,\n  StaticBlockCannotHaveModifier:\n    \"Static class blocks cannot have any modifier.\",\n  TupleOptionalAfterType:\n    \"A labeled tuple optional element must be declared using a question mark after the name and before the colon (`name?: type`), rather than after the type (`name: type?`).\",\n  TypeAnnotationAfterAssign:\n    \"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.\",\n  TypeImportCannotSpecifyDefaultAndNamed:\n    \"A type-only import can specify a default import or named bindings, but not both.\",\n  TypeModifierIsUsedInTypeExports:\n    \"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.\",\n  TypeModifierIsUsedInTypeImports:\n    \"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.\",\n  UnexpectedParameterModifier:\n    \"A parameter property is only allowed in a constructor implementation.\",\n  UnexpectedReadonly:\n    \"'readonly' type modifier is only permitted on array and tuple literal types.\",\n  UnexpectedTypeAnnotation: \"Did not expect a type annotation here.\",\n  UnexpectedTypeCastInParameter: \"Unexpected type cast in parameter position.\",\n  UnsupportedImportTypeArgument:\n    \"Argument in a type import must be a string literal.\",\n  UnsupportedParameterPropertyKind:\n    \"A parameter property may not be declared using a binding pattern.\",\n  UnsupportedSignatureParameterKind: ({ type }: { type: string }) =>\n    `Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got ${type}.`,\n});\n\n/* eslint-disable sort-keys */\n\n// Doesn't handle \"void\" or \"null\" because those are keywords, not identifiers.\n// It also doesn't handle \"intrinsic\", since usually it's not a keyword.\nfunction keywordTypeFromName(value: string): N.TsKeywordTypeType | undefined {\n  switch (value) {\n    case \"any\":\n      return \"TSAnyKeyword\";\n    case \"boolean\":\n      return \"TSBooleanKeyword\";\n    case \"bigint\":\n      return \"TSBigIntKeyword\";\n    case \"never\":\n      return \"TSNeverKeyword\";\n    case \"number\":\n      return \"TSNumberKeyword\";\n    case \"object\":\n      return \"TSObjectKeyword\";\n    case \"string\":\n      return \"TSStringKeyword\";\n    case \"symbol\":\n      return \"TSSymbolKeyword\";\n    case \"undefined\":\n      return \"TSUndefinedKeyword\";\n    case \"unknown\":\n      return \"TSUnknownKeyword\";\n    default:\n      return undefined;\n  }\n}\n\nfunction tsIsAccessModifier(modifier: string): modifier is N.Accessibility {\n  return (\n    modifier === \"private\" || modifier === \"public\" || modifier === \"protected\"\n  );\n}\n\nfunction tsIsVarianceAnnotations(\n  modifier: string,\n): modifier is N.VarianceAnnotations {\n  return modifier === \"in\" || modifier === \"out\";\n}\n\ntype ClassWithMixin<\n  T extends new (...args: any) => any,\n  M extends object,\n> = T extends new (...args: infer P) => infer I\n  ? new (...args: P) => I & M\n  : never;\n\nexport default (superClass: ClassWithMixin<typeof Parser, IJSXParserMixin>) =>\n  class TypeScriptParserMixin extends superClass implements Parser {\n    getScopeHandler(): {\n      new (...args: any): TypeScriptScopeHandler;\n    } {\n      return TypeScriptScopeHandler;\n    }\n\n    tsIsIdentifier(): boolean {\n      // TODO: actually a bit more complex in TypeScript, but shouldn't matter.\n      // See https://github.com/Microsoft/TypeScript/issues/15008\n      return tokenIsIdentifier(this.state.type);\n    }\n\n    tsTokenCanFollowModifier() {\n      return (\n        (this.match(tt.bracketL) ||\n          this.match(tt.braceL) ||\n          this.match(tt.star) ||\n          this.match(tt.ellipsis) ||\n          this.match(tt.privateName) ||\n          this.isLiteralPropertyName()) &&\n        !this.hasPrecedingLineBreak()\n      );\n    }\n\n    tsNextTokenCanFollowModifier() {\n      // Note: TypeScript's implementation is much more complicated because\n      // more things are considered modifiers there.\n      // This implementation only handles modifiers not handled by @babel/parser itself. And \"static\".\n      // TODO: Would be nice to avoid lookahead. Want a hasLineBreakUpNext() method...\n      this.next();\n      return this.tsTokenCanFollowModifier();\n    }\n\n    /** Parses a modifier matching one the given modifier names. */\n    tsParseModifier<T extends TsModifier>(\n      allowedModifiers: T[],\n      stopOnStartOfClassStaticBlock?: boolean,\n    ): T | undefined | null {\n      if (\n        !tokenIsIdentifier(this.state.type) &&\n        this.state.type !== tt._in &&\n        this.state.type !== tt._const\n      ) {\n        return undefined;\n      }\n\n      const modifier = this.state.value;\n      if (allowedModifiers.indexOf(modifier) !== -1) {\n        if (stopOnStartOfClassStaticBlock && this.tsIsStartOfStaticBlocks()) {\n          return undefined;\n        }\n        if (this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this))) {\n          return modifier;\n        }\n      }\n      return undefined;\n    }\n\n    /** Parses a list of modifiers, in any order.\n     *  If you need a specific order, you must call this function multiple times:\n     *    this.tsParseModifiers({ modified: node, allowedModifiers: [\"public\"] });\n     *    this.tsParseModifiers({ modified: node, allowedModifiers: [\"abstract\", \"readonly\"] });\n     */\n    tsParseModifiers<N extends ModifierBase>(\n      {\n        allowedModifiers,\n        disallowedModifiers,\n        stopOnStartOfClassStaticBlock,\n        errorTemplate = TSErrors.InvalidModifierOnTypeMember,\n      }: {\n        allowedModifiers: readonly TsModifier[];\n        disallowedModifiers?: TsModifier[];\n        stopOnStartOfClassStaticBlock?: boolean;\n        errorTemplate?: typeof TSErrors.InvalidModifierOnTypeMember;\n      },\n      modified: N,\n    ): void {\n      const enforceOrder = (\n        loc: Position,\n        modifier: TsModifier,\n        before: TsModifier,\n        after: TsModifier,\n      ) => {\n        if (modifier === before && modified[after]) {\n          this.raise(TSErrors.InvalidModifiersOrder, {\n            at: loc,\n            orderedModifiers: [before, after],\n          });\n        }\n      };\n      const incompatible = (\n        loc: Position,\n        modifier: TsModifier,\n        mod1: TsModifier,\n        mod2: TsModifier,\n      ) => {\n        if (\n          (modified[mod1] && modifier === mod2) ||\n          (modified[mod2] && modifier === mod1)\n        ) {\n          this.raise(TSErrors.IncompatibleModifiers, {\n            at: loc,\n            modifiers: [mod1, mod2],\n          });\n        }\n      };\n\n      for (;;) {\n        const { startLoc } = this.state;\n        const modifier: TsModifier | undefined | null = this.tsParseModifier(\n          allowedModifiers.concat(disallowedModifiers ?? []),\n          stopOnStartOfClassStaticBlock,\n        );\n\n        if (!modifier) break;\n\n        if (tsIsAccessModifier(modifier)) {\n          if (modified.accessibility) {\n            this.raise(TSErrors.DuplicateAccessibilityModifier, {\n              at: startLoc,\n              modifier,\n            });\n          } else {\n            enforceOrder(startLoc, modifier, modifier, \"override\");\n            enforceOrder(startLoc, modifier, modifier, \"static\");\n            enforceOrder(startLoc, modifier, modifier, \"readonly\");\n\n            modified.accessibility = modifier;\n          }\n        } else if (tsIsVarianceAnnotations(modifier)) {\n          if (modified[modifier]) {\n            this.raise(TSErrors.DuplicateModifier, { at: startLoc, modifier });\n          }\n          modified[modifier] = true;\n\n          enforceOrder(startLoc, modifier, \"in\", \"out\");\n        } else {\n          if (Object.hasOwnProperty.call(modified, modifier)) {\n            this.raise(TSErrors.DuplicateModifier, { at: startLoc, modifier });\n          } else {\n            enforceOrder(startLoc, modifier, \"static\", \"readonly\");\n            enforceOrder(startLoc, modifier, \"static\", \"override\");\n            enforceOrder(startLoc, modifier, \"override\", \"readonly\");\n            enforceOrder(startLoc, modifier, \"abstract\", \"override\");\n\n            incompatible(startLoc, modifier, \"declare\", \"override\");\n            incompatible(startLoc, modifier, \"static\", \"abstract\");\n          }\n          modified[modifier] = true;\n        }\n\n        if (disallowedModifiers?.includes(modifier)) {\n          this.raise(errorTemplate, {\n            at: startLoc,\n            modifier,\n          });\n        }\n      }\n    }\n\n    tsIsListTerminator(kind: ParsingContext): boolean {\n      switch (kind) {\n        case \"EnumMembers\":\n        case \"TypeMembers\":\n          return this.match(tt.braceR);\n        case \"HeritageClauseElement\":\n          return this.match(tt.braceL);\n        case \"TupleElementTypes\":\n          return this.match(tt.bracketR);\n        case \"TypeParametersOrArguments\":\n          return this.match(tt.gt);\n      }\n    }\n\n    tsParseList<T extends N.Node>(\n      kind: ParsingContext,\n      parseElement: () => T,\n    ): T[] {\n      const result: T[] = [];\n      while (!this.tsIsListTerminator(kind)) {\n        // Skipping \"parseListElement\" from the TS source since that's just for error handling.\n        result.push(parseElement());\n      }\n      return result;\n    }\n\n    tsParseDelimitedList<T extends N.Node>(\n      kind: ParsingContext,\n      parseElement: () => T,\n      refTrailingCommaPos?: {\n        value: number;\n      },\n    ): T[] {\n      return nonNull(\n        this.tsParseDelimitedListWorker(\n          kind,\n          parseElement,\n          /* expectSuccess */ true,\n          refTrailingCommaPos,\n        ),\n      );\n    }\n\n    /**\n     * If !expectSuccess, returns undefined instead of failing to parse.\n     * If expectSuccess, parseElement should always return a defined value.\n     */\n    tsParseDelimitedListWorker<T extends N.Node>(\n      kind: ParsingContext,\n      parseElement: () => T | undefined | null,\n      expectSuccess: boolean,\n      refTrailingCommaPos?: {\n        value: number;\n      },\n    ): T[] | undefined | null {\n      const result = [];\n      let trailingCommaPos = -1;\n\n      for (;;) {\n        if (this.tsIsListTerminator(kind)) {\n          break;\n        }\n        trailingCommaPos = -1;\n\n        const element = parseElement();\n        if (element == null) {\n          return undefined;\n        }\n        result.push(element);\n\n        if (this.eat(tt.comma)) {\n          trailingCommaPos = this.state.lastTokStart;\n          continue;\n        }\n\n        if (this.tsIsListTerminator(kind)) {\n          break;\n        }\n\n        if (expectSuccess) {\n          // This will fail with an error about a missing comma\n          this.expect(tt.comma);\n        }\n        return undefined;\n      }\n\n      if (refTrailingCommaPos) {\n        refTrailingCommaPos.value = trailingCommaPos;\n      }\n\n      return result;\n    }\n\n    tsParseBracketedList<T extends N.Node>(\n      kind: ParsingContext,\n      parseElement: () => T,\n      bracket: boolean,\n      skipFirstToken: boolean,\n      refTrailingCommaPos?: {\n        value: number;\n      },\n    ): T[] {\n      if (!skipFirstToken) {\n        if (bracket) {\n          this.expect(tt.bracketL);\n        } else {\n          this.expect(tt.lt);\n        }\n      }\n\n      const result = this.tsParseDelimitedList(\n        kind,\n        parseElement,\n        refTrailingCommaPos,\n      );\n\n      if (bracket) {\n        this.expect(tt.bracketR);\n      } else {\n        this.expect(tt.gt);\n      }\n\n      return result;\n    }\n\n    tsParseImportType(): N.TsImportType {\n      const node = this.startNode<N.TsImportType>();\n      this.expect(tt._import);\n      this.expect(tt.parenL);\n      if (!this.match(tt.string)) {\n        this.raise(TSErrors.UnsupportedImportTypeArgument, {\n          at: this.state.startLoc,\n        });\n      }\n\n      // For compatibility to estree we cannot call parseLiteral directly here\n      node.argument = super.parseExprAtom() as N.StringLiteral;\n      this.expect(tt.parenR);\n\n      if (this.eat(tt.dot)) {\n        // In this instance, the entity name will actually itself be a\n        // qualifier, so allow it to be a reserved word as well.\n        node.qualifier = this.tsParseEntityName();\n      }\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.tsParseTypeArguments();\n      }\n      return this.finishNode(node, \"TSImportType\");\n    }\n\n    tsParseEntityName(allowReservedWords: boolean = true): N.TsEntityName {\n      let entity: N.TsEntityName = this.parseIdentifier(allowReservedWords);\n      while (this.eat(tt.dot)) {\n        const node: Undone<N.TsQualifiedName> =\n          this.startNodeAtNode<N.TsQualifiedName>(entity);\n        node.left = entity;\n        node.right = this.parseIdentifier(allowReservedWords);\n        entity = this.finishNode(node, \"TSQualifiedName\");\n      }\n      return entity;\n    }\n\n    tsParseTypeReference(): N.TsTypeReference {\n      const node = this.startNode<N.TsTypeReference>();\n      node.typeName = this.tsParseEntityName();\n      if (!this.hasPrecedingLineBreak() && this.match(tt.lt)) {\n        node.typeParameters = this.tsParseTypeArguments();\n      }\n      return this.finishNode(node, \"TSTypeReference\");\n    }\n\n    tsParseThisTypePredicate(lhs: N.TsThisType): N.TsTypePredicate {\n      this.next();\n      const node = this.startNodeAtNode<N.TsTypePredicate>(lhs);\n      node.parameterName = lhs;\n      node.typeAnnotation = this.tsParseTypeAnnotation(/* eatColon */ false);\n      node.asserts = false;\n      return this.finishNode(node, \"TSTypePredicate\");\n    }\n\n    tsParseThisTypeNode(): N.TsThisType {\n      const node = this.startNode<N.TsThisType>();\n      this.next();\n      return this.finishNode(node, \"TSThisType\");\n    }\n\n    tsParseTypeQuery(): N.TsTypeQuery {\n      const node = this.startNode<N.TsTypeQuery>();\n      this.expect(tt._typeof);\n      if (this.match(tt._import)) {\n        node.exprName = this.tsParseImportType();\n      } else {\n        node.exprName = this.tsParseEntityName();\n      }\n      if (!this.hasPrecedingLineBreak() && this.match(tt.lt)) {\n        node.typeParameters = this.tsParseTypeArguments();\n      }\n      return this.finishNode(node, \"TSTypeQuery\");\n    }\n\n    tsParseInOutModifiers = this.tsParseModifiers.bind(this, {\n      allowedModifiers: [\"in\", \"out\"],\n      disallowedModifiers: [\n        \"const\",\n        \"public\",\n        \"private\",\n        \"protected\",\n        \"readonly\",\n        \"declare\",\n        \"abstract\",\n        \"override\",\n      ],\n      errorTemplate: TSErrors.InvalidModifierOnTypeParameter,\n    });\n\n    tsParseConstModifier = this.tsParseModifiers.bind(this, {\n      allowedModifiers: [\"const\"],\n      // for better error recovery\n      disallowedModifiers: [\"in\", \"out\"],\n      errorTemplate: TSErrors.InvalidModifierOnTypeParameterPositions,\n    });\n\n    tsParseInOutConstModifiers = this.tsParseModifiers.bind(this, {\n      allowedModifiers: [\"in\", \"out\", \"const\"],\n      disallowedModifiers: [\n        \"public\",\n        \"private\",\n        \"protected\",\n        \"readonly\",\n        \"declare\",\n        \"abstract\",\n        \"override\",\n      ],\n      errorTemplate: TSErrors.InvalidModifierOnTypeParameter,\n    });\n\n    tsParseTypeParameter(\n      parseModifiers: (node: Undone<N.TsTypeParameter>) => void,\n    ): N.TsTypeParameter {\n      const node = this.startNode<N.TsTypeParameter>();\n\n      parseModifiers(node);\n\n      node.name = this.tsParseTypeParameterName();\n      node.constraint = this.tsEatThenParseType(tt._extends);\n      node.default = this.tsEatThenParseType(tt.eq);\n      return this.finishNode(node, \"TSTypeParameter\");\n    }\n\n    tsTryParseTypeParameters(\n      parseModifiers: (node: N.TsTypeParameter) => void,\n    ): N.TsTypeParameterDeclaration | undefined | null {\n      if (this.match(tt.lt)) {\n        return this.tsParseTypeParameters(parseModifiers);\n      }\n    }\n\n    tsParseTypeParameters(parseModifiers: (node: N.TsTypeParameter) => void) {\n      const node = this.startNode<N.TsTypeParameterDeclaration>();\n\n      if (this.match(tt.lt) || this.match(tt.jsxTagStart)) {\n        this.next();\n      } else {\n        this.unexpected();\n      }\n\n      const refTrailingCommaPos = { value: -1 };\n\n      node.params = this.tsParseBracketedList(\n        \"TypeParametersOrArguments\",\n        // @ts-expect-error refine typings\n        this.tsParseTypeParameter.bind(this, parseModifiers),\n        /* bracket */ false,\n        /* skipFirstToken */ true,\n        refTrailingCommaPos,\n      );\n      if (node.params.length === 0) {\n        this.raise(TSErrors.EmptyTypeParameters, { at: node });\n      }\n      if (refTrailingCommaPos.value !== -1) {\n        this.addExtra(node, \"trailingComma\", refTrailingCommaPos.value);\n      }\n      return this.finishNode(node, \"TSTypeParameterDeclaration\");\n    }\n\n    // Note: In TypeScript implementation we must provide `yieldContext` and `awaitContext`,\n    // but here it's always false, because this is only used for types.\n    tsFillSignature(\n      returnToken: TokenType,\n      signature: Undone<N.TsSignatureDeclaration>,\n    ): void {\n      // Arrow fns *must* have return token (`=>`). Normal functions can omit it.\n      const returnTokenRequired = returnToken === tt.arrow;\n\n      // https://github.com/babel/babel/issues/9231\n      const paramsKey = process.env.BABEL_8_BREAKING ? \"params\" : \"parameters\";\n      const returnTypeKey = process.env.BABEL_8_BREAKING\n        ? \"returnType\"\n        : \"typeAnnotation\";\n\n      signature.typeParameters = this.tsTryParseTypeParameters(\n        this.tsParseConstModifier,\n      );\n      this.expect(tt.parenL);\n      signature[paramsKey] = this.tsParseBindingListForSignature();\n      if (returnTokenRequired) {\n        signature[returnTypeKey] =\n          this.tsParseTypeOrTypePredicateAnnotation(returnToken);\n      } else if (this.match(returnToken)) {\n        signature[returnTypeKey] =\n          this.tsParseTypeOrTypePredicateAnnotation(returnToken);\n      }\n    }\n\n    tsParseBindingListForSignature(): Array<\n      N.Identifier | N.RestElement | N.ObjectPattern | N.ArrayPattern\n    > {\n      const list = super.parseBindingList(\n        tt.parenR,\n        charCodes.rightParenthesis,\n        ParseBindingListFlags.IS_FUNCTION_PARAMS,\n      );\n      for (const pattern of list) {\n        const { type } = pattern;\n        if (type === \"AssignmentPattern\" || type === \"TSParameterProperty\") {\n          this.raise(TSErrors.UnsupportedSignatureParameterKind, {\n            at: pattern,\n            type,\n          });\n        }\n      }\n      return list as Exclude<\n        (typeof list)[0],\n        N.AssignmentPattern | N.TSParameterProperty\n      >[];\n    }\n\n    tsParseTypeMemberSemicolon(): void {\n      if (!this.eat(tt.comma) && !this.isLineTerminator()) {\n        this.expect(tt.semi);\n      }\n    }\n\n    tsParseSignatureMember(\n      kind: \"TSCallSignatureDeclaration\" | \"TSConstructSignatureDeclaration\",\n      node: Undone<\n        N.TsCallSignatureDeclaration | N.TsConstructSignatureDeclaration\n      >,\n    ): N.TsCallSignatureDeclaration | N.TsConstructSignatureDeclaration {\n      this.tsFillSignature(tt.colon, node);\n      this.tsParseTypeMemberSemicolon();\n      return this.finishNode(node, kind);\n    }\n\n    tsIsUnambiguouslyIndexSignature() {\n      this.next(); // Skip '{'\n      if (tokenIsIdentifier(this.state.type)) {\n        this.next();\n        return this.match(tt.colon);\n      }\n      return false;\n    }\n\n    tsTryParseIndexSignature(\n      node: Undone<N.TsIndexSignature>,\n    ): N.TsIndexSignature | undefined {\n      if (\n        !(\n          this.match(tt.bracketL) &&\n          this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this))\n        )\n      ) {\n        return;\n      }\n\n      this.expect(tt.bracketL);\n      const id = this.parseIdentifier();\n      id.typeAnnotation = this.tsParseTypeAnnotation();\n      this.resetEndLocation(id); // set end position to end of type\n\n      this.expect(tt.bracketR);\n      node.parameters = [id];\n\n      const type = this.tsTryParseTypeAnnotation();\n      if (type) node.typeAnnotation = type;\n      this.tsParseTypeMemberSemicolon();\n      return this.finishNode(node, \"TSIndexSignature\");\n    }\n\n    tsParsePropertyOrMethodSignature(\n      node: N.TsPropertySignature | N.TsMethodSignature,\n      readonly: boolean,\n    ): N.TsPropertySignature | N.TsMethodSignature {\n      if (this.eat(tt.question)) node.optional = true;\n      const nodeAny: any = node;\n\n      if (this.match(tt.parenL) || this.match(tt.lt)) {\n        if (readonly) {\n          this.raise(TSErrors.ReadonlyForMethodSignature, { at: node });\n        }\n        const method: N.TsMethodSignature = nodeAny;\n        if (method.kind && this.match(tt.lt)) {\n          this.raise(TSErrors.AccesorCannotHaveTypeParameters, {\n            at: this.state.curPosition(),\n          });\n        }\n        this.tsFillSignature(tt.colon, method);\n        this.tsParseTypeMemberSemicolon();\n        const paramsKey = process.env.BABEL_8_BREAKING\n          ? \"params\"\n          : \"parameters\";\n        const returnTypeKey = process.env.BABEL_8_BREAKING\n          ? \"returnType\"\n          : \"typeAnnotation\";\n        if (method.kind === \"get\") {\n          if (method[paramsKey].length > 0) {\n            this.raise(Errors.BadGetterArity, { at: this.state.curPosition() });\n            if (this.isThisParam(method[paramsKey][0])) {\n              this.raise(TSErrors.AccesorCannotDeclareThisParameter, {\n                at: this.state.curPosition(),\n              });\n            }\n          }\n        } else if (method.kind === \"set\") {\n          if (method[paramsKey].length !== 1) {\n            this.raise(Errors.BadSetterArity, { at: this.state.curPosition() });\n          } else {\n            const firstParameter = method[paramsKey][0];\n            if (this.isThisParam(firstParameter)) {\n              this.raise(TSErrors.AccesorCannotDeclareThisParameter, {\n                at: this.state.curPosition(),\n              });\n            }\n            if (\n              firstParameter.type === \"Identifier\" &&\n              firstParameter.optional\n            ) {\n              this.raise(TSErrors.SetAccesorCannotHaveOptionalParameter, {\n                at: this.state.curPosition(),\n              });\n            }\n            if (firstParameter.type === \"RestElement\") {\n              this.raise(TSErrors.SetAccesorCannotHaveRestParameter, {\n                at: this.state.curPosition(),\n              });\n            }\n          }\n          if (method[returnTypeKey]) {\n            this.raise(TSErrors.SetAccesorCannotHaveReturnType, {\n              at: method[returnTypeKey],\n            });\n          }\n        } else {\n          method.kind = \"method\";\n        }\n        return this.finishNode(method, \"TSMethodSignature\");\n      } else {\n        const property: N.TsPropertySignature = nodeAny;\n        if (readonly) property.readonly = true;\n        const type = this.tsTryParseTypeAnnotation();\n        if (type) property.typeAnnotation = type;\n        this.tsParseTypeMemberSemicolon();\n        return this.finishNode(property, \"TSPropertySignature\");\n      }\n    }\n\n    tsParseTypeMember(): N.TsTypeElement {\n      const node: any = this.startNode();\n\n      if (this.match(tt.parenL) || this.match(tt.lt)) {\n        return this.tsParseSignatureMember(\"TSCallSignatureDeclaration\", node);\n      }\n\n      if (this.match(tt._new)) {\n        const id = this.startNode<N.Identifier>();\n        this.next();\n        if (this.match(tt.parenL) || this.match(tt.lt)) {\n          return this.tsParseSignatureMember(\n            \"TSConstructSignatureDeclaration\",\n            node,\n          );\n        } else {\n          node.key = this.createIdentifier(id, \"new\");\n          return this.tsParsePropertyOrMethodSignature(node, false);\n        }\n      }\n\n      this.tsParseModifiers(\n        {\n          allowedModifiers: [\"readonly\"],\n          disallowedModifiers: [\n            \"declare\",\n            \"abstract\",\n            \"private\",\n            \"protected\",\n            \"public\",\n            \"static\",\n            \"override\",\n          ],\n        },\n        node,\n      );\n\n      const idx = this.tsTryParseIndexSignature(node);\n      if (idx) {\n        return idx;\n      }\n\n      super.parsePropertyName(node);\n      if (\n        !node.computed &&\n        node.key.type === \"Identifier\" &&\n        (node.key.name === \"get\" || node.key.name === \"set\") &&\n        this.tsTokenCanFollowModifier()\n      ) {\n        node.kind = node.key.name;\n        super.parsePropertyName(node);\n      }\n      return this.tsParsePropertyOrMethodSignature(node, !!node.readonly);\n    }\n\n    tsParseTypeLiteral(): N.TsTypeLiteral {\n      const node = this.startNode<N.TsTypeLiteral>();\n      node.members = this.tsParseObjectTypeMembers();\n      return this.finishNode(node, \"TSTypeLiteral\");\n    }\n\n    tsParseObjectTypeMembers(): Array<N.TsTypeElement> {\n      this.expect(tt.braceL);\n      const members = this.tsParseList(\n        \"TypeMembers\",\n        this.tsParseTypeMember.bind(this),\n      );\n      this.expect(tt.braceR);\n      return members;\n    }\n\n    tsIsStartOfMappedType(): boolean {\n      this.next();\n      if (this.eat(tt.plusMin)) {\n        return this.isContextual(tt._readonly);\n      }\n      if (this.isContextual(tt._readonly)) {\n        this.next();\n      }\n      if (!this.match(tt.bracketL)) {\n        return false;\n      }\n      this.next();\n      if (!this.tsIsIdentifier()) {\n        return false;\n      }\n      this.next();\n      return this.match(tt._in);\n    }\n\n    tsParseMappedTypeParameter(): N.TsTypeParameter {\n      const node = this.startNode<N.TsTypeParameter>();\n      node.name = this.tsParseTypeParameterName();\n      node.constraint = this.tsExpectThenParseType(tt._in);\n      return this.finishNode(node, \"TSTypeParameter\");\n    }\n\n    tsParseMappedType(): N.TsMappedType {\n      const node = this.startNode<N.TsMappedType>();\n\n      this.expect(tt.braceL);\n\n      if (this.match(tt.plusMin)) {\n        node.readonly = this.state.value;\n        this.next();\n        this.expectContextual(tt._readonly);\n      } else if (this.eatContextual(tt._readonly)) {\n        node.readonly = true;\n      }\n\n      this.expect(tt.bracketL);\n      node.typeParameter = this.tsParseMappedTypeParameter();\n      node.nameType = this.eatContextual(tt._as) ? this.tsParseType() : null;\n\n      this.expect(tt.bracketR);\n\n      if (this.match(tt.plusMin)) {\n        node.optional = this.state.value;\n        this.next();\n        this.expect(tt.question);\n      } else if (this.eat(tt.question)) {\n        node.optional = true;\n      }\n\n      node.typeAnnotation = this.tsTryParseType();\n      this.semicolon();\n      this.expect(tt.braceR);\n\n      return this.finishNode(node, \"TSMappedType\");\n    }\n\n    tsParseTupleType(): N.TsTupleType {\n      const node = this.startNode<N.TsTupleType>();\n      node.elementTypes = this.tsParseBracketedList(\n        \"TupleElementTypes\",\n        this.tsParseTupleElementType.bind(this),\n        /* bracket */ true,\n        /* skipFirstToken */ false,\n      );\n\n      // Validate the elementTypes to ensure that no mandatory elements\n      // follow optional elements\n      let seenOptionalElement = false;\n      let labeledElements: boolean | null = null;\n      node.elementTypes.forEach(elementNode => {\n        const { type } = elementNode;\n\n        if (\n          seenOptionalElement &&\n          type !== \"TSRestType\" &&\n          type !== \"TSOptionalType\" &&\n          !(type === \"TSNamedTupleMember\" && elementNode.optional)\n        ) {\n          this.raise(TSErrors.OptionalTypeBeforeRequired, {\n            at: elementNode,\n          });\n        }\n\n        seenOptionalElement ||=\n          (type === \"TSNamedTupleMember\" && elementNode.optional) ||\n          type === \"TSOptionalType\";\n\n        // When checking labels, check the argument of the spread operator\n        let checkType = type;\n        if (type === \"TSRestType\") {\n          elementNode = elementNode.typeAnnotation;\n          checkType = elementNode.type;\n        }\n\n        const isLabeled = checkType === \"TSNamedTupleMember\";\n        labeledElements ??= isLabeled;\n        if (labeledElements !== isLabeled) {\n          this.raise(TSErrors.MixedLabeledAndUnlabeledElements, {\n            at: elementNode,\n          });\n        }\n      });\n\n      return this.finishNode(node, \"TSTupleType\");\n    }\n\n    tsParseTupleElementType(): N.TsNamedTupleMember | N.TsType {\n      // parses `...TsType[]`\n\n      const { startLoc } = this.state;\n\n      const rest = this.eat(tt.ellipsis);\n\n      let labeled: boolean;\n      let label: N.Identifier;\n      let optional: boolean;\n      let type: N.TsNamedTupleMember | N.TsType;\n\n      const isWord = tokenIsKeywordOrIdentifier(this.state.type);\n      const chAfterWord = isWord ? this.lookaheadCharCode() : null;\n      if (chAfterWord === charCodes.colon) {\n        labeled = true;\n        optional = false;\n        label = this.parseIdentifier(true);\n        this.expect(tt.colon);\n        type = this.tsParseType();\n      } else if (chAfterWord === charCodes.questionMark) {\n        optional = true;\n        const startLoc = this.state.startLoc;\n        const wordName = this.state.value;\n        const typeOrLabel = this.tsParseNonArrayType();\n\n        if (this.lookaheadCharCode() === charCodes.colon) {\n          labeled = true;\n          label = this.createIdentifier(\n            this.startNodeAt<N.Identifier>(startLoc),\n            wordName,\n          );\n          this.expect(tt.question);\n          this.expect(tt.colon);\n          type = this.tsParseType();\n        } else {\n          labeled = false;\n          type = typeOrLabel;\n          this.expect(tt.question);\n        }\n      } else {\n        type = this.tsParseType();\n        optional = this.eat(tt.question);\n        // In this case (labeled === true) could be only in invalid label.\n        // E.g. [x.y:type]\n        // An error is raised while processing node.\n        labeled = this.eat(tt.colon);\n      }\n\n      if (labeled) {\n        let labeledNode: Undone<N.TsNamedTupleMember>;\n        if (label) {\n          labeledNode = this.startNodeAtNode<N.TsNamedTupleMember>(label);\n          labeledNode.optional = optional;\n          labeledNode.label = label;\n          labeledNode.elementType = type;\n\n          if (this.eat(tt.question)) {\n            labeledNode.optional = true;\n            this.raise(TSErrors.TupleOptionalAfterType, {\n              at: this.state.lastTokStartLoc,\n            });\n          }\n        } else {\n          labeledNode = this.startNodeAtNode<N.TsNamedTupleMember>(type);\n          labeledNode.optional = optional;\n          this.raise(TSErrors.InvalidTupleMemberLabel, { at: type });\n          // @ts-expect-error This produces an invalid AST, but at least we don't drop\n          // nodes representing the invalid source.\n          labeledNode.label = type;\n          labeledNode.elementType = this.tsParseType();\n        }\n        type = this.finishNode(labeledNode, \"TSNamedTupleMember\");\n      } else if (optional) {\n        const optionalTypeNode = this.startNodeAtNode<N.TsOptionalType>(type);\n        optionalTypeNode.typeAnnotation = type;\n        type = this.finishNode(optionalTypeNode, \"TSOptionalType\");\n      }\n\n      if (rest) {\n        const restNode = this.startNodeAt<N.TsRestType>(startLoc);\n        restNode.typeAnnotation = type;\n        type = this.finishNode(restNode, \"TSRestType\");\n      }\n\n      return type;\n    }\n\n    tsParseParenthesizedType(): N.TsParenthesizedType {\n      const node = this.startNode<N.TsParenthesizedType>();\n      this.expect(tt.parenL);\n      node.typeAnnotation = this.tsParseType();\n      this.expect(tt.parenR);\n      return this.finishNode(node, \"TSParenthesizedType\");\n    }\n\n    tsParseFunctionOrConstructorType(\n      type: \"TSFunctionType\" | \"TSConstructorType\",\n      abstract?: boolean,\n    ): N.TsFunctionOrConstructorType {\n      const node = this.startNode<\n        N.TsFunctionOrConstructorType | N.TsConstructorType\n      >();\n      if (type === \"TSConstructorType\") {\n        (node as Undone<N.TsConstructorType>).abstract = !!abstract;\n        if (abstract) this.next();\n        this.next(); // eat `new`\n      }\n      this.tsInAllowConditionalTypesContext(() =>\n        this.tsFillSignature(tt.arrow, node),\n      );\n      return this.finishNode(node, type);\n    }\n\n    tsParseLiteralTypeNode(): N.TsLiteralType {\n      const node = this.startNode<N.TsLiteralType>();\n      switch (this.state.type) {\n        case tt.num:\n        case tt.bigint:\n        case tt.string:\n        case tt._true:\n        case tt._false:\n          // For compatibility to estree we cannot call parseLiteral directly here\n          // @ts-expect-error refine typings\n          node.literal = super.parseExprAtom();\n          break;\n        default:\n          this.unexpected();\n      }\n      return this.finishNode(node, \"TSLiteralType\");\n    }\n\n    tsParseTemplateLiteralType(): N.TsType {\n      const node = this.startNode<N.TsLiteralType>();\n      node.literal = super.parseTemplate(false);\n      return this.finishNode(node, \"TSLiteralType\");\n    }\n\n    parseTemplateSubstitution(): N.TsType | N.Node {\n      if (this.state.inType) return this.tsParseType();\n      return super.parseTemplateSubstitution();\n    }\n\n    tsParseThisTypeOrThisTypePredicate(): N.TsThisType | N.TsTypePredicate {\n      const thisKeyword = this.tsParseThisTypeNode();\n      if (this.isContextual(tt._is) && !this.hasPrecedingLineBreak()) {\n        return this.tsParseThisTypePredicate(thisKeyword);\n      } else {\n        return thisKeyword;\n      }\n    }\n\n    tsParseNonArrayType(): N.TsType {\n      switch (this.state.type) {\n        case tt.string:\n        case tt.num:\n        case tt.bigint:\n        case tt._true:\n        case tt._false:\n          return this.tsParseLiteralTypeNode();\n        case tt.plusMin:\n          if (this.state.value === \"-\") {\n            const node = this.startNode<N.TsLiteralType>();\n            const nextToken = this.lookahead();\n            if (nextToken.type !== tt.num && nextToken.type !== tt.bigint) {\n              this.unexpected();\n            }\n            // @ts-expect-error: parseMaybeUnary must returns unary expression\n            node.literal = this.parseMaybeUnary();\n            return this.finishNode(node, \"TSLiteralType\");\n          }\n          break;\n        case tt._this:\n          return this.tsParseThisTypeOrThisTypePredicate();\n        case tt._typeof:\n          return this.tsParseTypeQuery();\n        case tt._import:\n          return this.tsParseImportType();\n        case tt.braceL:\n          return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))\n            ? this.tsParseMappedType()\n            : this.tsParseTypeLiteral();\n        case tt.bracketL:\n          return this.tsParseTupleType();\n        case tt.parenL:\n          if (process.env.BABEL_8_BREAKING) {\n            if (!this.options.createParenthesizedExpressions) {\n              const startLoc = this.state.startLoc;\n              this.next();\n              const type = this.tsParseType();\n              this.expect(tt.parenR);\n              this.addExtra(type, \"parenthesized\", true);\n              this.addExtra(type, \"parenStart\", startLoc.index);\n              return type;\n            }\n          }\n\n          return this.tsParseParenthesizedType();\n        case tt.templateNonTail:\n        case tt.templateTail:\n          return this.tsParseTemplateLiteralType();\n        default: {\n          const { type } = this.state;\n          if (\n            tokenIsIdentifier(type) ||\n            type === tt._void ||\n            type === tt._null\n          ) {\n            const nodeType =\n              type === tt._void\n                ? \"TSVoidKeyword\"\n                : type === tt._null\n                ? \"TSNullKeyword\"\n                : keywordTypeFromName(this.state.value);\n            if (\n              nodeType !== undefined &&\n              this.lookaheadCharCode() !== charCodes.dot\n            ) {\n              const node = this.startNode<N.TsKeywordType>();\n              this.next();\n              return this.finishNode(node, nodeType);\n            }\n            return this.tsParseTypeReference();\n          }\n        }\n      }\n\n      this.unexpected();\n    }\n\n    tsParseArrayTypeOrHigher(): N.TsType {\n      let type = this.tsParseNonArrayType();\n      while (!this.hasPrecedingLineBreak() && this.eat(tt.bracketL)) {\n        if (this.match(tt.bracketR)) {\n          const node = this.startNodeAtNode<N.TsArrayType>(type);\n          node.elementType = type;\n          this.expect(tt.bracketR);\n          type = this.finishNode(node, \"TSArrayType\");\n        } else {\n          const node = this.startNodeAtNode<N.TsIndexedAccessType>(type);\n          node.objectType = type;\n          node.indexType = this.tsParseType();\n          this.expect(tt.bracketR);\n          type = this.finishNode(node, \"TSIndexedAccessType\");\n        }\n      }\n      return type;\n    }\n\n    tsParseTypeOperator(): N.TsTypeOperator {\n      const node = this.startNode<N.TsTypeOperator>();\n      const operator = this.state.value;\n      this.next(); // eat operator\n      node.operator = operator;\n      node.typeAnnotation = this.tsParseTypeOperatorOrHigher();\n\n      if (operator === \"readonly\") {\n        this.tsCheckTypeAnnotationForReadOnly(\n          // @ts-expect-error todo(flow->ts)\n          node,\n        );\n      }\n\n      return this.finishNode(node, \"TSTypeOperator\");\n    }\n\n    tsCheckTypeAnnotationForReadOnly(node: N.Node) {\n      switch (node.typeAnnotation.type) {\n        case \"TSTupleType\":\n        case \"TSArrayType\":\n          return;\n        default:\n          this.raise(TSErrors.UnexpectedReadonly, { at: node });\n      }\n    }\n\n    tsParseInferType(): N.TsInferType {\n      const node = this.startNode<N.TsInferType>();\n      this.expectContextual(tt._infer);\n      const typeParameter = this.startNode<N.TsTypeParameter>();\n      typeParameter.name = this.tsParseTypeParameterName();\n      typeParameter.constraint = this.tsTryParse(() =>\n        this.tsParseConstraintForInferType(),\n      );\n      node.typeParameter = this.finishNode(typeParameter, \"TSTypeParameter\");\n      return this.finishNode(node, \"TSInferType\");\n    }\n\n    tsParseConstraintForInferType() {\n      if (this.eat(tt._extends)) {\n        const constraint = this.tsInDisallowConditionalTypesContext(() =>\n          this.tsParseType(),\n        );\n        if (\n          this.state.inDisallowConditionalTypesContext ||\n          !this.match(tt.question)\n        ) {\n          return constraint;\n        }\n      }\n    }\n\n    tsParseTypeOperatorOrHigher(): N.TsType {\n      const isTypeOperator =\n        tokenIsTSTypeOperator(this.state.type) && !this.state.containsEsc;\n      return isTypeOperator\n        ? this.tsParseTypeOperator()\n        : this.isContextual(tt._infer)\n        ? this.tsParseInferType()\n        : this.tsInAllowConditionalTypesContext(() =>\n            this.tsParseArrayTypeOrHigher(),\n          );\n    }\n\n    tsParseUnionOrIntersectionType(\n      kind: \"TSUnionType\" | \"TSIntersectionType\",\n      parseConstituentType: () => N.TsType,\n      operator: TokenType,\n    ): N.TsType {\n      const node = this.startNode<N.TsUnionType | N.TsIntersectionType>();\n      const hasLeadingOperator = this.eat(operator);\n      const types = [];\n      do {\n        types.push(parseConstituentType());\n      } while (this.eat(operator));\n      if (types.length === 1 && !hasLeadingOperator) {\n        return types[0];\n      }\n      node.types = types;\n      return this.finishNode(node, kind);\n    }\n\n    tsParseIntersectionTypeOrHigher(): N.TsType {\n      return this.tsParseUnionOrIntersectionType(\n        \"TSIntersectionType\",\n        this.tsParseTypeOperatorOrHigher.bind(this),\n        tt.bitwiseAND,\n      );\n    }\n\n    tsParseUnionTypeOrHigher() {\n      return this.tsParseUnionOrIntersectionType(\n        \"TSUnionType\",\n        this.tsParseIntersectionTypeOrHigher.bind(this),\n        tt.bitwiseOR,\n      );\n    }\n\n    tsIsStartOfFunctionType() {\n      if (this.match(tt.lt)) {\n        return true;\n      }\n      return (\n        this.match(tt.parenL) &&\n        this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))\n      );\n    }\n\n    tsSkipParameterStart(): boolean {\n      if (tokenIsIdentifier(this.state.type) || this.match(tt._this)) {\n        this.next();\n        return true;\n      }\n\n      if (this.match(tt.braceL)) {\n        // Return true if we can parse an object pattern without errors\n        const { errors } = this.state;\n        const previousErrorCount = errors.length;\n        try {\n          this.parseObjectLike(tt.braceR, true);\n          return errors.length === previousErrorCount;\n        } catch {\n          return false;\n        }\n      }\n\n      if (this.match(tt.bracketL)) {\n        this.next();\n        // Return true if we can parse an array pattern without errors\n        const { errors } = this.state;\n        const previousErrorCount = errors.length;\n        try {\n          super.parseBindingList(\n            tt.bracketR,\n            charCodes.rightSquareBracket,\n            ParseBindingListFlags.ALLOW_EMPTY,\n          );\n          return errors.length === previousErrorCount;\n        } catch {\n          return false;\n        }\n      }\n\n      return false;\n    }\n\n    tsIsUnambiguouslyStartOfFunctionType(): boolean {\n      this.next();\n      if (this.match(tt.parenR) || this.match(tt.ellipsis)) {\n        // ( )\n        // ( ...\n        return true;\n      }\n      if (this.tsSkipParameterStart()) {\n        if (\n          this.match(tt.colon) ||\n          this.match(tt.comma) ||\n          this.match(tt.question) ||\n          this.match(tt.eq)\n        ) {\n          // ( xxx :\n          // ( xxx ,\n          // ( xxx ?\n          // ( xxx =\n          return true;\n        }\n        if (this.match(tt.parenR)) {\n          this.next();\n          if (this.match(tt.arrow)) {\n            // ( xxx ) =>\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n\n    tsParseTypeOrTypePredicateAnnotation(\n      returnToken: TokenType,\n    ): N.TsTypeAnnotation {\n      return this.tsInType(() => {\n        const t = this.startNode<N.TsTypeAnnotation>();\n        this.expect(returnToken);\n\n        const node = this.startNode<N.TsTypePredicate>();\n\n        const asserts = !!this.tsTryParse(\n          this.tsParseTypePredicateAsserts.bind(this),\n        );\n\n        if (asserts && this.match(tt._this)) {\n          // When asserts is false, thisKeyword is handled by tsParseNonArrayType\n          // : asserts this is type\n          let thisTypePredicate = this.tsParseThisTypeOrThisTypePredicate();\n          // if it turns out to be a `TSThisType`, wrap it with `TSTypePredicate`\n          // : asserts this\n          if (thisTypePredicate.type === \"TSThisType\") {\n            node.parameterName = thisTypePredicate;\n            node.asserts = true;\n            (node as N.TsTypePredicate).typeAnnotation = null;\n            thisTypePredicate = this.finishNode(node, \"TSTypePredicate\");\n          } else {\n            this.resetStartLocationFromNode(thisTypePredicate, node);\n            thisTypePredicate.asserts = true;\n          }\n          t.typeAnnotation = thisTypePredicate;\n          return this.finishNode(t, \"TSTypeAnnotation\");\n        }\n\n        const typePredicateVariable =\n          this.tsIsIdentifier() &&\n          this.tsTryParse(this.tsParseTypePredicatePrefix.bind(this));\n\n        if (!typePredicateVariable) {\n          if (!asserts) {\n            // : type\n            return this.tsParseTypeAnnotation(/* eatColon */ false, t);\n          }\n\n          // : asserts foo\n          node.parameterName = this.parseIdentifier();\n          node.asserts = asserts;\n          (node as N.TsTypePredicate).typeAnnotation = null;\n          t.typeAnnotation = this.finishNode(node, \"TSTypePredicate\");\n          return this.finishNode(t, \"TSTypeAnnotation\");\n        }\n\n        // : asserts foo is type\n        const type = this.tsParseTypeAnnotation(/* eatColon */ false);\n        node.parameterName = typePredicateVariable;\n        node.typeAnnotation = type;\n        node.asserts = asserts;\n        t.typeAnnotation = this.finishNode(node, \"TSTypePredicate\");\n        return this.finishNode(t, \"TSTypeAnnotation\");\n      });\n    }\n\n    tsTryParseTypeOrTypePredicateAnnotation(): N.TsTypeAnnotation | undefined {\n      if (this.match(tt.colon)) {\n        return this.tsParseTypeOrTypePredicateAnnotation(tt.colon);\n      }\n    }\n\n    tsTryParseTypeAnnotation(): N.TsTypeAnnotation | undefined {\n      if (this.match(tt.colon)) {\n        return this.tsParseTypeAnnotation();\n      }\n    }\n\n    tsTryParseType(): N.TsType | undefined {\n      return this.tsEatThenParseType(tt.colon);\n    }\n\n    tsParseTypePredicatePrefix(): N.Identifier | undefined {\n      const id = this.parseIdentifier();\n      if (this.isContextual(tt._is) && !this.hasPrecedingLineBreak()) {\n        this.next();\n        return id;\n      }\n    }\n\n    tsParseTypePredicateAsserts(): boolean {\n      if (this.state.type !== tt._asserts) {\n        return false;\n      }\n      const containsEsc = this.state.containsEsc;\n      this.next();\n      if (!tokenIsIdentifier(this.state.type) && !this.match(tt._this)) {\n        return false;\n      }\n\n      if (containsEsc) {\n        this.raise(Errors.InvalidEscapedReservedWord, {\n          at: this.state.lastTokStartLoc,\n          reservedWord: \"asserts\",\n        });\n      }\n\n      return true;\n    }\n\n    tsParseTypeAnnotation(\n      eatColon = true,\n      t: Undone<N.TsTypeAnnotation> = this.startNode<N.TsTypeAnnotation>(),\n    ): N.TsTypeAnnotation {\n      this.tsInType(() => {\n        if (eatColon) this.expect(tt.colon);\n        t.typeAnnotation = this.tsParseType();\n      });\n      return this.finishNode(t, \"TSTypeAnnotation\");\n    }\n\n    /** Be sure to be in a type context before calling this, using `tsInType`. */\n    tsParseType(): N.TsType {\n      // Need to set `state.inType` so that we don't parse JSX in a type context.\n      assert(this.state.inType);\n      const type = this.tsParseNonConditionalType();\n\n      if (\n        this.state.inDisallowConditionalTypesContext ||\n        this.hasPrecedingLineBreak() ||\n        !this.eat(tt._extends)\n      ) {\n        return type;\n      }\n      const node = this.startNodeAtNode<N.TsConditionalType>(type);\n      node.checkType = type;\n\n      node.extendsType = this.tsInDisallowConditionalTypesContext(() =>\n        this.tsParseNonConditionalType(),\n      );\n\n      this.expect(tt.question);\n      node.trueType = this.tsInAllowConditionalTypesContext(() =>\n        this.tsParseType(),\n      );\n\n      this.expect(tt.colon);\n      node.falseType = this.tsInAllowConditionalTypesContext(() =>\n        this.tsParseType(),\n      );\n\n      return this.finishNode(node, \"TSConditionalType\");\n    }\n\n    isAbstractConstructorSignature(): boolean {\n      return (\n        this.isContextual(tt._abstract) && this.lookahead().type === tt._new\n      );\n    }\n\n    tsParseNonConditionalType(): N.TsType {\n      if (this.tsIsStartOfFunctionType()) {\n        return this.tsParseFunctionOrConstructorType(\"TSFunctionType\");\n      }\n      if (this.match(tt._new)) {\n        // As in `new () => Date`\n        return this.tsParseFunctionOrConstructorType(\"TSConstructorType\");\n      } else if (this.isAbstractConstructorSignature()) {\n        // As in `abstract new () => Date`\n        return this.tsParseFunctionOrConstructorType(\n          \"TSConstructorType\",\n          /* abstract */ true,\n        );\n      }\n      return this.tsParseUnionTypeOrHigher();\n    }\n\n    tsParseTypeAssertion(): N.TsTypeAssertion {\n      if (this.getPluginOption(\"typescript\", \"disallowAmbiguousJSXLike\")) {\n        this.raise(TSErrors.ReservedTypeAssertion, { at: this.state.startLoc });\n      }\n\n      const node = this.startNode<N.TsTypeAssertion>();\n      node.typeAnnotation = this.tsInType(() => {\n        this.next(); // \"<\"\n        return this.match(tt._const)\n          ? this.tsParseTypeReference()\n          : this.tsParseType();\n      });\n      this.expect(tt.gt);\n      node.expression = this.parseMaybeUnary();\n      return this.finishNode(node, \"TSTypeAssertion\");\n    }\n\n    tsParseHeritageClause(\n      token: \"extends\" | \"implements\",\n    ): Array<N.TsExpressionWithTypeArguments> {\n      const originalStartLoc = this.state.startLoc;\n\n      const delimitedList = this.tsParseDelimitedList(\n        \"HeritageClauseElement\",\n        () => {\n          const node = this.startNode<N.TsExpressionWithTypeArguments>();\n          node.expression = this.tsParseEntityName();\n          if (this.match(tt.lt)) {\n            node.typeParameters = this.tsParseTypeArguments();\n          }\n\n          return this.finishNode(node, \"TSExpressionWithTypeArguments\");\n        },\n      );\n\n      if (!delimitedList.length) {\n        this.raise(TSErrors.EmptyHeritageClauseType, {\n          at: originalStartLoc,\n          token,\n        });\n      }\n\n      return delimitedList;\n    }\n\n    tsParseInterfaceDeclaration(\n      node: Undone<N.TsInterfaceDeclaration>,\n      properties: {\n        declare?: true;\n      } = {},\n    ): N.TsInterfaceDeclaration | null {\n      if (this.hasFollowingLineBreak()) return null;\n      this.expectContextual(tt._interface);\n      if (properties.declare) node.declare = true;\n      if (tokenIsIdentifier(this.state.type)) {\n        node.id = this.parseIdentifier();\n        this.checkIdentifier(node.id, BIND_TS_INTERFACE);\n      } else {\n        node.id = null;\n        this.raise(TSErrors.MissingInterfaceName, { at: this.state.startLoc });\n      }\n\n      node.typeParameters = this.tsTryParseTypeParameters(\n        this.tsParseInOutConstModifiers,\n      );\n      if (this.eat(tt._extends)) {\n        node.extends = this.tsParseHeritageClause(\"extends\");\n      }\n      const body = this.startNode<N.TSInterfaceBody>();\n      body.body = this.tsInType(this.tsParseObjectTypeMembers.bind(this));\n      node.body = this.finishNode(body, \"TSInterfaceBody\");\n      return this.finishNode(node, \"TSInterfaceDeclaration\");\n    }\n\n    tsParseTypeAliasDeclaration(\n      node: N.TsTypeAliasDeclaration,\n    ): N.TsTypeAliasDeclaration {\n      node.id = this.parseIdentifier();\n      this.checkIdentifier(node.id, BIND_TS_TYPE);\n\n      node.typeAnnotation = this.tsInType(() => {\n        node.typeParameters = this.tsTryParseTypeParameters(\n          this.tsParseInOutModifiers,\n        );\n\n        this.expect(tt.eq);\n\n        if (\n          this.isContextual(tt._intrinsic) &&\n          this.lookahead().type !== tt.dot\n        ) {\n          const node = this.startNode<N.TsKeywordType>();\n          this.next();\n          return this.finishNode(node, \"TSIntrinsicKeyword\");\n        }\n\n        return this.tsParseType();\n      });\n\n      this.semicolon();\n      return this.finishNode(node, \"TSTypeAliasDeclaration\");\n    }\n\n    tsInNoContext<T>(cb: () => T): T {\n      const oldContext = this.state.context;\n      this.state.context = [oldContext[0]];\n      try {\n        return cb();\n      } finally {\n        this.state.context = oldContext;\n      }\n    }\n\n    /**\n     * Runs `cb` in a type context.\n     * This should be called one token *before* the first type token,\n     * so that the call to `next()` is run in type context.\n     */\n    tsInType<T>(cb: () => T): T {\n      const oldInType = this.state.inType;\n      this.state.inType = true;\n      try {\n        return cb();\n      } finally {\n        this.state.inType = oldInType;\n      }\n    }\n\n    tsInDisallowConditionalTypesContext<T>(cb: () => T): T {\n      const oldInDisallowConditionalTypesContext =\n        this.state.inDisallowConditionalTypesContext;\n      this.state.inDisallowConditionalTypesContext = true;\n      try {\n        return cb();\n      } finally {\n        this.state.inDisallowConditionalTypesContext =\n          oldInDisallowConditionalTypesContext;\n      }\n    }\n\n    tsInAllowConditionalTypesContext<T>(cb: () => T): T {\n      const oldInDisallowConditionalTypesContext =\n        this.state.inDisallowConditionalTypesContext;\n      this.state.inDisallowConditionalTypesContext = false;\n      try {\n        return cb();\n      } finally {\n        this.state.inDisallowConditionalTypesContext =\n          oldInDisallowConditionalTypesContext;\n      }\n    }\n\n    tsEatThenParseType(token: TokenType): N.TsType | undefined {\n      if (this.match(token)) {\n        return this.tsNextThenParseType();\n      }\n    }\n\n    tsExpectThenParseType(token: TokenType): N.TsType {\n      return this.tsInType(() => {\n        this.expect(token);\n        return this.tsParseType();\n      });\n    }\n\n    tsNextThenParseType(): N.TsType {\n      return this.tsInType(() => {\n        this.next();\n        return this.tsParseType();\n      });\n    }\n\n    tsParseEnumMember(): N.TsEnumMember {\n      const node = this.startNode<N.TsEnumMember>();\n      // Computed property names are grammar errors in an enum, so accept just string literal or identifier.\n      node.id = this.match(tt.string)\n        ? super.parseStringLiteral(this.state.value)\n        : this.parseIdentifier(/* liberal */ true);\n      if (this.eat(tt.eq)) {\n        node.initializer = super.parseMaybeAssignAllowIn();\n      }\n      return this.finishNode(node, \"TSEnumMember\");\n    }\n\n    tsParseEnumDeclaration(\n      node: Undone<N.TsEnumDeclaration>,\n      properties: {\n        const?: true;\n        declare?: true;\n      } = {},\n    ): N.TsEnumDeclaration {\n      if (properties.const) node.const = true;\n      if (properties.declare) node.declare = true;\n      this.expectContextual(tt._enum);\n      node.id = this.parseIdentifier();\n      this.checkIdentifier(\n        node.id,\n        node.const ? BIND_TS_CONST_ENUM : BIND_TS_ENUM,\n      );\n\n      this.expect(tt.braceL);\n      node.members = this.tsParseDelimitedList(\n        \"EnumMembers\",\n        this.tsParseEnumMember.bind(this),\n      );\n      this.expect(tt.braceR);\n      return this.finishNode(node, \"TSEnumDeclaration\");\n    }\n\n    tsParseModuleBlock(): N.TsModuleBlock {\n      const node = this.startNode<N.TsModuleBlock>();\n      this.scope.enter(SCOPE_OTHER);\n\n      this.expect(tt.braceL);\n      // Inside of a module block is considered \"top-level\", meaning it can have imports and exports.\n      super.parseBlockOrModuleBlockBody(\n        (node.body = []),\n        /* directives */ undefined,\n        /* topLevel */ true,\n        /* end */ tt.braceR,\n      );\n      this.scope.exit();\n      return this.finishNode(node, \"TSModuleBlock\");\n    }\n\n    tsParseModuleOrNamespaceDeclaration(\n      node: Undone<N.TsModuleDeclaration>,\n      nested: boolean = false,\n    ): N.TsModuleDeclaration {\n      node.id = this.parseIdentifier();\n\n      if (!nested) {\n        this.checkIdentifier(node.id, BIND_TS_NAMESPACE);\n      }\n\n      if (this.eat(tt.dot)) {\n        const inner = this.startNode<N.TsModuleDeclaration>();\n        this.tsParseModuleOrNamespaceDeclaration(inner, true);\n        // @ts-expect-error Fixme: refine typings\n        node.body = inner;\n      } else {\n        this.scope.enter(SCOPE_TS_MODULE);\n        this.prodParam.enter(PARAM);\n        node.body = this.tsParseModuleBlock();\n        this.prodParam.exit();\n        this.scope.exit();\n      }\n      return this.finishNode(node, \"TSModuleDeclaration\");\n    }\n\n    tsParseAmbientExternalModuleDeclaration(\n      node: N.TsModuleDeclaration,\n    ): N.TsModuleDeclaration {\n      if (this.isContextual(tt._global)) {\n        node.global = true;\n        node.id = this.parseIdentifier();\n      } else if (this.match(tt.string)) {\n        node.id = super.parseStringLiteral(this.state.value);\n      } else {\n        this.unexpected();\n      }\n      if (this.match(tt.braceL)) {\n        this.scope.enter(SCOPE_TS_MODULE);\n        this.prodParam.enter(PARAM);\n        node.body = this.tsParseModuleBlock();\n        this.prodParam.exit();\n        this.scope.exit();\n      } else {\n        this.semicolon();\n      }\n\n      return this.finishNode(node, \"TSModuleDeclaration\");\n    }\n\n    tsParseImportEqualsDeclaration(\n      node: Undone<N.TsImportEqualsDeclaration>,\n      maybeDefaultIdentifier?: N.Identifier | null,\n      isExport?: boolean,\n    ): N.TsImportEqualsDeclaration {\n      node.isExport = isExport || false;\n      node.id = maybeDefaultIdentifier || this.parseIdentifier();\n      this.checkIdentifier(node.id, BIND_FLAGS_TS_IMPORT);\n      this.expect(tt.eq);\n      const moduleReference = this.tsParseModuleReference();\n      if (\n        node.importKind === \"type\" &&\n        moduleReference.type !== \"TSExternalModuleReference\"\n      ) {\n        this.raise(TSErrors.ImportAliasHasImportType, {\n          at: moduleReference,\n        });\n      }\n      node.moduleReference = moduleReference;\n      this.semicolon();\n      return this.finishNode(node, \"TSImportEqualsDeclaration\");\n    }\n\n    tsIsExternalModuleReference(): boolean {\n      return (\n        this.isContextual(tt._require) &&\n        this.lookaheadCharCode() === charCodes.leftParenthesis\n      );\n    }\n\n    tsParseModuleReference(): N.TsModuleReference {\n      return this.tsIsExternalModuleReference()\n        ? this.tsParseExternalModuleReference()\n        : this.tsParseEntityName(/* allowReservedWords */ false);\n    }\n\n    tsParseExternalModuleReference(): N.TsExternalModuleReference {\n      const node = this.startNode<N.TsExternalModuleReference>();\n      this.expectContextual(tt._require);\n      this.expect(tt.parenL);\n      if (!this.match(tt.string)) {\n        this.unexpected();\n      }\n      // For compatibility to estree we cannot call parseLiteral directly here\n      node.expression = super.parseExprAtom() as N.StringLiteral;\n      this.expect(tt.parenR);\n      this.sawUnambiguousESM = true;\n      return this.finishNode(node, \"TSExternalModuleReference\");\n    }\n\n    // Utilities\n\n    tsLookAhead<T>(f: () => T): T {\n      const state = this.state.clone();\n      const res = f();\n      this.state = state;\n      return res;\n    }\n\n    tsTryParseAndCatch<T extends N.NodeBase | undefined | null>(\n      f: () => T,\n    ): T | undefined | null {\n      const result = this.tryParse(\n        abort =>\n          // @ts-expect-error todo(flow->ts)\n          f() || abort(),\n      );\n\n      if (result.aborted || !result.node) return;\n      if (result.error) this.state = result.failState;\n      // @ts-expect-error refine typings\n      return result.node;\n    }\n\n    tsTryParse<T>(f: () => T | undefined | false): T | undefined {\n      const state = this.state.clone();\n      const result = f();\n      if (result !== undefined && result !== false) {\n        return result;\n      }\n      this.state = state;\n    }\n\n    tsTryParseDeclare(nany: any): N.Declaration | undefined {\n      if (this.isLineTerminator()) {\n        return;\n      }\n      let startType = this.state.type;\n      let kind: \"let\" | null;\n\n      if (this.isContextual(tt._let)) {\n        startType = tt._var;\n        kind = \"let\";\n      }\n\n      // @ts-expect-error refine typings\n      return this.tsInAmbientContext(() => {\n        switch (startType) {\n          case tt._function:\n            nany.declare = true;\n            return super.parseFunctionStatement(\n              nany,\n              /* async */ false,\n              /* isHangingDeclaration */ false,\n            );\n          case tt._class:\n            // While this is also set by tsParseExpressionStatement, we need to set it\n            // before parsing the class declaration to know how to register it in the scope.\n            nany.declare = true;\n            return this.parseClass(\n              nany,\n              /* isStatement */ true,\n              /* optionalId */ false,\n            );\n          case tt._enum:\n            return this.tsParseEnumDeclaration(nany, { declare: true });\n          case tt._global:\n            return this.tsParseAmbientExternalModuleDeclaration(nany);\n          case tt._const:\n          case tt._var:\n            if (!this.match(tt._const) || !this.isLookaheadContextual(\"enum\")) {\n              nany.declare = true;\n              return this.parseVarStatement(\n                nany,\n                kind || this.state.value,\n                true,\n              );\n            }\n\n            // `const enum = 0;` not allowed because \"enum\" is a strict mode reserved word.\n            this.expect(tt._const);\n            return this.tsParseEnumDeclaration(nany, {\n              const: true,\n              declare: true,\n            });\n          case tt._interface: {\n            const result = this.tsParseInterfaceDeclaration(nany, {\n              declare: true,\n            });\n            if (result) return result;\n          }\n          // fallthrough\n          default:\n            if (tokenIsIdentifier(startType)) {\n              return this.tsParseDeclaration(\n                nany,\n                this.state.value,\n                /* next */ true,\n                /* decorators */ null,\n              );\n            }\n        }\n      });\n    }\n\n    // Note: this won't be called unless the keyword is allowed in `shouldParseExportDeclaration`.\n    tsTryParseExportDeclaration(): N.Declaration | undefined {\n      return this.tsParseDeclaration(\n        this.startNode(),\n        this.state.value,\n        /* next */ true,\n        /* decorators */ null,\n      );\n    }\n\n    tsParseExpressionStatement(\n      node: Undone<N.TsModuleDeclaration>,\n      expr: N.Identifier,\n      decorators: N.Decorator[] | null,\n    ): N.Declaration | undefined {\n      switch (expr.name) {\n        case \"declare\": {\n          const declaration = this.tsTryParseDeclare(node);\n          if (declaration) {\n            declaration.declare = true;\n          }\n          return declaration;\n        }\n        case \"global\":\n          // `global { }` (with no `declare`) may appear inside an ambient module declaration.\n          // Would like to use tsParseAmbientExternalModuleDeclaration here, but already ran past \"global\".\n          if (this.match(tt.braceL)) {\n            this.scope.enter(SCOPE_TS_MODULE);\n            this.prodParam.enter(PARAM);\n            const mod = node;\n            mod.global = true;\n            mod.id = expr;\n            mod.body = this.tsParseModuleBlock();\n            this.scope.exit();\n            this.prodParam.exit();\n            return this.finishNode(mod, \"TSModuleDeclaration\");\n          }\n          break;\n\n        default:\n          return this.tsParseDeclaration(\n            node,\n            expr.name,\n            /* next */ false,\n            decorators,\n          );\n      }\n    }\n\n    // Common to tsTryParseDeclare, tsTryParseExportDeclaration, and tsParseExpressionStatement.\n    tsParseDeclaration(\n      node: any,\n      value: string,\n      next: boolean,\n      decorators: N.Decorator[] | null,\n    ): N.Declaration | undefined | null {\n      // no declaration apart from enum can be followed by a line break.\n      switch (value) {\n        case \"abstract\":\n          if (\n            this.tsCheckLineTerminator(next) &&\n            (this.match(tt._class) || tokenIsIdentifier(this.state.type))\n          ) {\n            return this.tsParseAbstractDeclaration(node, decorators);\n          }\n          break;\n\n        case \"module\":\n          if (this.tsCheckLineTerminator(next)) {\n            if (this.match(tt.string)) {\n              return this.tsParseAmbientExternalModuleDeclaration(node);\n            } else if (tokenIsIdentifier(this.state.type)) {\n              return this.tsParseModuleOrNamespaceDeclaration(node);\n            }\n          }\n          break;\n\n        case \"namespace\":\n          if (\n            this.tsCheckLineTerminator(next) &&\n            tokenIsIdentifier(this.state.type)\n          ) {\n            return this.tsParseModuleOrNamespaceDeclaration(node);\n          }\n          break;\n\n        case \"type\":\n          if (\n            this.tsCheckLineTerminator(next) &&\n            tokenIsIdentifier(this.state.type)\n          ) {\n            return this.tsParseTypeAliasDeclaration(node);\n          }\n          break;\n      }\n    }\n\n    tsCheckLineTerminator(next: boolean) {\n      if (next) {\n        if (this.hasFollowingLineBreak()) return false;\n        this.next();\n        return true;\n      }\n      return !this.isLineTerminator();\n    }\n\n    tsTryParseGenericAsyncArrowFunction(\n      startLoc: Position,\n    ): N.ArrowFunctionExpression | undefined {\n      if (!this.match(tt.lt)) return;\n\n      const oldMaybeInArrowParameters = this.state.maybeInArrowParameters;\n      this.state.maybeInArrowParameters = true;\n\n      const res: Undone<N.ArrowFunctionExpression> | undefined =\n        this.tsTryParseAndCatch(() => {\n          const node = this.startNodeAt<N.ArrowFunctionExpression>(startLoc);\n          node.typeParameters = this.tsParseTypeParameters(\n            this.tsParseConstModifier,\n          );\n          // Don't use overloaded parseFunctionParams which would look for \"<\" again.\n          super.parseFunctionParams(node);\n          node.returnType = this.tsTryParseTypeOrTypePredicateAnnotation();\n          this.expect(tt.arrow);\n          return node;\n        });\n\n      this.state.maybeInArrowParameters = oldMaybeInArrowParameters;\n\n      if (!res) return;\n\n      return super.parseArrowExpression(\n        res,\n        /* params are already set */ null,\n        /* async */ true,\n      );\n    }\n\n    // Used when parsing type arguments from ES productions, where the first token\n    // has been created without state.inType. Thus we need to rescan the lt token.\n    tsParseTypeArgumentsInExpression(): N.TsTypeParameterInstantiation | void {\n      if (this.reScan_lt() !== tt.lt) return;\n      return this.tsParseTypeArguments();\n    }\n\n    tsParseTypeArguments(): N.TsTypeParameterInstantiation {\n      const node = this.startNode<N.TsTypeParameterInstantiation>();\n      node.params = this.tsInType(() =>\n        // Temporarily remove a JSX parsing context, which makes us scan different tokens.\n        this.tsInNoContext(() => {\n          this.expect(tt.lt);\n          return this.tsParseDelimitedList(\n            \"TypeParametersOrArguments\",\n            this.tsParseType.bind(this),\n          );\n        }),\n      );\n      if (node.params.length === 0) {\n        this.raise(TSErrors.EmptyTypeArguments, { at: node });\n      } else if (!this.state.inType && this.curContext() === tc.brace) {\n        // rescan `>` when we are no longer in type context and JSX parsing context\n        // since it was tokenized when `inType` is `true`.\n        this.reScan_lt_gt();\n      }\n      this.expect(tt.gt);\n      return this.finishNode(node, \"TSTypeParameterInstantiation\");\n    }\n\n    tsIsDeclarationStart(): boolean {\n      return tokenIsTSDeclarationStart(this.state.type);\n    }\n\n    // ======================================================\n    // OVERRIDES\n    // ======================================================\n\n    isExportDefaultSpecifier(): boolean {\n      if (this.tsIsDeclarationStart()) return false;\n      return super.isExportDefaultSpecifier();\n    }\n\n    parseAssignableListItem(\n      flags: ParseBindingListFlags,\n      decorators: N.Decorator[],\n    ): N.Pattern | N.TSParameterProperty {\n      // Store original location to include modifiers in range\n      const startLoc = this.state.startLoc;\n\n      const modified: ModifierBase = {};\n      this.tsParseModifiers(\n        {\n          allowedModifiers: [\n            \"public\",\n            \"private\",\n            \"protected\",\n            \"override\",\n            \"readonly\",\n          ],\n        },\n        modified,\n      );\n      const accessibility = modified.accessibility;\n      const override = modified.override;\n      const readonly = modified.readonly;\n      if (\n        !(flags & ParseBindingListFlags.IS_CONSTRUCTOR_PARAMS) &&\n        (accessibility || readonly || override)\n      ) {\n        this.raise(TSErrors.UnexpectedParameterModifier, { at: startLoc });\n      }\n\n      const left = this.parseMaybeDefault();\n      this.parseAssignableListItemTypes(left, flags);\n      const elt = this.parseMaybeDefault(left.loc.start, left);\n      if (accessibility || readonly || override) {\n        const pp = this.startNodeAt<N.TSParameterProperty>(startLoc);\n        if (decorators.length) {\n          pp.decorators = decorators;\n        }\n        if (accessibility) pp.accessibility = accessibility;\n        if (readonly) pp.readonly = readonly;\n        if (override) pp.override = override;\n        if (elt.type !== \"Identifier\" && elt.type !== \"AssignmentPattern\") {\n          this.raise(TSErrors.UnsupportedParameterPropertyKind, { at: pp });\n        }\n        pp.parameter = elt as any as N.Identifier | N.AssignmentPattern;\n        return this.finishNode(pp, \"TSParameterProperty\");\n      }\n\n      if (decorators.length) {\n        left.decorators = decorators;\n      }\n\n      return elt;\n    }\n\n    isSimpleParameter(node: N.Pattern | N.TSParameterProperty) {\n      return (\n        (node.type === \"TSParameterProperty\" &&\n          super.isSimpleParameter(node.parameter)) ||\n        super.isSimpleParameter(node)\n      );\n    }\n\n    tsDisallowOptionalPattern(node: Undone<N.Function>) {\n      for (const param of node.params) {\n        if (\n          param.type !== \"Identifier\" &&\n          (param as any).optional &&\n          !this.state.isAmbientContext\n        ) {\n          this.raise(TSErrors.PatternIsOptional, { at: param });\n        }\n      }\n    }\n\n    setArrowFunctionParameters(\n      node: Undone<N.ArrowFunctionExpression>,\n      params: N.Expression[],\n      trailingCommaLoc?: Position | null,\n    ): void {\n      super.setArrowFunctionParameters(node, params, trailingCommaLoc);\n      this.tsDisallowOptionalPattern(node);\n    }\n\n    parseFunctionBodyAndFinish<\n      T extends\n        | N.Function\n        | N.TSDeclareMethod\n        | N.TSDeclareFunction\n        | N.ClassPrivateMethod,\n    >(node: Undone<T>, type: T[\"type\"], isMethod: boolean = false): T {\n      if (this.match(tt.colon)) {\n        node.returnType = this.tsParseTypeOrTypePredicateAnnotation(tt.colon);\n      }\n\n      const bodilessType =\n        type === \"FunctionDeclaration\"\n          ? \"TSDeclareFunction\"\n          : type === \"ClassMethod\" || type === \"ClassPrivateMethod\"\n          ? \"TSDeclareMethod\"\n          : undefined;\n      if (bodilessType && !this.match(tt.braceL) && this.isLineTerminator()) {\n        return this.finishNode(node, bodilessType);\n      }\n      if (bodilessType === \"TSDeclareFunction\" && this.state.isAmbientContext) {\n        this.raise(TSErrors.DeclareFunctionHasImplementation, { at: node });\n        if ((node as Undone<N.FunctionDeclaration>).declare) {\n          return super.parseFunctionBodyAndFinish(node, bodilessType, isMethod);\n        }\n      }\n      this.tsDisallowOptionalPattern(node);\n\n      return super.parseFunctionBodyAndFinish(node, type, isMethod);\n    }\n\n    registerFunctionStatementId(node: N.Function): void {\n      if (!node.body && node.id) {\n        // Function ids are validated after parsing their body.\n        // For bodiless function, we need to do it here.\n        this.checkIdentifier(node.id, BIND_TS_AMBIENT);\n      } else {\n        super.registerFunctionStatementId(node);\n      }\n    }\n\n    tsCheckForInvalidTypeCasts(items: Array<N.Expression | undefined | null>) {\n      items.forEach(node => {\n        if (node?.type === \"TSTypeCastExpression\") {\n          this.raise(TSErrors.UnexpectedTypeAnnotation, {\n            at: node.typeAnnotation,\n          });\n        }\n      });\n    }\n\n    toReferencedList(\n      exprList: Array<N.Expression | undefined | null>,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      isInParens?: boolean,\n    ): Array<N.Expression | undefined | null> {\n      // Handles invalid scenarios like: `f(a:b)`, `(a:b);`, and `(a:b,c:d)`.\n      //\n      // Note that `f<T>(a:b)` goes through a different path and is handled\n      // in `parseSubscript` directly.\n      this.tsCheckForInvalidTypeCasts(exprList);\n      return exprList;\n    }\n\n    parseArrayLike(\n      close: TokenType,\n      canBePattern: boolean,\n      isTuple: boolean,\n      refExpressionErrors?: ExpressionErrors | null,\n    ): N.ArrayExpression | N.TupleExpression {\n      const node = super.parseArrayLike(\n        close,\n        canBePattern,\n        isTuple,\n        refExpressionErrors,\n      );\n\n      if (node.type === \"ArrayExpression\") {\n        this.tsCheckForInvalidTypeCasts(node.elements);\n      }\n\n      return node;\n    }\n\n    parseSubscript(\n      base: N.Expression,\n\n      startLoc: Position,\n      noCalls: boolean | undefined | null,\n      state: N.ParseSubscriptState,\n    ): N.Expression {\n      if (!this.hasPrecedingLineBreak() && this.match(tt.bang)) {\n        // When ! is consumed as a postfix operator (non-null assertion),\n        // disallow JSX tag forming after. e.g. When parsing `p! < n.p!`\n        // `<n.p` can not be a start of JSX tag\n        this.state.canStartJSXElement = false;\n        this.next();\n\n        const nonNullExpression =\n          this.startNodeAt<N.TsNonNullExpression>(startLoc);\n        nonNullExpression.expression = base;\n        return this.finishNode(nonNullExpression, \"TSNonNullExpression\");\n      }\n\n      let isOptionalCall = false;\n      if (\n        this.match(tt.questionDot) &&\n        this.lookaheadCharCode() === charCodes.lessThan\n      ) {\n        if (noCalls) {\n          state.stop = true;\n          return base;\n        }\n        state.optionalChainMember = isOptionalCall = true;\n        this.next();\n      }\n\n      // handles 'f<<T>'\n      if (this.match(tt.lt) || this.match(tt.bitShiftL)) {\n        let missingParenErrorLoc;\n        // tsTryParseAndCatch is expensive, so avoid if not necessary.\n        // There are number of things we are going to \"maybe\" parse, like type arguments on\n        // tagged template expressions. If any of them fail, walk it back and continue.\n        const result = this.tsTryParseAndCatch(() => {\n          if (!noCalls && this.atPossibleAsyncArrow(base)) {\n            // Almost certainly this is a generic async function `async <T>() => ...\n            // But it might be a call with a type argument `async<T>();`\n            const asyncArrowFn =\n              this.tsTryParseGenericAsyncArrowFunction(startLoc);\n            if (asyncArrowFn) {\n              return asyncArrowFn;\n            }\n          }\n\n          const typeArguments = this.tsParseTypeArgumentsInExpression();\n          if (!typeArguments) return;\n\n          if (isOptionalCall && !this.match(tt.parenL)) {\n            missingParenErrorLoc = this.state.curPosition();\n            return;\n          }\n\n          if (tokenIsTemplate(this.state.type)) {\n            const result = super.parseTaggedTemplateExpression(\n              base,\n\n              startLoc,\n              state,\n            );\n            result.typeParameters = typeArguments;\n            return result;\n          }\n\n          if (!noCalls && this.eat(tt.parenL)) {\n            const node = this.startNodeAt<\n              N.CallExpression | N.OptionalCallExpression\n            >(startLoc);\n            node.callee = base;\n            // possibleAsync always false here, because we would have handled it above.\n            // @ts-expect-error (won't be any undefined arguments)\n            node.arguments = this.parseCallExpressionArguments(\n              tt.parenR,\n              /* possibleAsync */ false,\n            );\n\n            // Handles invalid case: `f<T>(a:b)`\n            this.tsCheckForInvalidTypeCasts(node.arguments);\n\n            node.typeParameters = typeArguments;\n            if (state.optionalChainMember) {\n              (node as Undone<N.OptionalCallExpression>).optional =\n                isOptionalCall;\n            }\n\n            return this.finishCallExpression(node, state.optionalChainMember);\n          }\n\n          const tokenType = this.state.type;\n          if (\n            // a<b>>c is not (a<b>)>c, but a<(b>>c)\n            tokenType === tt.gt ||\n            // a<b>>>c is not (a<b>)>>c, but a<(b>>>c)\n            tokenType === tt.bitShiftR ||\n            // a<b>c is (a<b)>c\n            (tokenType !== tt.parenL &&\n              tokenCanStartExpression(tokenType) &&\n              !this.hasPrecedingLineBreak())\n          ) {\n            // Bail out.\n            return;\n          }\n\n          const node = this.startNodeAt<N.TsInstantiationExpression>(startLoc);\n          node.expression = base;\n          node.typeParameters = typeArguments;\n          return this.finishNode(node, \"TSInstantiationExpression\");\n        });\n\n        if (missingParenErrorLoc) {\n          this.unexpected(missingParenErrorLoc, tt.parenL);\n        }\n\n        if (result) {\n          if (\n            result.type === \"TSInstantiationExpression\" &&\n            (this.match(tt.dot) ||\n              (this.match(tt.questionDot) &&\n                this.lookaheadCharCode() !== charCodes.leftParenthesis))\n          ) {\n            this.raise(\n              TSErrors.InvalidPropertyAccessAfterInstantiationExpression,\n              { at: this.state.startLoc },\n            );\n          }\n          return result;\n        }\n      }\n\n      return super.parseSubscript(base, startLoc, noCalls, state);\n    }\n\n    parseNewCallee(node: N.NewExpression): void {\n      super.parseNewCallee(node);\n\n      const { callee } = node;\n      if (\n        callee.type === \"TSInstantiationExpression\" &&\n        !callee.extra?.parenthesized\n      ) {\n        node.typeParameters = callee.typeParameters;\n        node.callee = callee.expression;\n      }\n    }\n\n    parseExprOp(\n      left: N.Expression,\n      leftStartLoc: Position,\n      minPrec: number,\n    ): N.Expression {\n      let isSatisfies: boolean;\n      if (\n        tokenOperatorPrecedence(tt._in) > minPrec &&\n        !this.hasPrecedingLineBreak() &&\n        (this.isContextual(tt._as) ||\n          (isSatisfies = this.isContextual(tt._satisfies)))\n      ) {\n        const node = this.startNodeAt<\n          N.TsAsExpression | N.TsSatisfiesExpression\n        >(leftStartLoc);\n        node.expression = left;\n        node.typeAnnotation = this.tsInType(() => {\n          this.next(); // \"as\" or \"satisfies\"\n          if (this.match(tt._const)) {\n            if (isSatisfies) {\n              this.raise(Errors.UnexpectedKeyword, {\n                at: this.state.startLoc,\n                keyword: \"const\",\n              });\n            }\n            return this.tsParseTypeReference();\n          }\n\n          return this.tsParseType();\n        });\n        this.finishNode(\n          node,\n          isSatisfies ? \"TSSatisfiesExpression\" : \"TSAsExpression\",\n        );\n        // rescan `<`, `>` because they were scanned when this.state.inType was true\n        this.reScan_lt_gt();\n        return this.parseExprOp(\n          // @ts-expect-error todo(flow->ts)\n          node,\n          leftStartLoc,\n          minPrec,\n        );\n      }\n\n      return super.parseExprOp(left, leftStartLoc, minPrec);\n    }\n\n    checkReservedWord(\n      word: string,\n      startLoc: Position,\n      checkKeywords: boolean,\n      isBinding: boolean,\n    ): void {\n      // Strict mode words may be allowed as in `declare namespace N { const static: number; }`.\n      // And we have a type checker anyway, so don't bother having the parser do it.\n      if (!this.state.isAmbientContext) {\n        super.checkReservedWord(word, startLoc, checkKeywords, isBinding);\n      }\n    }\n\n    checkImportReflection(node: Undone<N.ImportDeclaration>) {\n      super.checkImportReflection(node);\n      if (node.module && node.importKind !== \"value\") {\n        this.raise(TSErrors.ImportReflectionHasImportType, {\n          at: node.specifiers[0].loc.start,\n        });\n      }\n    }\n\n    /*\n    Don't bother doing this check in TypeScript code because:\n    1. We may have a nested export statement with the same name:\n      export const x = 0;\n      export namespace N {\n        export const x = 1;\n      }\n    2. We have a type checker to warn us about this sort of thing.\n    */\n    checkDuplicateExports() {}\n\n    isPotentialImportPhase(isExport: boolean): boolean {\n      if (super.isPotentialImportPhase(isExport)) return true;\n      if (this.isContextual(tt._type)) {\n        const ch = this.lookaheadCharCode();\n        return isExport\n          ? ch === charCodes.leftCurlyBrace || ch === charCodes.asterisk\n          : ch !== charCodes.equalsTo;\n      }\n      return !isExport && this.isContextual(tt._typeof);\n    }\n\n    applyImportPhase(\n      node: Undone<N.ImportDeclaration | N.ExportNamedDeclaration>,\n      isExport: boolean,\n      phase: string | null,\n      loc?: Position,\n    ): void {\n      super.applyImportPhase(node, isExport, phase, loc);\n      if (isExport) {\n        (node as N.ExportNamedDeclaration).exportKind =\n          phase === \"type\" ? \"type\" : \"value\";\n      } else {\n        (node as N.ImportDeclaration).importKind =\n          phase === \"type\" || phase === \"typeof\" ? phase : \"value\";\n      }\n    }\n\n    parseImport(\n      node: Undone<N.ImportDeclaration | N.TsImportEqualsDeclaration>,\n    ): N.AnyImport {\n      if (this.match(tt.string)) {\n        node.importKind = \"value\";\n        return super.parseImport(node as Undone<N.ImportDeclaration>);\n      }\n\n      let importNode;\n      if (\n        tokenIsIdentifier(this.state.type) &&\n        this.lookaheadCharCode() === charCodes.equalsTo\n      ) {\n        node.importKind = \"value\";\n        return this.tsParseImportEqualsDeclaration(\n          node as Undone<N.TsImportEqualsDeclaration>,\n        );\n      } else if (this.isContextual(tt._type)) {\n        const maybeDefaultIdentifier = this.parseMaybeImportPhase(\n          node as Undone<N.ImportDeclaration>,\n          /* isExport */ false,\n        );\n        if (this.lookaheadCharCode() === charCodes.equalsTo) {\n          return this.tsParseImportEqualsDeclaration(\n            node as Undone<N.TsImportEqualsDeclaration>,\n            maybeDefaultIdentifier,\n          );\n        } else {\n          importNode = super.parseImportSpecifiersAndAfter(\n            node as Undone<N.ImportDeclaration>,\n            maybeDefaultIdentifier,\n          );\n        }\n      } else {\n        importNode = super.parseImport(node as Undone<N.ImportDeclaration>);\n      }\n\n      // `import type` can only be used on imports with named imports or with a\n      // default import - but not both\n      if (\n        importNode.importKind === \"type\" &&\n        // @ts-expect-error refine typings\n        importNode.specifiers.length > 1 &&\n        // @ts-expect-error refine typings\n        importNode.specifiers[0].type === \"ImportDefaultSpecifier\"\n      ) {\n        this.raise(TSErrors.TypeImportCannotSpecifyDefaultAndNamed, {\n          at: importNode,\n        });\n      }\n\n      return importNode;\n    }\n\n    parseExport(\n      node: Undone<N.Node>,\n      decorators: N.Decorator[] | null,\n    ): N.AnyExport {\n      if (this.match(tt._import)) {\n        // `export import A = B;`\n        this.next(); // eat `tt._import`\n        let maybeDefaultIdentifier: N.Identifier | null = null;\n        if (\n          this.isContextual(tt._type) &&\n          // We pass false here, because we are parsing an `import ... =`\n          this.isPotentialImportPhase(/* isExport */ false)\n        ) {\n          maybeDefaultIdentifier = this.parseMaybeImportPhase(\n            node as Undone<N.TsImportEqualsDeclaration>,\n            /* isExport */ false,\n          );\n        } else {\n          node.importKind = \"value\";\n        }\n        return this.tsParseImportEqualsDeclaration(\n          node as Undone<N.TsImportEqualsDeclaration>,\n          maybeDefaultIdentifier,\n          /* isExport */ true,\n        );\n      } else if (this.eat(tt.eq)) {\n        // `export = x;`\n        const assign = node as Undone<N.TsExportAssignment>;\n        assign.expression = super.parseExpression();\n        this.semicolon();\n        this.sawUnambiguousESM = true;\n        return this.finishNode(assign, \"TSExportAssignment\");\n      } else if (this.eatContextual(tt._as)) {\n        // `export as namespace A;`\n        const decl = node as Undone<N.TsNamespaceExportDeclaration>;\n        // See `parseNamespaceExportDeclaration` in TypeScript's own parser\n        this.expectContextual(tt._namespace);\n        decl.id = this.parseIdentifier();\n        this.semicolon();\n        return this.finishNode(decl, \"TSNamespaceExportDeclaration\");\n      } else {\n        return super.parseExport(\n          node as Undone<N.ExportAllDeclaration | N.ExportDefaultDeclaration>,\n          decorators,\n        );\n      }\n    }\n\n    isAbstractClass(): boolean {\n      return (\n        this.isContextual(tt._abstract) && this.lookahead().type === tt._class\n      );\n    }\n\n    parseExportDefaultExpression(): N.Expression | N.Declaration {\n      if (this.isAbstractClass()) {\n        const cls = this.startNode<N.Class>();\n        this.next(); // Skip \"abstract\"\n        cls.abstract = true;\n        return this.parseClass(cls, true, true);\n      }\n\n      // export default interface allowed in:\n      // https://github.com/Microsoft/TypeScript/pull/16040\n      if (this.match(tt._interface)) {\n        const result = this.tsParseInterfaceDeclaration(\n          this.startNode<N.TsInterfaceDeclaration>(),\n        );\n        if (result) return result;\n      }\n\n      return super.parseExportDefaultExpression();\n    }\n\n    parseVarStatement(\n      node: N.VariableDeclaration,\n      kind: \"var\" | \"let\" | \"const\" | \"using\",\n      allowMissingInitializer: boolean = false,\n    ) {\n      const { isAmbientContext } = this.state;\n      const declaration = super.parseVarStatement(\n        node,\n        kind,\n        allowMissingInitializer || isAmbientContext,\n      );\n\n      if (!isAmbientContext) return declaration;\n\n      for (const { id, init } of declaration.declarations) {\n        // Empty initializer is the easy case that we want.\n        if (!init) continue;\n\n        // var and let aren't ever allowed initializers.\n        if (kind !== \"const\" || !!id.typeAnnotation) {\n          this.raise(TSErrors.InitializerNotAllowedInAmbientContext, {\n            at: init,\n          });\n        } else if (\n          !isValidAmbientConstInitializer(init, this.hasPlugin(\"estree\"))\n        ) {\n          this.raise(\n            TSErrors.ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference,\n            { at: init },\n          );\n        }\n      }\n\n      return declaration;\n    }\n\n    parseStatementContent(\n      flags: ParseStatementFlag,\n      decorators?: N.Decorator[] | null,\n    ): N.Statement {\n      if (this.match(tt._const) && this.isLookaheadContextual(\"enum\")) {\n        const node = this.startNode<N.TsEnumDeclaration>();\n        this.expect(tt._const); // eat 'const'\n        return this.tsParseEnumDeclaration(node, { const: true });\n      }\n\n      if (this.isContextual(tt._enum)) {\n        return this.tsParseEnumDeclaration(\n          this.startNode<N.TsEnumDeclaration>(),\n        );\n      }\n\n      if (this.isContextual(tt._interface)) {\n        const result = this.tsParseInterfaceDeclaration(this.startNode());\n        if (result) return result;\n      }\n\n      return super.parseStatementContent(flags, decorators);\n    }\n\n    parseAccessModifier(): N.Accessibility | undefined | null {\n      return this.tsParseModifier([\"public\", \"protected\", \"private\"]);\n    }\n\n    tsHasSomeModifiers(member: any, modifiers: readonly TsModifier[]): boolean {\n      return modifiers.some(modifier => {\n        if (tsIsAccessModifier(modifier)) {\n          return member.accessibility === modifier;\n        }\n        return !!member[modifier];\n      });\n    }\n\n    tsIsStartOfStaticBlocks() {\n      return (\n        this.isContextual(tt._static) &&\n        this.lookaheadCharCode() === charCodes.leftCurlyBrace\n      );\n    }\n\n    parseClassMember(\n      classBody: N.ClassBody,\n      member: any,\n      state: N.ParseClassMemberState,\n    ): void {\n      const modifiers = [\n        \"declare\",\n        \"private\",\n        \"public\",\n        \"protected\",\n        \"override\",\n        \"abstract\",\n        \"readonly\",\n        \"static\",\n      ] as const;\n      this.tsParseModifiers(\n        {\n          allowedModifiers: modifiers,\n          disallowedModifiers: [\"in\", \"out\"],\n          stopOnStartOfClassStaticBlock: true,\n          errorTemplate: TSErrors.InvalidModifierOnTypeParameterPositions,\n        },\n        member,\n      );\n\n      const callParseClassMemberWithIsStatic = () => {\n        if (this.tsIsStartOfStaticBlocks()) {\n          this.next(); // eat \"static\"\n          this.next(); // eat \"{\"\n          if (this.tsHasSomeModifiers(member, modifiers)) {\n            this.raise(TSErrors.StaticBlockCannotHaveModifier, {\n              at: this.state.curPosition(),\n            });\n          }\n          super.parseClassStaticBlock(classBody, member as N.StaticBlock);\n        } else {\n          this.parseClassMemberWithIsStatic(\n            classBody,\n            member,\n            state,\n            !!member.static,\n          );\n        }\n      };\n      if (member.declare) {\n        this.tsInAmbientContext(callParseClassMemberWithIsStatic);\n      } else {\n        callParseClassMemberWithIsStatic();\n      }\n    }\n\n    parseClassMemberWithIsStatic(\n      classBody: N.ClassBody,\n      member: Undone<N.ClassMember | N.TsIndexSignature>,\n      state: N.ParseClassMemberState,\n      isStatic: boolean,\n    ): void {\n      const idx = this.tsTryParseIndexSignature(\n        member as Undone<N.TsIndexSignature>,\n      );\n      if (idx) {\n        classBody.body.push(idx);\n\n        if ((member as any).abstract) {\n          this.raise(TSErrors.IndexSignatureHasAbstract, { at: member });\n        }\n        if ((member as any).accessibility) {\n          this.raise(TSErrors.IndexSignatureHasAccessibility, {\n            at: member,\n            modifier: (member as any).accessibility,\n          });\n        }\n        if ((member as any).declare) {\n          this.raise(TSErrors.IndexSignatureHasDeclare, { at: member });\n        }\n        if ((member as any).override) {\n          this.raise(TSErrors.IndexSignatureHasOverride, { at: member });\n        }\n\n        return;\n      }\n\n      if (!this.state.inAbstractClass && (member as any).abstract) {\n        this.raise(TSErrors.NonAbstractClassHasAbstractMethod, {\n          at: member,\n        });\n      }\n\n      if ((member as any).override) {\n        if (!state.hadSuperClass) {\n          this.raise(TSErrors.OverrideNotInSubClass, { at: member });\n        }\n      }\n\n      /*:: invariant(member.type !== \"TSIndexSignature\") */\n\n      super.parseClassMemberWithIsStatic(\n        classBody,\n        member as Undone<N.ClassMember>,\n        state,\n        isStatic,\n      );\n    }\n\n    parsePostMemberNameModifiers(\n      methodOrProp: N.ClassMethod | N.ClassProperty | N.ClassPrivateProperty,\n    ): void {\n      const optional = this.eat(tt.question);\n      if (optional) methodOrProp.optional = true;\n\n      if ((methodOrProp as any).readonly && this.match(tt.parenL)) {\n        this.raise(TSErrors.ClassMethodHasReadonly, { at: methodOrProp });\n      }\n\n      if ((methodOrProp as any).declare && this.match(tt.parenL)) {\n        this.raise(TSErrors.ClassMethodHasDeclare, { at: methodOrProp });\n      }\n    }\n\n    // Note: The reason we do this in `parseExpressionStatement` and not `parseStatement`\n    // is that e.g. `type()` is valid JS, so we must try parsing that first.\n    // If it's really a type, we will parse `type` as the statement, and can correct it here\n    // by parsing the rest.\n    // @ts-expect-error plugin overrides interfaces\n    parseExpressionStatement(\n      node: Undone<N.ExpressionStatement>,\n      expr: N.Expression,\n      decorators: N.Decorator[] | null,\n    ): N.Statement {\n      const decl =\n        expr.type === \"Identifier\"\n          ? // @ts-expect-error refine typings\n            this.tsParseExpressionStatement(node, expr, decorators)\n          : undefined;\n      return decl || super.parseExpressionStatement(node, expr, decorators);\n    }\n\n    // export type\n    // Should be true for anything parsed by `tsTryParseExportDeclaration`.\n    shouldParseExportDeclaration(): boolean {\n      if (this.tsIsDeclarationStart()) return true;\n      return super.shouldParseExportDeclaration();\n    }\n\n    // An apparent conditional expression could actually be an optional parameter in an arrow function.\n    parseConditional(\n      expr: N.Expression,\n\n      startLoc: Position,\n      refExpressionErrors?: ExpressionErrors | null,\n    ): N.Expression {\n      // only do the expensive clone if there is a question mark\n      // and if we come from inside parens\n      if (!this.state.maybeInArrowParameters || !this.match(tt.question)) {\n        return super.parseConditional(\n          expr,\n\n          startLoc,\n          refExpressionErrors,\n        );\n      }\n\n      const result = this.tryParse(() =>\n        super.parseConditional(expr, startLoc),\n      );\n\n      if (!result.node) {\n        if (result.error) {\n          /*:: invariant(refExpressionErrors != null) */\n          super.setOptionalParametersError(refExpressionErrors, result.error);\n        }\n\n        return expr;\n      }\n      if (result.error) this.state = result.failState;\n      return result.node;\n    }\n\n    // Note: These \"type casts\" are *not* valid TS expressions.\n    // But we parse them here and change them when completing the arrow function.\n    parseParenItem(\n      node: N.Expression,\n\n      startLoc: Position,\n    ): N.Expression {\n      node = super.parseParenItem(node, startLoc);\n      if (this.eat(tt.question)) {\n        node.optional = true;\n        // Include questionmark in location of node\n        // Don't use this.finishNode() as otherwise we might process comments twice and\n        // include already consumed parens\n        this.resetEndLocation(node);\n      }\n\n      if (this.match(tt.colon)) {\n        const typeCastNode = this.startNodeAt<N.TsTypeCastExpression>(startLoc);\n        typeCastNode.expression = node;\n        typeCastNode.typeAnnotation = this.tsParseTypeAnnotation();\n\n        return this.finishNode(typeCastNode, \"TSTypeCastExpression\");\n      }\n\n      return node;\n    }\n\n    parseExportDeclaration(\n      node: N.ExportNamedDeclaration,\n    ): N.Declaration | undefined | null {\n      if (!this.state.isAmbientContext && this.isContextual(tt._declare)) {\n        return this.tsInAmbientContext(() => this.parseExportDeclaration(node));\n      }\n\n      // Store original location\n      const startLoc = this.state.startLoc;\n\n      const isDeclare = this.eatContextual(tt._declare);\n\n      if (\n        isDeclare &&\n        (this.isContextual(tt._declare) || !this.shouldParseExportDeclaration())\n      ) {\n        throw this.raise(TSErrors.ExpectedAmbientAfterExportDeclare, {\n          at: this.state.startLoc,\n        });\n      }\n\n      const isIdentifier = tokenIsIdentifier(this.state.type);\n      const declaration: N.Declaration | undefined | null =\n        (isIdentifier && this.tsTryParseExportDeclaration()) ||\n        super.parseExportDeclaration(node);\n\n      if (!declaration) return null;\n\n      if (\n        declaration.type === \"TSInterfaceDeclaration\" ||\n        declaration.type === \"TSTypeAliasDeclaration\" ||\n        isDeclare\n      ) {\n        node.exportKind = \"type\";\n      }\n\n      if (isDeclare) {\n        // Reset location to include `declare` in range\n        this.resetStartLocation(declaration, startLoc);\n\n        declaration.declare = true;\n      }\n\n      return declaration;\n    }\n\n    parseClassId(\n      node: N.Class,\n      isStatement: boolean,\n      optionalId?: boolean | null,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      bindingType?: BindingTypes,\n    ): void {\n      if ((!isStatement || optionalId) && this.isContextual(tt._implements)) {\n        return;\n      }\n\n      super.parseClassId(\n        node,\n        isStatement,\n        optionalId,\n        (node as any).declare ? BIND_TS_AMBIENT : BIND_CLASS,\n      );\n      const typeParameters = this.tsTryParseTypeParameters(\n        this.tsParseInOutConstModifiers,\n      );\n      if (typeParameters) node.typeParameters = typeParameters;\n    }\n\n    parseClassPropertyAnnotation(\n      node: N.ClassProperty | N.ClassPrivateProperty | N.ClassAccessorProperty,\n    ): void {\n      if (!node.optional) {\n        if (this.eat(tt.bang)) {\n          node.definite = true;\n        } else if (this.eat(tt.question)) {\n          node.optional = true;\n        }\n      }\n\n      const type = this.tsTryParseTypeAnnotation();\n      if (type) node.typeAnnotation = type;\n    }\n\n    parseClassProperty(node: N.ClassProperty): N.ClassProperty {\n      this.parseClassPropertyAnnotation(node);\n\n      if (\n        this.state.isAmbientContext &&\n        !(node.readonly && !node.typeAnnotation) &&\n        this.match(tt.eq)\n      ) {\n        this.raise(TSErrors.DeclareClassFieldHasInitializer, {\n          at: this.state.startLoc,\n        });\n      }\n      if (node.abstract && this.match(tt.eq)) {\n        const { key } = node;\n        this.raise(TSErrors.AbstractPropertyHasInitializer, {\n          at: this.state.startLoc,\n          propertyName:\n            key.type === \"Identifier\" && !node.computed\n              ? key.name\n              : `[${this.input.slice(key.start, key.end)}]`,\n        });\n      }\n\n      return super.parseClassProperty(node);\n    }\n\n    parseClassPrivateProperty(\n      node: N.ClassPrivateProperty,\n    ): N.ClassPrivateProperty {\n      // @ts-expect-error abstract may not index node\n      if (node.abstract) {\n        this.raise(TSErrors.PrivateElementHasAbstract, { at: node });\n      }\n\n      // @ts-expect-error accessibility may not index node\n      if (node.accessibility) {\n        this.raise(TSErrors.PrivateElementHasAccessibility, {\n          at: node,\n          // @ts-expect-error refine typings\n          modifier: node.accessibility,\n        });\n      }\n\n      this.parseClassPropertyAnnotation(node);\n      return super.parseClassPrivateProperty(node);\n    }\n\n    parseClassAccessorProperty(\n      node: N.ClassAccessorProperty,\n    ): N.ClassAccessorProperty {\n      this.parseClassPropertyAnnotation(node);\n      if (node.optional) {\n        this.raise(TSErrors.AccessorCannotBeOptional, { at: node });\n      }\n      return super.parseClassAccessorProperty(node);\n    }\n\n    pushClassMethod(\n      classBody: N.ClassBody,\n      method: N.ClassMethod,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isConstructor: boolean,\n      allowsDirectSuper: boolean,\n    ): void {\n      const typeParameters = this.tsTryParseTypeParameters(\n        this.tsParseConstModifier,\n      );\n      if (typeParameters && isConstructor) {\n        this.raise(TSErrors.ConstructorHasTypeParameters, {\n          at: typeParameters,\n        });\n      }\n\n      // @ts-expect-error declare does not exist in ClassMethod\n      const { declare = false, kind } = method;\n\n      if (declare && (kind === \"get\" || kind === \"set\")) {\n        this.raise(TSErrors.DeclareAccessor, { at: method, kind });\n      }\n      if (typeParameters) method.typeParameters = typeParameters;\n      super.pushClassMethod(\n        classBody,\n        method,\n        isGenerator,\n        isAsync,\n        isConstructor,\n        allowsDirectSuper,\n      );\n    }\n\n    pushClassPrivateMethod(\n      classBody: N.ClassBody,\n      method: N.ClassPrivateMethod,\n      isGenerator: boolean,\n      isAsync: boolean,\n    ): void {\n      const typeParameters = this.tsTryParseTypeParameters(\n        this.tsParseConstModifier,\n      );\n      if (typeParameters) method.typeParameters = typeParameters;\n      super.pushClassPrivateMethod(classBody, method, isGenerator, isAsync);\n    }\n\n    declareClassPrivateMethodInScope(\n      node: N.ClassPrivateMethod | N.EstreeMethodDefinition | N.TSDeclareMethod,\n      kind: number,\n    ) {\n      if (node.type === \"TSDeclareMethod\") return;\n      // This happens when using the \"estree\" plugin.\n      if (node.type === \"MethodDefinition\" && !node.value.body) return;\n\n      super.declareClassPrivateMethodInScope(node, kind);\n    }\n\n    parseClassSuper(node: N.Class): void {\n      super.parseClassSuper(node);\n      // handle `extends f<<T>\n      if (node.superClass && (this.match(tt.lt) || this.match(tt.bitShiftL))) {\n        // @ts-expect-error refine typings\n        node.superTypeParameters = this.tsParseTypeArgumentsInExpression();\n      }\n      if (this.eatContextual(tt._implements)) {\n        node.implements = this.tsParseHeritageClause(\"implements\");\n      }\n    }\n\n    parseObjPropValue(\n      prop: Undone<N.ObjectMethod | N.ObjectProperty>,\n      startLoc: Position | undefined | null,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isPattern: boolean,\n      isAccessor: boolean,\n      refExpressionErrors?: ExpressionErrors | null,\n    ) {\n      const typeParameters = this.tsTryParseTypeParameters(\n        this.tsParseConstModifier,\n      );\n      if (typeParameters) prop.typeParameters = typeParameters;\n\n      return super.parseObjPropValue(\n        prop,\n\n        startLoc,\n        isGenerator,\n        isAsync,\n        isPattern,\n        isAccessor,\n        refExpressionErrors,\n      );\n    }\n\n    parseFunctionParams(node: N.Function, isConstructor: boolean): void {\n      const typeParameters = this.tsTryParseTypeParameters(\n        this.tsParseConstModifier,\n      );\n      if (typeParameters) node.typeParameters = typeParameters;\n      super.parseFunctionParams(node, isConstructor);\n    }\n\n    // `let x: number;`\n    parseVarId(\n      decl: N.VariableDeclarator,\n      kind: \"var\" | \"let\" | \"const\" | \"using\",\n    ): void {\n      super.parseVarId(decl, kind);\n      if (\n        decl.id.type === \"Identifier\" &&\n        !this.hasPrecedingLineBreak() &&\n        this.eat(tt.bang)\n      ) {\n        decl.definite = true;\n      }\n\n      const type = this.tsTryParseTypeAnnotation();\n      if (type) {\n        decl.id.typeAnnotation = type;\n        this.resetEndLocation(decl.id); // set end position to end of type\n      }\n    }\n\n    // parse the return type of an async arrow function - let foo = (async (): number => {});\n    parseAsyncArrowFromCallExpression(\n      node: N.ArrowFunctionExpression,\n      call: N.CallExpression,\n    ): N.ArrowFunctionExpression {\n      if (this.match(tt.colon)) {\n        node.returnType = this.tsParseTypeAnnotation();\n      }\n      return super.parseAsyncArrowFromCallExpression(node, call);\n    }\n\n    parseMaybeAssign(\n      refExpressionErrors?: ExpressionErrors | null,\n      afterLeftParse?: Function,\n    ): N.Expression {\n      // Note: When the JSX plugin is on, type assertions (`<T> x`) aren't valid syntax.\n\n      let state: State | undefined | null;\n      let jsx;\n      let typeCast;\n\n      if (\n        this.hasPlugin(\"jsx\") &&\n        (this.match(tt.jsxTagStart) || this.match(tt.lt))\n      ) {\n        // Prefer to parse JSX if possible. But may be an arrow fn.\n        state = this.state.clone();\n\n        jsx = this.tryParse(\n          () => super.parseMaybeAssign(refExpressionErrors, afterLeftParse),\n          state,\n        );\n\n        /*:: invariant(!jsx.aborted) */\n        /*:: invariant(jsx.node != null) */\n        if (!jsx.error) return jsx.node;\n\n        // Remove `tc.j_expr` or `tc.j_oTag` from context added\n        // by parsing `jsxTagStart` to stop the JSX plugin from\n        // messing with the tokens\n        const { context } = this.state;\n        const currentContext = context[context.length - 1];\n        if (currentContext === tc.j_oTag || currentContext === tc.j_expr) {\n          context.pop();\n        }\n      }\n\n      if (!jsx?.error && !this.match(tt.lt)) {\n        return super.parseMaybeAssign(refExpressionErrors, afterLeftParse);\n      }\n\n      // Either way, we're looking at a '<': tt.jsxTagStart or relational.\n\n      // If the state was cloned in the JSX parsing branch above but there\n      // have been any error in the tryParse call, this.state is set to state\n      // so we still need to clone it.\n      if (!state || state === this.state) state = this.state.clone();\n\n      let typeParameters: N.TsTypeParameterDeclaration | undefined | null;\n      const arrow = this.tryParse(abort => {\n        // This is similar to TypeScript's `tryParseParenthesizedArrowFunctionExpression`.\n        typeParameters = this.tsParseTypeParameters(this.tsParseConstModifier);\n        const expr = super.parseMaybeAssign(\n          refExpressionErrors,\n          afterLeftParse,\n        );\n\n        if (\n          expr.type !== \"ArrowFunctionExpression\" ||\n          expr.extra?.parenthesized\n        ) {\n          abort();\n        }\n\n        // Correct TypeScript code should have at least 1 type parameter, but don't crash on bad code.\n        if (typeParameters?.params.length !== 0) {\n          this.resetStartLocationFromNode(expr, typeParameters);\n        }\n        expr.typeParameters = typeParameters;\n\n        if (process.env.BABEL_8_BREAKING) {\n          if (\n            this.hasPlugin(\"jsx\") &&\n            expr.typeParameters.params.length === 1 &&\n            !expr.typeParameters.extra?.trailingComma\n          ) {\n            // report error if single type parameter used without trailing comma.\n            const parameter = expr.typeParameters.params[0];\n            if (!parameter.constraint) {\n              // A single type parameter must either have constraints\n              // or a trailing comma, otherwise it's ambiguous with JSX.\n              this.raise(TSErrors.SingleTypeParameterWithoutTrailingComma, {\n                at: createPositionWithColumnOffset(parameter.loc.end, 1),\n                typeParameterName: parameter.name.name,\n              });\n            }\n          }\n        }\n\n        return expr;\n      }, state);\n\n      /*:: invariant(arrow.node != null) */\n      if (!arrow.error && !arrow.aborted) {\n        // This error is reported outside of the this.tryParse call so that\n        // in case of <T>(x) => 2, we don't consider <T>(x) as a type assertion\n        // because of this error.\n        if (typeParameters) this.reportReservedArrowTypeParam(typeParameters);\n        // @ts-expect-error refine typings\n        return arrow.node;\n      }\n\n      if (!jsx) {\n        // Try parsing a type cast instead of an arrow function.\n        // This will never happen outside of JSX.\n        // (Because in JSX the '<' should be a jsxTagStart and not a relational.\n        assert(!this.hasPlugin(\"jsx\"));\n\n        // This will start with a type assertion (via parseMaybeUnary).\n        // But don't directly call `this.tsParseTypeAssertion` because we want to handle any binary after it.\n        typeCast = this.tryParse(\n          () => super.parseMaybeAssign(refExpressionErrors, afterLeftParse),\n          state,\n        );\n        /*:: invariant(!typeCast.aborted) */\n        /*:: invariant(typeCast.node != null) */\n        if (!typeCast.error) return typeCast.node;\n      }\n\n      if (jsx?.node) {\n        /*:: invariant(jsx.failState) */\n        this.state = jsx.failState;\n        return jsx.node;\n      }\n\n      if (arrow.node) {\n        /*:: invariant(arrow.failState) */\n        this.state = arrow.failState;\n        if (typeParameters) this.reportReservedArrowTypeParam(typeParameters);\n        // @ts-expect-error refine typings\n        return arrow.node;\n      }\n\n      if (typeCast?.node) {\n        /*:: invariant(typeCast.failState) */\n        this.state = typeCast.failState;\n        return typeCast.node;\n      }\n\n      throw jsx?.error || arrow.error || typeCast?.error;\n    }\n\n    reportReservedArrowTypeParam(node: any) {\n      if (\n        node.params.length === 1 &&\n        !node.params[0].constraint &&\n        !node.extra?.trailingComma &&\n        this.getPluginOption(\"typescript\", \"disallowAmbiguousJSXLike\")\n      ) {\n        this.raise(TSErrors.ReservedArrowTypeParam, { at: node });\n      }\n    }\n\n    // Handle type assertions\n    parseMaybeUnary(\n      refExpressionErrors?: ExpressionErrors | null,\n      sawUnary?: boolean,\n    ): N.Expression {\n      if (!this.hasPlugin(\"jsx\") && this.match(tt.lt)) {\n        return this.tsParseTypeAssertion();\n      }\n      return super.parseMaybeUnary(refExpressionErrors, sawUnary);\n    }\n\n    parseArrow(\n      node: Undone<N.ArrowFunctionExpression>,\n    ): Undone<N.ArrowFunctionExpression> | undefined | null {\n      if (this.match(tt.colon)) {\n        // This is different from how the TS parser does it.\n        // TS uses lookahead. The Babel Parser parses it as a parenthesized expression and converts.\n\n        const result = this.tryParse(abort => {\n          const returnType = this.tsParseTypeOrTypePredicateAnnotation(\n            tt.colon,\n          );\n          if (this.canInsertSemicolon() || !this.match(tt.arrow)) abort();\n          return returnType;\n        });\n\n        if (result.aborted) return;\n\n        if (!result.thrown) {\n          if (result.error) this.state = result.failState;\n          // @ts-expect-error refine typings\n          node.returnType = result.node;\n        }\n      }\n\n      return super.parseArrow(node);\n    }\n\n    // Allow type annotations inside of a parameter list.\n    parseAssignableListItemTypes(\n      param: N.Pattern,\n      flags: ParseBindingListFlags,\n    ) {\n      if (!(flags & ParseBindingListFlags.IS_FUNCTION_PARAMS)) return param;\n\n      if (this.eat(tt.question)) {\n        (param as any as N.Identifier).optional = true;\n      }\n      const type = this.tsTryParseTypeAnnotation();\n      if (type) param.typeAnnotation = type;\n      this.resetEndLocation(param);\n\n      return param;\n    }\n\n    isAssignable(node: N.Node, isBinding?: boolean): boolean {\n      switch (node.type) {\n        case \"TSTypeCastExpression\":\n          return this.isAssignable(node.expression, isBinding);\n        case \"TSParameterProperty\":\n          return true;\n        default:\n          return super.isAssignable(node, isBinding);\n      }\n    }\n\n    toAssignable(node: N.Node, isLHS: boolean = false): void {\n      switch (node.type) {\n        case \"ParenthesizedExpression\":\n          this.toAssignableParenthesizedExpression(node, isLHS);\n          break;\n        case \"TSAsExpression\":\n        case \"TSSatisfiesExpression\":\n        case \"TSNonNullExpression\":\n        case \"TSTypeAssertion\":\n          if (isLHS) {\n            this.expressionScope.recordArrowParameterBindingError(\n              TSErrors.UnexpectedTypeCastInParameter,\n              { at: node },\n            );\n          } else {\n            this.raise(TSErrors.UnexpectedTypeCastInParameter, { at: node });\n          }\n          this.toAssignable(node.expression, isLHS);\n          break;\n        case \"AssignmentExpression\":\n          if (!isLHS && node.left.type === \"TSTypeCastExpression\") {\n            node.left = this.typeCastToParameter(node.left);\n          }\n        /* fall through */\n        default:\n          super.toAssignable(node, isLHS);\n      }\n    }\n\n    toAssignableParenthesizedExpression(node: N.Node, isLHS: boolean): void {\n      switch (node.expression.type) {\n        case \"TSAsExpression\":\n        case \"TSSatisfiesExpression\":\n        case \"TSNonNullExpression\":\n        case \"TSTypeAssertion\":\n        case \"ParenthesizedExpression\":\n          this.toAssignable(node.expression, isLHS);\n          break;\n        default:\n          super.toAssignable(node, isLHS);\n      }\n    }\n\n    checkToRestConversion(node: N.Node, allowPattern: boolean): void {\n      switch (node.type) {\n        case \"TSAsExpression\":\n        case \"TSSatisfiesExpression\":\n        case \"TSTypeAssertion\":\n        case \"TSNonNullExpression\":\n          this.checkToRestConversion(node.expression, false);\n          break;\n        default:\n          super.checkToRestConversion(node, allowPattern);\n      }\n    }\n\n    // @ts-expect-error plugin overrides interfaces\n    isValidLVal(\n      type:\n        | \"TSTypeCastExpression\"\n        | \"TSParameterProperty\"\n        | \"TSNonNullExpression\"\n        | \"TSAsExpression\"\n        | \"TSSatisfiesExpression\"\n        | \"TSTypeAssertion\",\n      isUnparenthesizedInAssign: boolean,\n      binding: BindingTypes,\n    ) {\n      return (\n        getOwn(\n          {\n            // Allow \"typecasts\" to appear on the left of assignment expressions,\n            // because it may be in an arrow function.\n            // e.g. `const f = (foo: number = 0) => foo;`\n            TSTypeCastExpression: true,\n            TSParameterProperty: \"parameter\",\n            TSNonNullExpression: \"expression\",\n            TSAsExpression: (binding !== BIND_NONE ||\n              !isUnparenthesizedInAssign) && [\"expression\", true],\n            TSSatisfiesExpression: (binding !== BIND_NONE ||\n              !isUnparenthesizedInAssign) && [\"expression\", true],\n            TSTypeAssertion: (binding !== BIND_NONE ||\n              !isUnparenthesizedInAssign) && [\"expression\", true],\n          },\n          type,\n        ) || super.isValidLVal(type, isUnparenthesizedInAssign, binding)\n      );\n    }\n\n    parseBindingAtom(): N.Pattern {\n      if (this.state.type === tt._this) {\n        return this.parseIdentifier(/* liberal */ true);\n      }\n      return super.parseBindingAtom();\n    }\n\n    parseMaybeDecoratorArguments(expr: N.Expression): N.Expression {\n      // handles `@f<<T>`\n      if (this.match(tt.lt) || this.match(tt.bitShiftL)) {\n        const typeArguments = this.tsParseTypeArgumentsInExpression();\n\n        if (this.match(tt.parenL)) {\n          const call = super.parseMaybeDecoratorArguments(expr);\n          call.typeParameters = typeArguments;\n          return call;\n        }\n\n        this.unexpected(null, tt.parenL);\n      }\n\n      return super.parseMaybeDecoratorArguments(expr);\n    }\n\n    checkCommaAfterRest(\n      close: (typeof charCodes)[keyof typeof charCodes],\n    ): boolean {\n      if (\n        this.state.isAmbientContext &&\n        this.match(tt.comma) &&\n        this.lookaheadCharCode() === close\n      ) {\n        this.next();\n        return false;\n      }\n      return super.checkCommaAfterRest(close);\n    }\n\n    // === === === === === === === === === === === === === === === ===\n    // Note: All below methods are duplicates of something in flow.js.\n    // Not sure what the best way to combine these is.\n    // === === === === === === === === === === === === === === === ===\n\n    isClassMethod(): boolean {\n      return this.match(tt.lt) || super.isClassMethod();\n    }\n\n    isClassProperty(): boolean {\n      return (\n        this.match(tt.bang) || this.match(tt.colon) || super.isClassProperty()\n      );\n    }\n\n    parseMaybeDefault(\n      startLoc?: Position | null,\n      left?: Pattern | null,\n    ): N.Pattern {\n      const node = super.parseMaybeDefault(startLoc, left);\n\n      if (\n        node.type === \"AssignmentPattern\" &&\n        node.typeAnnotation &&\n        node.right.start < node.typeAnnotation.start\n      ) {\n        this.raise(TSErrors.TypeAnnotationAfterAssign, {\n          at: node.typeAnnotation,\n        });\n      }\n\n      return node;\n    }\n\n    // ensure that inside types, we bypass the jsx parser plugin\n    getTokenFromCode(code: number): void {\n      if (this.state.inType) {\n        if (code === charCodes.greaterThan) {\n          this.finishOp(tt.gt, 1);\n          return;\n        }\n        if (code === charCodes.lessThan) {\n          this.finishOp(tt.lt, 1);\n          return;\n        }\n      }\n      super.getTokenFromCode(code);\n    }\n\n    // used after we have finished parsing types\n    reScan_lt_gt() {\n      const { type } = this.state;\n      if (type === tt.lt) {\n        this.state.pos -= 1;\n        this.readToken_lt();\n      } else if (type === tt.gt) {\n        this.state.pos -= 1;\n        this.readToken_gt();\n      }\n    }\n\n    reScan_lt() {\n      const { type } = this.state;\n      if (type === tt.bitShiftL) {\n        this.state.pos -= 2;\n        this.finishOp(tt.lt, 1);\n        return tt.lt;\n      }\n      return type;\n    }\n\n    toAssignableList(\n      exprList: Expression[],\n      trailingCommaLoc: Position | undefined | null,\n      isLHS: boolean,\n    ): void {\n      for (let i = 0; i < exprList.length; i++) {\n        const expr = exprList[i];\n        if (expr?.type === \"TSTypeCastExpression\") {\n          exprList[i] = this.typeCastToParameter(\n            expr as N.TsTypeCastExpression,\n          );\n        }\n      }\n      super.toAssignableList(exprList, trailingCommaLoc, isLHS);\n    }\n\n    typeCastToParameter(node: N.TsTypeCastExpression): N.Node {\n      node.expression.typeAnnotation = node.typeAnnotation;\n\n      this.resetEndLocation(node.expression, node.typeAnnotation.loc.end);\n\n      return node.expression;\n    }\n\n    shouldParseArrow(params: Array<N.Node>) {\n      if (this.match(tt.colon)) {\n        return params.every(expr => this.isAssignable(expr, true));\n      }\n      return super.shouldParseArrow(params);\n    }\n\n    shouldParseAsyncArrow(): boolean {\n      return this.match(tt.colon) || super.shouldParseAsyncArrow();\n    }\n\n    canHaveLeadingDecorator() {\n      // Avoid unnecessary lookahead in checking for abstract class unless needed!\n      return super.canHaveLeadingDecorator() || this.isAbstractClass();\n    }\n\n    jsxParseOpeningElementAfterName(\n      node: N.JSXOpeningElement,\n    ): N.JSXOpeningElement {\n      // handles `<Component<<T>`\n      if (this.match(tt.lt) || this.match(tt.bitShiftL)) {\n        const typeArguments = this.tsTryParseAndCatch(() =>\n          // @ts-expect-error: refine typings\n          this.tsParseTypeArgumentsInExpression(),\n        );\n        // @ts-expect-error: refine typings\n        if (typeArguments) node.typeParameters = typeArguments;\n      }\n      return super.jsxParseOpeningElementAfterName(node);\n    }\n\n    getGetterSetterExpectedParamCount(\n      method: N.ObjectMethod | N.ClassMethod,\n    ): number {\n      const baseCount = super.getGetterSetterExpectedParamCount(method);\n      const params = this.getObjectOrClassMethodParams(method);\n      const firstParam = params[0];\n      const hasContextParam = firstParam && this.isThisParam(firstParam);\n\n      return hasContextParam ? baseCount + 1 : baseCount;\n    }\n\n    parseCatchClauseParam(): N.Pattern {\n      const param = super.parseCatchClauseParam();\n      const type = this.tsTryParseTypeAnnotation();\n\n      if (type) {\n        param.typeAnnotation = type;\n        this.resetEndLocation(param);\n      }\n\n      return param;\n    }\n\n    tsInAmbientContext<T>(cb: () => T): T {\n      const oldIsAmbientContext = this.state.isAmbientContext;\n      this.state.isAmbientContext = true;\n      try {\n        return cb();\n      } finally {\n        this.state.isAmbientContext = oldIsAmbientContext;\n      }\n    }\n\n    parseClass<T extends N.Class>(\n      node: Undone<T>,\n      isStatement: boolean,\n      optionalId?: boolean,\n    ): T {\n      const oldInAbstractClass = this.state.inAbstractClass;\n      this.state.inAbstractClass = !!(node as any).abstract;\n      try {\n        return super.parseClass(node, isStatement, optionalId);\n      } finally {\n        this.state.inAbstractClass = oldInAbstractClass;\n      }\n    }\n\n    tsParseAbstractDeclaration(\n      node: any,\n      decorators: N.Decorator[] | null,\n    ): N.ClassDeclaration | N.TsInterfaceDeclaration | undefined | null {\n      if (this.match(tt._class)) {\n        node.abstract = true;\n        return this.maybeTakeDecorators(\n          decorators,\n          this.parseClass<N.ClassDeclaration>(\n            node as N.ClassDeclaration,\n            /* isStatement */ true,\n            /* optionalId */ false,\n          ),\n        );\n      } else if (this.isContextual(tt._interface)) {\n        // for invalid abstract interface\n\n        // To avoid\n        //   abstract interface\n        //   Foo {}\n        if (!this.hasFollowingLineBreak()) {\n          node.abstract = true;\n          this.raise(TSErrors.NonClassMethodPropertyHasAbstractModifer, {\n            at: node,\n          });\n          return this.tsParseInterfaceDeclaration(\n            node as N.TsInterfaceDeclaration,\n          );\n        }\n      } else {\n        this.unexpected(null, tt._class);\n      }\n    }\n\n    parseMethod<\n      T extends N.ObjectMethod | N.ClassMethod | N.ClassPrivateMethod,\n    >(\n      node: Undone<T>,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isConstructor: boolean,\n      allowDirectSuper: boolean,\n      type: T[\"type\"],\n      inClassScope?: boolean,\n    ) {\n      const method = super.parseMethod<T>(\n        node,\n        isGenerator,\n        isAsync,\n        isConstructor,\n        allowDirectSuper,\n        type,\n        inClassScope,\n      );\n      // @ts-expect-error todo(flow->ts) property not defined for all types in union\n      if (method.abstract) {\n        const hasBody = this.hasPlugin(\"estree\")\n          ? // @ts-expect-error estree typings\n            !!method.value.body\n          : !!method.body;\n        if (hasBody) {\n          const { key } = method;\n          this.raise(TSErrors.AbstractMethodHasImplementation, {\n            at: method,\n            methodName:\n              key.type === \"Identifier\" && !method.computed\n                ? key.name\n                : `[${this.input.slice(key.start, key.end)}]`,\n          });\n        }\n      }\n      return method;\n    }\n\n    tsParseTypeParameterName(): N.Identifier | string {\n      const typeName: N.Identifier = this.parseIdentifier();\n      return process.env.BABEL_8_BREAKING ? typeName : typeName.name;\n    }\n\n    shouldParseAsAmbientContext(): boolean {\n      return !!this.getPluginOption(\"typescript\", \"dts\");\n    }\n\n    parse() {\n      if (this.shouldParseAsAmbientContext()) {\n        this.state.isAmbientContext = true;\n      }\n      return super.parse();\n    }\n\n    getExpression() {\n      if (this.shouldParseAsAmbientContext()) {\n        this.state.isAmbientContext = true;\n      }\n      return super.getExpression();\n    }\n\n    parseExportSpecifier(\n      node: Undone<N.ExportSpecifier>,\n      isString: boolean,\n      isInTypeExport: boolean,\n      isMaybeTypeOnly: boolean,\n    ) {\n      if (!isString && isMaybeTypeOnly) {\n        this.parseTypeOnlyImportExportSpecifier(\n          node,\n          /* isImport */ false,\n          isInTypeExport,\n        );\n        return this.finishNode<N.ExportSpecifier>(node, \"ExportSpecifier\");\n      }\n      node.exportKind = \"value\";\n      return super.parseExportSpecifier(\n        node,\n        isString,\n        isInTypeExport,\n        isMaybeTypeOnly,\n      );\n    }\n\n    parseImportSpecifier(\n      specifier: Undone<N.ImportSpecifier>,\n      importedIsString: boolean,\n      isInTypeOnlyImport: boolean,\n      isMaybeTypeOnly: boolean,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      bindingType: BindingTypes | undefined,\n    ): N.ImportSpecifier {\n      if (!importedIsString && isMaybeTypeOnly) {\n        this.parseTypeOnlyImportExportSpecifier(\n          specifier,\n          /* isImport */ true,\n          isInTypeOnlyImport,\n        );\n        return this.finishNode<N.ImportSpecifier>(specifier, \"ImportSpecifier\");\n      }\n      specifier.importKind = \"value\";\n      return super.parseImportSpecifier(\n        specifier,\n        importedIsString,\n        isInTypeOnlyImport,\n        isMaybeTypeOnly,\n        isInTypeOnlyImport ? BIND_TS_TYPE_IMPORT : BIND_FLAGS_TS_IMPORT,\n      );\n    }\n\n    parseTypeOnlyImportExportSpecifier(\n      node: any,\n      isImport: boolean,\n      isInTypeOnlyImportExport: boolean,\n    ): void {\n      const leftOfAsKey = isImport ? \"imported\" : \"local\";\n      const rightOfAsKey = isImport ? \"local\" : \"exported\";\n\n      let leftOfAs = node[leftOfAsKey];\n      let rightOfAs;\n\n      let hasTypeSpecifier = false;\n      let canParseAsKeyword = true;\n\n      const loc = leftOfAs.loc.start;\n\n      // https://github.com/microsoft/TypeScript/blob/fc4f9d83d5939047aa6bb2a43965c6e9bbfbc35b/src/compiler/parser.ts#L7411-L7456\n      // import { type } from \"mod\";          - hasTypeSpecifier: false, leftOfAs: type\n      // import { type as } from \"mod\";       - hasTypeSpecifier: true,  leftOfAs: as\n      // import { type as as } from \"mod\";    - hasTypeSpecifier: false, leftOfAs: type, rightOfAs: as\n      // import { type as as as } from \"mod\"; - hasTypeSpecifier: true,  leftOfAs: as,   rightOfAs: as\n      if (this.isContextual(tt._as)) {\n        // { type as ...? }\n        const firstAs = this.parseIdentifier();\n        if (this.isContextual(tt._as)) {\n          // { type as as ...? }\n          const secondAs = this.parseIdentifier();\n          if (tokenIsKeywordOrIdentifier(this.state.type)) {\n            // { type as as something }\n            hasTypeSpecifier = true;\n            leftOfAs = firstAs;\n            rightOfAs = isImport\n              ? this.parseIdentifier()\n              : this.parseModuleExportName();\n            canParseAsKeyword = false;\n          } else {\n            // { type as as }\n            rightOfAs = secondAs;\n            canParseAsKeyword = false;\n          }\n        } else if (tokenIsKeywordOrIdentifier(this.state.type)) {\n          // { type as something }\n          canParseAsKeyword = false;\n          rightOfAs = isImport\n            ? this.parseIdentifier()\n            : this.parseModuleExportName();\n        } else {\n          // { type as }\n          hasTypeSpecifier = true;\n          leftOfAs = firstAs;\n        }\n      } else if (tokenIsKeywordOrIdentifier(this.state.type)) {\n        // { type something ...? }\n        hasTypeSpecifier = true;\n        if (isImport) {\n          leftOfAs = this.parseIdentifier(true);\n          if (!this.isContextual(tt._as)) {\n            this.checkReservedWord(\n              leftOfAs.name,\n              leftOfAs.loc.start,\n              true,\n              true,\n            );\n          }\n        } else {\n          leftOfAs = this.parseModuleExportName();\n        }\n      }\n      if (hasTypeSpecifier && isInTypeOnlyImportExport) {\n        this.raise(\n          isImport\n            ? TSErrors.TypeModifierIsUsedInTypeImports\n            : TSErrors.TypeModifierIsUsedInTypeExports,\n          { at: loc },\n        );\n      }\n\n      node[leftOfAsKey] = leftOfAs;\n      node[rightOfAsKey] = rightOfAs;\n\n      const kindKey = isImport ? \"importKind\" : \"exportKind\";\n      node[kindKey] = hasTypeSpecifier ? \"type\" : \"value\";\n\n      if (canParseAsKeyword && this.eatContextual(tt._as)) {\n        node[rightOfAsKey] = isImport\n          ? this.parseIdentifier()\n          : this.parseModuleExportName();\n      }\n      if (!node[rightOfAsKey]) {\n        node[rightOfAsKey] = cloneIdentifier(node[leftOfAsKey]);\n      }\n      if (isImport) {\n        this.checkIdentifier(\n          node[rightOfAsKey],\n          hasTypeSpecifier ? BIND_TS_TYPE_IMPORT : BIND_FLAGS_TS_IMPORT,\n        );\n      }\n    }\n  };\n\nfunction isPossiblyLiteralEnum(expression: N.Expression): boolean {\n  if (expression.type !== \"MemberExpression\") return false;\n\n  const { computed, property } = expression;\n\n  if (\n    computed &&\n    property.type !== \"StringLiteral\" &&\n    (property.type !== \"TemplateLiteral\" || property.expressions.length > 0)\n  ) {\n    return false;\n  }\n\n  return isUncomputedMemberExpressionChain(expression.object);\n}\n\n// If a const declaration has no type annotation and is initialized to\n// a string literal, numeric literal, or enum reference, then it is\n// allowed. In an ideal world, we'd check whether init was *actually* an\n// enum reference, but we allow anything that \"could be\" a literal enum\n// in `isPossiblyLiteralEnum` since we don't have all the information\n// that the typescript compiler has.\nfunction isValidAmbientConstInitializer(\n  expression: N.Expression,\n  estree: boolean,\n): boolean {\n  const { type } = expression;\n  if (expression.extra?.parenthesized) {\n    return false;\n  }\n  if (estree) {\n    if (type === \"Literal\") {\n      const { value } = expression;\n      if (typeof value === \"string\" || typeof value === \"boolean\") {\n        return true;\n      }\n    }\n  } else {\n    if (type === \"StringLiteral\" || type === \"BooleanLiteral\") {\n      return true;\n    }\n  }\n  if (isNumber(expression, estree) || isNegativeNumber(expression, estree)) {\n    return true;\n  }\n  if (type === \"TemplateLiteral\" && expression.expressions.length === 0) {\n    return true;\n  }\n  if (isPossiblyLiteralEnum(expression)) {\n    return true;\n  }\n  return false;\n}\n\nfunction isNumber(expression: N.Expression, estree: boolean): boolean {\n  if (estree) {\n    return (\n      expression.type === \"Literal\" &&\n      (typeof expression.value === \"number\" || \"bigint\" in expression)\n    );\n  }\n  return (\n    expression.type === \"NumericLiteral\" || expression.type === \"BigIntLiteral\"\n  );\n}\n\nfunction isNegativeNumber(expression: N.Expression, estree: boolean): boolean {\n  if (expression.type === \"UnaryExpression\") {\n    const { operator, argument } = expression as N.UnaryExpression;\n    if (operator === \"-\" && isNumber(argument, estree)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isUncomputedMemberExpressionChain(expression: N.Expression): boolean {\n  if (expression.type === \"Identifier\") return true;\n  if (expression.type !== \"MemberExpression\" || expression.computed) {\n    return false;\n  }\n\n  return isUncomputedMemberExpressionChain(expression.object);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAWA,IAAAC,QAAA,GAAAD,OAAA;AAGA,IAAAE,SAAA,GAAAF,OAAA;AAEA,IAAAG,WAAA,GAAAH,OAAA;AAeA,IAAAI,MAAA,GAAAJ,OAAA;AAIA,IAAAK,oBAAA,GAAAL,OAAA;AACA,IAAAM,WAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AAIA,IAAAQ,KAAA,GAAAR,OAAA;AAEA,MAAMS,MAAM,GAAGA,CAAeC,MAAS,EAAEC,GAAY,KACnDC,MAAM,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,IAAID,MAAM,CAACC,GAAG,CAAC;AAYxD,SAASI,OAAOA,CAAIC,CAAY,EAAK;EACnC,IAAIA,CAAC,IAAI,IAAI,EAAE;IACb,MAAM,IAAIC,KAAK,CAAE,cAAaD,CAAE,SAAQ,CAAC;EAC3C;EACA,OAAOA,CAAC;AACV;AAEA,SAASE,MAAMA,CAACF,CAAU,EAAQ;EAChC,IAAI,CAACA,CAAC,EAAE;IACN,MAAM,IAAIC,KAAK,CAAC,aAAa,CAAC;EAChC;AACF;AAgBA,MAAME,QAAQ,GAAG,IAAAC,0BAAc,CAAC,YAAW,CAAC;EAC1CC,+BAA+B,EAAEA,CAAC;IAAEC;EAAmC,CAAC,KACrE,WAAUA,UAAW,gEAA+D;EACvFC,8BAA8B,EAAEA,CAAC;IAC/BC;EAGF,CAAC,KACE,aAAYA,YAAa,6DAA4D;EACxFC,iCAAiC,EAC/B,6DAA6D;EAC/DC,+BAA+B,EAAE,0CAA0C;EAC3EC,wBAAwB,EACtB,qDAAqD;EACvDC,qBAAqB,EAAE,mDAAmD;EAC1EC,sBAAsB,EAAE,oDAAoD;EAC5EC,mEAAmE,EACjE,4GAA4G;EAC9GC,4BAA4B,EAC1B,6DAA6D;EAC/DC,eAAe,EAAEA,CAAC;IAAEC;EAA8B,CAAC,KAChD,+BAA8BA,IAAK,OAAM;EAC5CC,+BAA+B,EAC7B,mDAAmD;EACrDC,gCAAgC,EAC9B,2DAA2D;EAC7DC,8BAA8B,EAG5BA,CAAC;IAAEC;EAAwC,CAAC,KACzC,sCAAqC;EAC1CC,iBAAiB,EAAEA,CAAC;IAAED;EAAmC,CAAC,KACvD,wBAAuBA,QAAS,IAAG;EAGtCE,uBAAuB,EAAEA,CAAC;IAAEC;EAA2C,CAAC,KACrE,IAAGA,KAAM,yBAAwB;EACpCC,kBAAkB,EAAE,qCAAqC;EACzDC,mBAAmB,EAAE,sCAAsC;EAC3DC,iCAAiC,EAC/B,8DAA8D;EAChEC,wBAAwB,EAAE,4CAA4C;EACtEC,6BAA6B,EAC3B,4DAA4D;EAC9DC,qBAAqB,EAAEA,CAAC;IACtBC;EAGF,CAAC,KACE,IAAGA,SAAS,CAAC,CAAC,CAAE,mCAAkCA,SAAS,CAAC,CAAC,CAAE,aAAY;EAC9EC,yBAAyB,EACvB,uDAAuD;EACzDC,8BAA8B,EAAEA,CAAC;IAC/BZ;EAGF,CAAC,KACE,4DAA2DA,QAAS,KAAI;EAC3Ea,wBAAwB,EACtB,sDAAsD;EACxDC,yBAAyB,EACvB,0DAA0D;EAC5DC,uBAAuB,EACrB,qDAAqD;EACvDC,qCAAqC,EACnC,mDAAmD;EACrDC,2BAA2B,EAAEA,CAAC;IAAEjB;EAAmC,CAAC,KACjE,IAAGA,QAAS,4CAA2C;EAC1DkB,8BAA8B,EAAEA,CAAC;IAAElB;EAAmC,CAAC,KACpE,IAAGA,QAAS,+CAA8C;EAC7DmB,uCAAuC,EAAEA,CAAC;IACxCnB;EAGF,CAAC,KACE,IAAGA,QAAS,qFAAoF;EACnGoB,qBAAqB,EAAEA,CAAC;IACtBC;EAGF,CAAC,KACE,IAAGA,gBAAgB,CAAC,CAAC,CAAE,4BAA2BA,gBAAgB,CAAC,CAAC,CAAE,aAAY;EACrFC,iDAAiD,EAC/C,6DAA6D,GAC7D,gGAAgG;EAClGC,uBAAuB,EACrB,yDAAyD;EAC3DC,oBAAoB,EAClB,6DAA6D;EAC/DC,gCAAgC,EAC9B,0DAA0D;EAC5DC,iCAAiC,EAC/B,4DAA4D;EAC9DC,wCAAwC,EACtC,kFAAkF;EACpFC,0BAA0B,EACxB,uDAAuD;EACzDC,qBAAqB,EACnB,4GAA4G;EAC9GC,iBAAiB,EACf,gFAAgF;EAClFC,yBAAyB,EACvB,uDAAuD;EACzDC,8BAA8B,EAAEA,CAAC;IAC/BhC;EAGF,CAAC,KACE,4DAA2DA,QAAS,KAAI;EAC3EiC,0BAA0B,EACxB,mFAAmF;EACrFC,sBAAsB,EACpB,gHAAgH;EAClHC,qBAAqB,EACnB,mGAAmG;EAErGC,qCAAqC,EACnC,qDAAqD;EACvDC,iCAAiC,EAC/B,8CAA8C;EAChDC,8BAA8B,EAC5B,wDAAwD;EAC1DC,uCAAuC,EAAEA,CAAC;IACxCC;EAGF,CAAC,KACE,yBAAwBA,iBAAkB,kDAAiDA,iBAAkB,KAAI;EACpHC,6BAA6B,EAC3B,+CAA+C;EACjDC,sBAAsB,EACpB,0KAA0K;EAC5KC,yBAAyB,EACvB,mHAAmH;EACrHC,sCAAsC,EACpC,kFAAkF;EACpFC,+BAA+B,EAC7B,0GAA0G;EAC5GC,+BAA+B,EAC7B,0GAA0G;EAC5GC,2BAA2B,EACzB,uEAAuE;EACzEC,kBAAkB,EAChB,8EAA8E;EAChFC,wBAAwB,EAAE,wCAAwC;EAClEC,6BAA6B,EAAE,6CAA6C;EAC5EC,6BAA6B,EAC3B,qDAAqD;EACvDC,gCAAgC,EAC9B,mEAAmE;EACrEC,iCAAiC,EAAEA,CAAC;IAAEC;EAAuB,CAAC,KAC3D,yFAAwFA,IAAK;AAClG,CAAC,CAAC;AAMF,SAASC,mBAAmBA,CAACC,KAAa,EAAmC;EAC3E,QAAQA,KAAK;IACX,KAAK,KAAK;MACR,OAAO,cAAc;IACvB,KAAK,SAAS;MACZ,OAAO,kBAAkB;IAC3B,KAAK,QAAQ;MACX,OAAO,iBAAiB;IAC1B,KAAK,OAAO;MACV,OAAO,gBAAgB;IACzB,KAAK,QAAQ;MACX,OAAO,iBAAiB;IAC1B,KAAK,QAAQ;MACX,OAAO,iBAAiB;IAC1B,KAAK,QAAQ;MACX,OAAO,iBAAiB;IAC1B,KAAK,QAAQ;MACX,OAAO,iBAAiB;IAC1B,KAAK,WAAW;MACd,OAAO,oBAAoB;IAC7B,KAAK,SAAS;MACZ,OAAO,kBAAkB;IAC3B;MACE,OAAOC,SAAS;EACpB;AACF;AAEA,SAASC,kBAAkBA,CAAC1D,QAAgB,EAA+B;EACzE,OACEA,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,WAAW;AAE/E;AAEA,SAAS2D,uBAAuBA,CAC9B3D,QAAgB,EACmB;EACnC,OAAOA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK;AAChD;AAAC,IAAA4D,QAAA,GASeC,UAA0D,IACxE,MAAMC,qBAAqB,SAASD,UAAU,CAAmB;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAyW/DC,qBAAqB,GAAG,IAAI,CAACC,gBAAgB,CAACC,IAAI,CAAC,IAAI,EAAE;MACvDC,gBAAgB,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;MAC/BC,mBAAmB,EAAE,CACnB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,WAAW,EACX,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,CACX;MACDC,aAAa,EAAExF,QAAQ,CAACoC;IAC1B,CAAC,CAAC;IAAA,KAEFqD,oBAAoB,GAAG,IAAI,CAACL,gBAAgB,CAACC,IAAI,CAAC,IAAI,EAAE;MACtDC,gBAAgB,EAAE,CAAC,OAAO,CAAC;MAE3BC,mBAAmB,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;MAClCC,aAAa,EAAExF,QAAQ,CAACqC;IAC1B,CAAC,CAAC;IAAA,KAEFqD,0BAA0B,GAAG,IAAI,CAACN,gBAAgB,CAACC,IAAI,CAAC,IAAI,EAAE;MAC5DC,gBAAgB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;MACxCC,mBAAmB,EAAE,CACnB,QAAQ,EACR,SAAS,EACT,WAAW,EACX,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,CACX;MACDC,aAAa,EAAExF,QAAQ,CAACoC;IAC1B,CAAC,CAAC;EAAA;EA1YFuD,eAAeA,CAAA,EAEb;IACA,OAAOC,cAAsB;EAC/B;EAEAC,cAAcA,CAAA,EAAY;IAGxB,OAAO,IAAAC,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC;EAC3C;EAEAwB,wBAAwBA,CAAA,EAAG;IACzB,OACE,CAAC,IAAI,CAACC,KAAK,EAAY,CAAC,IACtB,IAAI,CAACA,KAAK,EAAU,CAAC,IACrB,IAAI,CAACA,KAAK,GAAQ,CAAC,IACnB,IAAI,CAACA,KAAK,GAAY,CAAC,IACvB,IAAI,CAACA,KAAK,IAAe,CAAC,IAC1B,IAAI,CAACC,qBAAqB,CAAC,CAAC,KAC9B,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAEjC;EAEAC,4BAA4BA,CAAA,EAAG;IAK7B,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACL,wBAAwB,CAAC,CAAC;EACxC;EAGAM,eAAeA,CACbhB,gBAAqB,EACrBiB,6BAAuC,EACjB;IACtB,IACE,CAAC,IAAAT,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,IACnC,IAAI,CAACuB,KAAK,CAACvB,IAAI,OAAW,IAC1B,IAAI,CAACuB,KAAK,CAACvB,IAAI,OAAc,EAC7B;MACA,OAAOG,SAAS;IAClB;IAEA,MAAMzD,QAAQ,GAAG,IAAI,CAAC6E,KAAK,CAACrB,KAAK;IACjC,IAAIY,gBAAgB,CAACkB,OAAO,CAACtF,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7C,IAAIqF,6BAA6B,IAAI,IAAI,CAACE,uBAAuB,CAAC,CAAC,EAAE;QACnE,OAAO9B,SAAS;MAClB;MACA,IAAI,IAAI,CAAC+B,UAAU,CAAC,IAAI,CAACN,4BAA4B,CAACf,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;QACjE,OAAOnE,QAAQ;MACjB;IACF;IACA,OAAOyD,SAAS;EAClB;EAOAS,gBAAgBA,CACd;IACEE,gBAAgB;IAChBC,mBAAmB;IACnBgB,6BAA6B;IAC7Bf,aAAa,GAAGxF,QAAQ,CAACmC;EAM3B,CAAC,EACDwE,QAAW,EACL;IACN,MAAMC,YAAY,GAAGA,CACnBC,GAAa,EACb3F,QAAoB,EACpB4F,MAAkB,EAClBC,KAAiB,KACd;MACH,IAAI7F,QAAQ,KAAK4F,MAAM,IAAIH,QAAQ,CAACI,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACC,KAAK,CAAChH,QAAQ,CAACsC,qBAAqB,EAAE;UACzC2E,EAAE,EAAEJ,GAAG;UACPtE,gBAAgB,EAAE,CAACuE,MAAM,EAAEC,KAAK;QAClC,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAMG,YAAY,GAAGA,CACnBL,GAAa,EACb3F,QAAoB,EACpBiG,IAAgB,EAChBC,IAAgB,KACb;MACH,IACGT,QAAQ,CAACQ,IAAI,CAAC,IAAIjG,QAAQ,KAAKkG,IAAI,IACnCT,QAAQ,CAACS,IAAI,CAAC,IAAIlG,QAAQ,KAAKiG,IAAK,EACrC;QACA,IAAI,CAACH,KAAK,CAAChH,QAAQ,CAAC2B,qBAAqB,EAAE;UACzCsF,EAAE,EAAEJ,GAAG;UACPjF,SAAS,EAAE,CAACuF,IAAI,EAAEC,IAAI;QACxB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,SAAS;MACP,MAAM;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACtB,KAAK;MAC/B,MAAM7E,QAAuC,GAAG,IAAI,CAACoF,eAAe,CAClEhB,gBAAgB,CAACgC,MAAM,CAAC/B,mBAAmB,WAAnBA,mBAAmB,GAAI,EAAE,CAAC,EAClDgB,6BACF,CAAC;MAED,IAAI,CAACrF,QAAQ,EAAE;MAEf,IAAI0D,kBAAkB,CAAC1D,QAAQ,CAAC,EAAE;QAChC,IAAIyF,QAAQ,CAACY,aAAa,EAAE;UAC1B,IAAI,CAACP,KAAK,CAAChH,QAAQ,CAACiB,8BAA8B,EAAE;YAClDgG,EAAE,EAAEI,QAAQ;YACZnG;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAEA,QAAQ,EAAE,UAAU,CAAC;UACtD0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAEA,QAAQ,EAAE,QAAQ,CAAC;UACpD0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAEA,QAAQ,EAAE,UAAU,CAAC;UAEtDyF,QAAQ,CAACY,aAAa,GAAGrG,QAAQ;QACnC;MACF,CAAC,MAAM,IAAI2D,uBAAuB,CAAC3D,QAAQ,CAAC,EAAE;QAC5C,IAAIyF,QAAQ,CAACzF,QAAQ,CAAC,EAAE;UACtB,IAAI,CAAC8F,KAAK,CAAChH,QAAQ,CAACmB,iBAAiB,EAAE;YAAE8F,EAAE,EAAEI,QAAQ;YAAEnG;UAAS,CAAC,CAAC;QACpE;QACAyF,QAAQ,CAACzF,QAAQ,CAAC,GAAG,IAAI;QAEzB0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC;MAC/C,CAAC,MAAM;QACL,IAAIzB,MAAM,CAACC,cAAc,CAACC,IAAI,CAACgH,QAAQ,EAAEzF,QAAQ,CAAC,EAAE;UAClD,IAAI,CAAC8F,KAAK,CAAChH,QAAQ,CAACmB,iBAAiB,EAAE;YAAE8F,EAAE,EAAEI,QAAQ;YAAEnG;UAAS,CAAC,CAAC;QACpE,CAAC,MAAM;UACL0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;UACtD0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;UACtD0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC;UACxD0F,YAAY,CAACS,QAAQ,EAAEnG,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC;UAExDgG,YAAY,CAACG,QAAQ,EAAEnG,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;UACvDgG,YAAY,CAACG,QAAQ,EAAEnG,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC;QACxD;QACAyF,QAAQ,CAACzF,QAAQ,CAAC,GAAG,IAAI;MAC3B;MAEA,IAAIqE,mBAAmB,YAAnBA,mBAAmB,CAAEiC,QAAQ,CAACtG,QAAQ,CAAC,EAAE;QAC3C,IAAI,CAAC8F,KAAK,CAACxB,aAAa,EAAE;UACxByB,EAAE,EAAEI,QAAQ;UACZnG;QACF,CAAC,CAAC;MACJ;IACF;EACF;EAEAuG,kBAAkBA,CAAC3G,IAAoB,EAAW;IAChD,QAAQA,IAAI;MACV,KAAK,aAAa;MAClB,KAAK,aAAa;QAChB,OAAO,IAAI,CAACmF,KAAK,EAAU,CAAC;MAC9B,KAAK,uBAAuB;QAC1B,OAAO,IAAI,CAACA,KAAK,EAAU,CAAC;MAC9B,KAAK,mBAAmB;QACtB,OAAO,IAAI,CAACA,KAAK,EAAY,CAAC;MAChC,KAAK,2BAA2B;QAC9B,OAAO,IAAI,CAACA,KAAK,GAAM,CAAC;IAC5B;EACF;EAEAyB,WAAWA,CACT5G,IAAoB,EACpB6G,YAAqB,EAChB;IACL,MAAMC,MAAW,GAAG,EAAE;IACtB,OAAO,CAAC,IAAI,CAACH,kBAAkB,CAAC3G,IAAI,CAAC,EAAE;MAErC8G,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC;IAC7B;IACA,OAAOC,MAAM;EACf;EAEAE,oBAAoBA,CAClBhH,IAAoB,EACpB6G,YAAqB,EACrBI,mBAEC,EACI;IACL,OAAOnI,OAAO,CACZ,IAAI,CAACoI,0BAA0B,CAC7BlH,IAAI,EACJ6G,YAAY,EACQ,IAAI,EACxBI,mBACF,CACF,CAAC;EACH;EAMAC,0BAA0BA,CACxBlH,IAAoB,EACpB6G,YAAwC,EACxCM,aAAsB,EACtBF,mBAEC,EACuB;IACxB,MAAMH,MAAM,GAAG,EAAE;IACjB,IAAIM,gBAAgB,GAAG,CAAC,CAAC;IAEzB,SAAS;MACP,IAAI,IAAI,CAACT,kBAAkB,CAAC3G,IAAI,CAAC,EAAE;QACjC;MACF;MACAoH,gBAAgB,GAAG,CAAC,CAAC;MAErB,MAAMC,OAAO,GAAGR,YAAY,CAAC,CAAC;MAC9B,IAAIQ,OAAO,IAAI,IAAI,EAAE;QACnB,OAAOxD,SAAS;MAClB;MACAiD,MAAM,CAACC,IAAI,CAACM,OAAO,CAAC;MAEpB,IAAI,IAAI,CAACC,GAAG,GAAS,CAAC,EAAE;QACtBF,gBAAgB,GAAG,IAAI,CAACnC,KAAK,CAACsC,YAAY;QAC1C;MACF;MAEA,IAAI,IAAI,CAACZ,kBAAkB,CAAC3G,IAAI,CAAC,EAAE;QACjC;MACF;MAEA,IAAImH,aAAa,EAAE;QAEjB,IAAI,CAACK,MAAM,GAAS,CAAC;MACvB;MACA,OAAO3D,SAAS;IAClB;IAEA,IAAIoD,mBAAmB,EAAE;MACvBA,mBAAmB,CAACrD,KAAK,GAAGwD,gBAAgB;IAC9C;IAEA,OAAON,MAAM;EACf;EAEAW,oBAAoBA,CAClBzH,IAAoB,EACpB6G,YAAqB,EACrBa,OAAgB,EAChBC,cAAuB,EACvBV,mBAEC,EACI;IACL,IAAI,CAACU,cAAc,EAAE;MACnB,IAAID,OAAO,EAAE;QACX,IAAI,CAACF,MAAM,EAAY,CAAC;MAC1B,CAAC,MAAM;QACL,IAAI,CAACA,MAAM,GAAM,CAAC;MACpB;IACF;IAEA,MAAMV,MAAM,GAAG,IAAI,CAACE,oBAAoB,CACtChH,IAAI,EACJ6G,YAAY,EACZI,mBACF,CAAC;IAED,IAAIS,OAAO,EAAE;MACX,IAAI,CAACF,MAAM,EAAY,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACA,MAAM,GAAM,CAAC;IACpB;IAEA,OAAOV,MAAM;EACf;EAEAc,iBAAiBA,CAAA,EAAmB;IAClC,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAiB,CAAC;IAC7C,IAAI,CAACN,MAAM,GAAW,CAAC;IACvB,IAAI,CAACA,MAAM,GAAU,CAAC;IACtB,IAAI,CAAC,IAAI,CAACrC,KAAK,IAAU,CAAC,EAAE;MAC1B,IAAI,CAACe,KAAK,CAAChH,QAAQ,CAACqE,6BAA6B,EAAE;QACjD4C,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB;MACjB,CAAC,CAAC;IACJ;IAGAsB,IAAI,CAACE,QAAQ,GAAG,KAAK,CAACC,aAAa,CAAC,CAAoB;IACxD,IAAI,CAACR,MAAM,GAAU,CAAC;IAEtB,IAAI,IAAI,CAACF,GAAG,GAAO,CAAC,EAAE;MAGpBO,IAAI,CAACI,SAAS,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC3C;IACA,IAAI,IAAI,CAAC/C,KAAK,GAAM,CAAC,EAAE;MACrB0C,IAAI,CAACM,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACnD;IACA,OAAO,IAAI,CAACC,UAAU,CAACR,IAAI,EAAE,cAAc,CAAC;EAC9C;EAEAK,iBAAiBA,CAACI,kBAA2B,GAAG,IAAI,EAAkB;IACpE,IAAIC,MAAsB,GAAG,IAAI,CAACC,eAAe,CAACF,kBAAkB,CAAC;IACrE,OAAO,IAAI,CAAChB,GAAG,GAAO,CAAC,EAAE;MACvB,MAAMO,IAA+B,GACnC,IAAI,CAACY,eAAe,CAAoBF,MAAM,CAAC;MACjDV,IAAI,CAACa,IAAI,GAAGH,MAAM;MAClBV,IAAI,CAACc,KAAK,GAAG,IAAI,CAACH,eAAe,CAACF,kBAAkB,CAAC;MACrDC,MAAM,GAAG,IAAI,CAACF,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;IACnD;IACA,OAAOU,MAAM;EACf;EAEAK,oBAAoBA,CAAA,EAAsB;IACxC,MAAMf,IAAI,GAAG,IAAI,CAACC,SAAS,CAAoB,CAAC;IAChDD,IAAI,CAACgB,QAAQ,GAAG,IAAI,CAACX,iBAAiB,CAAC,CAAC;IACxC,IAAI,CAAC,IAAI,CAAC7C,qBAAqB,CAAC,CAAC,IAAI,IAAI,CAACF,KAAK,GAAM,CAAC,EAAE;MACtD0C,IAAI,CAACM,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACnD;IACA,OAAO,IAAI,CAACC,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAiB,wBAAwBA,CAACC,GAAiB,EAAqB;IAC7D,IAAI,CAACxD,IAAI,CAAC,CAAC;IACX,MAAMsC,IAAI,GAAG,IAAI,CAACY,eAAe,CAAoBM,GAAG,CAAC;IACzDlB,IAAI,CAACmB,aAAa,GAAGD,GAAG;IACxBlB,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAgB,KAAK,CAAC;IACtErB,IAAI,CAACsB,OAAO,GAAG,KAAK;IACpB,OAAO,IAAI,CAACd,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAuB,mBAAmBA,CAAA,EAAiB;IAClC,MAAMvB,IAAI,GAAG,IAAI,CAACC,SAAS,CAAe,CAAC;IAC3C,IAAI,CAACvC,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC8C,UAAU,CAACR,IAAI,EAAE,YAAY,CAAC;EAC5C;EAEAwB,gBAAgBA,CAAA,EAAkB;IAChC,MAAMxB,IAAI,GAAG,IAAI,CAACC,SAAS,CAAgB,CAAC;IAC5C,IAAI,CAACN,MAAM,GAAW,CAAC;IACvB,IAAI,IAAI,CAACrC,KAAK,GAAW,CAAC,EAAE;MAC1B0C,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAAC1B,iBAAiB,CAAC,CAAC;IAC1C,CAAC,MAAM;MACLC,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACpB,iBAAiB,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC,IAAI,CAAC7C,qBAAqB,CAAC,CAAC,IAAI,IAAI,CAACF,KAAK,GAAM,CAAC,EAAE;MACtD0C,IAAI,CAACM,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACnD;IACA,OAAO,IAAI,CAACC,UAAU,CAACR,IAAI,EAAE,aAAa,CAAC;EAC7C;EAsCA0B,oBAAoBA,CAClBC,cAAyD,EACtC;IACnB,MAAM3B,IAAI,GAAG,IAAI,CAACC,SAAS,CAAoB,CAAC;IAEhD0B,cAAc,CAAC3B,IAAI,CAAC;IAEpBA,IAAI,CAAC4B,IAAI,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC3C7B,IAAI,CAAC8B,UAAU,GAAG,IAAI,CAACC,kBAAkB,GAAY,CAAC;IACtD/B,IAAI,CAACgC,OAAO,GAAG,IAAI,CAACD,kBAAkB,GAAM,CAAC;IAC7C,OAAO,IAAI,CAACvB,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAiC,wBAAwBA,CACtBN,cAAiD,EACA;IACjD,IAAI,IAAI,CAACrE,KAAK,GAAM,CAAC,EAAE;MACrB,OAAO,IAAI,CAAC4E,qBAAqB,CAACP,cAAc,CAAC;IACnD;EACF;EAEAO,qBAAqBA,CAACP,cAAiD,EAAE;IACvE,MAAM3B,IAAI,GAAG,IAAI,CAACC,SAAS,CAA+B,CAAC;IAE3D,IAAI,IAAI,CAAC3C,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,IAAe,CAAC,EAAE;MACnD,IAAI,CAACI,IAAI,CAAC,CAAC;IACb,CAAC,MAAM;MACL,IAAI,CAACyE,UAAU,CAAC,CAAC;IACnB;IAEA,MAAM/C,mBAAmB,GAAG;MAAErD,KAAK,EAAE,CAAC;IAAE,CAAC;IAEzCiE,IAAI,CAACoC,MAAM,GAAG,IAAI,CAACxC,oBAAoB,CACrC,2BAA2B,EAE3B,IAAI,CAAC8B,oBAAoB,CAAChF,IAAI,CAAC,IAAI,EAAEiF,cAAc,CAAC,EACtC,KAAK,EACE,IAAI,EACzBvC,mBACF,CAAC;IACD,IAAIY,IAAI,CAACoC,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAChE,KAAK,CAAChH,QAAQ,CAACuB,mBAAmB,EAAE;QAAE0F,EAAE,EAAE0B;MAAK,CAAC,CAAC;IACxD;IACA,IAAIZ,mBAAmB,CAACrD,KAAK,KAAK,CAAC,CAAC,EAAE;MACpC,IAAI,CAACuG,QAAQ,CAACtC,IAAI,EAAE,eAAe,EAAEZ,mBAAmB,CAACrD,KAAK,CAAC;IACjE;IACA,OAAO,IAAI,CAACyE,UAAU,CAACR,IAAI,EAAE,4BAA4B,CAAC;EAC5D;EAIAuC,eAAeA,CACbC,WAAsB,EACtBC,SAA2C,EACrC;IAEN,MAAMC,mBAAmB,GAAGF,WAAW,OAAa;IAGpD,MAAMG,SAAS,GAA6C,YAAY;IACxE,MAAMC,aAAa,GAEf,gBAAgB;IAEpBH,SAAS,CAACnC,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CACtD,IAAI,CAACnF,oBACP,CAAC;IACD,IAAI,CAAC6C,MAAM,GAAU,CAAC;IACtB8C,SAAS,CAACE,SAAS,CAAC,GAAG,IAAI,CAACE,8BAA8B,CAAC,CAAC;IAC5D,IAAIH,mBAAmB,EAAE;MACvBD,SAAS,CAACG,aAAa,CAAC,GACtB,IAAI,CAACE,oCAAoC,CAACN,WAAW,CAAC;IAC1D,CAAC,MAAM,IAAI,IAAI,CAAClF,KAAK,CAACkF,WAAW,CAAC,EAAE;MAClCC,SAAS,CAACG,aAAa,CAAC,GACtB,IAAI,CAACE,oCAAoC,CAACN,WAAW,CAAC;IAC1D;EACF;EAEAK,8BAA8BA,CAAA,EAE5B;IACA,MAAME,IAAI,GAAG,KAAK,CAACC,gBAAgB,SAGjCC,2BAAqB,CAACC,kBACxB,CAAC;IACD,KAAK,MAAMC,OAAO,IAAIJ,IAAI,EAAE;MAC1B,MAAM;QAAElH;MAAK,CAAC,GAAGsH,OAAO;MACxB,IAAItH,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,qBAAqB,EAAE;QAClE,IAAI,CAACwC,KAAK,CAAChH,QAAQ,CAACuE,iCAAiC,EAAE;UACrD0C,EAAE,EAAE6E,OAAO;UACXtH;QACF,CAAC,CAAC;MACJ;IACF;IACA,OAAOkH,IAAI;EAIb;EAEAK,0BAA0BA,CAAA,EAAS;IACjC,IAAI,CAAC,IAAI,CAAC3D,GAAG,GAAS,CAAC,IAAI,CAAC,IAAI,CAAC4D,gBAAgB,CAAC,CAAC,EAAE;MACnD,IAAI,CAAC1D,MAAM,GAAQ,CAAC;IACtB;EACF;EAEA2D,sBAAsBA,CACpBnL,IAAsE,EACtE6H,IAEC,EACiE;IAClE,IAAI,CAACuC,eAAe,KAAWvC,IAAI,CAAC;IACpC,IAAI,CAACoD,0BAA0B,CAAC,CAAC;IACjC,OAAO,IAAI,CAAC5C,UAAU,CAACR,IAAI,EAAE7H,IAAI,CAAC;EACpC;EAEAoL,+BAA+BA,CAAA,EAAG;IAChC,IAAI,CAAC7F,IAAI,CAAC,CAAC;IACX,IAAI,IAAAP,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,EAAE;MACtC,IAAI,CAAC6B,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACJ,KAAK,GAAS,CAAC;IAC7B;IACA,OAAO,KAAK;EACd;EAEAkG,wBAAwBA,CACtBxD,IAAgC,EACA;IAChC,IACE,EACE,IAAI,CAAC1C,KAAK,EAAY,CAAC,IACvB,IAAI,CAACmG,WAAW,CAAC,IAAI,CAACF,+BAA+B,CAAC7G,IAAI,CAAC,IAAI,CAAC,CAAC,CAClE,EACD;MACA;IACF;IAEA,IAAI,CAACiD,MAAM,EAAY,CAAC;IACxB,MAAM+D,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IACjC+C,EAAE,CAACtC,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChD,IAAI,CAACsC,gBAAgB,CAACD,EAAE,CAAC;IAEzB,IAAI,CAAC/D,MAAM,EAAY,CAAC;IACxBK,IAAI,CAAC4D,UAAU,GAAG,CAACF,EAAE,CAAC;IAEtB,MAAM7H,IAAI,GAAG,IAAI,CAACgI,wBAAwB,CAAC,CAAC;IAC5C,IAAIhI,IAAI,EAAEmE,IAAI,CAACoB,cAAc,GAAGvF,IAAI;IACpC,IAAI,CAACuH,0BAA0B,CAAC,CAAC;IACjC,OAAO,IAAI,CAAC5C,UAAU,CAACR,IAAI,EAAE,kBAAkB,CAAC;EAClD;EAEA8D,gCAAgCA,CAC9B9D,IAAiD,EACjD+D,QAAiB,EAC4B;IAC7C,IAAI,IAAI,CAACtE,GAAG,GAAY,CAAC,EAAEO,IAAI,CAACgE,QAAQ,GAAG,IAAI;IAC/C,MAAMC,OAAY,GAAGjE,IAAI;IAEzB,IAAI,IAAI,CAAC1C,KAAK,GAAU,CAAC,IAAI,IAAI,CAACA,KAAK,GAAM,CAAC,EAAE;MAC9C,IAAIyG,QAAQ,EAAE;QACZ,IAAI,CAAC1F,KAAK,CAAChH,QAAQ,CAACmD,0BAA0B,EAAE;UAAE8D,EAAE,EAAE0B;QAAK,CAAC,CAAC;MAC/D;MACA,MAAMkE,MAA2B,GAAGD,OAAO;MAC3C,IAAIC,MAAM,CAAC/L,IAAI,IAAI,IAAI,CAACmF,KAAK,GAAM,CAAC,EAAE;QACpC,IAAI,CAACe,KAAK,CAAChH,QAAQ,CAACO,+BAA+B,EAAE;UACnD0G,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;QAC7B,CAAC,CAAC;MACJ;MACA,IAAI,CAAC5B,eAAe,KAAW2B,MAAM,CAAC;MACtC,IAAI,CAACd,0BAA0B,CAAC,CAAC;MACjC,MAAMT,SAAS,GAEX,YAAY;MAChB,MAAMC,aAAa,GAEf,gBAAgB;MACpB,IAAIsB,MAAM,CAAC/L,IAAI,KAAK,KAAK,EAAE;QACzB,IAAI+L,MAAM,CAACvB,SAAS,CAAC,CAACN,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAAChE,KAAK,CAAC+F,kBAAM,CAACC,cAAc,EAAE;YAAE/F,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;UAAE,CAAC,CAAC;UACnE,IAAI,IAAI,CAACG,WAAW,CAACJ,MAAM,CAACvB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1C,IAAI,CAACtE,KAAK,CAAChH,QAAQ,CAACM,iCAAiC,EAAE;cACrD2G,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;YAC7B,CAAC,CAAC;UACJ;QACF;MACF,CAAC,MAAM,IAAID,MAAM,CAAC/L,IAAI,KAAK,KAAK,EAAE;QAChC,IAAI+L,MAAM,CAACvB,SAAS,CAAC,CAACN,MAAM,KAAK,CAAC,EAAE;UAClC,IAAI,CAAChE,KAAK,CAAC+F,kBAAM,CAACG,cAAc,EAAE;YAAEjG,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;UAAE,CAAC,CAAC;QACrE,CAAC,MAAM;UACL,MAAMK,cAAc,GAAGN,MAAM,CAACvB,SAAS,CAAC,CAAC,CAAC,CAAC;UAC3C,IAAI,IAAI,CAAC2B,WAAW,CAACE,cAAc,CAAC,EAAE;YACpC,IAAI,CAACnG,KAAK,CAAChH,QAAQ,CAACM,iCAAiC,EAAE;cACrD2G,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;YAC7B,CAAC,CAAC;UACJ;UACA,IACEK,cAAc,CAAC3I,IAAI,KAAK,YAAY,IACpC2I,cAAc,CAACR,QAAQ,EACvB;YACA,IAAI,CAAC3F,KAAK,CAAChH,QAAQ,CAACsD,qCAAqC,EAAE;cACzD2D,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;YAC7B,CAAC,CAAC;UACJ;UACA,IAAIK,cAAc,CAAC3I,IAAI,KAAK,aAAa,EAAE;YACzC,IAAI,CAACwC,KAAK,CAAChH,QAAQ,CAACuD,iCAAiC,EAAE;cACrD0D,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;YAC7B,CAAC,CAAC;UACJ;QACF;QACA,IAAID,MAAM,CAACtB,aAAa,CAAC,EAAE;UACzB,IAAI,CAACvE,KAAK,CAAChH,QAAQ,CAACwD,8BAA8B,EAAE;YAClDyD,EAAE,EAAE4F,MAAM,CAACtB,aAAa;UAC1B,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLsB,MAAM,CAAC/L,IAAI,GAAG,QAAQ;MACxB;MACA,OAAO,IAAI,CAACqI,UAAU,CAAC0D,MAAM,EAAE,mBAAmB,CAAC;IACrD,CAAC,MAAM;MACL,MAAMO,QAA+B,GAAGR,OAAO;MAC/C,IAAIF,QAAQ,EAAEU,QAAQ,CAACV,QAAQ,GAAG,IAAI;MACtC,MAAMlI,IAAI,GAAG,IAAI,CAACgI,wBAAwB,CAAC,CAAC;MAC5C,IAAIhI,IAAI,EAAE4I,QAAQ,CAACrD,cAAc,GAAGvF,IAAI;MACxC,IAAI,CAACuH,0BAA0B,CAAC,CAAC;MACjC,OAAO,IAAI,CAAC5C,UAAU,CAACiE,QAAQ,EAAE,qBAAqB,CAAC;IACzD;EACF;EAEAC,iBAAiBA,CAAA,EAAoB;IACnC,MAAM1E,IAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAElC,IAAI,IAAI,CAAC3C,KAAK,GAAU,CAAC,IAAI,IAAI,CAACA,KAAK,GAAM,CAAC,EAAE;MAC9C,OAAO,IAAI,CAACgG,sBAAsB,CAAC,4BAA4B,EAAEtD,IAAI,CAAC;IACxE;IAEA,IAAI,IAAI,CAAC1C,KAAK,GAAQ,CAAC,EAAE;MACvB,MAAMoG,EAAE,GAAG,IAAI,CAACzD,SAAS,CAAe,CAAC;MACzC,IAAI,CAACvC,IAAI,CAAC,CAAC;MACX,IAAI,IAAI,CAACJ,KAAK,GAAU,CAAC,IAAI,IAAI,CAACA,KAAK,GAAM,CAAC,EAAE;QAC9C,OAAO,IAAI,CAACgG,sBAAsB,CAChC,iCAAiC,EACjCtD,IACF,CAAC;MACH,CAAC,MAAM;QACLA,IAAI,CAACnJ,GAAG,GAAG,IAAI,CAAC8N,gBAAgB,CAACjB,EAAE,EAAE,KAAK,CAAC;QAC3C,OAAO,IAAI,CAACI,gCAAgC,CAAC9D,IAAI,EAAE,KAAK,CAAC;MAC3D;IACF;IAEA,IAAI,CAACvD,gBAAgB,CACnB;MACEE,gBAAgB,EAAE,CAAC,UAAU,CAAC;MAC9BC,mBAAmB,EAAE,CACnB,SAAS,EACT,UAAU,EACV,SAAS,EACT,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,UAAU;IAEd,CAAC,EACDoD,IACF,CAAC;IAED,MAAM4E,GAAG,GAAG,IAAI,CAACpB,wBAAwB,CAACxD,IAAI,CAAC;IAC/C,IAAI4E,GAAG,EAAE;MACP,OAAOA,GAAG;IACZ;IAEA,KAAK,CAACC,iBAAiB,CAAC7E,IAAI,CAAC;IAC7B,IACE,CAACA,IAAI,CAAC8E,QAAQ,IACd9E,IAAI,CAACnJ,GAAG,CAACgF,IAAI,KAAK,YAAY,KAC7BmE,IAAI,CAACnJ,GAAG,CAAC+K,IAAI,KAAK,KAAK,IAAI5B,IAAI,CAACnJ,GAAG,CAAC+K,IAAI,KAAK,KAAK,CAAC,IACpD,IAAI,CAACvE,wBAAwB,CAAC,CAAC,EAC/B;MACA2C,IAAI,CAAC7H,IAAI,GAAG6H,IAAI,CAACnJ,GAAG,CAAC+K,IAAI;MACzB,KAAK,CAACiD,iBAAiB,CAAC7E,IAAI,CAAC;IAC/B;IACA,OAAO,IAAI,CAAC8D,gCAAgC,CAAC9D,IAAI,EAAE,CAAC,CAACA,IAAI,CAAC+D,QAAQ,CAAC;EACrE;EAEAgB,kBAAkBA,CAAA,EAAoB;IACpC,MAAM/E,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkB,CAAC;IAC9CD,IAAI,CAACgF,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACzE,UAAU,CAACR,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEAiF,wBAAwBA,CAAA,EAA2B;IACjD,IAAI,CAACtF,MAAM,EAAU,CAAC;IACtB,MAAMqF,OAAO,GAAG,IAAI,CAACjG,WAAW,CAC9B,aAAa,EACb,IAAI,CAAC2F,iBAAiB,CAAChI,IAAI,CAAC,IAAI,CAClC,CAAC;IACD,IAAI,CAACiD,MAAM,EAAU,CAAC;IACtB,OAAOqF,OAAO;EAChB;EAEAE,qBAAqBA,CAAA,EAAY;IAC/B,IAAI,CAACxH,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAAC+B,GAAG,GAAW,CAAC,EAAE;MACxB,OAAO,IAAI,CAAC0F,YAAY,IAAa,CAAC;IACxC;IACA,IAAI,IAAI,CAACA,YAAY,IAAa,CAAC,EAAE;MACnC,IAAI,CAACzH,IAAI,CAAC,CAAC;IACb;IACA,IAAI,CAAC,IAAI,CAACJ,KAAK,EAAY,CAAC,EAAE;MAC5B,OAAO,KAAK;IACd;IACA,IAAI,CAACI,IAAI,CAAC,CAAC;IACX,IAAI,CAAC,IAAI,CAACR,cAAc,CAAC,CAAC,EAAE;MAC1B,OAAO,KAAK;IACd;IACA,IAAI,CAACQ,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACJ,KAAK,GAAO,CAAC;EAC3B;EAEA8H,0BAA0BA,CAAA,EAAsB;IAC9C,MAAMpF,IAAI,GAAG,IAAI,CAACC,SAAS,CAAoB,CAAC;IAChDD,IAAI,CAAC4B,IAAI,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC3C7B,IAAI,CAAC8B,UAAU,GAAG,IAAI,CAACuD,qBAAqB,GAAO,CAAC;IACpD,OAAO,IAAI,CAAC7E,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAsF,iBAAiBA,CAAA,EAAmB;IAClC,MAAMtF,IAAI,GAAG,IAAI,CAACC,SAAS,CAAiB,CAAC;IAE7C,IAAI,CAACN,MAAM,EAAU,CAAC;IAEtB,IAAI,IAAI,CAACrC,KAAK,GAAW,CAAC,EAAE;MAC1B0C,IAAI,CAAC+D,QAAQ,GAAG,IAAI,CAAC3G,KAAK,CAACrB,KAAK;MAChC,IAAI,CAAC2B,IAAI,CAAC,CAAC;MACX,IAAI,CAAC6H,gBAAgB,IAAa,CAAC;IACrC,CAAC,MAAM,IAAI,IAAI,CAACC,aAAa,IAAa,CAAC,EAAE;MAC3CxF,IAAI,CAAC+D,QAAQ,GAAG,IAAI;IACtB;IAEA,IAAI,CAACpE,MAAM,EAAY,CAAC;IACxBK,IAAI,CAACyF,aAAa,GAAG,IAAI,CAACL,0BAA0B,CAAC,CAAC;IACtDpF,IAAI,CAAC0F,QAAQ,GAAG,IAAI,CAACF,aAAa,GAAO,CAAC,GAAG,IAAI,CAACG,WAAW,CAAC,CAAC,GAAG,IAAI;IAEtE,IAAI,CAAChG,MAAM,EAAY,CAAC;IAExB,IAAI,IAAI,CAACrC,KAAK,GAAW,CAAC,EAAE;MAC1B0C,IAAI,CAACgE,QAAQ,GAAG,IAAI,CAAC5G,KAAK,CAACrB,KAAK;MAChC,IAAI,CAAC2B,IAAI,CAAC,CAAC;MACX,IAAI,CAACiC,MAAM,GAAY,CAAC;IAC1B,CAAC,MAAM,IAAI,IAAI,CAACF,GAAG,GAAY,CAAC,EAAE;MAChCO,IAAI,CAACgE,QAAQ,GAAG,IAAI;IACtB;IAEAhE,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACwE,cAAc,CAAC,CAAC;IAC3C,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAAClG,MAAM,EAAU,CAAC;IAEtB,OAAO,IAAI,CAACa,UAAU,CAACR,IAAI,EAAE,cAAc,CAAC;EAC9C;EAEA8F,gBAAgBA,CAAA,EAAkB;IAChC,MAAM9F,IAAI,GAAG,IAAI,CAACC,SAAS,CAAgB,CAAC;IAC5CD,IAAI,CAAC+F,YAAY,GAAG,IAAI,CAACnG,oBAAoB,CAC3C,mBAAmB,EACnB,IAAI,CAACoG,uBAAuB,CAACtJ,IAAI,CAAC,IAAI,CAAC,EACzB,IAAI,EACG,KACvB,CAAC;IAID,IAAIuJ,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,eAA+B,GAAG,IAAI;IAC1ClG,IAAI,CAAC+F,YAAY,CAACI,OAAO,CAACC,WAAW,IAAI;MAAA,IAAAC,gBAAA;MACvC,MAAM;QAAExK;MAAK,CAAC,GAAGuK,WAAW;MAE5B,IACEH,mBAAmB,IACnBpK,IAAI,KAAK,YAAY,IACrBA,IAAI,KAAK,gBAAgB,IACzB,EAAEA,IAAI,KAAK,oBAAoB,IAAIuK,WAAW,CAACpC,QAAQ,CAAC,EACxD;QACA,IAAI,CAAC3F,KAAK,CAAChH,QAAQ,CAAC8C,0BAA0B,EAAE;UAC9CmE,EAAE,EAAE8H;QACN,CAAC,CAAC;MACJ;MAEAH,mBAAmB,KAAnBA,mBAAmB,GAChBpK,IAAI,KAAK,oBAAoB,IAAIuK,WAAW,CAACpC,QAAQ,IACtDnI,IAAI,KAAK,gBAAgB;MAG3B,IAAIyK,SAAS,GAAGzK,IAAI;MACpB,IAAIA,IAAI,KAAK,YAAY,EAAE;QACzBuK,WAAW,GAAGA,WAAW,CAAChF,cAAc;QACxCkF,SAAS,GAAGF,WAAW,CAACvK,IAAI;MAC9B;MAEA,MAAM0K,SAAS,GAAGD,SAAS,KAAK,oBAAoB;MACpD,CAAAD,gBAAA,GAAAH,eAAe,YAAAG,gBAAA,GAAfH,eAAe,GAAKK,SAAS;MAC7B,IAAIL,eAAe,KAAKK,SAAS,EAAE;QACjC,IAAI,CAAClI,KAAK,CAAChH,QAAQ,CAAC2C,gCAAgC,EAAE;UACpDsE,EAAE,EAAE8H;QACN,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC5F,UAAU,CAACR,IAAI,EAAE,aAAa,CAAC;EAC7C;EAEAgG,uBAAuBA,CAAA,EAAoC;IAGzD,MAAM;MAAEtH;IAAS,CAAC,GAAG,IAAI,CAACtB,KAAK;IAE/B,MAAMoJ,IAAI,GAAG,IAAI,CAAC/G,GAAG,GAAY,CAAC;IAElC,IAAIgH,OAAgB;IACpB,IAAIC,KAAmB;IACvB,IAAI1C,QAAiB;IACrB,IAAInI,IAAqC;IAEzC,MAAM8K,MAAM,GAAG,IAAAC,iCAA0B,EAAC,IAAI,CAACxJ,KAAK,CAACvB,IAAI,CAAC;IAC1D,MAAMgL,WAAW,GAAGF,MAAM,GAAG,IAAI,CAACG,iBAAiB,CAAC,CAAC,GAAG,IAAI;IAC5D,IAAID,WAAW,OAAoB,EAAE;MACnCJ,OAAO,GAAG,IAAI;MACdzC,QAAQ,GAAG,KAAK;MAChB0C,KAAK,GAAG,IAAI,CAAC/F,eAAe,CAAC,IAAI,CAAC;MAClC,IAAI,CAAChB,MAAM,GAAS,CAAC;MACrB9D,IAAI,GAAG,IAAI,CAAC8J,WAAW,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAIkB,WAAW,OAA2B,EAAE;MACjD7C,QAAQ,GAAG,IAAI;MACf,MAAMtF,QAAQ,GAAG,IAAI,CAACtB,KAAK,CAACsB,QAAQ;MACpC,MAAMqI,QAAQ,GAAG,IAAI,CAAC3J,KAAK,CAACrB,KAAK;MACjC,MAAMiL,WAAW,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAE9C,IAAI,IAAI,CAACH,iBAAiB,CAAC,CAAC,OAAoB,EAAE;QAChDL,OAAO,GAAG,IAAI;QACdC,KAAK,GAAG,IAAI,CAAC/B,gBAAgB,CAC3B,IAAI,CAACuC,WAAW,CAAexI,QAAQ,CAAC,EACxCqI,QACF,CAAC;QACD,IAAI,CAACpH,MAAM,GAAY,CAAC;QACxB,IAAI,CAACA,MAAM,GAAS,CAAC;QACrB9D,IAAI,GAAG,IAAI,CAAC8J,WAAW,CAAC,CAAC;MAC3B,CAAC,MAAM;QACLc,OAAO,GAAG,KAAK;QACf5K,IAAI,GAAGmL,WAAW;QAClB,IAAI,CAACrH,MAAM,GAAY,CAAC;MAC1B;IACF,CAAC,MAAM;MACL9D,IAAI,GAAG,IAAI,CAAC8J,WAAW,CAAC,CAAC;MACzB3B,QAAQ,GAAG,IAAI,CAACvE,GAAG,GAAY,CAAC;MAIhCgH,OAAO,GAAG,IAAI,CAAChH,GAAG,GAAS,CAAC;IAC9B;IAEA,IAAIgH,OAAO,EAAE;MACX,IAAIU,WAAyC;MAC7C,IAAIT,KAAK,EAAE;QACTS,WAAW,GAAG,IAAI,CAACvG,eAAe,CAAuB8F,KAAK,CAAC;QAC/DS,WAAW,CAACnD,QAAQ,GAAGA,QAAQ;QAC/BmD,WAAW,CAACT,KAAK,GAAGA,KAAK;QACzBS,WAAW,CAACC,WAAW,GAAGvL,IAAI;QAE9B,IAAI,IAAI,CAAC4D,GAAG,GAAY,CAAC,EAAE;UACzB0H,WAAW,CAACnD,QAAQ,GAAG,IAAI;UAC3B,IAAI,CAAC3F,KAAK,CAAChH,QAAQ,CAAC4D,sBAAsB,EAAE;YAC1CqD,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACiK;UACjB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLF,WAAW,GAAG,IAAI,CAACvG,eAAe,CAAuB/E,IAAI,CAAC;QAC9DsL,WAAW,CAACnD,QAAQ,GAAGA,QAAQ;QAC/B,IAAI,CAAC3F,KAAK,CAAChH,QAAQ,CAACyC,uBAAuB,EAAE;UAAEwE,EAAE,EAAEzC;QAAK,CAAC,CAAC;QAG1DsL,WAAW,CAACT,KAAK,GAAG7K,IAAI;QACxBsL,WAAW,CAACC,WAAW,GAAG,IAAI,CAACzB,WAAW,CAAC,CAAC;MAC9C;MACA9J,IAAI,GAAG,IAAI,CAAC2E,UAAU,CAAC2G,WAAW,EAAE,oBAAoB,CAAC;IAC3D,CAAC,MAAM,IAAInD,QAAQ,EAAE;MACnB,MAAMsD,gBAAgB,GAAG,IAAI,CAAC1G,eAAe,CAAmB/E,IAAI,CAAC;MACrEyL,gBAAgB,CAAClG,cAAc,GAAGvF,IAAI;MACtCA,IAAI,GAAG,IAAI,CAAC2E,UAAU,CAAC8G,gBAAgB,EAAE,gBAAgB,CAAC;IAC5D;IAEA,IAAId,IAAI,EAAE;MACR,MAAMe,QAAQ,GAAG,IAAI,CAACL,WAAW,CAAexI,QAAQ,CAAC;MACzD6I,QAAQ,CAACnG,cAAc,GAAGvF,IAAI;MAC9BA,IAAI,GAAG,IAAI,CAAC2E,UAAU,CAAC+G,QAAQ,EAAE,YAAY,CAAC;IAChD;IAEA,OAAO1L,IAAI;EACb;EAEA2L,wBAAwBA,CAAA,EAA0B;IAChD,MAAMxH,IAAI,GAAG,IAAI,CAACC,SAAS,CAAwB,CAAC;IACpD,IAAI,CAACN,MAAM,GAAU,CAAC;IACtBK,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACuE,WAAW,CAAC,CAAC;IACxC,IAAI,CAAChG,MAAM,GAAU,CAAC;IACtB,OAAO,IAAI,CAACa,UAAU,CAACR,IAAI,EAAE,qBAAqB,CAAC;EACrD;EAEAyH,gCAAgCA,CAC9B5L,IAA4C,EAC5C6L,QAAkB,EACa;IAC/B,MAAM1H,IAAI,GAAG,IAAI,CAACC,SAAS,CAEzB,CAAC;IACH,IAAIpE,IAAI,KAAK,mBAAmB,EAAE;MAC/BmE,IAAI,CAAiC0H,QAAQ,GAAG,CAAC,CAACA,QAAQ;MAC3D,IAAIA,QAAQ,EAAE,IAAI,CAAChK,IAAI,CAAC,CAAC;MACzB,IAAI,CAACA,IAAI,CAAC,CAAC;IACb;IACA,IAAI,CAACiK,gCAAgC,CAAC,MACpC,IAAI,CAACpF,eAAe,KAAWvC,IAAI,CACrC,CAAC;IACD,OAAO,IAAI,CAACQ,UAAU,CAACR,IAAI,EAAEnE,IAAI,CAAC;EACpC;EAEA+L,sBAAsBA,CAAA,EAAoB;IACxC,MAAM5H,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkB,CAAC;IAC9C,QAAQ,IAAI,CAAC7C,KAAK,CAACvB,IAAI;MACrB;MACA;MACA;MACA;MACA;QAGEmE,IAAI,CAAC6H,OAAO,GAAG,KAAK,CAAC1H,aAAa,CAAC,CAAC;QACpC;MACF;QACE,IAAI,CAACgC,UAAU,CAAC,CAAC;IACrB;IACA,OAAO,IAAI,CAAC3B,UAAU,CAACR,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEA8H,0BAA0BA,CAAA,EAAa;IACrC,MAAM9H,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkB,CAAC;IAC9CD,IAAI,CAAC6H,OAAO,GAAG,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;IACzC,OAAO,IAAI,CAACvH,UAAU,CAACR,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEAgI,yBAAyBA,CAAA,EAAsB;IAC7C,IAAI,IAAI,CAAC5K,KAAK,CAAC6K,MAAM,EAAE,OAAO,IAAI,CAACtC,WAAW,CAAC,CAAC;IAChD,OAAO,KAAK,CAACqC,yBAAyB,CAAC,CAAC;EAC1C;EAEAE,kCAAkCA,CAAA,EAAqC;IACrE,MAAMC,WAAW,GAAG,IAAI,CAAC5G,mBAAmB,CAAC,CAAC;IAC9C,IAAI,IAAI,CAAC4D,YAAY,IAAO,CAAC,IAAI,CAAC,IAAI,CAAC3H,qBAAqB,CAAC,CAAC,EAAE;MAC9D,OAAO,IAAI,CAACyD,wBAAwB,CAACkH,WAAW,CAAC;IACnD,CAAC,MAAM;MACL,OAAOA,WAAW;IACpB;EACF;EAEAlB,mBAAmBA,CAAA,EAAa;IAC9B,QAAQ,IAAI,CAAC7J,KAAK,CAACvB,IAAI;MACrB;MACA;MACA;MACA;MACA;QACE,OAAO,IAAI,CAAC+L,sBAAsB,CAAC,CAAC;MACtC;QACE,IAAI,IAAI,CAACxK,KAAK,CAACrB,KAAK,KAAK,GAAG,EAAE;UAC5B,MAAMiE,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkB,CAAC;UAC9C,MAAMmI,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;UAClC,IAAID,SAAS,CAACvM,IAAI,QAAW,IAAIuM,SAAS,CAACvM,IAAI,QAAc,EAAE;YAC7D,IAAI,CAACsG,UAAU,CAAC,CAAC;UACnB;UAEAnC,IAAI,CAAC6H,OAAO,GAAG,IAAI,CAACS,eAAe,CAAC,CAAC;UACrC,OAAO,IAAI,CAAC9H,UAAU,CAACR,IAAI,EAAE,eAAe,CAAC;QAC/C;QACA;MACF;QACE,OAAO,IAAI,CAACkI,kCAAkC,CAAC,CAAC;MAClD;QACE,OAAO,IAAI,CAAC1G,gBAAgB,CAAC,CAAC;MAChC;QACE,OAAO,IAAI,CAACzB,iBAAiB,CAAC,CAAC;MACjC;QACE,OAAO,IAAI,CAAC0D,WAAW,CAAC,IAAI,CAACyB,qBAAqB,CAACxI,IAAI,CAAC,IAAI,CAAC,CAAC,GAC1D,IAAI,CAAC4I,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACP,kBAAkB,CAAC,CAAC;MAC/B;QACE,OAAO,IAAI,CAACe,gBAAgB,CAAC,CAAC;MAChC;QAAA;QAaE,OAAO,IAAI,CAAC0B,wBAAwB,CAAC,CAAC;MACxC;MACA;QACE,OAAO,IAAI,CAACM,0BAA0B,CAAC,CAAC;MAC1C;QAAS;UACP,MAAM;YAAEjM;UAAK,CAAC,GAAG,IAAI,CAACuB,KAAK;UAC3B,IACE,IAAAD,wBAAiB,EAACtB,IAAI,CAAC,IACvBA,IAAI,OAAa,IACjBA,IAAI,OAAa,EACjB;YACA,MAAM0M,QAAQ,GACZ1M,IAAI,OAAa,GACb,eAAe,GACfA,IAAI,OAAa,GACjB,eAAe,GACfC,mBAAmB,CAAC,IAAI,CAACsB,KAAK,CAACrB,KAAK,CAAC;YAC3C,IACEwM,QAAQ,KAAKvM,SAAS,IACtB,IAAI,CAAC8K,iBAAiB,CAAC,CAAC,OAAkB,EAC1C;cACA,MAAM9G,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkB,CAAC;cAC9C,IAAI,CAACvC,IAAI,CAAC,CAAC;cACX,OAAO,IAAI,CAAC8C,UAAU,CAACR,IAAI,EAAEuI,QAAQ,CAAC;YACxC;YACA,OAAO,IAAI,CAACxH,oBAAoB,CAAC,CAAC;UACpC;QACF;IACF;IAEA,IAAI,CAACoB,UAAU,CAAC,CAAC;EACnB;EAEAqG,wBAAwBA,CAAA,EAAa;IACnC,IAAI3M,IAAI,GAAG,IAAI,CAACoL,mBAAmB,CAAC,CAAC;IACrC,OAAO,CAAC,IAAI,CAACzJ,qBAAqB,CAAC,CAAC,IAAI,IAAI,CAACiC,GAAG,EAAY,CAAC,EAAE;MAC7D,IAAI,IAAI,CAACnC,KAAK,EAAY,CAAC,EAAE;QAC3B,MAAM0C,IAAI,GAAG,IAAI,CAACY,eAAe,CAAgB/E,IAAI,CAAC;QACtDmE,IAAI,CAACoH,WAAW,GAAGvL,IAAI;QACvB,IAAI,CAAC8D,MAAM,EAAY,CAAC;QACxB9D,IAAI,GAAG,IAAI,CAAC2E,UAAU,CAACR,IAAI,EAAE,aAAa,CAAC;MAC7C,CAAC,MAAM;QACL,MAAMA,IAAI,GAAG,IAAI,CAACY,eAAe,CAAwB/E,IAAI,CAAC;QAC9DmE,IAAI,CAACyI,UAAU,GAAG5M,IAAI;QACtBmE,IAAI,CAAC0I,SAAS,GAAG,IAAI,CAAC/C,WAAW,CAAC,CAAC;QACnC,IAAI,CAAChG,MAAM,EAAY,CAAC;QACxB9D,IAAI,GAAG,IAAI,CAAC2E,UAAU,CAACR,IAAI,EAAE,qBAAqB,CAAC;MACrD;IACF;IACA,OAAOnE,IAAI;EACb;EAEA8M,mBAAmBA,CAAA,EAAqB;IACtC,MAAM3I,IAAI,GAAG,IAAI,CAACC,SAAS,CAAmB,CAAC;IAC/C,MAAM2I,QAAQ,GAAG,IAAI,CAACxL,KAAK,CAACrB,KAAK;IACjC,IAAI,CAAC2B,IAAI,CAAC,CAAC;IACXsC,IAAI,CAAC4I,QAAQ,GAAGA,QAAQ;IACxB5I,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACyH,2BAA2B,CAAC,CAAC;IAExD,IAAID,QAAQ,KAAK,UAAU,EAAE;MAC3B,IAAI,CAACE,gCAAgC,CAEnC9I,IACF,CAAC;IACH;IAEA,OAAO,IAAI,CAACQ,UAAU,CAACR,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAEA8I,gCAAgCA,CAAC9I,IAAY,EAAE;IAC7C,QAAQA,IAAI,CAACoB,cAAc,CAACvF,IAAI;MAC9B,KAAK,aAAa;MAClB,KAAK,aAAa;QAChB;MACF;QACE,IAAI,CAACwC,KAAK,CAAChH,QAAQ,CAACkE,kBAAkB,EAAE;UAAE+C,EAAE,EAAE0B;QAAK,CAAC,CAAC;IACzD;EACF;EAEA+I,gBAAgBA,CAAA,EAAkB;IAChC,MAAM/I,IAAI,GAAG,IAAI,CAACC,SAAS,CAAgB,CAAC;IAC5C,IAAI,CAACsF,gBAAgB,IAAU,CAAC;IAChC,MAAME,aAAa,GAAG,IAAI,CAACxF,SAAS,CAAoB,CAAC;IACzDwF,aAAa,CAAC7D,IAAI,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACpD4D,aAAa,CAAC3D,UAAU,GAAG,IAAI,CAAC/D,UAAU,CAAC,MACzC,IAAI,CAACiL,6BAA6B,CAAC,CACrC,CAAC;IACDhJ,IAAI,CAACyF,aAAa,GAAG,IAAI,CAACjF,UAAU,CAACiF,aAAa,EAAE,iBAAiB,CAAC;IACtE,OAAO,IAAI,CAACjF,UAAU,CAACR,IAAI,EAAE,aAAa,CAAC;EAC7C;EAEAgJ,6BAA6BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACvJ,GAAG,GAAY,CAAC,EAAE;MACzB,MAAMqC,UAAU,GAAG,IAAI,CAACmH,mCAAmC,CAAC,MAC1D,IAAI,CAACtD,WAAW,CAAC,CACnB,CAAC;MACD,IACE,IAAI,CAACvI,KAAK,CAAC8L,iCAAiC,IAC5C,CAAC,IAAI,CAAC5L,KAAK,GAAY,CAAC,EACxB;QACA,OAAOwE,UAAU;MACnB;IACF;EACF;EAEA+G,2BAA2BA,CAAA,EAAa;IACtC,MAAMM,cAAc,GAClB,IAAAC,4BAAqB,EAAC,IAAI,CAAChM,KAAK,CAACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAACuB,KAAK,CAACiM,WAAW;IACnE,OAAOF,cAAc,GACjB,IAAI,CAACR,mBAAmB,CAAC,CAAC,GAC1B,IAAI,CAACxD,YAAY,IAAU,CAAC,GAC5B,IAAI,CAAC4D,gBAAgB,CAAC,CAAC,GACvB,IAAI,CAACpB,gCAAgC,CAAC,MACpC,IAAI,CAACa,wBAAwB,CAAC,CAChC,CAAC;EACP;EAEAc,8BAA8BA,CAC5BnR,IAA0C,EAC1CoR,oBAAoC,EACpCX,QAAmB,EACT;IACV,MAAM5I,IAAI,GAAG,IAAI,CAACC,SAAS,CAAuC,CAAC;IACnE,MAAMuJ,kBAAkB,GAAG,IAAI,CAAC/J,GAAG,CAACmJ,QAAQ,CAAC;IAC7C,MAAMa,KAAK,GAAG,EAAE;IAChB,GAAG;MACDA,KAAK,CAACvK,IAAI,CAACqK,oBAAoB,CAAC,CAAC,CAAC;IACpC,CAAC,QAAQ,IAAI,CAAC9J,GAAG,CAACmJ,QAAQ,CAAC;IAC3B,IAAIa,KAAK,CAACpH,MAAM,KAAK,CAAC,IAAI,CAACmH,kBAAkB,EAAE;MAC7C,OAAOC,KAAK,CAAC,CAAC,CAAC;IACjB;IACAzJ,IAAI,CAACyJ,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI,CAACjJ,UAAU,CAACR,IAAI,EAAE7H,IAAI,CAAC;EACpC;EAEAuR,+BAA+BA,CAAA,EAAa;IAC1C,OAAO,IAAI,CAACJ,8BAA8B,CACxC,oBAAoB,EACpB,IAAI,CAACT,2BAA2B,CAACnM,IAAI,CAAC,IAAI,CAAC,IAE7C,CAAC;EACH;EAEAiN,wBAAwBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACL,8BAA8B,CACxC,aAAa,EACb,IAAI,CAACI,+BAA+B,CAAChN,IAAI,CAAC,IAAI,CAAC,IAEjD,CAAC;EACH;EAEAkN,uBAAuBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACtM,KAAK,GAAM,CAAC,EAAE;MACrB,OAAO,IAAI;IACb;IACA,OACE,IAAI,CAACA,KAAK,GAAU,CAAC,IACrB,IAAI,CAACmG,WAAW,CAAC,IAAI,CAACoG,oCAAoC,CAACnN,IAAI,CAAC,IAAI,CAAC,CAAC;EAE1E;EAEAoN,oBAAoBA,CAAA,EAAY;IAC9B,IAAI,IAAA3M,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,IAAI,IAAI,CAACyB,KAAK,GAAS,CAAC,EAAE;MAC9D,IAAI,CAACI,IAAI,CAAC,CAAC;MACX,OAAO,IAAI;IACb;IAEA,IAAI,IAAI,CAACJ,KAAK,EAAU,CAAC,EAAE;MAEzB,MAAM;QAAEyM;MAAO,CAAC,GAAG,IAAI,CAAC3M,KAAK;MAC7B,MAAM4M,kBAAkB,GAAGD,MAAM,CAAC1H,MAAM;MACxC,IAAI;QACF,IAAI,CAAC4H,eAAe,IAAY,IAAI,CAAC;QACrC,OAAOF,MAAM,CAAC1H,MAAM,KAAK2H,kBAAkB;MAC7C,CAAC,CAAC,OAAAE,OAAA,EAAM;QACN,OAAO,KAAK;MACd;IACF;IAEA,IAAI,IAAI,CAAC5M,KAAK,EAAY,CAAC,EAAE;MAC3B,IAAI,CAACI,IAAI,CAAC,CAAC;MAEX,MAAM;QAAEqM;MAAO,CAAC,GAAG,IAAI,CAAC3M,KAAK;MAC7B,MAAM4M,kBAAkB,GAAGD,MAAM,CAAC1H,MAAM;MACxC,IAAI;QACF,KAAK,CAACW,gBAAgB,QAGpBC,2BAAqB,CAACkH,WACxB,CAAC;QACD,OAAOJ,MAAM,CAAC1H,MAAM,KAAK2H,kBAAkB;MAC7C,CAAC,CAAC,OAAAI,QAAA,EAAM;QACN,OAAO,KAAK;MACd;IACF;IAEA,OAAO,KAAK;EACd;EAEAP,oCAAoCA,CAAA,EAAY;IAC9C,IAAI,CAACnM,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACJ,KAAK,GAAU,CAAC,IAAI,IAAI,CAACA,KAAK,GAAY,CAAC,EAAE;MAGpD,OAAO,IAAI;IACb;IACA,IAAI,IAAI,CAACwM,oBAAoB,CAAC,CAAC,EAAE;MAC/B,IACE,IAAI,CAACxM,KAAK,GAAS,CAAC,IACpB,IAAI,CAACA,KAAK,GAAS,CAAC,IACpB,IAAI,CAACA,KAAK,GAAY,CAAC,IACvB,IAAI,CAACA,KAAK,GAAM,CAAC,EACjB;QAKA,OAAO,IAAI;MACb;MACA,IAAI,IAAI,CAACA,KAAK,GAAU,CAAC,EAAE;QACzB,IAAI,CAACI,IAAI,CAAC,CAAC;QACX,IAAI,IAAI,CAACJ,KAAK,GAAS,CAAC,EAAE;UAExB,OAAO,IAAI;QACb;MACF;IACF;IACA,OAAO,KAAK;EACd;EAEAwF,oCAAoCA,CAClCN,WAAsB,EACF;IACpB,OAAO,IAAI,CAAC6H,QAAQ,CAAC,MAAM;MACzB,MAAMC,CAAC,GAAG,IAAI,CAACrK,SAAS,CAAqB,CAAC;MAC9C,IAAI,CAACN,MAAM,CAAC6C,WAAW,CAAC;MAExB,MAAMxC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAoB,CAAC;MAEhD,MAAMqB,OAAO,GAAG,CAAC,CAAC,IAAI,CAACvD,UAAU,CAC/B,IAAI,CAACwM,2BAA2B,CAAC7N,IAAI,CAAC,IAAI,CAC5C,CAAC;MAED,IAAI4E,OAAO,IAAI,IAAI,CAAChE,KAAK,GAAS,CAAC,EAAE;QAGnC,IAAIkN,iBAAiB,GAAG,IAAI,CAACtC,kCAAkC,CAAC,CAAC;QAGjE,IAAIsC,iBAAiB,CAAC3O,IAAI,KAAK,YAAY,EAAE;UAC3CmE,IAAI,CAACmB,aAAa,GAAGqJ,iBAAiB;UACtCxK,IAAI,CAACsB,OAAO,GAAG,IAAI;UAClBtB,IAAI,CAAuBoB,cAAc,GAAG,IAAI;UACjDoJ,iBAAiB,GAAG,IAAI,CAAChK,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;QAC9D,CAAC,MAAM;UACL,IAAI,CAACyK,0BAA0B,CAACD,iBAAiB,EAAExK,IAAI,CAAC;UACxDwK,iBAAiB,CAAClJ,OAAO,GAAG,IAAI;QAClC;QACAgJ,CAAC,CAAClJ,cAAc,GAAGoJ,iBAAiB;QACpC,OAAO,IAAI,CAAChK,UAAU,CAAC8J,CAAC,EAAE,kBAAkB,CAAC;MAC/C;MAEA,MAAMI,qBAAqB,GACzB,IAAI,CAACxN,cAAc,CAAC,CAAC,IACrB,IAAI,CAACa,UAAU,CAAC,IAAI,CAAC4M,0BAA0B,CAACjO,IAAI,CAAC,IAAI,CAAC,CAAC;MAE7D,IAAI,CAACgO,qBAAqB,EAAE;QAC1B,IAAI,CAACpJ,OAAO,EAAE;UAEZ,OAAO,IAAI,CAACD,qBAAqB,CAAgB,KAAK,EAAEiJ,CAAC,CAAC;QAC5D;QAGAtK,IAAI,CAACmB,aAAa,GAAG,IAAI,CAACR,eAAe,CAAC,CAAC;QAC3CX,IAAI,CAACsB,OAAO,GAAGA,OAAO;QACrBtB,IAAI,CAAuBoB,cAAc,GAAG,IAAI;QACjDkJ,CAAC,CAAClJ,cAAc,GAAG,IAAI,CAACZ,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;QAC3D,OAAO,IAAI,CAACQ,UAAU,CAAC8J,CAAC,EAAE,kBAAkB,CAAC;MAC/C;MAGA,MAAMzO,IAAI,GAAG,IAAI,CAACwF,qBAAqB,CAAgB,KAAK,CAAC;MAC7DrB,IAAI,CAACmB,aAAa,GAAGuJ,qBAAqB;MAC1C1K,IAAI,CAACoB,cAAc,GAAGvF,IAAI;MAC1BmE,IAAI,CAACsB,OAAO,GAAGA,OAAO;MACtBgJ,CAAC,CAAClJ,cAAc,GAAG,IAAI,CAACZ,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;MAC3D,OAAO,IAAI,CAACQ,UAAU,CAAC8J,CAAC,EAAE,kBAAkB,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEAM,uCAAuCA,CAAA,EAAmC;IACxE,IAAI,IAAI,CAACtN,KAAK,GAAS,CAAC,EAAE;MACxB,OAAO,IAAI,CAACwF,oCAAoC,GAAS,CAAC;IAC5D;EACF;EAEAe,wBAAwBA,CAAA,EAAmC;IACzD,IAAI,IAAI,CAACvG,KAAK,GAAS,CAAC,EAAE;MACxB,OAAO,IAAI,CAAC+D,qBAAqB,CAAC,CAAC;IACrC;EACF;EAEAuE,cAAcA,CAAA,EAAyB;IACrC,OAAO,IAAI,CAAC7D,kBAAkB,GAAS,CAAC;EAC1C;EAEA4I,0BAA0BA,CAAA,EAA6B;IACrD,MAAMjH,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IACjC,IAAI,IAAI,CAACwE,YAAY,IAAO,CAAC,IAAI,CAAC,IAAI,CAAC3H,qBAAqB,CAAC,CAAC,EAAE;MAC9D,IAAI,CAACE,IAAI,CAAC,CAAC;MACX,OAAOgG,EAAE;IACX;EACF;EAEA6G,2BAA2BA,CAAA,EAAY;IACrC,IAAI,IAAI,CAACnN,KAAK,CAACvB,IAAI,QAAgB,EAAE;MACnC,OAAO,KAAK;IACd;IACA,MAAMwN,WAAW,GAAG,IAAI,CAACjM,KAAK,CAACiM,WAAW;IAC1C,IAAI,CAAC3L,IAAI,CAAC,CAAC;IACX,IAAI,CAAC,IAAAP,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAACyB,KAAK,GAAS,CAAC,EAAE;MAChE,OAAO,KAAK;IACd;IAEA,IAAI+L,WAAW,EAAE;MACf,IAAI,CAAChL,KAAK,CAAC+F,kBAAM,CAACyG,0BAA0B,EAAE;QAC5CvM,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACiK,eAAe;QAC9ByD,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI;EACb;EAEAzJ,qBAAqBA,CACnB0J,QAAQ,GAAG,IAAI,EACfT,CAA6B,GAAG,IAAI,CAACrK,SAAS,CAAqB,CAAC,EAChD;IACpB,IAAI,CAACoK,QAAQ,CAAC,MAAM;MAClB,IAAIU,QAAQ,EAAE,IAAI,CAACpL,MAAM,GAAS,CAAC;MACnC2K,CAAC,CAAClJ,cAAc,GAAG,IAAI,CAACuE,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC;IACF,OAAO,IAAI,CAACnF,UAAU,CAAC8J,CAAC,EAAE,kBAAkB,CAAC;EAC/C;EAGA3E,WAAWA,CAAA,EAAa;IAEtBvO,MAAM,CAAC,IAAI,CAACgG,KAAK,CAAC6K,MAAM,CAAC;IACzB,MAAMpM,IAAI,GAAG,IAAI,CAACmP,yBAAyB,CAAC,CAAC;IAE7C,IACE,IAAI,CAAC5N,KAAK,CAAC8L,iCAAiC,IAC5C,IAAI,CAAC1L,qBAAqB,CAAC,CAAC,IAC5B,CAAC,IAAI,CAACiC,GAAG,GAAY,CAAC,EACtB;MACA,OAAO5D,IAAI;IACb;IACA,MAAMmE,IAAI,GAAG,IAAI,CAACY,eAAe,CAAsB/E,IAAI,CAAC;IAC5DmE,IAAI,CAACsG,SAAS,GAAGzK,IAAI;IAErBmE,IAAI,CAACiL,WAAW,GAAG,IAAI,CAAChC,mCAAmC,CAAC,MAC1D,IAAI,CAAC+B,yBAAyB,CAAC,CACjC,CAAC;IAED,IAAI,CAACrL,MAAM,GAAY,CAAC;IACxBK,IAAI,CAACkL,QAAQ,GAAG,IAAI,CAACvD,gCAAgC,CAAC,MACpD,IAAI,CAAChC,WAAW,CAAC,CACnB,CAAC;IAED,IAAI,CAAChG,MAAM,GAAS,CAAC;IACrBK,IAAI,CAACmL,SAAS,GAAG,IAAI,CAACxD,gCAAgC,CAAC,MACrD,IAAI,CAAChC,WAAW,CAAC,CACnB,CAAC;IAED,OAAO,IAAI,CAACnF,UAAU,CAACR,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAoL,8BAA8BA,CAAA,EAAY;IACxC,OACE,IAAI,CAACjG,YAAY,IAAa,CAAC,IAAI,IAAI,CAACkD,SAAS,CAAC,CAAC,CAACxM,IAAI,OAAY;EAExE;EAEAmP,yBAAyBA,CAAA,EAAa;IACpC,IAAI,IAAI,CAACpB,uBAAuB,CAAC,CAAC,EAAE;MAClC,OAAO,IAAI,CAACnC,gCAAgC,CAAC,gBAAgB,CAAC;IAChE;IACA,IAAI,IAAI,CAACnK,KAAK,GAAQ,CAAC,EAAE;MAEvB,OAAO,IAAI,CAACmK,gCAAgC,CAAC,mBAAmB,CAAC;IACnE,CAAC,MAAM,IAAI,IAAI,CAAC2D,8BAA8B,CAAC,CAAC,EAAE;MAEhD,OAAO,IAAI,CAAC3D,gCAAgC,CAC1C,mBAAmB,EACJ,IACjB,CAAC;IACH;IACA,OAAO,IAAI,CAACkC,wBAAwB,CAAC,CAAC;EACxC;EAEA0B,oBAAoBA,CAAA,EAAsB;IACxC,IAAI,IAAI,CAACC,eAAe,CAAC,YAAY,EAAE,0BAA0B,CAAC,EAAE;MAClE,IAAI,CAACjN,KAAK,CAAChH,QAAQ,CAACqD,qBAAqB,EAAE;QAAE4D,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB;MAAS,CAAC,CAAC;IACzE;IAEA,MAAMsB,IAAI,GAAG,IAAI,CAACC,SAAS,CAAoB,CAAC;IAChDD,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACiJ,QAAQ,CAAC,MAAM;MACxC,IAAI,CAAC3M,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACJ,KAAK,GAAU,CAAC,GACxB,IAAI,CAACyD,oBAAoB,CAAC,CAAC,GAC3B,IAAI,CAAC4E,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC;IACF,IAAI,CAAChG,MAAM,GAAM,CAAC;IAClBK,IAAI,CAACuL,UAAU,GAAG,IAAI,CAACjD,eAAe,CAAC,CAAC;IACxC,OAAO,IAAI,CAAC9H,UAAU,CAACR,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAwL,qBAAqBA,CACnB9S,KAA+B,EACS;IACxC,MAAM+S,gBAAgB,GAAG,IAAI,CAACrO,KAAK,CAACsB,QAAQ;IAE5C,MAAMgN,aAAa,GAAG,IAAI,CAACvM,oBAAoB,CAC7C,uBAAuB,EACvB,MAAM;MACJ,MAAMa,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkC,CAAC;MAC9DD,IAAI,CAACuL,UAAU,GAAG,IAAI,CAAClL,iBAAiB,CAAC,CAAC;MAC1C,IAAI,IAAI,CAAC/C,KAAK,GAAM,CAAC,EAAE;QACrB0C,IAAI,CAACM,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MACnD;MAEA,OAAO,IAAI,CAACC,UAAU,CAACR,IAAI,EAAE,+BAA+B,CAAC;IAC/D,CACF,CAAC;IAED,IAAI,CAAC0L,aAAa,CAACrJ,MAAM,EAAE;MACzB,IAAI,CAAChE,KAAK,CAAChH,QAAQ,CAACoB,uBAAuB,EAAE;QAC3C6F,EAAE,EAAEmN,gBAAgB;QACpB/S;MACF,CAAC,CAAC;IACJ;IAEA,OAAOgT,aAAa;EACtB;EAEAC,2BAA2BA,CACzB3L,IAAsC,EACtC4L,UAEC,GAAG,CAAC,CAAC,EAC2B;IACjC,IAAI,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAI,CAACtG,gBAAgB,IAAc,CAAC;IACpC,IAAIqG,UAAU,CAACE,OAAO,EAAE9L,IAAI,CAAC8L,OAAO,GAAG,IAAI;IAC3C,IAAI,IAAA3O,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,EAAE;MACtCmE,IAAI,CAAC0D,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;MAChC,IAAI,CAACoL,eAAe,CAAC/L,IAAI,CAAC0D,EAAE,EAAEsI,6BAAiB,CAAC;IAClD,CAAC,MAAM;MACLhM,IAAI,CAAC0D,EAAE,GAAG,IAAI;MACd,IAAI,CAACrF,KAAK,CAAChH,QAAQ,CAAC0C,oBAAoB,EAAE;QAAEuE,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB;MAAS,CAAC,CAAC;IACxE;IAEAsB,IAAI,CAACM,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CACjD,IAAI,CAAClF,0BACP,CAAC;IACD,IAAI,IAAI,CAAC0C,GAAG,GAAY,CAAC,EAAE;MACzBO,IAAI,CAACiM,OAAO,GAAG,IAAI,CAACT,qBAAqB,CAAC,SAAS,CAAC;IACtD;IACA,MAAMU,IAAI,GAAG,IAAI,CAACjM,SAAS,CAAoB,CAAC;IAChDiM,IAAI,CAACA,IAAI,GAAG,IAAI,CAAC7B,QAAQ,CAAC,IAAI,CAACpF,wBAAwB,CAACvI,IAAI,CAAC,IAAI,CAAC,CAAC;IACnEsD,IAAI,CAACkM,IAAI,GAAG,IAAI,CAAC1L,UAAU,CAAC0L,IAAI,EAAE,iBAAiB,CAAC;IACpD,OAAO,IAAI,CAAC1L,UAAU,CAACR,IAAI,EAAE,wBAAwB,CAAC;EACxD;EAEAmM,2BAA2BA,CACzBnM,IAA8B,EACJ;IAC1BA,IAAI,CAAC0D,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IAChC,IAAI,CAACoL,eAAe,CAAC/L,IAAI,CAAC0D,EAAE,EAAE0I,wBAAY,CAAC;IAE3CpM,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACiJ,QAAQ,CAAC,MAAM;MACxCrK,IAAI,CAACM,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CACjD,IAAI,CAACzF,qBACP,CAAC;MAED,IAAI,CAACmD,MAAM,GAAM,CAAC;MAElB,IACE,IAAI,CAACwF,YAAY,IAAc,CAAC,IAChC,IAAI,CAACkD,SAAS,CAAC,CAAC,CAACxM,IAAI,OAAW,EAChC;QACA,MAAMmE,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkB,CAAC;QAC9C,IAAI,CAACvC,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAAC8C,UAAU,CAACR,IAAI,EAAE,oBAAoB,CAAC;MACpD;MAEA,OAAO,IAAI,CAAC2F,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,IAAI,CAACE,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAACrF,UAAU,CAACR,IAAI,EAAE,wBAAwB,CAAC;EACxD;EAEAqM,aAAaA,CAAIC,EAAW,EAAK;IAC/B,MAAMC,UAAU,GAAG,IAAI,CAACnP,KAAK,CAACoP,OAAO;IACrC,IAAI,CAACpP,KAAK,CAACoP,OAAO,GAAG,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI;MACF,OAAOD,EAAE,CAAC,CAAC;IACb,CAAC,SAAS;MACR,IAAI,CAAClP,KAAK,CAACoP,OAAO,GAAGD,UAAU;IACjC;EACF;EAOAlC,QAAQA,CAAIiC,EAAW,EAAK;IAC1B,MAAMG,SAAS,GAAG,IAAI,CAACrP,KAAK,CAAC6K,MAAM;IACnC,IAAI,CAAC7K,KAAK,CAAC6K,MAAM,GAAG,IAAI;IACxB,IAAI;MACF,OAAOqE,EAAE,CAAC,CAAC;IACb,CAAC,SAAS;MACR,IAAI,CAAClP,KAAK,CAAC6K,MAAM,GAAGwE,SAAS;IAC/B;EACF;EAEAxD,mCAAmCA,CAAIqD,EAAW,EAAK;IACrD,MAAMI,oCAAoC,GACxC,IAAI,CAACtP,KAAK,CAAC8L,iCAAiC;IAC9C,IAAI,CAAC9L,KAAK,CAAC8L,iCAAiC,GAAG,IAAI;IACnD,IAAI;MACF,OAAOoD,EAAE,CAAC,CAAC;IACb,CAAC,SAAS;MACR,IAAI,CAAClP,KAAK,CAAC8L,iCAAiC,GAC1CwD,oCAAoC;IACxC;EACF;EAEA/E,gCAAgCA,CAAI2E,EAAW,EAAK;IAClD,MAAMI,oCAAoC,GACxC,IAAI,CAACtP,KAAK,CAAC8L,iCAAiC;IAC9C,IAAI,CAAC9L,KAAK,CAAC8L,iCAAiC,GAAG,KAAK;IACpD,IAAI;MACF,OAAOoD,EAAE,CAAC,CAAC;IACb,CAAC,SAAS;MACR,IAAI,CAAClP,KAAK,CAAC8L,iCAAiC,GAC1CwD,oCAAoC;IACxC;EACF;EAEA3K,kBAAkBA,CAACrJ,KAAgB,EAAwB;IACzD,IAAI,IAAI,CAAC4E,KAAK,CAAC5E,KAAK,CAAC,EAAE;MACrB,OAAO,IAAI,CAACiU,mBAAmB,CAAC,CAAC;IACnC;EACF;EAEAtH,qBAAqBA,CAAC3M,KAAgB,EAAY;IAChD,OAAO,IAAI,CAAC2R,QAAQ,CAAC,MAAM;MACzB,IAAI,CAAC1K,MAAM,CAACjH,KAAK,CAAC;MAClB,OAAO,IAAI,CAACiN,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAgH,mBAAmBA,CAAA,EAAa;IAC9B,OAAO,IAAI,CAACtC,QAAQ,CAAC,MAAM;MACzB,IAAI,CAAC3M,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACiI,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAiH,iBAAiBA,CAAA,EAAmB;IAClC,MAAM5M,IAAI,GAAG,IAAI,CAACC,SAAS,CAAiB,CAAC;IAE7CD,IAAI,CAAC0D,EAAE,GAAG,IAAI,CAACpG,KAAK,IAAU,CAAC,GAC3B,KAAK,CAACuP,kBAAkB,CAAC,IAAI,CAACzP,KAAK,CAACrB,KAAK,CAAC,GAC1C,IAAI,CAAC4E,eAAe,CAAe,IAAI,CAAC;IAC5C,IAAI,IAAI,CAAClB,GAAG,GAAM,CAAC,EAAE;MACnBO,IAAI,CAAC8M,WAAW,GAAG,KAAK,CAACC,uBAAuB,CAAC,CAAC;IACpD;IACA,OAAO,IAAI,CAACvM,UAAU,CAACR,IAAI,EAAE,cAAc,CAAC;EAC9C;EAEAgN,sBAAsBA,CACpBhN,IAAiC,EACjC4L,UAGC,GAAG,CAAC,CAAC,EACe;IACrB,IAAIA,UAAU,CAACqB,KAAK,EAAEjN,IAAI,CAACiN,KAAK,GAAG,IAAI;IACvC,IAAIrB,UAAU,CAACE,OAAO,EAAE9L,IAAI,CAAC8L,OAAO,GAAG,IAAI;IAC3C,IAAI,CAACvG,gBAAgB,IAAS,CAAC;IAC/BvF,IAAI,CAAC0D,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IAChC,IAAI,CAACoL,eAAe,CAClB/L,IAAI,CAAC0D,EAAE,EACP1D,IAAI,CAACiN,KAAK,GAAGC,8BAAkB,GAAGC,wBACpC,CAAC;IAED,IAAI,CAACxN,MAAM,EAAU,CAAC;IACtBK,IAAI,CAACgF,OAAO,GAAG,IAAI,CAAC7F,oBAAoB,CACtC,aAAa,EACb,IAAI,CAACyN,iBAAiB,CAAClQ,IAAI,CAAC,IAAI,CAClC,CAAC;IACD,IAAI,CAACiD,MAAM,EAAU,CAAC;IACtB,OAAO,IAAI,CAACa,UAAU,CAACR,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAoN,kBAAkBA,CAAA,EAAoB;IACpC,MAAMpN,IAAI,GAAG,IAAI,CAACC,SAAS,CAAkB,CAAC;IAC9C,IAAI,CAACoN,KAAK,CAACC,KAAK,CAACC,uBAAW,CAAC;IAE7B,IAAI,CAAC5N,MAAM,EAAU,CAAC;IAEtB,KAAK,CAAC6N,2BAA2B,CAC9BxN,IAAI,CAACkM,IAAI,GAAG,EAAE,EACElQ,SAAS,EACX,IAAI,GAErB,CAAC;IACD,IAAI,CAACqR,KAAK,CAACI,IAAI,CAAC,CAAC;IACjB,OAAO,IAAI,CAACjN,UAAU,CAACR,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEA0N,mCAAmCA,CACjC1N,IAAmC,EACnC2N,MAAe,GAAG,KAAK,EACA;IACvB3N,IAAI,CAAC0D,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IAEhC,IAAI,CAACgN,MAAM,EAAE;MACX,IAAI,CAAC5B,eAAe,CAAC/L,IAAI,CAAC0D,EAAE,EAAEkK,6BAAiB,CAAC;IAClD;IAEA,IAAI,IAAI,CAACnO,GAAG,GAAO,CAAC,EAAE;MACpB,MAAMoO,KAAK,GAAG,IAAI,CAAC5N,SAAS,CAAwB,CAAC;MACrD,IAAI,CAACyN,mCAAmC,CAACG,KAAK,EAAE,IAAI,CAAC;MAErD7N,IAAI,CAACkM,IAAI,GAAG2B,KAAK;IACnB,CAAC,MAAM;MACL,IAAI,CAACR,KAAK,CAACC,KAAK,CAACQ,2BAAe,CAAC;MACjC,IAAI,CAACC,SAAS,CAACT,KAAK,CAACU,0BAAK,CAAC;MAC3BhO,IAAI,CAACkM,IAAI,GAAG,IAAI,CAACkB,kBAAkB,CAAC,CAAC;MACrC,IAAI,CAACW,SAAS,CAACN,IAAI,CAAC,CAAC;MACrB,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC,CAAC;IACnB;IACA,OAAO,IAAI,CAACjN,UAAU,CAACR,IAAI,EAAE,qBAAqB,CAAC;EACrD;EAEAiO,uCAAuCA,CACrCjO,IAA2B,EACJ;IACvB,IAAI,IAAI,CAACmF,YAAY,IAAW,CAAC,EAAE;MACjCnF,IAAI,CAACkO,MAAM,GAAG,IAAI;MAClBlO,IAAI,CAAC0D,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;IAClC,CAAC,MAAM,IAAI,IAAI,CAACrD,KAAK,IAAU,CAAC,EAAE;MAChC0C,IAAI,CAAC0D,EAAE,GAAG,KAAK,CAACmJ,kBAAkB,CAAC,IAAI,CAACzP,KAAK,CAACrB,KAAK,CAAC;IACtD,CAAC,MAAM;MACL,IAAI,CAACoG,UAAU,CAAC,CAAC;IACnB;IACA,IAAI,IAAI,CAAC7E,KAAK,EAAU,CAAC,EAAE;MACzB,IAAI,CAAC+P,KAAK,CAACC,KAAK,CAACQ,2BAAe,CAAC;MACjC,IAAI,CAACC,SAAS,CAACT,KAAK,CAACU,0BAAK,CAAC;MAC3BhO,IAAI,CAACkM,IAAI,GAAG,IAAI,CAACkB,kBAAkB,CAAC,CAAC;MACrC,IAAI,CAACW,SAAS,CAACN,IAAI,CAAC,CAAC;MACrB,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC,CAAC;IACnB,CAAC,MAAM;MACL,IAAI,CAAC5H,SAAS,CAAC,CAAC;IAClB;IAEA,OAAO,IAAI,CAACrF,UAAU,CAACR,IAAI,EAAE,qBAAqB,CAAC;EACrD;EAEAmO,8BAA8BA,CAC5BnO,IAAyC,EACzCoO,sBAA4C,EAC5CC,QAAkB,EACW;IAC7BrO,IAAI,CAACqO,QAAQ,GAAGA,QAAQ,IAAI,KAAK;IACjCrO,IAAI,CAAC0D,EAAE,GAAG0K,sBAAsB,IAAI,IAAI,CAACzN,eAAe,CAAC,CAAC;IAC1D,IAAI,CAACoL,eAAe,CAAC/L,IAAI,CAAC0D,EAAE,EAAE4K,gCAAoB,CAAC;IACnD,IAAI,CAAC3O,MAAM,GAAM,CAAC;IAClB,MAAM4O,eAAe,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;IACrD,IACExO,IAAI,CAACyO,UAAU,KAAK,MAAM,IAC1BF,eAAe,CAAC1S,IAAI,KAAK,2BAA2B,EACpD;MACA,IAAI,CAACwC,KAAK,CAAChH,QAAQ,CAACyB,wBAAwB,EAAE;QAC5CwF,EAAE,EAAEiQ;MACN,CAAC,CAAC;IACJ;IACAvO,IAAI,CAACuO,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC1I,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAACrF,UAAU,CAACR,IAAI,EAAE,2BAA2B,CAAC;EAC3D;EAEA0O,2BAA2BA,CAAA,EAAY;IACrC,OACE,IAAI,CAACvJ,YAAY,IAAY,CAAC,IAC9B,IAAI,CAAC2B,iBAAiB,CAAC,CAAC,OAA8B;EAE1D;EAEA0H,sBAAsBA,CAAA,EAAwB;IAC5C,OAAO,IAAI,CAACE,2BAA2B,CAAC,CAAC,GACrC,IAAI,CAACC,8BAA8B,CAAC,CAAC,GACrC,IAAI,CAACtO,iBAAiB,CAA0B,KAAK,CAAC;EAC5D;EAEAsO,8BAA8BA,CAAA,EAAgC;IAC5D,MAAM3O,IAAI,GAAG,IAAI,CAACC,SAAS,CAA8B,CAAC;IAC1D,IAAI,CAACsF,gBAAgB,IAAY,CAAC;IAClC,IAAI,CAAC5F,MAAM,GAAU,CAAC;IACtB,IAAI,CAAC,IAAI,CAACrC,KAAK,IAAU,CAAC,EAAE;MAC1B,IAAI,CAAC6E,UAAU,CAAC,CAAC;IACnB;IAEAnC,IAAI,CAACuL,UAAU,GAAG,KAAK,CAACpL,aAAa,CAAC,CAAoB;IAC1D,IAAI,CAACR,MAAM,GAAU,CAAC;IACtB,IAAI,CAACiP,iBAAiB,GAAG,IAAI;IAC7B,OAAO,IAAI,CAACpO,UAAU,CAACR,IAAI,EAAE,2BAA2B,CAAC;EAC3D;EAIAyD,WAAWA,CAAIoL,CAAU,EAAK;IAC5B,MAAMzR,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0R,KAAK,CAAC,CAAC;IAChC,MAAMC,GAAG,GAAGF,CAAC,CAAC,CAAC;IACf,IAAI,CAACzR,KAAK,GAAGA,KAAK;IAClB,OAAO2R,GAAG;EACZ;EAEAC,kBAAkBA,CAChBH,CAAU,EACY;IACtB,MAAM5P,MAAM,GAAG,IAAI,CAACgQ,QAAQ,CAC1BC,KAAK,IAEHL,CAAC,CAAC,CAAC,IAAIK,KAAK,CAAC,CACjB,CAAC;IAED,IAAIjQ,MAAM,CAACkQ,OAAO,IAAI,CAAClQ,MAAM,CAACe,IAAI,EAAE;IACpC,IAAIf,MAAM,CAACmQ,KAAK,EAAE,IAAI,CAAChS,KAAK,GAAG6B,MAAM,CAACoQ,SAAS;IAE/C,OAAOpQ,MAAM,CAACe,IAAI;EACpB;EAEAjC,UAAUA,CAAI8Q,CAA8B,EAAiB;IAC3D,MAAMzR,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0R,KAAK,CAAC,CAAC;IAChC,MAAM7P,MAAM,GAAG4P,CAAC,CAAC,CAAC;IAClB,IAAI5P,MAAM,KAAKjD,SAAS,IAAIiD,MAAM,KAAK,KAAK,EAAE;MAC5C,OAAOA,MAAM;IACf;IACA,IAAI,CAAC7B,KAAK,GAAGA,KAAK;EACpB;EAEAkS,iBAAiBA,CAACC,IAAS,EAA6B;IACtD,IAAI,IAAI,CAAClM,gBAAgB,CAAC,CAAC,EAAE;MAC3B;IACF;IACA,IAAImM,SAAS,GAAG,IAAI,CAACpS,KAAK,CAACvB,IAAI;IAC/B,IAAI1D,IAAkB;IAEtB,IAAI,IAAI,CAACgN,YAAY,GAAQ,CAAC,EAAE;MAC9BqK,SAAS,KAAU;MACnBrX,IAAI,GAAG,KAAK;IACd;IAGA,OAAO,IAAI,CAACsX,kBAAkB,CAAC,MAAM;MACnC,QAAQD,SAAS;QACf;UACED,IAAI,CAACzD,OAAO,GAAG,IAAI;UACnB,OAAO,KAAK,CAAC4D,sBAAsB,CACjCH,IAAI,EACQ,KAAK,EACU,KAC7B,CAAC;QACH;UAGEA,IAAI,CAACzD,OAAO,GAAG,IAAI;UACnB,OAAO,IAAI,CAAC6D,UAAU,CACpBJ,IAAI,EACc,IAAI,EACL,KACnB,CAAC;QACH;UACE,OAAO,IAAI,CAACvC,sBAAsB,CAACuC,IAAI,EAAE;YAAEzD,OAAO,EAAE;UAAK,CAAC,CAAC;QAC7D;UACE,OAAO,IAAI,CAACmC,uCAAuC,CAACsB,IAAI,CAAC;QAC3D;QACA;UACE,IAAI,CAAC,IAAI,CAACjS,KAAK,GAAU,CAAC,IAAI,CAAC,IAAI,CAACsS,qBAAqB,CAAC,MAAM,CAAC,EAAE;YACjEL,IAAI,CAACzD,OAAO,GAAG,IAAI;YACnB,OAAO,IAAI,CAAC+D,iBAAiB,CAC3BN,IAAI,EACJpX,IAAI,IAAI,IAAI,CAACiF,KAAK,CAACrB,KAAK,EACxB,IACF,CAAC;UACH;UAGA,IAAI,CAAC4D,MAAM,GAAU,CAAC;UACtB,OAAO,IAAI,CAACqN,sBAAsB,CAACuC,IAAI,EAAE;YACvCtC,KAAK,EAAE,IAAI;YACXnB,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;UAAoB;YAClB,MAAM7M,MAAM,GAAG,IAAI,CAAC0M,2BAA2B,CAAC4D,IAAI,EAAE;cACpDzD,OAAO,EAAE;YACX,CAAC,CAAC;YACF,IAAI7M,MAAM,EAAE,OAAOA,MAAM;UAC3B;QAEA;UACE,IAAI,IAAA9B,wBAAiB,EAACqS,SAAS,CAAC,EAAE;YAChC,OAAO,IAAI,CAACM,kBAAkB,CAC5BP,IAAI,EACJ,IAAI,CAACnS,KAAK,CAACrB,KAAK,EACL,IAAI,EACE,IACnB,CAAC;UACH;MACJ;IACF,CAAC,CAAC;EACJ;EAGAgU,2BAA2BA,CAAA,EAA8B;IACvD,OAAO,IAAI,CAACD,kBAAkB,CAC5B,IAAI,CAAC7P,SAAS,CAAC,CAAC,EAChB,IAAI,CAAC7C,KAAK,CAACrB,KAAK,EACL,IAAI,EACE,IACnB,CAAC;EACH;EAEAiU,0BAA0BA,CACxBhQ,IAAmC,EACnCiQ,IAAkB,EAClBC,UAAgC,EACL;IAC3B,QAAQD,IAAI,CAACrO,IAAI;MACf,KAAK,SAAS;QAAE;UACd,MAAMuO,WAAW,GAAG,IAAI,CAACb,iBAAiB,CAACtP,IAAI,CAAC;UAChD,IAAImQ,WAAW,EAAE;YACfA,WAAW,CAACrE,OAAO,GAAG,IAAI;UAC5B;UACA,OAAOqE,WAAW;QACpB;MACA,KAAK,QAAQ;QAGX,IAAI,IAAI,CAAC7S,KAAK,EAAU,CAAC,EAAE;UACzB,IAAI,CAAC+P,KAAK,CAACC,KAAK,CAACQ,2BAAe,CAAC;UACjC,IAAI,CAACC,SAAS,CAACT,KAAK,CAACU,0BAAK,CAAC;UAC3B,MAAMoC,GAAG,GAAGpQ,IAAI;UAChBoQ,GAAG,CAAClC,MAAM,GAAG,IAAI;UACjBkC,GAAG,CAAC1M,EAAE,GAAGuM,IAAI;UACbG,GAAG,CAAClE,IAAI,GAAG,IAAI,CAACkB,kBAAkB,CAAC,CAAC;UACpC,IAAI,CAACC,KAAK,CAACI,IAAI,CAAC,CAAC;UACjB,IAAI,CAACM,SAAS,CAACN,IAAI,CAAC,CAAC;UACrB,OAAO,IAAI,CAACjN,UAAU,CAAC4P,GAAG,EAAE,qBAAqB,CAAC;QACpD;QACA;MAEF;QACE,OAAO,IAAI,CAACN,kBAAkB,CAC5B9P,IAAI,EACJiQ,IAAI,CAACrO,IAAI,EACE,KAAK,EAChBsO,UACF,CAAC;IACL;EACF;EAGAJ,kBAAkBA,CAChB9P,IAAS,EACTjE,KAAa,EACb2B,IAAa,EACbwS,UAAgC,EACE;IAElC,QAAQnU,KAAK;MACX,KAAK,UAAU;QACb,IACE,IAAI,CAACsU,qBAAqB,CAAC3S,IAAI,CAAC,KAC/B,IAAI,CAACJ,KAAK,GAAU,CAAC,IAAI,IAAAH,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,CAAC,EAC7D;UACA,OAAO,IAAI,CAACyU,0BAA0B,CAACtQ,IAAI,EAAEkQ,UAAU,CAAC;QAC1D;QACA;MAEF,KAAK,QAAQ;QACX,IAAI,IAAI,CAACG,qBAAqB,CAAC3S,IAAI,CAAC,EAAE;UACpC,IAAI,IAAI,CAACJ,KAAK,IAAU,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC2Q,uCAAuC,CAACjO,IAAI,CAAC;UAC3D,CAAC,MAAM,IAAI,IAAA7C,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,EAAE;YAC7C,OAAO,IAAI,CAAC6R,mCAAmC,CAAC1N,IAAI,CAAC;UACvD;QACF;QACA;MAEF,KAAK,WAAW;QACd,IACE,IAAI,CAACqQ,qBAAqB,CAAC3S,IAAI,CAAC,IAChC,IAAAP,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,EAClC;UACA,OAAO,IAAI,CAAC6R,mCAAmC,CAAC1N,IAAI,CAAC;QACvD;QACA;MAEF,KAAK,MAAM;QACT,IACE,IAAI,CAACqQ,qBAAqB,CAAC3S,IAAI,CAAC,IAChC,IAAAP,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,EAClC;UACA,OAAO,IAAI,CAACsQ,2BAA2B,CAACnM,IAAI,CAAC;QAC/C;QACA;IACJ;EACF;EAEAqQ,qBAAqBA,CAAC3S,IAAa,EAAE;IACnC,IAAIA,IAAI,EAAE;MACR,IAAI,IAAI,CAACmO,qBAAqB,CAAC,CAAC,EAAE,OAAO,KAAK;MAC9C,IAAI,CAACnO,IAAI,CAAC,CAAC;MACX,OAAO,IAAI;IACb;IACA,OAAO,CAAC,IAAI,CAAC2F,gBAAgB,CAAC,CAAC;EACjC;EAEAkN,mCAAmCA,CACjC7R,QAAkB,EACqB;IACvC,IAAI,CAAC,IAAI,CAACpB,KAAK,GAAM,CAAC,EAAE;IAExB,MAAMkT,yBAAyB,GAAG,IAAI,CAACpT,KAAK,CAACqT,sBAAsB;IACnE,IAAI,CAACrT,KAAK,CAACqT,sBAAsB,GAAG,IAAI;IAExC,MAAM1B,GAAkD,GACtD,IAAI,CAACC,kBAAkB,CAAC,MAAM;MAC5B,MAAMhP,IAAI,GAAG,IAAI,CAACkH,WAAW,CAA4BxI,QAAQ,CAAC;MAClEsB,IAAI,CAACM,cAAc,GAAG,IAAI,CAAC4B,qBAAqB,CAC9C,IAAI,CAACpF,oBACP,CAAC;MAED,KAAK,CAAC4T,mBAAmB,CAAC1Q,IAAI,CAAC;MAC/BA,IAAI,CAAC2Q,UAAU,GAAG,IAAI,CAAC/F,uCAAuC,CAAC,CAAC;MAChE,IAAI,CAACjL,MAAM,GAAS,CAAC;MACrB,OAAOK,IAAI;IACb,CAAC,CAAC;IAEJ,IAAI,CAAC5C,KAAK,CAACqT,sBAAsB,GAAGD,yBAAyB;IAE7D,IAAI,CAACzB,GAAG,EAAE;IAEV,OAAO,KAAK,CAAC6B,oBAAoB,CAC/B7B,GAAG,EAC0B,IAAI,EACrB,IACd,CAAC;EACH;EAIA8B,gCAAgCA,CAAA,EAA0C;IACxE,IAAI,IAAI,CAACC,SAAS,CAAC,CAAC,OAAU,EAAE;IAChC,OAAO,IAAI,CAACvQ,oBAAoB,CAAC,CAAC;EACpC;EAEAA,oBAAoBA,CAAA,EAAmC;IACrD,MAAMP,IAAI,GAAG,IAAI,CAACC,SAAS,CAAiC,CAAC;IAC7DD,IAAI,CAACoC,MAAM,GAAG,IAAI,CAACiI,QAAQ,CAAC,MAE1B,IAAI,CAACgC,aAAa,CAAC,MAAM;MACvB,IAAI,CAAC1M,MAAM,GAAM,CAAC;MAClB,OAAO,IAAI,CAACR,oBAAoB,CAC9B,2BAA2B,EAC3B,IAAI,CAACwG,WAAW,CAACjJ,IAAI,CAAC,IAAI,CAC5B,CAAC;IACH,CAAC,CACH,CAAC;IACD,IAAIsD,IAAI,CAACoC,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAChE,KAAK,CAAChH,QAAQ,CAACsB,kBAAkB,EAAE;QAAE2F,EAAE,EAAE0B;MAAK,CAAC,CAAC;IACvD,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC5C,KAAK,CAAC6K,MAAM,IAAI,IAAI,CAAC8I,UAAU,CAAC,CAAC,KAAKC,cAAE,CAACC,KAAK,EAAE;MAG/D,IAAI,CAACC,YAAY,CAAC,CAAC;IACrB;IACA,IAAI,CAACvR,MAAM,GAAM,CAAC;IAClB,OAAO,IAAI,CAACa,UAAU,CAACR,IAAI,EAAE,8BAA8B,CAAC;EAC9D;EAEAmR,oBAAoBA,CAAA,EAAY;IAC9B,OAAO,IAAAC,gCAAyB,EAAC,IAAI,CAAChU,KAAK,CAACvB,IAAI,CAAC;EACnD;EAMAwV,wBAAwBA,CAAA,EAAY;IAClC,IAAI,IAAI,CAACF,oBAAoB,CAAC,CAAC,EAAE,OAAO,KAAK;IAC7C,OAAO,KAAK,CAACE,wBAAwB,CAAC,CAAC;EACzC;EAEAC,uBAAuBA,CACrBC,KAA4B,EAC5BrB,UAAyB,EACU;IAEnC,MAAMxR,QAAQ,GAAG,IAAI,CAACtB,KAAK,CAACsB,QAAQ;IAEpC,MAAMV,QAAsB,GAAG,CAAC,CAAC;IACjC,IAAI,CAACvB,gBAAgB,CACnB;MACEE,gBAAgB,EAAE,CAChB,QAAQ,EACR,SAAS,EACT,WAAW,EACX,UAAU,EACV,UAAU;IAEd,CAAC,EACDqB,QACF,CAAC;IACD,MAAMY,aAAa,GAAGZ,QAAQ,CAACY,aAAa;IAC5C,MAAM4S,QAAQ,GAAGxT,QAAQ,CAACwT,QAAQ;IAClC,MAAMzN,QAAQ,GAAG/F,QAAQ,CAAC+F,QAAQ;IAClC,IACE,EAAEwN,KAAK,GAAGtO,2BAAqB,CAACwO,qBAAqB,CAAC,KACrD7S,aAAa,IAAImF,QAAQ,IAAIyN,QAAQ,CAAC,EACvC;MACA,IAAI,CAACnT,KAAK,CAAChH,QAAQ,CAACiE,2BAA2B,EAAE;QAAEgD,EAAE,EAAEI;MAAS,CAAC,CAAC;IACpE;IAEA,MAAMmC,IAAI,GAAG,IAAI,CAAC6Q,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,4BAA4B,CAAC9Q,IAAI,EAAE0Q,KAAK,CAAC;IAC9C,MAAMK,GAAG,GAAG,IAAI,CAACF,iBAAiB,CAAC7Q,IAAI,CAAC3C,GAAG,CAAC2T,KAAK,EAAEhR,IAAI,CAAC;IACxD,IAAIjC,aAAa,IAAImF,QAAQ,IAAIyN,QAAQ,EAAE;MACzC,MAAMM,EAAE,GAAG,IAAI,CAAC5K,WAAW,CAAwBxI,QAAQ,CAAC;MAC5D,IAAIwR,UAAU,CAAC7N,MAAM,EAAE;QACrByP,EAAE,CAAC5B,UAAU,GAAGA,UAAU;MAC5B;MACA,IAAItR,aAAa,EAAEkT,EAAE,CAAClT,aAAa,GAAGA,aAAa;MACnD,IAAImF,QAAQ,EAAE+N,EAAE,CAAC/N,QAAQ,GAAGA,QAAQ;MACpC,IAAIyN,QAAQ,EAAEM,EAAE,CAACN,QAAQ,GAAGA,QAAQ;MACpC,IAAII,GAAG,CAAC/V,IAAI,KAAK,YAAY,IAAI+V,GAAG,CAAC/V,IAAI,KAAK,mBAAmB,EAAE;QACjE,IAAI,CAACwC,KAAK,CAAChH,QAAQ,CAACsE,gCAAgC,EAAE;UAAE2C,EAAE,EAAEwT;QAAG,CAAC,CAAC;MACnE;MACAA,EAAE,CAACC,SAAS,GAAGH,GAAgD;MAC/D,OAAO,IAAI,CAACpR,UAAU,CAACsR,EAAE,EAAE,qBAAqB,CAAC;IACnD;IAEA,IAAI5B,UAAU,CAAC7N,MAAM,EAAE;MACrBxB,IAAI,CAACqP,UAAU,GAAGA,UAAU;IAC9B;IAEA,OAAO0B,GAAG;EACZ;EAEAI,iBAAiBA,CAAChS,IAAuC,EAAE;IACzD,OACGA,IAAI,CAACnE,IAAI,KAAK,qBAAqB,IAClC,KAAK,CAACmW,iBAAiB,CAAChS,IAAI,CAAC+R,SAAS,CAAC,IACzC,KAAK,CAACC,iBAAiB,CAAChS,IAAI,CAAC;EAEjC;EAEAiS,yBAAyBA,CAACjS,IAAwB,EAAE;IAClD,KAAK,MAAMkS,KAAK,IAAIlS,IAAI,CAACoC,MAAM,EAAE;MAC/B,IACE8P,KAAK,CAACrW,IAAI,KAAK,YAAY,IAC1BqW,KAAK,CAASlO,QAAQ,IACvB,CAAC,IAAI,CAAC5G,KAAK,CAAC+U,gBAAgB,EAC5B;QACA,IAAI,CAAC9T,KAAK,CAAChH,QAAQ,CAACgD,iBAAiB,EAAE;UAAEiE,EAAE,EAAE4T;QAAM,CAAC,CAAC;MACvD;IACF;EACF;EAEAE,0BAA0BA,CACxBpS,IAAuC,EACvCoC,MAAsB,EACtBiQ,gBAAkC,EAC5B;IACN,KAAK,CAACD,0BAA0B,CAACpS,IAAI,EAAEoC,MAAM,EAAEiQ,gBAAgB,CAAC;IAChE,IAAI,CAACJ,yBAAyB,CAACjS,IAAI,CAAC;EACtC;EAEAsS,0BAA0BA,CAMxBtS,IAAe,EAAEnE,IAAe,EAAE0W,QAAiB,GAAG,KAAK,EAAK;IAChE,IAAI,IAAI,CAACjV,KAAK,GAAS,CAAC,EAAE;MACxB0C,IAAI,CAAC2Q,UAAU,GAAG,IAAI,CAAC7N,oCAAoC,GAAS,CAAC;IACvE;IAEA,MAAM0P,YAAY,GAChB3W,IAAI,KAAK,qBAAqB,GAC1B,mBAAmB,GACnBA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,oBAAoB,GACvD,iBAAiB,GACjBG,SAAS;IACf,IAAIwW,YAAY,IAAI,CAAC,IAAI,CAAClV,KAAK,EAAU,CAAC,IAAI,IAAI,CAAC+F,gBAAgB,CAAC,CAAC,EAAE;MACrE,OAAO,IAAI,CAAC7C,UAAU,CAACR,IAAI,EAAEwS,YAAY,CAAC;IAC5C;IACA,IAAIA,YAAY,KAAK,mBAAmB,IAAI,IAAI,CAACpV,KAAK,CAAC+U,gBAAgB,EAAE;MACvE,IAAI,CAAC9T,KAAK,CAAChH,QAAQ,CAACgB,gCAAgC,EAAE;QAAEiG,EAAE,EAAE0B;MAAK,CAAC,CAAC;MACnE,IAAKA,IAAI,CAAmC8L,OAAO,EAAE;QACnD,OAAO,KAAK,CAACwG,0BAA0B,CAACtS,IAAI,EAAEwS,YAAY,EAAED,QAAQ,CAAC;MACvE;IACF;IACA,IAAI,CAACN,yBAAyB,CAACjS,IAAI,CAAC;IAEpC,OAAO,KAAK,CAACsS,0BAA0B,CAACtS,IAAI,EAAEnE,IAAI,EAAE0W,QAAQ,CAAC;EAC/D;EAEAE,2BAA2BA,CAACzS,IAAgB,EAAQ;IAClD,IAAI,CAACA,IAAI,CAACkM,IAAI,IAAIlM,IAAI,CAAC0D,EAAE,EAAE;MAGzB,IAAI,CAACqI,eAAe,CAAC/L,IAAI,CAAC0D,EAAE,EAAEgP,2BAAe,CAAC;IAChD,CAAC,MAAM;MACL,KAAK,CAACD,2BAA2B,CAACzS,IAAI,CAAC;IACzC;EACF;EAEA2S,0BAA0BA,CAACC,KAA6C,EAAE;IACxEA,KAAK,CAACzM,OAAO,CAACnG,IAAI,IAAI;MACpB,IAAI,CAAAA,IAAI,oBAAJA,IAAI,CAAEnE,IAAI,MAAK,sBAAsB,EAAE;QACzC,IAAI,CAACwC,KAAK,CAAChH,QAAQ,CAACmE,wBAAwB,EAAE;UAC5C8C,EAAE,EAAE0B,IAAI,CAACoB;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAyR,gBAAgBA,CACdC,QAAgD,EAEhDC,UAAoB,EACoB;IAKxC,IAAI,CAACJ,0BAA0B,CAACG,QAAQ,CAAC;IACzC,OAAOA,QAAQ;EACjB;EAEAE,cAAcA,CACZC,KAAgB,EAChBC,YAAqB,EACrBC,OAAgB,EAChBC,mBAA6C,EACN;IACvC,MAAMpT,IAAI,GAAG,KAAK,CAACgT,cAAc,CAC/BC,KAAK,EACLC,YAAY,EACZC,OAAO,EACPC,mBACF,CAAC;IAED,IAAIpT,IAAI,CAACnE,IAAI,KAAK,iBAAiB,EAAE;MACnC,IAAI,CAAC8W,0BAA0B,CAAC3S,IAAI,CAACqT,QAAQ,CAAC;IAChD;IAEA,OAAOrT,IAAI;EACb;EAEAsT,cAAcA,CACZC,IAAkB,EAElB7U,QAAkB,EAClB8U,OAAmC,EACnCpW,KAA4B,EACd;IACd,IAAI,CAAC,IAAI,CAACI,qBAAqB,CAAC,CAAC,IAAI,IAAI,CAACF,KAAK,GAAQ,CAAC,EAAE;MAIxD,IAAI,CAACF,KAAK,CAACqW,kBAAkB,GAAG,KAAK;MACrC,IAAI,CAAC/V,IAAI,CAAC,CAAC;MAEX,MAAMgW,iBAAiB,GACrB,IAAI,CAACxM,WAAW,CAAwBxI,QAAQ,CAAC;MACnDgV,iBAAiB,CAACnI,UAAU,GAAGgI,IAAI;MACnC,OAAO,IAAI,CAAC/S,UAAU,CAACkT,iBAAiB,EAAE,qBAAqB,CAAC;IAClE;IAEA,IAAIC,cAAc,GAAG,KAAK;IAC1B,IACE,IAAI,CAACrW,KAAK,GAAe,CAAC,IAC1B,IAAI,CAACwJ,iBAAiB,CAAC,CAAC,OAAuB,EAC/C;MACA,IAAI0M,OAAO,EAAE;QACXpW,KAAK,CAACwW,IAAI,GAAG,IAAI;QACjB,OAAOL,IAAI;MACb;MACAnW,KAAK,CAACyW,mBAAmB,GAAGF,cAAc,GAAG,IAAI;MACjD,IAAI,CAACjW,IAAI,CAAC,CAAC;IACb;IAGA,IAAI,IAAI,CAACJ,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,GAAa,CAAC,EAAE;MACjD,IAAIwW,oBAAoB;MAIxB,MAAM7U,MAAM,GAAG,IAAI,CAAC+P,kBAAkB,CAAC,MAAM;QAC3C,IAAI,CAACwE,OAAO,IAAI,IAAI,CAACO,oBAAoB,CAACR,IAAI,CAAC,EAAE;UAG/C,MAAMS,YAAY,GAChB,IAAI,CAACzD,mCAAmC,CAAC7R,QAAQ,CAAC;UACpD,IAAIsV,YAAY,EAAE;YAChB,OAAOA,YAAY;UACrB;QACF;QAEA,MAAMC,aAAa,GAAG,IAAI,CAACpD,gCAAgC,CAAC,CAAC;QAC7D,IAAI,CAACoD,aAAa,EAAE;QAEpB,IAAIN,cAAc,IAAI,CAAC,IAAI,CAACrW,KAAK,GAAU,CAAC,EAAE;UAC5CwW,oBAAoB,GAAG,IAAI,CAAC1W,KAAK,CAAC+G,WAAW,CAAC,CAAC;UAC/C;QACF;QAEA,IAAI,IAAA+P,sBAAe,EAAC,IAAI,CAAC9W,KAAK,CAACvB,IAAI,CAAC,EAAE;UACpC,MAAMoD,MAAM,GAAG,KAAK,CAACkV,6BAA6B,CAChDZ,IAAI,EAEJ7U,QAAQ,EACRtB,KACF,CAAC;UACD6B,MAAM,CAACqB,cAAc,GAAG2T,aAAa;UACrC,OAAOhV,MAAM;QACf;QAEA,IAAI,CAACuU,OAAO,IAAI,IAAI,CAAC/T,GAAG,GAAU,CAAC,EAAE;UACnC,MAAMO,IAAI,GAAG,IAAI,CAACkH,WAAW,CAE3BxI,QAAQ,CAAC;UACXsB,IAAI,CAACoU,MAAM,GAAGb,IAAI;UAGlBvT,IAAI,CAACqU,SAAS,GAAG,IAAI,CAACC,4BAA4B,KAE5B,KACtB,CAAC;UAGD,IAAI,CAAC3B,0BAA0B,CAAC3S,IAAI,CAACqU,SAAS,CAAC;UAE/CrU,IAAI,CAACM,cAAc,GAAG2T,aAAa;UACnC,IAAI7W,KAAK,CAACyW,mBAAmB,EAAE;YAC5B7T,IAAI,CAAsCgE,QAAQ,GACjD2P,cAAc;UAClB;UAEA,OAAO,IAAI,CAACY,oBAAoB,CAACvU,IAAI,EAAE5C,KAAK,CAACyW,mBAAmB,CAAC;QACnE;QAEA,MAAMW,SAAS,GAAG,IAAI,CAACpX,KAAK,CAACvB,IAAI;QACjC,IAEE2Y,SAAS,OAAU,IAEnBA,SAAS,OAAiB,IAEzBA,SAAS,OAAc,IACtB,IAAAC,8BAAuB,EAACD,SAAS,CAAC,IAClC,CAAC,IAAI,CAAChX,qBAAqB,CAAC,CAAE,EAChC;UAEA;QACF;QAEA,MAAMwC,IAAI,GAAG,IAAI,CAACkH,WAAW,CAA8BxI,QAAQ,CAAC;QACpEsB,IAAI,CAACuL,UAAU,GAAGgI,IAAI;QACtBvT,IAAI,CAACM,cAAc,GAAG2T,aAAa;QACnC,OAAO,IAAI,CAACzT,UAAU,CAACR,IAAI,EAAE,2BAA2B,CAAC;MAC3D,CAAC,CAAC;MAEF,IAAI8T,oBAAoB,EAAE;QACxB,IAAI,CAAC3R,UAAU,CAAC2R,oBAAoB,IAAW,CAAC;MAClD;MAEA,IAAI7U,MAAM,EAAE;QACV,IACEA,MAAM,CAACpD,IAAI,KAAK,2BAA2B,KAC1C,IAAI,CAACyB,KAAK,GAAO,CAAC,IAChB,IAAI,CAACA,KAAK,GAAe,CAAC,IACzB,IAAI,CAACwJ,iBAAiB,CAAC,CAAC,OAA+B,CAAC,EAC5D;UACA,IAAI,CAACzI,KAAK,CACRhH,QAAQ,CAACwC,iDAAiD,EAC1D;YAAEyE,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB;UAAS,CAC5B,CAAC;QACH;QACA,OAAOO,MAAM;MACf;IACF;IAEA,OAAO,KAAK,CAACqU,cAAc,CAACC,IAAI,EAAE7U,QAAQ,EAAE8U,OAAO,EAAEpW,KAAK,CAAC;EAC7D;EAEAsX,cAAcA,CAAC1U,IAAqB,EAAQ;IAAA,IAAA2U,aAAA;IAC1C,KAAK,CAACD,cAAc,CAAC1U,IAAI,CAAC;IAE1B,MAAM;MAAEoU;IAAO,CAAC,GAAGpU,IAAI;IACvB,IACEoU,MAAM,CAACvY,IAAI,KAAK,2BAA2B,IAC3C,GAAA8Y,aAAA,GAACP,MAAM,CAACQ,KAAK,aAAZD,aAAA,CAAcE,aAAa,GAC5B;MACA7U,IAAI,CAACM,cAAc,GAAG8T,MAAM,CAAC9T,cAAc;MAC3CN,IAAI,CAACoU,MAAM,GAAGA,MAAM,CAAC7I,UAAU;IACjC;EACF;EAEAuJ,WAAWA,CACTjU,IAAkB,EAClBkU,YAAsB,EACtBC,OAAe,EACD;IACd,IAAIC,WAAoB;IACxB,IACE,IAAAC,8BAAuB,IAAO,CAAC,GAAGF,OAAO,IACzC,CAAC,IAAI,CAACxX,qBAAqB,CAAC,CAAC,KAC5B,IAAI,CAAC2H,YAAY,GAAO,CAAC,KACvB8P,WAAW,GAAG,IAAI,CAAC9P,YAAY,IAAc,CAAC,CAAC,CAAC,EACnD;MACA,MAAMnF,IAAI,GAAG,IAAI,CAACkH,WAAW,CAE3B6N,YAAY,CAAC;MACf/U,IAAI,CAACuL,UAAU,GAAG1K,IAAI;MACtBb,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACiJ,QAAQ,CAAC,MAAM;QACxC,IAAI,CAAC3M,IAAI,CAAC,CAAC;QACX,IAAI,IAAI,CAACJ,KAAK,GAAU,CAAC,EAAE;UACzB,IAAI2X,WAAW,EAAE;YACf,IAAI,CAAC5W,KAAK,CAAC+F,kBAAM,CAAC+Q,iBAAiB,EAAE;cACnC7W,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB,QAAQ;cACvB0W,OAAO,EAAE;YACX,CAAC,CAAC;UACJ;UACA,OAAO,IAAI,CAACrU,oBAAoB,CAAC,CAAC;QACpC;QAEA,OAAO,IAAI,CAAC4E,WAAW,CAAC,CAAC;MAC3B,CAAC,CAAC;MACF,IAAI,CAACnF,UAAU,CACbR,IAAI,EACJiV,WAAW,GAAG,uBAAuB,GAAG,gBAC1C,CAAC;MAED,IAAI,CAAC/D,YAAY,CAAC,CAAC;MACnB,OAAO,IAAI,CAAC4D,WAAW,CAErB9U,IAAI,EACJ+U,YAAY,EACZC,OACF,CAAC;IACH;IAEA,OAAO,KAAK,CAACF,WAAW,CAACjU,IAAI,EAAEkU,YAAY,EAAEC,OAAO,CAAC;EACvD;EAEAK,iBAAiBA,CACfC,IAAY,EACZ5W,QAAkB,EAClB6W,aAAsB,EACtBC,SAAkB,EACZ;IAGN,IAAI,CAAC,IAAI,CAACpY,KAAK,CAAC+U,gBAAgB,EAAE;MAChC,KAAK,CAACkD,iBAAiB,CAACC,IAAI,EAAE5W,QAAQ,EAAE6W,aAAa,EAAEC,SAAS,CAAC;IACnE;EACF;EAEAC,qBAAqBA,CAACzV,IAAiC,EAAE;IACvD,KAAK,CAACyV,qBAAqB,CAACzV,IAAI,CAAC;IACjC,IAAIA,IAAI,CAAC0V,MAAM,IAAI1V,IAAI,CAACyO,UAAU,KAAK,OAAO,EAAE;MAC9C,IAAI,CAACpQ,KAAK,CAAChH,QAAQ,CAAC0B,6BAA6B,EAAE;QACjDuF,EAAE,EAAE0B,IAAI,CAAC2V,UAAU,CAAC,CAAC,CAAC,CAACzX,GAAG,CAAC2T;MAC7B,CAAC,CAAC;IACJ;EACF;EAWA+D,qBAAqBA,CAAA,EAAG,CAAC;EAEzBC,sBAAsBA,CAACxH,QAAiB,EAAW;IACjD,IAAI,KAAK,CAACwH,sBAAsB,CAACxH,QAAQ,CAAC,EAAE,OAAO,IAAI;IACvD,IAAI,IAAI,CAAClJ,YAAY,IAAS,CAAC,EAAE;MAC/B,MAAM2Q,EAAE,GAAG,IAAI,CAAChP,iBAAiB,CAAC,CAAC;MACnC,OAAOuH,QAAQ,GACXyH,EAAE,QAA6B,IAAIA,EAAE,OAAuB,GAC5DA,EAAE,OAAuB;IAC/B;IACA,OAAO,CAACzH,QAAQ,IAAI,IAAI,CAAClJ,YAAY,GAAW,CAAC;EACnD;EAEA4Q,gBAAgBA,CACd/V,IAA4D,EAC5DqO,QAAiB,EACjB2H,KAAoB,EACpB9X,GAAc,EACR;IACN,KAAK,CAAC6X,gBAAgB,CAAC/V,IAAI,EAAEqO,QAAQ,EAAE2H,KAAK,EAAE9X,GAAG,CAAC;IAClD,IAAImQ,QAAQ,EAAE;MACXrO,IAAI,CAA8BiW,UAAU,GAC3CD,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO;IACvC,CAAC,MAAM;MACJhW,IAAI,CAAyByO,UAAU,GACtCuH,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,OAAO;IAC5D;EACF;EAEAE,WAAWA,CACTlW,IAA+D,EAClD;IACb,IAAI,IAAI,CAAC1C,KAAK,IAAU,CAAC,EAAE;MACzB0C,IAAI,CAACyO,UAAU,GAAG,OAAO;MACzB,OAAO,KAAK,CAACyH,WAAW,CAAClW,IAAmC,CAAC;IAC/D;IAEA,IAAImW,UAAU;IACd,IACE,IAAAhZ,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC,IAClC,IAAI,CAACiL,iBAAiB,CAAC,CAAC,OAAuB,EAC/C;MACA9G,IAAI,CAACyO,UAAU,GAAG,OAAO;MACzB,OAAO,IAAI,CAACN,8BAA8B,CACxCnO,IACF,CAAC;IACH,CAAC,MAAM,IAAI,IAAI,CAACmF,YAAY,IAAS,CAAC,EAAE;MACtC,MAAMiJ,sBAAsB,GAAG,IAAI,CAACgI,qBAAqB,CACvDpW,IAAI,EACW,KACjB,CAAC;MACD,IAAI,IAAI,CAAC8G,iBAAiB,CAAC,CAAC,OAAuB,EAAE;QACnD,OAAO,IAAI,CAACqH,8BAA8B,CACxCnO,IAAI,EACJoO,sBACF,CAAC;MACH,CAAC,MAAM;QACL+H,UAAU,GAAG,KAAK,CAACE,6BAA6B,CAC9CrW,IAAI,EACJoO,sBACF,CAAC;MACH;IACF,CAAC,MAAM;MACL+H,UAAU,GAAG,KAAK,CAACD,WAAW,CAAClW,IAAmC,CAAC;IACrE;IAIA,IACEmW,UAAU,CAAC1H,UAAU,KAAK,MAAM,IAEhC0H,UAAU,CAACR,UAAU,CAACtT,MAAM,GAAG,CAAC,IAEhC8T,UAAU,CAACR,UAAU,CAAC,CAAC,CAAC,CAAC9Z,IAAI,KAAK,wBAAwB,EAC1D;MACA,IAAI,CAACwC,KAAK,CAAChH,QAAQ,CAAC8D,sCAAsC,EAAE;QAC1DmD,EAAE,EAAE6X;MACN,CAAC,CAAC;IACJ;IAEA,OAAOA,UAAU;EACnB;EAEAG,WAAWA,CACTtW,IAAoB,EACpBkQ,UAAgC,EACnB;IACb,IAAI,IAAI,CAAC5S,KAAK,GAAW,CAAC,EAAE;MAE1B,IAAI,CAACI,IAAI,CAAC,CAAC;MACX,IAAI0Q,sBAA2C,GAAG,IAAI;MACtD,IACE,IAAI,CAACjJ,YAAY,IAAS,CAAC,IAE3B,IAAI,CAAC0Q,sBAAsB,CAAgB,KAAK,CAAC,EACjD;QACAzH,sBAAsB,GAAG,IAAI,CAACgI,qBAAqB,CACjDpW,IAAI,EACW,KACjB,CAAC;MACH,CAAC,MAAM;QACLA,IAAI,CAACyO,UAAU,GAAG,OAAO;MAC3B;MACA,OAAO,IAAI,CAACN,8BAA8B,CACxCnO,IAAI,EACJoO,sBAAsB,EACP,IACjB,CAAC;IACH,CAAC,MAAM,IAAI,IAAI,CAAC3O,GAAG,GAAM,CAAC,EAAE;MAE1B,MAAM8W,MAAM,GAAGvW,IAAoC;MACnDuW,MAAM,CAAChL,UAAU,GAAG,KAAK,CAACiL,eAAe,CAAC,CAAC;MAC3C,IAAI,CAAC3Q,SAAS,CAAC,CAAC;MAChB,IAAI,CAAC+I,iBAAiB,GAAG,IAAI;MAC7B,OAAO,IAAI,CAACpO,UAAU,CAAC+V,MAAM,EAAE,oBAAoB,CAAC;IACtD,CAAC,MAAM,IAAI,IAAI,CAAC/Q,aAAa,GAAO,CAAC,EAAE;MAErC,MAAMiR,IAAI,GAAGzW,IAA8C;MAE3D,IAAI,CAACuF,gBAAgB,IAAc,CAAC;MACpCkR,IAAI,CAAC/S,EAAE,GAAG,IAAI,CAAC/C,eAAe,CAAC,CAAC;MAChC,IAAI,CAACkF,SAAS,CAAC,CAAC;MAChB,OAAO,IAAI,CAACrF,UAAU,CAACiW,IAAI,EAAE,8BAA8B,CAAC;IAC9D,CAAC,MAAM;MACL,OAAO,KAAK,CAACH,WAAW,CACtBtW,IAAI,EACJkQ,UACF,CAAC;IACH;EACF;EAEAwG,eAAeA,CAAA,EAAY;IACzB,OACE,IAAI,CAACvR,YAAY,IAAa,CAAC,IAAI,IAAI,CAACkD,SAAS,CAAC,CAAC,CAACxM,IAAI,OAAc;EAE1E;EAEA8a,4BAA4BA,CAAA,EAAiC;IAC3D,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EAAE;MAC1B,MAAME,GAAG,GAAG,IAAI,CAAC3W,SAAS,CAAU,CAAC;MACrC,IAAI,CAACvC,IAAI,CAAC,CAAC;MACXkZ,GAAG,CAAClP,QAAQ,GAAG,IAAI;MACnB,OAAO,IAAI,CAACiI,UAAU,CAACiH,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC;IAIA,IAAI,IAAI,CAACtZ,KAAK,IAAc,CAAC,EAAE;MAC7B,MAAM2B,MAAM,GAAG,IAAI,CAAC0M,2BAA2B,CAC7C,IAAI,CAAC1L,SAAS,CAA2B,CAC3C,CAAC;MACD,IAAIhB,MAAM,EAAE,OAAOA,MAAM;IAC3B;IAEA,OAAO,KAAK,CAAC0X,4BAA4B,CAAC,CAAC;EAC7C;EAEA9G,iBAAiBA,CACf7P,IAA2B,EAC3B7H,IAAuC,EACvC0e,uBAAgC,GAAG,KAAK,EACxC;IACA,MAAM;MAAE1E;IAAiB,CAAC,GAAG,IAAI,CAAC/U,KAAK;IACvC,MAAM+S,WAAW,GAAG,KAAK,CAACN,iBAAiB,CACzC7P,IAAI,EACJ7H,IAAI,EACJ0e,uBAAuB,IAAI1E,gBAC7B,CAAC;IAED,IAAI,CAACA,gBAAgB,EAAE,OAAOhC,WAAW;IAEzC,KAAK,MAAM;MAAEzM,EAAE;MAAEoT;IAAK,CAAC,IAAI3G,WAAW,CAAC4G,YAAY,EAAE;MAEnD,IAAI,CAACD,IAAI,EAAE;MAGX,IAAI3e,IAAI,KAAK,OAAO,IAAI,CAAC,CAACuL,EAAE,CAACtC,cAAc,EAAE;QAC3C,IAAI,CAAC/C,KAAK,CAAChH,QAAQ,CAACkC,qCAAqC,EAAE;UACzD+E,EAAE,EAAEwY;QACN,CAAC,CAAC;MACJ,CAAC,MAAM,IACL,CAACE,8BAA8B,CAACF,IAAI,EAAE,IAAI,CAACG,SAAS,CAAC,QAAQ,CAAC,CAAC,EAC/D;QACA,IAAI,CAAC5Y,KAAK,CACRhH,QAAQ,CAACW,mEAAmE,EAC5E;UAAEsG,EAAE,EAAEwY;QAAK,CACb,CAAC;MACH;IACF;IAEA,OAAO3G,WAAW;EACpB;EAEA+G,qBAAqBA,CACnB3F,KAAyB,EACzBrB,UAAiC,EACpB;IACb,IAAI,IAAI,CAAC5S,KAAK,GAAU,CAAC,IAAI,IAAI,CAACsS,qBAAqB,CAAC,MAAM,CAAC,EAAE;MAC/D,MAAM5P,IAAI,GAAG,IAAI,CAACC,SAAS,CAAsB,CAAC;MAClD,IAAI,CAACN,MAAM,GAAU,CAAC;MACtB,OAAO,IAAI,CAACqN,sBAAsB,CAAChN,IAAI,EAAE;QAAEiN,KAAK,EAAE;MAAK,CAAC,CAAC;IAC3D;IAEA,IAAI,IAAI,CAAC9H,YAAY,IAAS,CAAC,EAAE;MAC/B,OAAO,IAAI,CAAC6H,sBAAsB,CAChC,IAAI,CAAC/M,SAAS,CAAsB,CACtC,CAAC;IACH;IAEA,IAAI,IAAI,CAACkF,YAAY,IAAc,CAAC,EAAE;MACpC,MAAMlG,MAAM,GAAG,IAAI,CAAC0M,2BAA2B,CAAC,IAAI,CAAC1L,SAAS,CAAC,CAAC,CAAC;MACjE,IAAIhB,MAAM,EAAE,OAAOA,MAAM;IAC3B;IAEA,OAAO,KAAK,CAACiY,qBAAqB,CAAC3F,KAAK,EAAErB,UAAU,CAAC;EACvD;EAEAiH,mBAAmBA,CAAA,EAAuC;IACxD,OAAO,IAAI,CAACxZ,eAAe,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;EACjE;EAEAyZ,kBAAkBA,CAACC,MAAW,EAAEpe,SAAgC,EAAW;IACzE,OAAOA,SAAS,CAACqe,IAAI,CAAC/e,QAAQ,IAAI;MAChC,IAAI0D,kBAAkB,CAAC1D,QAAQ,CAAC,EAAE;QAChC,OAAO8e,MAAM,CAACzY,aAAa,KAAKrG,QAAQ;MAC1C;MACA,OAAO,CAAC,CAAC8e,MAAM,CAAC9e,QAAQ,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAuF,uBAAuBA,CAAA,EAAG;IACxB,OACE,IAAI,CAACqH,YAAY,IAAW,CAAC,IAC7B,IAAI,CAAC2B,iBAAiB,CAAC,CAAC,QAA6B;EAEzD;EAEAyQ,gBAAgBA,CACdC,SAAsB,EACtBH,MAAW,EACXja,KAA8B,EACxB;IACN,MAAMnE,SAAS,GAAG,CAChB,SAAS,EACT,SAAS,EACT,QAAQ,EACR,WAAW,EACX,UAAU,EACV,UAAU,EACV,UAAU,EACV,QAAQ,CACA;IACV,IAAI,CAACwD,gBAAgB,CACnB;MACEE,gBAAgB,EAAE1D,SAAS;MAC3B2D,mBAAmB,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;MAClCgB,6BAA6B,EAAE,IAAI;MACnCf,aAAa,EAAExF,QAAQ,CAACqC;IAC1B,CAAC,EACD2d,MACF,CAAC;IAED,MAAMI,gCAAgC,GAAGA,CAAA,KAAM;MAC7C,IAAI,IAAI,CAAC3Z,uBAAuB,CAAC,CAAC,EAAE;QAClC,IAAI,CAACJ,IAAI,CAAC,CAAC;QACX,IAAI,CAACA,IAAI,CAAC,CAAC;QACX,IAAI,IAAI,CAAC0Z,kBAAkB,CAACC,MAAM,EAAEpe,SAAS,CAAC,EAAE;UAC9C,IAAI,CAACoF,KAAK,CAAChH,QAAQ,CAAC2D,6BAA6B,EAAE;YACjDsD,EAAE,EAAE,IAAI,CAAClB,KAAK,CAAC+G,WAAW,CAAC;UAC7B,CAAC,CAAC;QACJ;QACA,KAAK,CAACuT,qBAAqB,CAACF,SAAS,EAAEH,MAAuB,CAAC;MACjE,CAAC,MAAM;QACL,IAAI,CAACM,4BAA4B,CAC/BH,SAAS,EACTH,MAAM,EACNja,KAAK,EACL,CAAC,CAACia,MAAM,CAACO,MACX,CAAC;MACH;IACF,CAAC;IACD,IAAIP,MAAM,CAACvL,OAAO,EAAE;MAClB,IAAI,CAAC2D,kBAAkB,CAACgI,gCAAgC,CAAC;IAC3D,CAAC,MAAM;MACLA,gCAAgC,CAAC,CAAC;IACpC;EACF;EAEAE,4BAA4BA,CAC1BH,SAAsB,EACtBH,MAAkD,EAClDja,KAA8B,EAC9Bya,QAAiB,EACX;IACN,MAAMjT,GAAG,GAAG,IAAI,CAACpB,wBAAwB,CACvC6T,MACF,CAAC;IACD,IAAIzS,GAAG,EAAE;MACP4S,SAAS,CAACtL,IAAI,CAAChN,IAAI,CAAC0F,GAAG,CAAC;MAExB,IAAKyS,MAAM,CAAS3P,QAAQ,EAAE;QAC5B,IAAI,CAACrJ,KAAK,CAAChH,QAAQ,CAAC6B,yBAAyB,EAAE;UAAEoF,EAAE,EAAE+Y;QAAO,CAAC,CAAC;MAChE;MACA,IAAKA,MAAM,CAASzY,aAAa,EAAE;QACjC,IAAI,CAACP,KAAK,CAAChH,QAAQ,CAAC8B,8BAA8B,EAAE;UAClDmF,EAAE,EAAE+Y,MAAM;UACV9e,QAAQ,EAAG8e,MAAM,CAASzY;QAC5B,CAAC,CAAC;MACJ;MACA,IAAKyY,MAAM,CAASvL,OAAO,EAAE;QAC3B,IAAI,CAACzN,KAAK,CAAChH,QAAQ,CAAC+B,wBAAwB,EAAE;UAAEkF,EAAE,EAAE+Y;QAAO,CAAC,CAAC;MAC/D;MACA,IAAKA,MAAM,CAAS7F,QAAQ,EAAE;QAC5B,IAAI,CAACnT,KAAK,CAAChH,QAAQ,CAACgC,yBAAyB,EAAE;UAAEiF,EAAE,EAAE+Y;QAAO,CAAC,CAAC;MAChE;MAEA;IACF;IAEA,IAAI,CAAC,IAAI,CAACja,KAAK,CAAC0a,eAAe,IAAKT,MAAM,CAAS3P,QAAQ,EAAE;MAC3D,IAAI,CAACrJ,KAAK,CAAChH,QAAQ,CAAC4C,iCAAiC,EAAE;QACrDqE,EAAE,EAAE+Y;MACN,CAAC,CAAC;IACJ;IAEA,IAAKA,MAAM,CAAS7F,QAAQ,EAAE;MAC5B,IAAI,CAACpU,KAAK,CAAC2a,aAAa,EAAE;QACxB,IAAI,CAAC1Z,KAAK,CAAChH,QAAQ,CAAC+C,qBAAqB,EAAE;UAAEkE,EAAE,EAAE+Y;QAAO,CAAC,CAAC;MAC5D;IACF;IAIA,KAAK,CAACM,4BAA4B,CAChCH,SAAS,EACTH,MAAM,EACNja,KAAK,EACLya,QACF,CAAC;EACH;EAEAG,4BAA4BA,CAC1BC,YAAsE,EAChE;IACN,MAAMjU,QAAQ,GAAG,IAAI,CAACvE,GAAG,GAAY,CAAC;IACtC,IAAIuE,QAAQ,EAAEiU,YAAY,CAACjU,QAAQ,GAAG,IAAI;IAE1C,IAAKiU,YAAY,CAASlU,QAAQ,IAAI,IAAI,CAACzG,KAAK,GAAU,CAAC,EAAE;MAC3D,IAAI,CAACe,KAAK,CAAChH,QAAQ,CAACU,sBAAsB,EAAE;QAAEuG,EAAE,EAAE2Z;MAAa,CAAC,CAAC;IACnE;IAEA,IAAKA,YAAY,CAASnM,OAAO,IAAI,IAAI,CAACxO,KAAK,GAAU,CAAC,EAAE;MAC1D,IAAI,CAACe,KAAK,CAAChH,QAAQ,CAACS,qBAAqB,EAAE;QAAEwG,EAAE,EAAE2Z;MAAa,CAAC,CAAC;IAClE;EACF;EAOAC,wBAAwBA,CACtBlY,IAAmC,EACnCiQ,IAAkB,EAClBC,UAAgC,EACnB;IACb,MAAMuG,IAAI,GACRxG,IAAI,CAACpU,IAAI,KAAK,YAAY,GAEtB,IAAI,CAACmU,0BAA0B,CAAChQ,IAAI,EAAEiQ,IAAI,EAAEC,UAAU,CAAC,GACvDlU,SAAS;IACf,OAAOya,IAAI,IAAI,KAAK,CAACyB,wBAAwB,CAAClY,IAAI,EAAEiQ,IAAI,EAAEC,UAAU,CAAC;EACvE;EAIAiI,4BAA4BA,CAAA,EAAY;IACtC,IAAI,IAAI,CAAChH,oBAAoB,CAAC,CAAC,EAAE,OAAO,IAAI;IAC5C,OAAO,KAAK,CAACgH,4BAA4B,CAAC,CAAC;EAC7C;EAGAC,gBAAgBA,CACdnI,IAAkB,EAElBvR,QAAkB,EAClB0U,mBAA6C,EAC/B;IAGd,IAAI,CAAC,IAAI,CAAChW,KAAK,CAACqT,sBAAsB,IAAI,CAAC,IAAI,CAACnT,KAAK,GAAY,CAAC,EAAE;MAClE,OAAO,KAAK,CAAC8a,gBAAgB,CAC3BnI,IAAI,EAEJvR,QAAQ,EACR0U,mBACF,CAAC;IACH;IAEA,MAAMnU,MAAM,GAAG,IAAI,CAACgQ,QAAQ,CAAC,MAC3B,KAAK,CAACmJ,gBAAgB,CAACnI,IAAI,EAAEvR,QAAQ,CACvC,CAAC;IAED,IAAI,CAACO,MAAM,CAACe,IAAI,EAAE;MAChB,IAAIf,MAAM,CAACmQ,KAAK,EAAE;QAEhB,KAAK,CAACiJ,0BAA0B,CAACjF,mBAAmB,EAAEnU,MAAM,CAACmQ,KAAK,CAAC;MACrE;MAEA,OAAOa,IAAI;IACb;IACA,IAAIhR,MAAM,CAACmQ,KAAK,EAAE,IAAI,CAAChS,KAAK,GAAG6B,MAAM,CAACoQ,SAAS;IAC/C,OAAOpQ,MAAM,CAACe,IAAI;EACpB;EAIAsY,cAAcA,CACZtY,IAAkB,EAElBtB,QAAkB,EACJ;IACdsB,IAAI,GAAG,KAAK,CAACsY,cAAc,CAACtY,IAAI,EAAEtB,QAAQ,CAAC;IAC3C,IAAI,IAAI,CAACe,GAAG,GAAY,CAAC,EAAE;MACzBO,IAAI,CAACgE,QAAQ,GAAG,IAAI;MAIpB,IAAI,CAACL,gBAAgB,CAAC3D,IAAI,CAAC;IAC7B;IAEA,IAAI,IAAI,CAAC1C,KAAK,GAAS,CAAC,EAAE;MACxB,MAAMib,YAAY,GAAG,IAAI,CAACrR,WAAW,CAAyBxI,QAAQ,CAAC;MACvE6Z,YAAY,CAAChN,UAAU,GAAGvL,IAAI;MAC9BuY,YAAY,CAACnX,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAE1D,OAAO,IAAI,CAACb,UAAU,CAAC+X,YAAY,EAAE,sBAAsB,CAAC;IAC9D;IAEA,OAAOvY,IAAI;EACb;EAEAwY,sBAAsBA,CACpBxY,IAA8B,EACI;IAClC,IAAI,CAAC,IAAI,CAAC5C,KAAK,CAAC+U,gBAAgB,IAAI,IAAI,CAAChN,YAAY,IAAY,CAAC,EAAE;MAClE,OAAO,IAAI,CAACsK,kBAAkB,CAAC,MAAM,IAAI,CAAC+I,sBAAsB,CAACxY,IAAI,CAAC,CAAC;IACzE;IAGA,MAAMtB,QAAQ,GAAG,IAAI,CAACtB,KAAK,CAACsB,QAAQ;IAEpC,MAAM+Z,SAAS,GAAG,IAAI,CAACjT,aAAa,IAAY,CAAC;IAEjD,IACEiT,SAAS,KACR,IAAI,CAACtT,YAAY,IAAY,CAAC,IAAI,CAAC,IAAI,CAACgT,4BAA4B,CAAC,CAAC,CAAC,EACxE;MACA,MAAM,IAAI,CAAC9Z,KAAK,CAAChH,QAAQ,CAACwB,iCAAiC,EAAE;QAC3DyF,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB;MACjB,CAAC,CAAC;IACJ;IAEA,MAAMga,YAAY,GAAG,IAAAvb,wBAAiB,EAAC,IAAI,CAACC,KAAK,CAACvB,IAAI,CAAC;IACvD,MAAMsU,WAA6C,GAChDuI,YAAY,IAAI,IAAI,CAAC3I,2BAA2B,CAAC,CAAC,IACnD,KAAK,CAACyI,sBAAsB,CAACxY,IAAI,CAAC;IAEpC,IAAI,CAACmQ,WAAW,EAAE,OAAO,IAAI;IAE7B,IACEA,WAAW,CAACtU,IAAI,KAAK,wBAAwB,IAC7CsU,WAAW,CAACtU,IAAI,KAAK,wBAAwB,IAC7C4c,SAAS,EACT;MACAzY,IAAI,CAACiW,UAAU,GAAG,MAAM;IAC1B;IAEA,IAAIwC,SAAS,EAAE;MAEb,IAAI,CAACE,kBAAkB,CAACxI,WAAW,EAAEzR,QAAQ,CAAC;MAE9CyR,WAAW,CAACrE,OAAO,GAAG,IAAI;IAC5B;IAEA,OAAOqE,WAAW;EACpB;EAEAyI,YAAYA,CACV5Y,IAAa,EACb6Y,WAAoB,EACpBC,UAA2B,EAE3BC,WAA0B,EACpB;IACN,IAAI,CAAC,CAACF,WAAW,IAAIC,UAAU,KAAK,IAAI,CAAC3T,YAAY,IAAe,CAAC,EAAE;MACrE;IACF;IAEA,KAAK,CAACyT,YAAY,CAChB5Y,IAAI,EACJ6Y,WAAW,EACXC,UAAU,EACT9Y,IAAI,CAAS8L,OAAO,GAAG4G,2BAAe,GAAGsG,sBAC5C,CAAC;IACD,MAAM1Y,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CAClD,IAAI,CAAClF,0BACP,CAAC;IACD,IAAIuD,cAAc,EAAEN,IAAI,CAACM,cAAc,GAAGA,cAAc;EAC1D;EAEA2Y,4BAA4BA,CAC1BjZ,IAAwE,EAClE;IACN,IAAI,CAACA,IAAI,CAACgE,QAAQ,EAAE;MAClB,IAAI,IAAI,CAACvE,GAAG,GAAQ,CAAC,EAAE;QACrBO,IAAI,CAACkZ,QAAQ,GAAG,IAAI;MACtB,CAAC,MAAM,IAAI,IAAI,CAACzZ,GAAG,GAAY,CAAC,EAAE;QAChCO,IAAI,CAACgE,QAAQ,GAAG,IAAI;MACtB;IACF;IAEA,MAAMnI,IAAI,GAAG,IAAI,CAACgI,wBAAwB,CAAC,CAAC;IAC5C,IAAIhI,IAAI,EAAEmE,IAAI,CAACoB,cAAc,GAAGvF,IAAI;EACtC;EAEAsd,kBAAkBA,CAACnZ,IAAqB,EAAmB;IACzD,IAAI,CAACiZ,4BAA4B,CAACjZ,IAAI,CAAC;IAEvC,IACE,IAAI,CAAC5C,KAAK,CAAC+U,gBAAgB,IAC3B,EAAEnS,IAAI,CAAC+D,QAAQ,IAAI,CAAC/D,IAAI,CAACoB,cAAc,CAAC,IACxC,IAAI,CAAC9D,KAAK,GAAM,CAAC,EACjB;MACA,IAAI,CAACe,KAAK,CAAChH,QAAQ,CAACe,+BAA+B,EAAE;QACnDkG,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB;MACjB,CAAC,CAAC;IACJ;IACA,IAAIsB,IAAI,CAAC0H,QAAQ,IAAI,IAAI,CAACpK,KAAK,GAAM,CAAC,EAAE;MACtC,MAAM;QAAEzG;MAAI,CAAC,GAAGmJ,IAAI;MACpB,IAAI,CAAC3B,KAAK,CAAChH,QAAQ,CAACI,8BAA8B,EAAE;QAClD6G,EAAE,EAAE,IAAI,CAAClB,KAAK,CAACsB,QAAQ;QACvBhH,YAAY,EACVb,GAAG,CAACgF,IAAI,KAAK,YAAY,IAAI,CAACmE,IAAI,CAAC8E,QAAQ,GACvCjO,GAAG,CAAC+K,IAAI,GACP,IAAG,IAAI,CAACwX,KAAK,CAACC,KAAK,CAACxiB,GAAG,CAACgb,KAAK,EAAEhb,GAAG,CAACyiB,GAAG,CAAE;MACjD,CAAC,CAAC;IACJ;IAEA,OAAO,KAAK,CAACH,kBAAkB,CAACnZ,IAAI,CAAC;EACvC;EAEAuZ,yBAAyBA,CACvBvZ,IAA4B,EACJ;IAExB,IAAIA,IAAI,CAAC0H,QAAQ,EAAE;MACjB,IAAI,CAACrJ,KAAK,CAAChH,QAAQ,CAACiD,yBAAyB,EAAE;QAAEgE,EAAE,EAAE0B;MAAK,CAAC,CAAC;IAC9D;IAGA,IAAIA,IAAI,CAACpB,aAAa,EAAE;MACtB,IAAI,CAACP,KAAK,CAAChH,QAAQ,CAACkD,8BAA8B,EAAE;QAClD+D,EAAE,EAAE0B,IAAI;QAERzH,QAAQ,EAAEyH,IAAI,CAACpB;MACjB,CAAC,CAAC;IACJ;IAEA,IAAI,CAACqa,4BAA4B,CAACjZ,IAAI,CAAC;IACvC,OAAO,KAAK,CAACuZ,yBAAyB,CAACvZ,IAAI,CAAC;EAC9C;EAEAwZ,0BAA0BA,CACxBxZ,IAA6B,EACJ;IACzB,IAAI,CAACiZ,4BAA4B,CAACjZ,IAAI,CAAC;IACvC,IAAIA,IAAI,CAACgE,QAAQ,EAAE;MACjB,IAAI,CAAC3F,KAAK,CAAChH,QAAQ,CAACQ,wBAAwB,EAAE;QAAEyG,EAAE,EAAE0B;MAAK,CAAC,CAAC;IAC7D;IACA,OAAO,KAAK,CAACwZ,0BAA0B,CAACxZ,IAAI,CAAC;EAC/C;EAEAyZ,eAAeA,CACbjC,SAAsB,EACtBtT,MAAqB,EACrBwV,WAAoB,EACpBC,OAAgB,EAChBC,aAAsB,EACtBC,iBAA0B,EACpB;IACN,MAAMvZ,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CAClD,IAAI,CAACnF,oBACP,CAAC;IACD,IAAIwD,cAAc,IAAIsZ,aAAa,EAAE;MACnC,IAAI,CAACvb,KAAK,CAAChH,QAAQ,CAACY,4BAA4B,EAAE;QAChDqG,EAAE,EAAEgC;MACN,CAAC,CAAC;IACJ;IAGA,MAAM;MAAEwL,OAAO,GAAG,KAAK;MAAE3T;IAAK,CAAC,GAAG+L,MAAM;IAExC,IAAI4H,OAAO,KAAK3T,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MACjD,IAAI,CAACkG,KAAK,CAAChH,QAAQ,CAACa,eAAe,EAAE;QAAEoG,EAAE,EAAE4F,MAAM;QAAE/L;MAAK,CAAC,CAAC;IAC5D;IACA,IAAImI,cAAc,EAAE4D,MAAM,CAAC5D,cAAc,GAAGA,cAAc;IAC1D,KAAK,CAACmZ,eAAe,CACnBjC,SAAS,EACTtT,MAAM,EACNwV,WAAW,EACXC,OAAO,EACPC,aAAa,EACbC,iBACF,CAAC;EACH;EAEAC,sBAAsBA,CACpBtC,SAAsB,EACtBtT,MAA4B,EAC5BwV,WAAoB,EACpBC,OAAgB,EACV;IACN,MAAMrZ,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CAClD,IAAI,CAACnF,oBACP,CAAC;IACD,IAAIwD,cAAc,EAAE4D,MAAM,CAAC5D,cAAc,GAAGA,cAAc;IAC1D,KAAK,CAACwZ,sBAAsB,CAACtC,SAAS,EAAEtT,MAAM,EAAEwV,WAAW,EAAEC,OAAO,CAAC;EACvE;EAEAI,gCAAgCA,CAC9B/Z,IAAyE,EACzE7H,IAAY,EACZ;IACA,IAAI6H,IAAI,CAACnE,IAAI,KAAK,iBAAiB,EAAE;IAErC,IAAImE,IAAI,CAACnE,IAAI,KAAK,kBAAkB,IAAI,CAACmE,IAAI,CAACjE,KAAK,CAACmQ,IAAI,EAAE;IAE1D,KAAK,CAAC6N,gCAAgC,CAAC/Z,IAAI,EAAE7H,IAAI,CAAC;EACpD;EAEA6hB,eAAeA,CAACha,IAAa,EAAQ;IACnC,KAAK,CAACga,eAAe,CAACha,IAAI,CAAC;IAE3B,IAAIA,IAAI,CAAC5D,UAAU,KAAK,IAAI,CAACkB,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,GAAa,CAAC,CAAC,EAAE;MAEtE0C,IAAI,CAACia,mBAAmB,GAAG,IAAI,CAACpJ,gCAAgC,CAAC,CAAC;IACpE;IACA,IAAI,IAAI,CAACrL,aAAa,IAAe,CAAC,EAAE;MACtCxF,IAAI,CAACka,UAAU,GAAG,IAAI,CAAC1O,qBAAqB,CAAC,YAAY,CAAC;IAC5D;EACF;EAEA2O,iBAAiBA,CACfC,IAA+C,EAC/C1b,QAAqC,EACrCgb,WAAoB,EACpBC,OAAgB,EAChBU,SAAkB,EAClBC,UAAmB,EACnBlH,mBAA6C,EAC7C;IACA,MAAM9S,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CAClD,IAAI,CAACnF,oBACP,CAAC;IACD,IAAIwD,cAAc,EAAE8Z,IAAI,CAAC9Z,cAAc,GAAGA,cAAc;IAExD,OAAO,KAAK,CAAC6Z,iBAAiB,CAC5BC,IAAI,EAEJ1b,QAAQ,EACRgb,WAAW,EACXC,OAAO,EACPU,SAAS,EACTC,UAAU,EACVlH,mBACF,CAAC;EACH;EAEA1C,mBAAmBA,CAAC1Q,IAAgB,EAAE4Z,aAAsB,EAAQ;IAClE,MAAMtZ,cAAc,GAAG,IAAI,CAAC2B,wBAAwB,CAClD,IAAI,CAACnF,oBACP,CAAC;IACD,IAAIwD,cAAc,EAAEN,IAAI,CAACM,cAAc,GAAGA,cAAc;IACxD,KAAK,CAACoQ,mBAAmB,CAAC1Q,IAAI,EAAE4Z,aAAa,CAAC;EAChD;EAGAW,UAAUA,CACR9D,IAA0B,EAC1Bte,IAAuC,EACjC;IACN,KAAK,CAACoiB,UAAU,CAAC9D,IAAI,EAAEte,IAAI,CAAC;IAC5B,IACEse,IAAI,CAAC/S,EAAE,CAAC7H,IAAI,KAAK,YAAY,IAC7B,CAAC,IAAI,CAAC2B,qBAAqB,CAAC,CAAC,IAC7B,IAAI,CAACiC,GAAG,GAAQ,CAAC,EACjB;MACAgX,IAAI,CAACyC,QAAQ,GAAG,IAAI;IACtB;IAEA,MAAMrd,IAAI,GAAG,IAAI,CAACgI,wBAAwB,CAAC,CAAC;IAC5C,IAAIhI,IAAI,EAAE;MACR4a,IAAI,CAAC/S,EAAE,CAACtC,cAAc,GAAGvF,IAAI;MAC7B,IAAI,CAAC8H,gBAAgB,CAAC8S,IAAI,CAAC/S,EAAE,CAAC;IAChC;EACF;EAGA8W,iCAAiCA,CAC/Bxa,IAA+B,EAC/BhJ,IAAsB,EACK;IAC3B,IAAI,IAAI,CAACsG,KAAK,GAAS,CAAC,EAAE;MACxB0C,IAAI,CAAC2Q,UAAU,GAAG,IAAI,CAACtP,qBAAqB,CAAC,CAAC;IAChD;IACA,OAAO,KAAK,CAACmZ,iCAAiC,CAACxa,IAAI,EAAEhJ,IAAI,CAAC;EAC5D;EAEAyjB,gBAAgBA,CACdrH,mBAA6C,EAC7CsH,cAAyB,EACX;IAAA,IAAAC,IAAA,EAAAC,KAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,UAAA;IAGd,IAAI3d,KAA+B;IACnC,IAAI4d,GAAG;IACP,IAAIC,QAAQ;IAEZ,IACE,IAAI,CAAChE,SAAS,CAAC,KAAK,CAAC,KACpB,IAAI,CAAC3Z,KAAK,IAAe,CAAC,IAAI,IAAI,CAACA,KAAK,GAAM,CAAC,CAAC,EACjD;MAEAF,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0R,KAAK,CAAC,CAAC;MAE1BkM,GAAG,GAAG,IAAI,CAAC/L,QAAQ,CACjB,MAAM,KAAK,CAACwL,gBAAgB,CAACrH,mBAAmB,EAAEsH,cAAc,CAAC,EACjEtd,KACF,CAAC;MAID,IAAI,CAAC4d,GAAG,CAAC5L,KAAK,EAAE,OAAO4L,GAAG,CAAChb,IAAI;MAK/B,MAAM;QAAEwM;MAAQ,CAAC,GAAG,IAAI,CAACpP,KAAK;MAC9B,MAAM8d,cAAc,GAAG1O,OAAO,CAACA,OAAO,CAACnK,MAAM,GAAG,CAAC,CAAC;MAClD,IAAI6Y,cAAc,KAAKlK,cAAE,CAACmK,MAAM,IAAID,cAAc,KAAKlK,cAAE,CAACoK,MAAM,EAAE;QAChE5O,OAAO,CAAC6O,GAAG,CAAC,CAAC;MACf;IACF;IAEA,IAAI,GAAAV,IAAA,GAACK,GAAG,aAAHL,IAAA,CAAKvL,KAAK,KAAI,CAAC,IAAI,CAAC9R,KAAK,GAAM,CAAC,EAAE;MACrC,OAAO,KAAK,CAACmd,gBAAgB,CAACrH,mBAAmB,EAAEsH,cAAc,CAAC;IACpE;IAOA,IAAI,CAACtd,KAAK,IAAIA,KAAK,KAAK,IAAI,CAACA,KAAK,EAAEA,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0R,KAAK,CAAC,CAAC;IAE9D,IAAIxO,cAA+D;IACnE,MAAMgb,KAAK,GAAG,IAAI,CAACrM,QAAQ,CAACC,KAAK,IAAI;MAAA,IAAAqM,WAAA,EAAAC,eAAA;MAEnClb,cAAc,GAAG,IAAI,CAAC4B,qBAAqB,CAAC,IAAI,CAACpF,oBAAoB,CAAC;MACtE,MAAMmT,IAAI,GAAG,KAAK,CAACwK,gBAAgB,CACjCrH,mBAAmB,EACnBsH,cACF,CAAC;MAED,IACEzK,IAAI,CAACpU,IAAI,KAAK,yBAAyB,KAAA0f,WAAA,GACvCtL,IAAI,CAAC2E,KAAK,aAAV2G,WAAA,CAAY1G,aAAa,EACzB;QACA3F,KAAK,CAAC,CAAC;MACT;MAGA,IAAI,EAAAsM,eAAA,GAAAlb,cAAc,qBAAdkb,eAAA,CAAgBpZ,MAAM,CAACC,MAAM,MAAK,CAAC,EAAE;QACvC,IAAI,CAACoI,0BAA0B,CAACwF,IAAI,EAAE3P,cAAc,CAAC;MACvD;MACA2P,IAAI,CAAC3P,cAAc,GAAGA,cAAc;MAAC;MAqBrC,OAAO2P,IAAI;IACb,CAAC,EAAE7S,KAAK,CAAC;IAGT,IAAI,CAACke,KAAK,CAAClM,KAAK,IAAI,CAACkM,KAAK,CAACnM,OAAO,EAAE;MAIlC,IAAI7O,cAAc,EAAE,IAAI,CAACmb,4BAA4B,CAACnb,cAAc,CAAC;MAErE,OAAOgb,KAAK,CAACtb,IAAI;IACnB;IAEA,IAAI,CAACgb,GAAG,EAAE;MAIR5jB,MAAM,CAAC,CAAC,IAAI,CAAC6f,SAAS,CAAC,KAAK,CAAC,CAAC;MAI9BgE,QAAQ,GAAG,IAAI,CAAChM,QAAQ,CACtB,MAAM,KAAK,CAACwL,gBAAgB,CAACrH,mBAAmB,EAAEsH,cAAc,CAAC,EACjEtd,KACF,CAAC;MAGD,IAAI,CAAC6d,QAAQ,CAAC7L,KAAK,EAAE,OAAO6L,QAAQ,CAACjb,IAAI;IAC3C;IAEA,KAAA4a,KAAA,GAAII,GAAG,aAAHJ,KAAA,CAAK5a,IAAI,EAAE;MAEb,IAAI,CAAC5C,KAAK,GAAG4d,GAAG,CAAC3L,SAAS;MAC1B,OAAO2L,GAAG,CAAChb,IAAI;IACjB;IAEA,IAAIsb,KAAK,CAACtb,IAAI,EAAE;MAEd,IAAI,CAAC5C,KAAK,GAAGke,KAAK,CAACjM,SAAS;MAC5B,IAAI/O,cAAc,EAAE,IAAI,CAACmb,4BAA4B,CAACnb,cAAc,CAAC;MAErE,OAAOgb,KAAK,CAACtb,IAAI;IACnB;IAEA,KAAA6a,SAAA,GAAII,QAAQ,aAARJ,SAAA,CAAU7a,IAAI,EAAE;MAElB,IAAI,CAAC5C,KAAK,GAAG6d,QAAQ,CAAC5L,SAAS;MAC/B,OAAO4L,QAAQ,CAACjb,IAAI;IACtB;IAEA,MAAM,EAAA8a,KAAA,GAAAE,GAAG,qBAAHF,KAAA,CAAK1L,KAAK,KAAIkM,KAAK,CAAClM,KAAK,MAAA2L,UAAA,GAAIE,QAAQ,qBAARF,UAAA,CAAU3L,KAAK;EACpD;EAEAqM,4BAA4BA,CAACzb,IAAS,EAAE;IAAA,IAAA0b,WAAA;IACtC,IACE1b,IAAI,CAACoC,MAAM,CAACC,MAAM,KAAK,CAAC,IACxB,CAACrC,IAAI,CAACoC,MAAM,CAAC,CAAC,CAAC,CAACN,UAAU,IAC1B,GAAA4Z,WAAA,GAAC1b,IAAI,CAAC4U,KAAK,aAAV8G,WAAA,CAAYC,aAAa,KAC1B,IAAI,CAACrQ,eAAe,CAAC,YAAY,EAAE,0BAA0B,CAAC,EAC9D;MACA,IAAI,CAACjN,KAAK,CAAChH,QAAQ,CAACoD,sBAAsB,EAAE;QAAE6D,EAAE,EAAE0B;MAAK,CAAC,CAAC;IAC3D;EACF;EAGAsI,eAAeA,CACb8K,mBAA6C,EAC7CwI,QAAkB,EACJ;IACd,IAAI,CAAC,IAAI,CAAC3E,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC3Z,KAAK,GAAM,CAAC,EAAE;MAC/C,OAAO,IAAI,CAAC+N,oBAAoB,CAAC,CAAC;IACpC;IACA,OAAO,KAAK,CAAC/C,eAAe,CAAC8K,mBAAmB,EAAEwI,QAAQ,CAAC;EAC7D;EAEAC,UAAUA,CACR7b,IAAuC,EACe;IACtD,IAAI,IAAI,CAAC1C,KAAK,GAAS,CAAC,EAAE;MAIxB,MAAM2B,MAAM,GAAG,IAAI,CAACgQ,QAAQ,CAACC,KAAK,IAAI;QACpC,MAAMyB,UAAU,GAAG,IAAI,CAAC7N,oCAAoC,GAE5D,CAAC;QACD,IAAI,IAAI,CAACgZ,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAACxe,KAAK,GAAS,CAAC,EAAE4R,KAAK,CAAC,CAAC;QAC/D,OAAOyB,UAAU;MACnB,CAAC,CAAC;MAEF,IAAI1R,MAAM,CAACkQ,OAAO,EAAE;MAEpB,IAAI,CAAClQ,MAAM,CAAC8c,MAAM,EAAE;QAClB,IAAI9c,MAAM,CAACmQ,KAAK,EAAE,IAAI,CAAChS,KAAK,GAAG6B,MAAM,CAACoQ,SAAS;QAE/CrP,IAAI,CAAC2Q,UAAU,GAAG1R,MAAM,CAACe,IAAI;MAC/B;IACF;IAEA,OAAO,KAAK,CAAC6b,UAAU,CAAC7b,IAAI,CAAC;EAC/B;EAGA2R,4BAA4BA,CAC1BO,KAAgB,EAChBX,KAA4B,EAC5B;IACA,IAAI,EAAEA,KAAK,GAAGtO,2BAAqB,CAACC,kBAAkB,CAAC,EAAE,OAAOgP,KAAK;IAErE,IAAI,IAAI,CAACzS,GAAG,GAAY,CAAC,EAAE;MACxByS,KAAK,CAAyBlO,QAAQ,GAAG,IAAI;IAChD;IACA,MAAMnI,IAAI,GAAG,IAAI,CAACgI,wBAAwB,CAAC,CAAC;IAC5C,IAAIhI,IAAI,EAAEqW,KAAK,CAAC9Q,cAAc,GAAGvF,IAAI;IACrC,IAAI,CAAC8H,gBAAgB,CAACuO,KAAK,CAAC;IAE5B,OAAOA,KAAK;EACd;EAEA8J,YAAYA,CAAChc,IAAY,EAAEwV,SAAmB,EAAW;IACvD,QAAQxV,IAAI,CAACnE,IAAI;MACf,KAAK,sBAAsB;QACzB,OAAO,IAAI,CAACmgB,YAAY,CAAChc,IAAI,CAACuL,UAAU,EAAEiK,SAAS,CAAC;MACtD,KAAK,qBAAqB;QACxB,OAAO,IAAI;MACb;QACE,OAAO,KAAK,CAACwG,YAAY,CAAChc,IAAI,EAAEwV,SAAS,CAAC;IAC9C;EACF;EAEAyG,YAAYA,CAACjc,IAAY,EAAEkc,KAAc,GAAG,KAAK,EAAQ;IACvD,QAAQlc,IAAI,CAACnE,IAAI;MACf,KAAK,yBAAyB;QAC5B,IAAI,CAACsgB,mCAAmC,CAACnc,IAAI,EAAEkc,KAAK,CAAC;QACrD;MACF,KAAK,gBAAgB;MACrB,KAAK,uBAAuB;MAC5B,KAAK,qBAAqB;MAC1B,KAAK,iBAAiB;QACpB,IAAIA,KAAK,EAAE;UACT,IAAI,CAACE,eAAe,CAACC,gCAAgC,CACnDhlB,QAAQ,CAACoE,6BAA6B,EACtC;YAAE6C,EAAE,EAAE0B;UAAK,CACb,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAAC3B,KAAK,CAAChH,QAAQ,CAACoE,6BAA6B,EAAE;YAAE6C,EAAE,EAAE0B;UAAK,CAAC,CAAC;QAClE;QACA,IAAI,CAACic,YAAY,CAACjc,IAAI,CAACuL,UAAU,EAAE2Q,KAAK,CAAC;QACzC;MACF,KAAK,sBAAsB;QACzB,IAAI,CAACA,KAAK,IAAIlc,IAAI,CAACa,IAAI,CAAChF,IAAI,KAAK,sBAAsB,EAAE;UACvDmE,IAAI,CAACa,IAAI,GAAG,IAAI,CAACyb,mBAAmB,CAACtc,IAAI,CAACa,IAAI,CAAC;QACjD;MAEF;QACE,KAAK,CAACob,YAAY,CAACjc,IAAI,EAAEkc,KAAK,CAAC;IACnC;EACF;EAEAC,mCAAmCA,CAACnc,IAAY,EAAEkc,KAAc,EAAQ;IACtE,QAAQlc,IAAI,CAACuL,UAAU,CAAC1P,IAAI;MAC1B,KAAK,gBAAgB;MACrB,KAAK,uBAAuB;MAC5B,KAAK,qBAAqB;MAC1B,KAAK,iBAAiB;MACtB,KAAK,yBAAyB;QAC5B,IAAI,CAACogB,YAAY,CAACjc,IAAI,CAACuL,UAAU,EAAE2Q,KAAK,CAAC;QACzC;MACF;QACE,KAAK,CAACD,YAAY,CAACjc,IAAI,EAAEkc,KAAK,CAAC;IACnC;EACF;EAEAK,qBAAqBA,CAACvc,IAAY,EAAEwc,YAAqB,EAAQ;IAC/D,QAAQxc,IAAI,CAACnE,IAAI;MACf,KAAK,gBAAgB;MACrB,KAAK,uBAAuB;MAC5B,KAAK,iBAAiB;MACtB,KAAK,qBAAqB;QACxB,IAAI,CAAC0gB,qBAAqB,CAACvc,IAAI,CAACuL,UAAU,EAAE,KAAK,CAAC;QAClD;MACF;QACE,KAAK,CAACgR,qBAAqB,CAACvc,IAAI,EAAEwc,YAAY,CAAC;IACnD;EACF;EAGAC,WAAWA,CACT5gB,IAMqB,EACrB6gB,yBAAkC,EAClCC,OAAqB,EACrB;IACA,OACEhmB,MAAM,CACJ;MAIEimB,oBAAoB,EAAE,IAAI;MAC1BC,mBAAmB,EAAE,WAAW;MAChCC,mBAAmB,EAAE,YAAY;MACjCC,cAAc,EAAE,CAACJ,OAAO,KAAKK,qBAAS,IACpC,CAACN,yBAAyB,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC;MACrDO,qBAAqB,EAAE,CAACN,OAAO,KAAKK,qBAAS,IAC3C,CAACN,yBAAyB,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC;MACrDQ,eAAe,EAAE,CAACP,OAAO,KAAKK,qBAAS,IACrC,CAACN,yBAAyB,KAAK,CAAC,YAAY,EAAE,IAAI;IACtD,CAAC,EACD7gB,IACF,CAAC,IAAI,KAAK,CAAC4gB,WAAW,CAAC5gB,IAAI,EAAE6gB,yBAAyB,EAAEC,OAAO,CAAC;EAEpE;EAEAQ,gBAAgBA,CAAA,EAAc;IAC5B,IAAI,IAAI,CAAC/f,KAAK,CAACvB,IAAI,OAAa,EAAE;MAChC,OAAO,IAAI,CAAC8E,eAAe,CAAe,IAAI,CAAC;IACjD;IACA,OAAO,KAAK,CAACwc,gBAAgB,CAAC,CAAC;EACjC;EAEAC,4BAA4BA,CAACnN,IAAkB,EAAgB;IAE7D,IAAI,IAAI,CAAC3S,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,GAAa,CAAC,EAAE;MACjD,MAAM2W,aAAa,GAAG,IAAI,CAACpD,gCAAgC,CAAC,CAAC;MAE7D,IAAI,IAAI,CAACvT,KAAK,GAAU,CAAC,EAAE;QACzB,MAAMtG,IAAI,GAAG,KAAK,CAAComB,4BAA4B,CAACnN,IAAI,CAAC;QACrDjZ,IAAI,CAACsJ,cAAc,GAAG2T,aAAa;QACnC,OAAOjd,IAAI;MACb;MAEA,IAAI,CAACmL,UAAU,CAAC,IAAI,IAAW,CAAC;IAClC;IAEA,OAAO,KAAK,CAACib,4BAA4B,CAACnN,IAAI,CAAC;EACjD;EAEAoN,mBAAmBA,CACjBpK,KAAiD,EACxC;IACT,IACE,IAAI,CAAC7V,KAAK,CAAC+U,gBAAgB,IAC3B,IAAI,CAAC7U,KAAK,GAAS,CAAC,IACpB,IAAI,CAACwJ,iBAAiB,CAAC,CAAC,KAAKmM,KAAK,EAClC;MACA,IAAI,CAACvV,IAAI,CAAC,CAAC;MACX,OAAO,KAAK;IACd;IACA,OAAO,KAAK,CAAC2f,mBAAmB,CAACpK,KAAK,CAAC;EACzC;EAOAqK,aAAaA,CAAA,EAAY;IACvB,OAAO,IAAI,CAAChgB,KAAK,GAAM,CAAC,IAAI,KAAK,CAACggB,aAAa,CAAC,CAAC;EACnD;EAEAC,eAAeA,CAAA,EAAY;IACzB,OACE,IAAI,CAACjgB,KAAK,GAAQ,CAAC,IAAI,IAAI,CAACA,KAAK,GAAS,CAAC,IAAI,KAAK,CAACigB,eAAe,CAAC,CAAC;EAE1E;EAEA7L,iBAAiBA,CACfhT,QAA0B,EAC1BmC,IAAqB,EACV;IACX,MAAMb,IAAI,GAAG,KAAK,CAAC0R,iBAAiB,CAAChT,QAAQ,EAAEmC,IAAI,CAAC;IAEpD,IACEb,IAAI,CAACnE,IAAI,KAAK,mBAAmB,IACjCmE,IAAI,CAACoB,cAAc,IACnBpB,IAAI,CAACc,KAAK,CAAC+Q,KAAK,GAAG7R,IAAI,CAACoB,cAAc,CAACyQ,KAAK,EAC5C;MACA,IAAI,CAACxT,KAAK,CAAChH,QAAQ,CAAC6D,yBAAyB,EAAE;QAC7CoD,EAAE,EAAE0B,IAAI,CAACoB;MACX,CAAC,CAAC;IACJ;IAEA,OAAOpB,IAAI;EACb;EAGAwd,gBAAgBA,CAACC,IAAY,EAAQ;IACnC,IAAI,IAAI,CAACrgB,KAAK,CAAC6K,MAAM,EAAE;MACrB,IAAIwV,IAAI,OAA0B,EAAE;QAClC,IAAI,CAACC,QAAQ,KAAQ,CAAC,CAAC;QACvB;MACF;MACA,IAAID,IAAI,OAAuB,EAAE;QAC/B,IAAI,CAACC,QAAQ,KAAQ,CAAC,CAAC;QACvB;MACF;IACF;IACA,KAAK,CAACF,gBAAgB,CAACC,IAAI,CAAC;EAC9B;EAGAvM,YAAYA,CAAA,EAAG;IACb,MAAM;MAAErV;IAAK,CAAC,GAAG,IAAI,CAACuB,KAAK;IAC3B,IAAIvB,IAAI,OAAU,EAAE;MAClB,IAAI,CAACuB,KAAK,CAACugB,GAAG,IAAI,CAAC;MACnB,IAAI,CAACC,YAAY,CAAC,CAAC;IACrB,CAAC,MAAM,IAAI/hB,IAAI,OAAU,EAAE;MACzB,IAAI,CAACuB,KAAK,CAACugB,GAAG,IAAI,CAAC;MACnB,IAAI,CAACE,YAAY,CAAC,CAAC;IACrB;EACF;EAEA/M,SAASA,CAAA,EAAG;IACV,MAAM;MAAEjV;IAAK,CAAC,GAAG,IAAI,CAACuB,KAAK;IAC3B,IAAIvB,IAAI,OAAiB,EAAE;MACzB,IAAI,CAACuB,KAAK,CAACugB,GAAG,IAAI,CAAC;MACnB,IAAI,CAACD,QAAQ,KAAQ,CAAC,CAAC;MACvB;IACF;IACA,OAAO7hB,IAAI;EACb;EAEAiiB,gBAAgBA,CACdhL,QAAsB,EACtBT,gBAA6C,EAC7C6J,KAAc,EACR;IACN,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjL,QAAQ,CAACzQ,MAAM,EAAE0b,CAAC,EAAE,EAAE;MACxC,MAAM9N,IAAI,GAAG6C,QAAQ,CAACiL,CAAC,CAAC;MACxB,IAAI,CAAA9N,IAAI,oBAAJA,IAAI,CAAEpU,IAAI,MAAK,sBAAsB,EAAE;QACzCiX,QAAQ,CAACiL,CAAC,CAAC,GAAG,IAAI,CAACzB,mBAAmB,CACpCrM,IACF,CAAC;MACH;IACF;IACA,KAAK,CAAC6N,gBAAgB,CAAChL,QAAQ,EAAET,gBAAgB,EAAE6J,KAAK,CAAC;EAC3D;EAEAI,mBAAmBA,CAACtc,IAA4B,EAAU;IACxDA,IAAI,CAACuL,UAAU,CAACnK,cAAc,GAAGpB,IAAI,CAACoB,cAAc;IAEpD,IAAI,CAACuC,gBAAgB,CAAC3D,IAAI,CAACuL,UAAU,EAAEvL,IAAI,CAACoB,cAAc,CAAClD,GAAG,CAACob,GAAG,CAAC;IAEnE,OAAOtZ,IAAI,CAACuL,UAAU;EACxB;EAEAyS,gBAAgBA,CAAC5b,MAAqB,EAAE;IACtC,IAAI,IAAI,CAAC9E,KAAK,GAAS,CAAC,EAAE;MACxB,OAAO8E,MAAM,CAAC6b,KAAK,CAAChO,IAAI,IAAI,IAAI,CAAC+L,YAAY,CAAC/L,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5D;IACA,OAAO,KAAK,CAAC+N,gBAAgB,CAAC5b,MAAM,CAAC;EACvC;EAEA8b,qBAAqBA,CAAA,EAAY;IAC/B,OAAO,IAAI,CAAC5gB,KAAK,GAAS,CAAC,IAAI,KAAK,CAAC4gB,qBAAqB,CAAC,CAAC;EAC9D;EAEAC,uBAAuBA,CAAA,EAAG;IAExB,OAAO,KAAK,CAACA,uBAAuB,CAAC,CAAC,IAAI,IAAI,CAACzH,eAAe,CAAC,CAAC;EAClE;EAEA0H,+BAA+BA,CAC7Bpe,IAAyB,EACJ;IAErB,IAAI,IAAI,CAAC1C,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,GAAa,CAAC,EAAE;MACjD,MAAM2W,aAAa,GAAG,IAAI,CAACjF,kBAAkB,CAAC,MAE5C,IAAI,CAAC6B,gCAAgC,CAAC,CACxC,CAAC;MAED,IAAIoD,aAAa,EAAEjU,IAAI,CAACM,cAAc,GAAG2T,aAAa;IACxD;IACA,OAAO,KAAK,CAACmK,+BAA+B,CAACpe,IAAI,CAAC;EACpD;EAEAqe,iCAAiCA,CAC/Bna,MAAsC,EAC9B;IACR,MAAMoa,SAAS,GAAG,KAAK,CAACD,iCAAiC,CAACna,MAAM,CAAC;IACjE,MAAM9B,MAAM,GAAG,IAAI,CAACmc,4BAA4B,CAACra,MAAM,CAAC;IACxD,MAAMsa,UAAU,GAAGpc,MAAM,CAAC,CAAC,CAAC;IAC5B,MAAMqc,eAAe,GAAGD,UAAU,IAAI,IAAI,CAACla,WAAW,CAACka,UAAU,CAAC;IAElE,OAAOC,eAAe,GAAGH,SAAS,GAAG,CAAC,GAAGA,SAAS;EACpD;EAEAI,qBAAqBA,CAAA,EAAc;IACjC,MAAMxM,KAAK,GAAG,KAAK,CAACwM,qBAAqB,CAAC,CAAC;IAC3C,MAAM7iB,IAAI,GAAG,IAAI,CAACgI,wBAAwB,CAAC,CAAC;IAE5C,IAAIhI,IAAI,EAAE;MACRqW,KAAK,CAAC9Q,cAAc,GAAGvF,IAAI;MAC3B,IAAI,CAAC8H,gBAAgB,CAACuO,KAAK,CAAC;IAC9B;IAEA,OAAOA,KAAK;EACd;EAEAzC,kBAAkBA,CAAInD,EAAW,EAAK;IACpC,MAAMqS,mBAAmB,GAAG,IAAI,CAACvhB,KAAK,CAAC+U,gBAAgB;IACvD,IAAI,CAAC/U,KAAK,CAAC+U,gBAAgB,GAAG,IAAI;IAClC,IAAI;MACF,OAAO7F,EAAE,CAAC,CAAC;IACb,CAAC,SAAS;MACR,IAAI,CAAClP,KAAK,CAAC+U,gBAAgB,GAAGwM,mBAAmB;IACnD;EACF;EAEAhP,UAAUA,CACR3P,IAAe,EACf6Y,WAAoB,EACpBC,UAAoB,EACjB;IACH,MAAM8F,kBAAkB,GAAG,IAAI,CAACxhB,KAAK,CAAC0a,eAAe;IACrD,IAAI,CAAC1a,KAAK,CAAC0a,eAAe,GAAG,CAAC,CAAE9X,IAAI,CAAS0H,QAAQ;IACrD,IAAI;MACF,OAAO,KAAK,CAACiI,UAAU,CAAC3P,IAAI,EAAE6Y,WAAW,EAAEC,UAAU,CAAC;IACxD,CAAC,SAAS;MACR,IAAI,CAAC1b,KAAK,CAAC0a,eAAe,GAAG8G,kBAAkB;IACjD;EACF;EAEAtO,0BAA0BA,CACxBtQ,IAAS,EACTkQ,UAAgC,EACkC;IAClE,IAAI,IAAI,CAAC5S,KAAK,GAAU,CAAC,EAAE;MACzB0C,IAAI,CAAC0H,QAAQ,GAAG,IAAI;MACpB,OAAO,IAAI,CAACmX,mBAAmB,CAC7B3O,UAAU,EACV,IAAI,CAACP,UAAU,CACb3P,IAAI,EACc,IAAI,EACL,KACnB,CACF,CAAC;IACH,CAAC,MAAM,IAAI,IAAI,CAACmF,YAAY,IAAc,CAAC,EAAE;MAM3C,IAAI,CAAC,IAAI,CAAC0G,qBAAqB,CAAC,CAAC,EAAE;QACjC7L,IAAI,CAAC0H,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACrJ,KAAK,CAAChH,QAAQ,CAAC6C,wCAAwC,EAAE;UAC5DoE,EAAE,EAAE0B;QACN,CAAC,CAAC;QACF,OAAO,IAAI,CAAC2L,2BAA2B,CACrC3L,IACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,IAAI,CAACmC,UAAU,CAAC,IAAI,IAAW,CAAC;IAClC;EACF;EAEA2c,WAAWA,CAGT9e,IAAe,EACf0Z,WAAoB,EACpBC,OAAgB,EAChBC,aAAsB,EACtBmF,gBAAyB,EACzBljB,IAAe,EACfmjB,YAAsB,EACtB;IACA,MAAM9a,MAAM,GAAG,KAAK,CAAC4a,WAAW,CAC9B9e,IAAI,EACJ0Z,WAAW,EACXC,OAAO,EACPC,aAAa,EACbmF,gBAAgB,EAChBljB,IAAI,EACJmjB,YACF,CAAC;IAED,IAAI9a,MAAM,CAACwD,QAAQ,EAAE;MACnB,MAAMuX,OAAO,GAAG,IAAI,CAAChI,SAAS,CAAC,QAAQ,CAAC,GAEpC,CAAC,CAAC/S,MAAM,CAACnI,KAAK,CAACmQ,IAAI,GACnB,CAAC,CAAChI,MAAM,CAACgI,IAAI;MACjB,IAAI+S,OAAO,EAAE;QACX,MAAM;UAAEpoB;QAAI,CAAC,GAAGqN,MAAM;QACtB,IAAI,CAAC7F,KAAK,CAAChH,QAAQ,CAACE,+BAA+B,EAAE;UACnD+G,EAAE,EAAE4F,MAAM;UACV1M,UAAU,EACRX,GAAG,CAACgF,IAAI,KAAK,YAAY,IAAI,CAACqI,MAAM,CAACY,QAAQ,GACzCjO,GAAG,CAAC+K,IAAI,GACP,IAAG,IAAI,CAACwX,KAAK,CAACC,KAAK,CAACxiB,GAAG,CAACgb,KAAK,EAAEhb,GAAG,CAACyiB,GAAG,CAAE;QACjD,CAAC,CAAC;MACJ;IACF;IACA,OAAOpV,MAAM;EACf;EAEArC,wBAAwBA,CAAA,EAA0B;IAChD,MAAMb,QAAsB,GAAG,IAAI,CAACL,eAAe,CAAC,CAAC;IACrD,OAAiDK,QAAQ,CAACY,IAAI;EAChE;EAEAsd,2BAA2BA,CAAA,EAAY;IACrC,OAAO,CAAC,CAAC,IAAI,CAAC5T,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC;EACpD;EAEA6T,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACD,2BAA2B,CAAC,CAAC,EAAE;MACtC,IAAI,CAAC9hB,KAAK,CAAC+U,gBAAgB,GAAG,IAAI;IACpC;IACA,OAAO,KAAK,CAACgN,KAAK,CAAC,CAAC;EACtB;EAEAC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACF,2BAA2B,CAAC,CAAC,EAAE;MACtC,IAAI,CAAC9hB,KAAK,CAAC+U,gBAAgB,GAAG,IAAI;IACpC;IACA,OAAO,KAAK,CAACiN,aAAa,CAAC,CAAC;EAC9B;EAEAC,oBAAoBA,CAClBrf,IAA+B,EAC/Bsf,QAAiB,EACjBC,cAAuB,EACvBC,eAAwB,EACxB;IACA,IAAI,CAACF,QAAQ,IAAIE,eAAe,EAAE;MAChC,IAAI,CAACC,kCAAkC,CACrCzf,IAAI,EACW,KAAK,EACpBuf,cACF,CAAC;MACD,OAAO,IAAI,CAAC/e,UAAU,CAAoBR,IAAI,EAAE,iBAAiB,CAAC;IACpE;IACAA,IAAI,CAACiW,UAAU,GAAG,OAAO;IACzB,OAAO,KAAK,CAACoJ,oBAAoB,CAC/Brf,IAAI,EACJsf,QAAQ,EACRC,cAAc,EACdC,eACF,CAAC;EACH;EAEAE,oBAAoBA,CAClBC,SAAoC,EACpCC,gBAAyB,EACzBC,kBAA2B,EAC3BL,eAAwB,EAExBzG,WAAqC,EAClB;IACnB,IAAI,CAAC6G,gBAAgB,IAAIJ,eAAe,EAAE;MACxC,IAAI,CAACC,kCAAkC,CACrCE,SAAS,EACM,IAAI,EACnBE,kBACF,CAAC;MACD,OAAO,IAAI,CAACrf,UAAU,CAAoBmf,SAAS,EAAE,iBAAiB,CAAC;IACzE;IACAA,SAAS,CAAClR,UAAU,GAAG,OAAO;IAC9B,OAAO,KAAK,CAACiR,oBAAoB,CAC/BC,SAAS,EACTC,gBAAgB,EAChBC,kBAAkB,EAClBL,eAAe,EACfK,kBAAkB,GAAGC,+BAAmB,GAAGxR,gCAC7C,CAAC;EACH;EAEAmR,kCAAkCA,CAChCzf,IAAS,EACT+f,QAAiB,EACjBC,wBAAiC,EAC3B;IACN,MAAMC,WAAW,GAAGF,QAAQ,GAAG,UAAU,GAAG,OAAO;IACnD,MAAMG,YAAY,GAAGH,QAAQ,GAAG,OAAO,GAAG,UAAU;IAEpD,IAAII,QAAQ,GAAGngB,IAAI,CAACigB,WAAW,CAAC;IAChC,IAAIG,SAAS;IAEb,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,iBAAiB,GAAG,IAAI;IAE5B,MAAMpiB,GAAG,GAAGiiB,QAAQ,CAACjiB,GAAG,CAAC2T,KAAK;IAO9B,IAAI,IAAI,CAAC1M,YAAY,GAAO,CAAC,EAAE;MAE7B,MAAMob,OAAO,GAAG,IAAI,CAAC5f,eAAe,CAAC,CAAC;MACtC,IAAI,IAAI,CAACwE,YAAY,GAAO,CAAC,EAAE;QAE7B,MAAMqb,QAAQ,GAAG,IAAI,CAAC7f,eAAe,CAAC,CAAC;QACvC,IAAI,IAAAiG,iCAA0B,EAAC,IAAI,CAACxJ,KAAK,CAACvB,IAAI,CAAC,EAAE;UAE/CwkB,gBAAgB,GAAG,IAAI;UACvBF,QAAQ,GAAGI,OAAO;UAClBH,SAAS,GAAGL,QAAQ,GAChB,IAAI,CAACpf,eAAe,CAAC,CAAC,GACtB,IAAI,CAAC8f,qBAAqB,CAAC,CAAC;UAChCH,iBAAiB,GAAG,KAAK;QAC3B,CAAC,MAAM;UAELF,SAAS,GAAGI,QAAQ;UACpBF,iBAAiB,GAAG,KAAK;QAC3B;MACF,CAAC,MAAM,IAAI,IAAA1Z,iCAA0B,EAAC,IAAI,CAACxJ,KAAK,CAACvB,IAAI,CAAC,EAAE;QAEtDykB,iBAAiB,GAAG,KAAK;QACzBF,SAAS,GAAGL,QAAQ,GAChB,IAAI,CAACpf,eAAe,CAAC,CAAC,GACtB,IAAI,CAAC8f,qBAAqB,CAAC,CAAC;MAClC,CAAC,MAAM;QAELJ,gBAAgB,GAAG,IAAI;QACvBF,QAAQ,GAAGI,OAAO;MACpB;IACF,CAAC,MAAM,IAAI,IAAA3Z,iCAA0B,EAAC,IAAI,CAACxJ,KAAK,CAACvB,IAAI,CAAC,EAAE;MAEtDwkB,gBAAgB,GAAG,IAAI;MACvB,IAAIN,QAAQ,EAAE;QACZI,QAAQ,GAAG,IAAI,CAACxf,eAAe,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,IAAI,CAACwE,YAAY,GAAO,CAAC,EAAE;UAC9B,IAAI,CAACkQ,iBAAiB,CACpB8K,QAAQ,CAACve,IAAI,EACbue,QAAQ,CAACjiB,GAAG,CAAC2T,KAAK,EAClB,IAAI,EACJ,IACF,CAAC;QACH;MACF,CAAC,MAAM;QACLsO,QAAQ,GAAG,IAAI,CAACM,qBAAqB,CAAC,CAAC;MACzC;IACF;IACA,IAAIJ,gBAAgB,IAAIL,wBAAwB,EAAE;MAChD,IAAI,CAAC3hB,KAAK,CACR0hB,QAAQ,GACJ1oB,QAAQ,CAACgE,+BAA+B,GACxChE,QAAQ,CAAC+D,+BAA+B,EAC5C;QAAEkD,EAAE,EAAEJ;MAAI,CACZ,CAAC;IACH;IAEA8B,IAAI,CAACigB,WAAW,CAAC,GAAGE,QAAQ;IAC5BngB,IAAI,CAACkgB,YAAY,CAAC,GAAGE,SAAS;IAE9B,MAAMM,OAAO,GAAGX,QAAQ,GAAG,YAAY,GAAG,YAAY;IACtD/f,IAAI,CAAC0gB,OAAO,CAAC,GAAGL,gBAAgB,GAAG,MAAM,GAAG,OAAO;IAEnD,IAAIC,iBAAiB,IAAI,IAAI,CAAC9a,aAAa,GAAO,CAAC,EAAE;MACnDxF,IAAI,CAACkgB,YAAY,CAAC,GAAGH,QAAQ,GACzB,IAAI,CAACpf,eAAe,CAAC,CAAC,GACtB,IAAI,CAAC8f,qBAAqB,CAAC,CAAC;IAClC;IACA,IAAI,CAACzgB,IAAI,CAACkgB,YAAY,CAAC,EAAE;MACvBlgB,IAAI,CAACkgB,YAAY,CAAC,GAAG,IAAAS,qBAAe,EAAC3gB,IAAI,CAACigB,WAAW,CAAC,CAAC;IACzD;IACA,IAAIF,QAAQ,EAAE;MACZ,IAAI,CAAChU,eAAe,CAClB/L,IAAI,CAACkgB,YAAY,CAAC,EAClBG,gBAAgB,GAAGP,+BAAmB,GAAGxR,gCAC3C,CAAC;IACH;EACF;AACF,CAAC;AAAAsS,OAAA,CAAA5e,OAAA,GAAA7F,QAAA;AAEH,SAAS0kB,qBAAqBA,CAACtV,UAAwB,EAAW;EAChE,IAAIA,UAAU,CAAC1P,IAAI,KAAK,kBAAkB,EAAE,OAAO,KAAK;EAExD,MAAM;IAAEiJ,QAAQ;IAAEL;EAAS,CAAC,GAAG8G,UAAU;EAEzC,IACEzG,QAAQ,IACRL,QAAQ,CAAC5I,IAAI,KAAK,eAAe,KAChC4I,QAAQ,CAAC5I,IAAI,KAAK,iBAAiB,IAAI4I,QAAQ,CAACqc,WAAW,CAACze,MAAM,GAAG,CAAC,CAAC,EACxE;IACA,OAAO,KAAK;EACd;EAEA,OAAO0e,iCAAiC,CAACxV,UAAU,CAAC3U,MAAM,CAAC;AAC7D;AAQA,SAASogB,8BAA8BA,CACrCzL,UAAwB,EACxByV,MAAe,EACN;EAAA,IAAAC,iBAAA;EACT,MAAM;IAAEplB;EAAK,CAAC,GAAG0P,UAAU;EAC3B,KAAA0V,iBAAA,GAAI1V,UAAU,CAACqJ,KAAK,aAAhBqM,iBAAA,CAAkBpM,aAAa,EAAE;IACnC,OAAO,KAAK;EACd;EACA,IAAImM,MAAM,EAAE;IACV,IAAInlB,IAAI,KAAK,SAAS,EAAE;MACtB,MAAM;QAAEE;MAAM,CAAC,GAAGwP,UAAU;MAC5B,IAAI,OAAOxP,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;QAC3D,OAAO,IAAI;MACb;IACF;EACF,CAAC,MAAM;IACL,IAAIF,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,gBAAgB,EAAE;MACzD,OAAO,IAAI;IACb;EACF;EACA,IAAIqlB,QAAQ,CAAC3V,UAAU,EAAEyV,MAAM,CAAC,IAAIG,gBAAgB,CAAC5V,UAAU,EAAEyV,MAAM,CAAC,EAAE;IACxE,OAAO,IAAI;EACb;EACA,IAAInlB,IAAI,KAAK,iBAAiB,IAAI0P,UAAU,CAACuV,WAAW,CAACze,MAAM,KAAK,CAAC,EAAE;IACrE,OAAO,IAAI;EACb;EACA,IAAIwe,qBAAqB,CAACtV,UAAU,CAAC,EAAE;IACrC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEA,SAAS2V,QAAQA,CAAC3V,UAAwB,EAAEyV,MAAe,EAAW;EACpE,IAAIA,MAAM,EAAE;IACV,OACEzV,UAAU,CAAC1P,IAAI,KAAK,SAAS,KAC5B,OAAO0P,UAAU,CAACxP,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIwP,UAAU,CAAC;EAEpE;EACA,OACEA,UAAU,CAAC1P,IAAI,KAAK,gBAAgB,IAAI0P,UAAU,CAAC1P,IAAI,KAAK,eAAe;AAE/E;AAEA,SAASslB,gBAAgBA,CAAC5V,UAAwB,EAAEyV,MAAe,EAAW;EAC5E,IAAIzV,UAAU,CAAC1P,IAAI,KAAK,iBAAiB,EAAE;IACzC,MAAM;MAAE+M,QAAQ;MAAE1I;IAAS,CAAC,GAAGqL,UAA+B;IAC9D,IAAI3C,QAAQ,KAAK,GAAG,IAAIsY,QAAQ,CAAChhB,QAAQ,EAAE8gB,MAAM,CAAC,EAAE;MAClD,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAEA,SAASD,iCAAiCA,CAACxV,UAAwB,EAAW;EAC5E,IAAIA,UAAU,CAAC1P,IAAI,KAAK,YAAY,EAAE,OAAO,IAAI;EACjD,IAAI0P,UAAU,CAAC1P,IAAI,KAAK,kBAAkB,IAAI0P,UAAU,CAACzG,QAAQ,EAAE;IACjE,OAAO,KAAK;EACd;EAEA,OAAOic,iCAAiC,CAACxV,UAAU,CAAC3U,MAAM,CAAC;AAC7D"}