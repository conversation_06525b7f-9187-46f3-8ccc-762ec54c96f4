{"version": 3, "names": ["_location", "require", "_context", "_types", "State", "constructor", "strict", "curLine", "lineStart", "startLoc", "endLoc", "errors", "potentialArrowAt", "noArrowAt", "noArrowParamsConversionAt", "maybeInArrowParameters", "inType", "noAnonFunctionType", "hasFlowComment", "isAmbientContext", "inAbstractClass", "inDisallowConditionalTypesContext", "topicContext", "maxNumOfResolvableTopics", "maxTopicIndex", "soloA<PERSON>t", "inFSharpPipelineDirectBody", "labels", "comments", "commentStack", "pos", "type", "value", "start", "end", "lastTokEndLoc", "lastTokStartLoc", "lastTokStart", "context", "ct", "brace", "canStartJSXElement", "containsEsc", "firstInvalidTemplateEscapePos", "strictErrors", "Map", "tokensLength", "init", "strictMode", "sourceType", "startLine", "startColumn", "Position", "curPosition", "clone", "<PERSON><PERSON><PERSON><PERSON>", "state", "keys", "Object", "i", "length", "key", "val", "Array", "isArray", "slice", "exports", "default"], "sources": ["../../src/tokenizer/state.ts"], "sourcesContent": ["import type { Options } from \"../options\";\nimport type * as N from \"../types\";\nimport type { CommentWhitespace } from \"../parser/comments\";\nimport { Position } from \"../util/location\";\n\nimport { types as ct, type TokContext } from \"./context\";\nimport { tt, type TokenType } from \"./types\";\nimport type { Errors } from \"../parse-error\";\nimport type { ParseError } from \"../parse-error\";\n\nexport type DeferredStrictError =\n  | typeof Errors.StrictNumericEscape\n  | typeof Errors.StrictOctalLiteral;\n\ntype TopicContextState = {\n  // When a topic binding has been currently established,\n  // then this is 1. Otherwise, it is 0. This is forwards compatible\n  // with a future plugin for multiple lexical topics.\n  maxNumOfResolvableTopics: number;\n  // When a topic binding has been currently established, and if that binding\n  // has been used as a topic reference `#`, then this is 0. Otherwise, it is\n  // `null`. This is forwards compatible with a future plugin for multiple\n  // lexical topics.\n  maxTopicIndex: null | 0;\n};\n\nexport default class State {\n  strict: boolean;\n  curLine: number;\n  lineStart: number;\n\n  // And, if locations are used, the {line, column} object\n  // corresponding to those offsets\n  startLoc: Position;\n  endLoc: Position;\n\n  init({ strictMode, sourceType, startLine, startColumn }: Options): void {\n    this.strict =\n      strictMode === false\n        ? false\n        : strictMode === true\n        ? true\n        : sourceType === \"module\";\n\n    this.curLine = startLine;\n    this.lineStart = -startColumn;\n    this.startLoc = this.endLoc = new Position(startLine, startColumn, 0);\n  }\n\n  errors: ParseError<any>[] = [];\n\n  // Used to signify the start of a potential arrow function\n  potentialArrowAt: number = -1;\n\n  // Used to signify the start of an expression which looks like a\n  // typed arrow function, but it isn't\n  // e.g. a ? (b) : c => d\n  //          ^\n  noArrowAt: number[] = [];\n\n  // Used to signify the start of an expression whose params, if it looks like\n  // an arrow function, shouldn't be converted to assignable nodes.\n  // This is used to defer the validation of typed arrow functions inside\n  // conditional expressions.\n  // e.g. a ? (b) : c => d\n  //          ^\n  noArrowParamsConversionAt: number[] = [];\n\n  // Flags to track\n  maybeInArrowParameters: boolean = false;\n  inType: boolean = false;\n  noAnonFunctionType: boolean = false;\n  hasFlowComment: boolean = false;\n  isAmbientContext: boolean = false;\n  inAbstractClass: boolean = false;\n  inDisallowConditionalTypesContext: boolean = false;\n\n  // For the Hack-style pipelines plugin\n  topicContext: TopicContextState = {\n    maxNumOfResolvableTopics: 0,\n    maxTopicIndex: null,\n  };\n\n  // For the F#-style pipelines plugin\n  soloAwait: boolean = false;\n  inFSharpPipelineDirectBody: boolean = false;\n\n  // Labels in scope.\n  labels: Array<{\n    kind: \"loop\" | \"switch\" | undefined | null;\n    name?: string | null;\n    statementStart?: number;\n  }> = [];\n\n  // Comment store for Program.comments\n  comments: Array<N.Comment> = [];\n\n  // Comment attachment store\n  commentStack: Array<CommentWhitespace> = [];\n\n  // The current position of the tokenizer in the input.\n  pos: number = 0;\n\n  // Properties of the current token:\n  // Its type\n  type: TokenType = tt.eof;\n\n  // For tokens that include more information than their type, the value\n  value: any = null;\n\n  // Its start and end offset\n  start: number = 0;\n  end: number = 0;\n\n  // Position information for the previous token\n  // this is initialized when generating the second token.\n  lastTokEndLoc: Position = null;\n  // this is initialized when generating the second token.\n  lastTokStartLoc: Position = null;\n  lastTokStart: number = 0;\n\n  // The context stack is used to track whether the apostrophe \"`\" starts\n  // or ends a string template\n  context: Array<TokContext> = [ct.brace];\n  // Used to track whether a JSX element is allowed to form\n  canStartJSXElement: boolean = true;\n\n  // Used to signal to callers of `readWord1` whether the word\n  // contained any escape sequences. This is needed because words with\n  // escape sequences must not be interpreted as keywords.\n  containsEsc: boolean = false;\n\n  // Used to track invalid escape sequences in template literals,\n  // that must be reported if the template is not tagged.\n  firstInvalidTemplateEscapePos: null | Position = null;\n\n  // This property is used to track the following errors\n  // - StrictNumericEscape\n  // - StrictOctalLiteral\n  //\n  // in a literal that occurs prior to/immediately after a \"use strict\" directive.\n\n  // todo(JLHwung): set strictErrors to null and avoid recording string errors\n  // after a non-directive is parsed\n  strictErrors: Map<number, [DeferredStrictError, Position]> = new Map();\n\n  // Tokens length in token store\n  tokensLength: number = 0;\n\n  curPosition(): Position {\n    return new Position(this.curLine, this.pos - this.lineStart, this.pos);\n  }\n\n  clone(skipArrays?: boolean): State {\n    const state = new State();\n    const keys = Object.keys(this) as (keyof State)[];\n    for (let i = 0, length = keys.length; i < length; i++) {\n      const key = keys[i];\n      let val = this[key];\n\n      if (!skipArrays && Array.isArray(val)) {\n        val = val.slice();\n      }\n\n      // @ts-expect-error val must conform to S[key]\n      state[key] = val;\n    }\n\n    return state;\n  }\n}\n\nexport type LookaheadState = {\n  pos: number;\n  value: any;\n  type: TokenType;\n  start: number;\n  end: number;\n  context: TokContext[];\n  startLoc: Position;\n  lastTokEndLoc: Position;\n  curLine: number;\n  lineStart: number;\n  curPosition: () => Position;\n  /* Used only in readToken_mult_modulo */\n  inType: boolean;\n  // These boolean properties are not initialized in createLookaheadState()\n  // instead they will only be set by the tokenizer\n  containsEsc?: boolean;\n};\n"], "mappings": ";;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAoBe,MAAMG,KAAK,CAAC;EAAAC,YAAA;IAAA,KACzBC,MAAM;IAAA,KACNC,OAAO;IAAA,KACPC,SAAS;IAAA,KAITC,QAAQ;IAAA,KACRC,MAAM;IAAA,KAeNC,MAAM,GAAsB,EAAE;IAAA,KAG9BC,gBAAgB,GAAW,CAAC,CAAC;IAAA,KAM7BC,SAAS,GAAa,EAAE;IAAA,KAQxBC,yBAAyB,GAAa,EAAE;IAAA,KAGxCC,sBAAsB,GAAY,KAAK;IAAA,KACvCC,MAAM,GAAY,KAAK;IAAA,KACvBC,kBAAkB,GAAY,KAAK;IAAA,KACnCC,cAAc,GAAY,KAAK;IAAA,KAC/BC,gBAAgB,GAAY,KAAK;IAAA,KACjCC,eAAe,GAAY,KAAK;IAAA,KAChCC,iCAAiC,GAAY,KAAK;IAAA,KAGlDC,YAAY,GAAsB;MAChCC,wBAAwB,EAAE,CAAC;MAC3BC,aAAa,EAAE;IACjB,CAAC;IAAA,KAGDC,SAAS,GAAY,KAAK;IAAA,KAC1BC,0BAA0B,GAAY,KAAK;IAAA,KAG3CC,MAAM,GAID,EAAE;IAAA,KAGPC,QAAQ,GAAqB,EAAE;IAAA,KAG/BC,YAAY,GAA6B,EAAE;IAAA,KAG3CC,GAAG,GAAW,CAAC;IAAA,KAIfC,IAAI;IAAA,KAGJC,KAAK,GAAQ,IAAI;IAAA,KAGjBC,KAAK,GAAW,CAAC;IAAA,KACjBC,GAAG,GAAW,CAAC;IAAA,KAIfC,aAAa,GAAa,IAAI;IAAA,KAE9BC,eAAe,GAAa,IAAI;IAAA,KAChCC,YAAY,GAAW,CAAC;IAAA,KAIxBC,OAAO,GAAsB,CAACC,cAAE,CAACC,KAAK,CAAC;IAAA,KAEvCC,kBAAkB,GAAY,IAAI;IAAA,KAKlCC,WAAW,GAAY,KAAK;IAAA,KAI5BC,6BAA6B,GAAoB,IAAI;IAAA,KAUrDC,YAAY,GAAiD,IAAIC,GAAG,CAAC,CAAC;IAAA,KAGtEC,YAAY,GAAW,CAAC;EAAA;EA/GxBC,IAAIA,CAAC;IAAEC,UAAU;IAAEC,UAAU;IAAEC,SAAS;IAAEC;EAAqB,CAAC,EAAQ;IACtE,IAAI,CAAC7C,MAAM,GACT0C,UAAU,KAAK,KAAK,GAChB,KAAK,GACLA,UAAU,KAAK,IAAI,GACnB,IAAI,GACJC,UAAU,KAAK,QAAQ;IAE7B,IAAI,CAAC1C,OAAO,GAAG2C,SAAS;IACxB,IAAI,CAAC1C,SAAS,GAAG,CAAC2C,WAAW;IAC7B,IAAI,CAAC1C,QAAQ,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI0C,kBAAQ,CAACF,SAAS,EAAEC,WAAW,EAAE,CAAC,CAAC;EACvE;EAsGAE,WAAWA,CAAA,EAAa;IACtB,OAAO,IAAID,kBAAQ,CAAC,IAAI,CAAC7C,OAAO,EAAE,IAAI,CAACuB,GAAG,GAAG,IAAI,CAACtB,SAAS,EAAE,IAAI,CAACsB,GAAG,CAAC;EACxE;EAEAwB,KAAKA,CAACC,UAAoB,EAAS;IACjC,MAAMC,KAAK,GAAG,IAAIpD,KAAK,CAAC,CAAC;IACzB,MAAMqD,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC,IAAI,CAAoB;IACjD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;MACrD,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;MACnB,IAAIG,GAAG,GAAG,IAAI,CAACD,GAAG,CAAC;MAEnB,IAAI,CAACN,UAAU,IAAIQ,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;QACrCA,GAAG,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC;MACnB;MAGAT,KAAK,CAACK,GAAG,CAAC,GAAGC,GAAG;IAClB;IAEA,OAAON,KAAK;EACd;AACF;AAACU,OAAA,CAAAC,OAAA,GAAA/D,KAAA"}