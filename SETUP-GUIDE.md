# 🎰 BetFyre Casino - Complete Setup Guide

## Prerequisites Installation

### 1. Install XAMPP (Recommended)
- Download from: https://www.apachefriends.org/download.html
- Install with PHP 7.4+ and MySQL
- Start Apache and MySQL services from XAMPP Control Panel

### 2. Install Composer
- Download from: https://getcomposer.org/download/
- Install globally on Windows

### 3. Install Redis (Optional but Recommended)
- Download Redis for Windows
- Or use Redis Cloud service

## Database Setup

### 1. Create Database
```sql
-- Open phpMyAdmin (http://localhost/phpmyadmin)
-- Create new database named 'casino'
CREATE DATABASE casino;
```

### 2. Import Database
- Import `betfyre/betfyre.sql` into the 'casino' database
- This contains all tables and sample data

## Laravel Application Setup

### 1. Install PHP Dependencies
```bash
cd betfyre
composer install
```

### 2. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 3. Update .env File
```env
APP_NAME="BetFyre Casino"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=casino
DB_USERNAME=root
DB_PASSWORD=

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
```

### 4. Run Database Migrations
```bash
php artisan migrate
php artisan db:seed
```

## Frontend Setup

### 1. Build Assets
```bash
npm run dev
# or for production
npm run production
```

### 2. Start Socket Server (for real-time features)
```bash
node socket-server.js
```

## Starting the Application

### 1. Start Laravel Server
```bash
php artisan serve
```

### 2. Access the Website
- Main site: http://localhost:8000
- Admin panel: http://localhost:8000/admin

## Game Configuration

### 1. Configure Game Settings
- Access admin panel
- Set game parameters (RTP, house edge, etc.)
- Configure payment methods

### 2. Test Games
- Create test user account
- Add test balance
- Test each game functionality

## Security Notes

⚠️ **IMPORTANT**: This setup is for TESTING ONLY
- Remove `code.php` file (security vulnerability)
- Change all default passwords
- Update Laravel to latest version
- Implement proper SSL certificates
- Review all security configurations

## Troubleshooting

### Common Issues:
1. **Composer errors**: Update PHP version
2. **Database connection**: Check MySQL service
3. **Permission errors**: Set proper folder permissions
4. **Asset loading**: Run `npm run dev`

### File Permissions (Linux/Mac):
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

## Next Steps After Setup

1. **Legal Compliance**: Obtain proper gambling licenses
2. **Security Audit**: Professional security review
3. **Payment Integration**: Configure real payment gateways
4. **Game Testing**: Extensive game functionality testing
5. **Performance Optimization**: Database and server optimization

---

**Note**: This is a complex casino application. Ensure you understand all legal and regulatory requirements before deploying to production.
