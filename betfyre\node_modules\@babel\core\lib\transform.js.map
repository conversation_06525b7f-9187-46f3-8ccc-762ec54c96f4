{"version": 3, "names": ["_gensync", "data", "require", "_config", "_transformation", "_rewriteStackTrace", "transformRunner", "gens<PERSON>", "transform", "code", "opts", "config", "loadConfig", "run", "optsOrCallback", "maybe<PERSON><PERSON><PERSON>", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "exports", "transformSync", "args", "transformAsync", "async"], "sources": ["../src/transform.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config\";\nimport type { InputOptions, ResolvedConfig } from \"./config\";\nimport { run } from \"./transformation\";\n\nimport type { FileResult, FileResultCallback } from \"./transformation\";\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace\";\n\nexport type { FileResult } from \"./transformation\";\n\ntype Transform = {\n  (code: string, callback: FileResultCallback): void;\n  (\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileResultCallback,\n  ): void;\n  (code: string, opts?: InputOptions | null): FileResult | null;\n};\n\nconst transformRunner = gensync(function* transform(\n  code: string,\n  opts?: InputOptions,\n): Handler<FileResult | null> {\n  const config: ResolvedConfig | null = yield* loadConfig(opts);\n  if (config === null) return null;\n\n  return yield* run(config, code);\n});\n\nexport const transform: Transform = function transform(\n  code,\n  optsOrCallback?: InputOptions | null | undefined | FileResultCallback,\n  maybeCallback?: FileResultCallback,\n) {\n  let opts: InputOptions | undefined | null;\n  let callback: FileResultCallback | undefined;\n  if (typeof optsOrCallback === \"function\") {\n    callback = optsOrCallback;\n    opts = undefined;\n  } else {\n    opts = optsOrCallback;\n    callback = maybeCallback;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'transform' function expects a callback. If you need to call it synchronously, please use 'transformSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'transform' function will expect a callback. If you need to call it synchronously, please use 'transformSync'.\",\n      // );\n      return beginHiddenCallStack(transformRunner.sync)(code, opts);\n    }\n  }\n\n  beginHiddenCallStack(transformRunner.errback)(code, opts, callback);\n};\n\nexport function transformSync(\n  ...args: Parameters<typeof transformRunner.sync>\n) {\n  return beginHiddenCallStack(transformRunner.sync)(...args);\n}\nexport function transformAsync(\n  ...args: Parameters<typeof transformRunner.async>\n) {\n  return beginHiddenCallStack(transformRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,OAAA,GAAAD,OAAA;AAEA,IAAAE,eAAA,GAAAF,OAAA;AAGA,IAAAG,kBAAA,GAAAH,OAAA;AAcA,MAAMI,eAAe,GAAGC,SAAMA,CAAC,CAAC,UAAUC,SAASA,CACjDC,IAAY,EACZC,IAAmB,EACS;EAC5B,MAAMC,MAA6B,GAAG,OAAO,IAAAC,eAAU,EAACF,IAAI,CAAC;EAC7D,IAAIC,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;EAEhC,OAAO,OAAO,IAAAE,mBAAG,EAACF,MAAM,EAAEF,IAAI,CAAC;AACjC,CAAC,CAAC;AAEK,MAAMD,SAAoB,GAAG,SAASA,SAASA,CACpDC,IAAI,EACJK,cAAqE,EACrEC,aAAkC,EAClC;EACA,IAAIL,IAAqC;EACzC,IAAIM,QAAwC;EAC5C,IAAI,OAAOF,cAAc,KAAK,UAAU,EAAE;IACxCE,QAAQ,GAAGF,cAAc;IACzBJ,IAAI,GAAGO,SAAS;EAClB,CAAC,MAAM;IACLP,IAAI,GAAGI,cAAc;IACrBE,QAAQ,GAAGD,aAAa;EAC1B;EAEA,IAAIC,QAAQ,KAAKC,SAAS,EAAE;IAKnB;MAIL,OAAO,IAAAC,uCAAoB,EAACZ,eAAe,CAACa,IAAI,CAAC,CAACV,IAAI,EAAEC,IAAI,CAAC;IAC/D;EACF;EAEA,IAAAQ,uCAAoB,EAACZ,eAAe,CAACc,OAAO,CAAC,CAACX,IAAI,EAAEC,IAAI,EAAEM,QAAQ,CAAC;AACrE,CAAC;AAACK,OAAA,CAAAb,SAAA,GAAAA,SAAA;AAEK,SAASc,aAAaA,CAC3B,GAAGC,IAA6C,EAChD;EACA,OAAO,IAAAL,uCAAoB,EAACZ,eAAe,CAACa,IAAI,CAAC,CAAC,GAAGI,IAAI,CAAC;AAC5D;AACO,SAASC,cAAcA,CAC5B,GAAGD,IAA8C,EACjD;EACA,OAAO,IAAAL,uCAAoB,EAACZ,eAAe,CAACmB,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AAC7D;AAAC"}