{"version": 3, "names": ["JSXAttribute", "node", "print", "name", "value", "token", "JSXIdentifier", "word", "JSXNamespacedName", "namespace", "JSXMemberExpression", "object", "property", "JSXSpreadAttribute", "argument", "JSXExpressionContainer", "expression", "JSXSpreadChild", "JSXText", "raw", "getPossibleRaw", "undefined", "JSXElement", "open", "openingElement", "selfClosing", "indent", "child", "children", "dedent", "closingElement", "spaceSeparator", "space", "JSXOpeningElement", "typeParameters", "attributes", "length", "printJoin", "separator", "JSXClosingElement", "JSXEmptyExpression", "printInnerComments", "JSXFragment", "openingFragment", "closingFragment", "JSXOpeningFragment", "JSXClosingFragment"], "sources": ["../../src/generators/jsx.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function JSXAttribute(this: Printer, node: t.JSXAttribute) {\n  this.print(node.name, node);\n  if (node.value) {\n    this.token(\"=\");\n    this.print(node.value, node);\n  }\n}\n\nexport function JSXIdentifier(this: Printer, node: t.JSXIdentifier) {\n  this.word(node.name);\n}\n\nexport function JSXNamespacedName(this: Printer, node: t.JSXNamespacedName) {\n  this.print(node.namespace, node);\n  this.token(\":\");\n  this.print(node.name, node);\n}\n\nexport function JSXMemberExpression(\n  this: Printer,\n  node: t.JSXMemberExpression,\n) {\n  this.print(node.object, node);\n  this.token(\".\");\n  this.print(node.property, node);\n}\n\nexport function JSXSpreadAttribute(this: Printer, node: t.JSXSpreadAttribute) {\n  this.token(\"{\");\n  this.token(\"...\");\n  this.print(node.argument, node);\n  this.token(\"}\");\n}\n\nexport function JSXExpressionContainer(\n  this: Printer,\n  node: t.JSXExpressionContainer,\n) {\n  this.token(\"{\");\n  this.print(node.expression, node);\n  this.token(\"}\");\n}\n\nexport function JSXSpreadChild(this: Printer, node: t.JSXSpreadChild) {\n  this.token(\"{\");\n  this.token(\"...\");\n  this.print(node.expression, node);\n  this.token(\"}\");\n}\n\nexport function JSXText(this: Printer, node: t.JSXText) {\n  const raw = this.getPossibleRaw(node);\n\n  if (raw !== undefined) {\n    this.token(raw, true);\n  } else {\n    this.token(node.value, true);\n  }\n}\n\nexport function JSXElement(this: Printer, node: t.JSXElement) {\n  const open = node.openingElement;\n  this.print(open, node);\n  if (open.selfClosing) return;\n\n  this.indent();\n  for (const child of node.children) {\n    this.print(child, node);\n  }\n  this.dedent();\n\n  this.print(node.closingElement, node);\n}\n\nfunction spaceSeparator(this: Printer) {\n  this.space();\n}\n\nexport function JSXOpeningElement(this: Printer, node: t.JSXOpeningElement) {\n  this.token(\"<\");\n  this.print(node.name, node);\n  this.print(node.typeParameters, node); // TS\n  if (node.attributes.length > 0) {\n    this.space();\n    this.printJoin(node.attributes, node, { separator: spaceSeparator });\n  }\n  if (node.selfClosing) {\n    this.space();\n    this.token(\"/>\");\n  } else {\n    this.token(\">\");\n  }\n}\n\nexport function JSXClosingElement(this: Printer, node: t.JSXClosingElement) {\n  this.token(\"</\");\n  this.print(node.name, node);\n  this.token(\">\");\n}\n\nexport function JSXEmptyExpression(this: Printer) {\n  // This node is empty, so forcefully print its inner comments.\n  this.printInnerComments();\n}\n\nexport function JSXFragment(this: Printer, node: t.JSXFragment) {\n  this.print(node.openingFragment, node);\n\n  this.indent();\n  for (const child of node.children) {\n    this.print(child, node);\n  }\n  this.dedent();\n\n  this.print(node.closingFragment, node);\n}\n\nexport function JSXOpeningFragment(this: Printer) {\n  this.token(\"<\");\n  this.token(\">\");\n}\n\nexport function JSXClosingFragment(this: Printer) {\n  this.token(\"</\");\n  this.token(\">\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGO,SAASA,YAAYA,CAAgBC,IAAoB,EAAE;EAChE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,IAAI,EAAEF,IAAI,CAAC;EAC3B,IAAIA,IAAI,CAACG,KAAK,EAAE;IACd,IAAI,CAACC,SAAK,GAAI,CAAC;IACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACG,KAAK,EAAEH,IAAI,CAAC;EAC9B;AACF;AAEO,SAASK,aAAaA,CAAgBL,IAAqB,EAAE;EAClE,IAAI,CAACM,IAAI,CAACN,IAAI,CAACE,IAAI,CAAC;AACtB;AAEO,SAASK,iBAAiBA,CAAgBP,IAAyB,EAAE;EAC1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAACQ,SAAS,EAAER,IAAI,CAAC;EAChC,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACE,IAAI,EAAEF,IAAI,CAAC;AAC7B;AAEO,SAASS,mBAAmBA,CAEjCT,IAA2B,EAC3B;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACU,MAAM,EAAEV,IAAI,CAAC;EAC7B,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACW,QAAQ,EAAEX,IAAI,CAAC;AACjC;AAEO,SAASY,kBAAkBA,CAAgBZ,IAA0B,EAAE;EAC5E,IAAI,CAACI,SAAK,IAAI,CAAC;EACf,IAAI,CAACA,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACH,KAAK,CAACD,IAAI,CAACa,QAAQ,EAAEb,IAAI,CAAC;EAC/B,IAAI,CAACI,SAAK,IAAI,CAAC;AACjB;AAEO,SAASU,sBAAsBA,CAEpCd,IAA8B,EAC9B;EACA,IAAI,CAACI,SAAK,IAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACe,UAAU,EAAEf,IAAI,CAAC;EACjC,IAAI,CAACI,SAAK,IAAI,CAAC;AACjB;AAEO,SAASY,cAAcA,CAAgBhB,IAAsB,EAAE;EACpE,IAAI,CAACI,SAAK,IAAI,CAAC;EACf,IAAI,CAACA,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACH,KAAK,CAACD,IAAI,CAACe,UAAU,EAAEf,IAAI,CAAC;EACjC,IAAI,CAACI,SAAK,IAAI,CAAC;AACjB;AAEO,SAASa,OAAOA,CAAgBjB,IAAe,EAAE;EACtD,MAAMkB,GAAG,GAAG,IAAI,CAACC,cAAc,CAACnB,IAAI,CAAC;EAErC,IAAIkB,GAAG,KAAKE,SAAS,EAAE;IACrB,IAAI,CAAChB,KAAK,CAACc,GAAG,EAAE,IAAI,CAAC;EACvB,CAAC,MAAM;IACL,IAAI,CAACd,KAAK,CAACJ,IAAI,CAACG,KAAK,EAAE,IAAI,CAAC;EAC9B;AACF;AAEO,SAASkB,UAAUA,CAAgBrB,IAAkB,EAAE;EAC5D,MAAMsB,IAAI,GAAGtB,IAAI,CAACuB,cAAc;EAChC,IAAI,CAACtB,KAAK,CAACqB,IAAI,EAAEtB,IAAI,CAAC;EACtB,IAAIsB,IAAI,CAACE,WAAW,EAAE;EAEtB,IAAI,CAACC,MAAM,CAAC,CAAC;EACb,KAAK,MAAMC,KAAK,IAAI1B,IAAI,CAAC2B,QAAQ,EAAE;IACjC,IAAI,CAAC1B,KAAK,CAACyB,KAAK,EAAE1B,IAAI,CAAC;EACzB;EACA,IAAI,CAAC4B,MAAM,CAAC,CAAC;EAEb,IAAI,CAAC3B,KAAK,CAACD,IAAI,CAAC6B,cAAc,EAAE7B,IAAI,CAAC;AACvC;AAEA,SAAS8B,cAAcA,CAAA,EAAgB;EACrC,IAAI,CAACC,KAAK,CAAC,CAAC;AACd;AAEO,SAASC,iBAAiBA,CAAgBhC,IAAyB,EAAE;EAC1E,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACH,KAAK,CAACD,IAAI,CAACE,IAAI,EAAEF,IAAI,CAAC;EAC3B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACiC,cAAc,EAAEjC,IAAI,CAAC;EACrC,IAAIA,IAAI,CAACkC,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;IAC9B,IAAI,CAACJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACK,SAAS,CAACpC,IAAI,CAACkC,UAAU,EAAElC,IAAI,EAAE;MAAEqC,SAAS,EAAEP;IAAe,CAAC,CAAC;EACtE;EACA,IAAI9B,IAAI,CAACwB,WAAW,EAAE;IACpB,IAAI,CAACO,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC3B,KAAK,CAAC,IAAI,CAAC;EAClB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,GAAI,CAAC;EACjB;AACF;AAEO,SAASkC,iBAAiBA,CAAgBtC,IAAyB,EAAE;EAC1E,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACH,KAAK,CAACD,IAAI,CAACE,IAAI,EAAEF,IAAI,CAAC;EAC3B,IAAI,CAACI,SAAK,GAAI,CAAC;AACjB;AAEO,SAASmC,kBAAkBA,CAAA,EAAgB;EAEhD,IAAI,CAACC,kBAAkB,CAAC,CAAC;AAC3B;AAEO,SAASC,WAAWA,CAAgBzC,IAAmB,EAAE;EAC9D,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC0C,eAAe,EAAE1C,IAAI,CAAC;EAEtC,IAAI,CAACyB,MAAM,CAAC,CAAC;EACb,KAAK,MAAMC,KAAK,IAAI1B,IAAI,CAAC2B,QAAQ,EAAE;IACjC,IAAI,CAAC1B,KAAK,CAACyB,KAAK,EAAE1B,IAAI,CAAC;EACzB;EACA,IAAI,CAAC4B,MAAM,CAAC,CAAC;EAEb,IAAI,CAAC3B,KAAK,CAACD,IAAI,CAAC2C,eAAe,EAAE3C,IAAI,CAAC;AACxC;AAEO,SAAS4C,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAACxC,SAAK,GAAI,CAAC;EACf,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB;AAEO,SAASyC,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAACzC,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB"}