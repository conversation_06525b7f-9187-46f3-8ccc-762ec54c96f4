{"version": 3, "names": ["_types", "require", "_context", "_identifier", "_scope", "_scopeflags", "_parseError", "_node", "reservedTypes", "Set", "FlowErrors", "ParseErrorEnum", "AmbiguousConditionalArrow", "AmbiguousDeclareModuleKind", "AssignReservedType", "reservedType", "DeclareClassElement", "DeclareClassFieldInitializer", "DuplicateDeclareModuleExports", "EnumBooleanMemberNotInitialized", "memberName", "enumName", "EnumDuplicateMemberName", "EnumInconsistentMemberValues", "EnumInvalidExplicitType", "invalidEnumType", "EnumInvalidExplicitTypeUnknownSupplied", "EnumInvalidMemberInitializerPrimaryType", "explicitType", "EnumInvalidMemberInitializerSymbolType", "EnumInvalidMemberInitializerUnknownType", "EnumInvalidMemberName", "suggestion", "EnumNumberMemberNotInitialized", "EnumStringMemberInconsistentlyInitailized", "GetterMayNotHaveThisParam", "ImportReflectionHasImportType", "ImportTypeShorthandOnlyInPureImport", "InexactInsideExact", "InexactInsideNonObject", "InexactVariance", "InvalidNonTypeImportInDeclareModule", "MissingTypeParamDefault", "NestedDeclareModule", "NestedFlowComment", "PatternIsOptional", "Object", "assign", "message", "reasonCode", "SetterMayNotHaveThisParam", "Spread<PERSON><PERSON><PERSON>", "ThisParamAnnotationRequired", "ThisParamBannedInConstructor", "ThisParamMayNotBeOptional", "ThisParamMustBeFirst", "ThisParamNoDefault", "TypeBeforeInitializer", "TypeCastInPattern", "UnexpectedExplicitInexactInObject", "UnexpectedReservedType", "UnexpectedReservedUnderscore", "UnexpectedSpaceBetweenModuloChecks", "UnexpectedSpreadType", "UnexpectedSubtractionOperand", "UnexpectedTokenAfterTypeParameter", "UnexpectedTypeParameterBeforeAsyncArrowFunction", "UnsupportedDeclareExportKind", "unsupportedExportKind", "UnsupportedStatementInDeclareModule", "UnterminatedFlowComment", "isEsModuleType", "bodyElement", "type", "declaration", "hasTypeImportKind", "node", "importKind", "exportSuggestions", "const", "let", "interface", "partition", "list", "test", "list1", "list2", "i", "length", "push", "FLOW_PRAGMA_REGEX", "_default", "superClass", "FlowParserMixin", "constructor", "args", "flowPragma", "undefined", "getScopeHandler", "FlowScopeHandler", "shouldParseTypes", "getPluginOption", "shouldParseEnums", "finishToken", "val", "addComment", "comment", "matches", "exec", "value", "Error", "flowParseTypeInitialiser", "tok", "oldInType", "state", "inType", "expect", "flowParseType", "flowParsePredicate", "startNode", "moduloLoc", "startLoc", "next", "expectContextual", "lastTokStart", "index", "raise", "at", "eat", "parseExpression", "finishNode", "flowParseTypeAndPredicateInitialiser", "predicate", "match", "flowParseDeclareClass", "flowParseInterfaceish", "flowParseDeclareFunction", "id", "parseIdentifier", "typeNode", "typeContainer", "typeParameters", "flowParseTypeParameterDeclaration", "tmp", "flowParseFunctionTypeParams", "params", "rest", "this", "_this", "returnType", "typeAnnotation", "resetEndLocation", "semicolon", "scope", "declareName", "name", "BIND_FLOW_DECLARE_FN", "loc", "start", "flowParseDeclare", "insideModule", "flowParseDeclareVariable", "eatContextual", "flowParseDeclareModuleExports", "lastTokStartLoc", "flowParseDeclareModule", "isContextual", "flowParseDeclareTypeAlias", "flowParseDeclareOpaqueType", "flowParseDeclareInterface", "flowParseDeclareExportDeclaration", "unexpected", "flowParseTypeAnnotatableIdentifier", "BIND_VAR", "enter", "SCOPE_OTHER", "parseExprAtom", "bodyNode", "body", "parseImport", "exit", "kind", "hasModuleExport", "for<PERSON>ach", "default", "isLet", "label", "parseExport", "exportKind", "flowParseTypeAnnotation", "finished", "flowParseTypeAlias", "flowParseOpaqueType", "isClass", "flowParseRestrictedIdentifier", "BIND_FUNCTION", "BIND_LEXICAL", "extends", "flowParseInterfaceExtends", "implements", "mixins", "flowParseObjectType", "allowStatic", "allowExact", "allowSpread", "allowProto", "allowInexact", "flowParseQualifiedTypeIdentifier", "flowParseTypeParameterInstantiation", "flowParseInterface", "checkNotUnderscore", "word", "checkReservedType", "has", "liberal", "right", "declare", "supertype", "impltype", "flowParseTypeParameter", "requireDefault", "nodeStartLoc", "variance", "flowParseVariance", "ident", "bound", "defaultRequired", "typeParameter", "oldNoAnonFunctionType", "noAnonFunctionType", "flowParseTypeParameterInstantiationCallOrNew", "flowParseTypeOrImplicitInstantiation", "flowParseInterfaceType", "flowParseObjectPropertyKey", "flowParseObjectTypeIndexer", "isStatic", "static", "<PERSON><PERSON><PERSON>", "key", "flowParseObjectTypeInternalSlot", "method", "optional", "flowParseObjectTypeMethodish", "startNodeAt", "flowParseFunctionTypeParam", "flowParseObjectTypeCallProperty", "valueNode", "nodeStart", "callProperties", "properties", "indexers", "internalSlots", "<PERSON><PERSON><PERSON><PERSON>", "exact", "inexact", "protoStartLoc", "inexactStartLoc", "tokenIsLiteralPropertyName", "propOrInexact", "flowParseObjectTypeProperty", "flowObjectTypeSemicolon", "out", "isInexactToken", "argument", "proto", "flowCheckGetterSetterParams", "property", "paramCount", "Errors", "BadGetterArity", "BadSetterArity", "BadSetterRestParameter", "_startLoc", "node2", "qualification", "flowParseGenericType", "flowParseTypeofType", "flowParsePrimaryType", "flowParseTupleType", "types", "pos", "first", "lh", "isThis", "reinterpretTypeAsFunctionTypeParam", "flowIdentToTypeAnnotation", "isGroupedType", "tokenIsIdentifier", "token", "parseLiteral", "parseLiteralAtNode", "tokenIsKeyword", "tokenLabelName", "createIdentifier", "flowParsePostfixType", "seenOptionalIndexedAccess", "canInsertSemicolon", "elementType", "objectType", "indexType", "flowParsePrefixType", "flowParseAnonFunctionWithoutParens", "param", "flowParseIntersectionType", "flowParseUnionType", "allowPrimitiveOverride", "typeCastToParameter", "expression", "end", "parseFunctionBody", "allowExpressionBody", "isMethod", "forwardNoArrowParamsConversionAt", "parseFunctionBodyAndFinish", "parseStatementLike", "flags", "strict", "tokenIsKeywordOrIdentifier", "flowParseEnumDeclaration", "stmt", "isValidDirective", "parseExpressionStatement", "expr", "decorators", "shouldParseExportDeclaration", "tokenIsFlowInterfaceOrTypeOrOpaque", "containsEsc", "isExportDefaultSpecifier", "parseExportDefaultExpression", "parseConditional", "refExpressionErrors", "maybeInArrowParameters", "nextCh", "lookaheadCharCode", "setOptionalParametersError", "clone", "originalNoArrowAt", "noArrowAt", "consequent", "failed", "tryParseConditionalConsequent", "valid", "invalid", "getArrowLikeExpressions", "alternate", "parseMaybeAssign", "noArrowParamsConversionAt", "parseMaybeAssignAllowIn", "pop", "disallowInvalid", "stack", "arrows", "finishArrowValidation", "every", "isAssignable", "_node$extra", "toAssignableList", "extra", "trailingCommaLoc", "SCOPE_FUNCTION", "SCOPE_ARROW", "checkParams", "parse", "result", "indexOf", "parseParenItem", "typeCastNode", "assertModuleNodeAllowed", "parseExportDeclaration", "declarationNode", "specifiers", "parseExportSpecifiers", "parseExportFrom", "eatExportStar", "maybeParseExportNamespaceSpecifier", "hasNamespace", "parseClassId", "isStatement", "optionalId", "parseClassMember", "classBody", "member", "parseClassMemberFromModifier", "isIterator", "readIterator", "readWord1", "fullWord", "InvalidIdentifier", "curPosition", "identifierName", "getTokenFromCode", "code", "input", "charCodeAt", "finishOp", "isIteratorStart", "isBinding", "toAssignable", "isLHS", "left", "exprList", "toReferencedList", "isParenthesizedExpr", "_expr$extra", "parenthesized", "parseArrayLike", "close", "canBePattern", "isTuple", "elements", "isValidLVal", "isParenthesized", "binding", "parseClassProperty", "parseClassPrivateProperty", "isClassMethod", "isClassProperty", "isNonstaticConstructor", "pushClassMethod", "isGenerator", "isAsync", "isConstructor", "allowsDirectSuper", "isThisParam", "pushClassPrivateMethod", "parseClassSuper", "superTypeParameters", "implemented", "checkGetterSetterParams", "getObjectOrClassMethodParams", "parsePropertyNamePrefixOperator", "parseObjPropValue", "prop", "isPattern", "isAccessor", "parseAssignableListItemTypes", "parseMaybeDefault", "checkImportReflection", "module", "parseImportSpecifierLocal", "specifier", "local", "finishImportSpecifier", "isPotentialImportPhase", "isExport", "ch", "applyImportPhase", "phase", "parseImportSpecifier", "importedIsString", "isInTypeOnlyImport", "isMaybeTypeOnly", "bindingType", "firstIdent", "imported", "specifierTypeKind", "isLookaheadContextual", "as_ident", "cloneIdentifier", "ImportBindingIsString", "importName", "specifierIsTypeImport", "checkReservedWord", "parseBindingAtom", "parseFunctionParams", "parseVarId", "decl", "parseAsyncArrowFromCallExpression", "call", "shouldParseAsyncArrow", "afterLeftParse", "_jsx", "jsx", "hasPlugin", "try<PERSON><PERSON><PERSON>", "error", "context", "currentContext", "tc", "j_oTag", "j_expr", "_jsx2", "_jsx3", "arrow", "abort", "_arrowExpression$extr", "arrowExpression", "resetStartLocationFromNode", "maybeUnwrapTypeCastExpression", "aborted", "async", "failState", "thrown", "parseArrow", "shouldParseArrow", "setArrowFunctionParameters", "allowDuplicates", "isArrowFunction", "strictModeChanged", "parseParenAndDistinguishExpression", "canBeArrow", "parseSubscripts", "base", "noCalls", "callee", "arguments", "parseCallExpressionArguments", "parseAsyncArrowWithTypeParameters", "parseSubscript", "subscriptState", "isLookaheadToken_lt", "optionalChainMember", "stop", "typeArguments", "finishCallExpression", "parseNewCallee", "targs", "parseArrowExpression", "readToken_mult_modulo", "hasFlowComment", "nextToken", "readToken_pipe_amp", "parseTopLevel", "file", "program", "fileNode", "skip<PERSON><PERSON>Com<PERSON>", "skipFlowComment", "hasFlowCommentCompletion", "commentSkip", "shiftToFirstNonWhiteSpace", "includes", "ch2", "ch3", "slice", "UnterminatedComment", "flowEnumErrorBooleanMemberNotInitialized", "flowEnumErrorInvalidMemberInitializer", "enumContext", "flowEnumErrorNumberMemberNotInitialized", "flowEnumErrorStringMemberInconsistentlyInitailized", "flowEnumMemberInit", "endOfInit", "literal", "parseNumericLiteral", "parseStringLiteral", "parseBooleanLiteral", "flowEnumMemberRaw", "init", "flowEnumCheckExplicitTypeMismatch", "expectedType", "flowEnumMembers", "seenNames", "members", "booleanMembers", "numberMembers", "stringMembers", "defaultedMembers", "hasUnknownMembers", "memberNode", "toUpperCase", "add", "flowEnumStringMembers", "initializedMembers", "flowEnumParseExplicitType", "flowEnumBody", "nameLoc", "empty", "boolsLen", "numsLen", "strsLen", "defaultedLen", "nextTokenStart", "afterNext", "exports"], "sources": ["../../../src/plugins/flow/index.ts"], "sourcesContent": ["/*:: declare var invariant; */\n\nimport type Parser from \"../../parser\";\nimport {\n  tokenIsIdentifier,\n  tokenIsKeyword,\n  tokenIsKeywordOrIdentifier,\n  tokenIsLiteralPropertyName,\n  tokenLabelName,\n  tt,\n  type TokenType,\n  tokenIsFlowInterfaceOrTypeOrOpaque,\n} from \"../../tokenizer/types\";\nimport type * as N from \"../../types\";\nimport type { Position } from \"../../util/location\";\nimport { types as tc } from \"../../tokenizer/context\";\nimport * as charCodes from \"charcodes\";\nimport { isIteratorStart } from \"../../util/identifier\";\nimport FlowScopeHandler from \"./scope\";\nimport {\n  BIND_LEXICAL,\n  BIND_VAR,\n  BIND_FUNCTION,\n  BIND_FLOW_DECLARE_FN,\n  SCOPE_ARROW,\n  SCOPE_FUNCTION,\n  SCOPE_OTHER,\n  type BindingTypes,\n} from \"../../util/scopeflags\";\nimport type { ExpressionErrors } from \"../../parser/util\";\nimport type { ParseStatementFlag } from \"../../parser/statement\";\nimport { Errors, ParseErrorEnum } from \"../../parse-error\";\nimport { cloneIdentifier, type Undone } from \"../../parser/node\";\n\nconst reservedTypes = new Set([\n  \"_\",\n  \"any\",\n  \"bool\",\n  \"boolean\",\n  \"empty\",\n  \"extends\",\n  \"false\",\n  \"interface\",\n  \"mixed\",\n  \"null\",\n  \"number\",\n  \"static\",\n  \"string\",\n  \"true\",\n  \"typeof\",\n  \"void\",\n]);\n\n/* eslint sort-keys: \"error\" */\n// The Errors key follows https://github.com/facebook/flow/blob/master/src/parser/parse_error.ml unless it does not exist\nconst FlowErrors = ParseErrorEnum`flow`({\n  AmbiguousConditionalArrow:\n    \"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.\",\n  AmbiguousDeclareModuleKind:\n    \"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.\",\n  // TODO: When we get proper string enums in typescript make this ReservedType.\n  // Not really worth it to do the whole $Values dance with reservedTypes set.\n  AssignReservedType: ({ reservedType }: { reservedType: string }) =>\n    `Cannot overwrite reserved type ${reservedType}.`,\n  DeclareClassElement:\n    \"The `declare` modifier can only appear on class fields.\",\n  DeclareClassFieldInitializer:\n    \"Initializers are not allowed in fields with the `declare` modifier.\",\n  DuplicateDeclareModuleExports:\n    \"Duplicate `declare module.exports` statement.\",\n  EnumBooleanMemberNotInitialized: ({\n    memberName,\n    enumName,\n  }: {\n    memberName: string;\n    enumName: string;\n  }) =>\n    `Boolean enum members need to be initialized. Use either \\`${memberName} = true,\\` or \\`${memberName} = false,\\` in enum \\`${enumName}\\`.`,\n  EnumDuplicateMemberName: ({\n    memberName,\n    enumName,\n  }: {\n    memberName: string;\n    enumName: string;\n  }) =>\n    `Enum member names need to be unique, but the name \\`${memberName}\\` has already been used before in enum \\`${enumName}\\`.`,\n  EnumInconsistentMemberValues: ({ enumName }: { enumName: string }) =>\n    `Enum \\`${enumName}\\` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.`,\n  EnumInvalidExplicitType: ({\n    invalidEnumType,\n    enumName,\n  }: {\n    invalidEnumType: string;\n    enumName: string;\n  }) =>\n    `Enum type \\`${invalidEnumType}\\` is not valid. Use one of \\`boolean\\`, \\`number\\`, \\`string\\`, or \\`symbol\\` in enum \\`${enumName}\\`.`,\n  EnumInvalidExplicitTypeUnknownSupplied: ({\n    enumName,\n  }: {\n    enumName: string;\n  }) =>\n    `Supplied enum type is not valid. Use one of \\`boolean\\`, \\`number\\`, \\`string\\`, or \\`symbol\\` in enum \\`${enumName}\\`.`,\n\n  // TODO: When moving to typescript, we should either have each of the\n  // following errors only accept the specific strings they want:\n  //\n  // ...PrimaryType: explicitType: \"string\" | \"number\" | \"boolean\"\n  // ...SymbolType: explicitType: \"symbol\"\n  // ...UnknownType: explicitType: null\n  //\n  // Or, alternatively, merge these three errors together into one\n  // `EnumInvalidMemberInitializer` error that can accept `EnumExplicitType`\n  // without alteration, and then just have its message change based on the\n  // explicitType.\n  EnumInvalidMemberInitializerPrimaryType: ({\n    enumName,\n    memberName,\n    explicitType,\n  }: {\n    enumName: string;\n    memberName: string;\n    explicitType: EnumExplicitType;\n  }) =>\n    `Enum \\`${enumName}\\` has type \\`${explicitType}\\`, so the initializer of \\`${memberName}\\` needs to be a ${explicitType} literal.`,\n  EnumInvalidMemberInitializerSymbolType: ({\n    enumName,\n    memberName,\n  }: {\n    enumName: string;\n    memberName: string;\n    explicitType: EnumExplicitType;\n  }) =>\n    `Symbol enum members cannot be initialized. Use \\`${memberName},\\` in enum \\`${enumName}\\`.`,\n  EnumInvalidMemberInitializerUnknownType: ({\n    enumName,\n    memberName,\n  }: {\n    enumName: string;\n    memberName: string;\n    explicitType: EnumExplicitType;\n  }) =>\n    `The enum member initializer for \\`${memberName}\\` needs to be a literal (either a boolean, number, or string) in enum \\`${enumName}\\`.`,\n  EnumInvalidMemberName: ({\n    enumName,\n    memberName,\n    suggestion,\n  }: {\n    enumName: string;\n    memberName: string;\n    suggestion: string;\n  }) =>\n    `Enum member names cannot start with lowercase 'a' through 'z'. Instead of using \\`${memberName}\\`, consider using \\`${suggestion}\\`, in enum \\`${enumName}\\`.`,\n  EnumNumberMemberNotInitialized: ({\n    enumName,\n    memberName,\n  }: {\n    enumName: string;\n    memberName: string;\n  }) =>\n    `Number enum members need to be initialized, e.g. \\`${memberName} = 1\\` in enum \\`${enumName}\\`.`,\n  EnumStringMemberInconsistentlyInitailized: ({\n    enumName,\n  }: {\n    enumName: string;\n  }) =>\n    `String enum members need to consistently either all use initializers, or use no initializers, in enum \\`${enumName}\\`.`,\n  GetterMayNotHaveThisParam: \"A getter cannot have a `this` parameter.\",\n  ImportReflectionHasImportType:\n    \"An `import module` declaration can not use `type` or `typeof` keyword.\",\n  ImportTypeShorthandOnlyInPureImport:\n    \"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.\",\n  InexactInsideExact:\n    \"Explicit inexact syntax cannot appear inside an explicit exact object type.\",\n  InexactInsideNonObject:\n    \"Explicit inexact syntax cannot appear in class or interface definitions.\",\n  InexactVariance: \"Explicit inexact syntax cannot have variance.\",\n  InvalidNonTypeImportInDeclareModule:\n    \"Imports within a `declare module` body must always be `import type` or `import typeof`.\",\n  MissingTypeParamDefault:\n    \"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.\",\n  NestedDeclareModule:\n    \"`declare module` cannot be used inside another `declare module`.\",\n  NestedFlowComment: \"Cannot have a flow comment inside another flow comment.\",\n  PatternIsOptional: {\n    message:\n      \"A binding pattern parameter cannot be optional in an implementation signature.\",\n    // For consistency in TypeScript and Flow error codes\n    ...(!process.env.BABEL_8_BREAKING\n      ? { reasonCode: \"OptionalBindingPattern\" }\n      : {}),\n  },\n  SetterMayNotHaveThisParam: \"A setter cannot have a `this` parameter.\",\n  SpreadVariance: \"Spread properties cannot have variance.\",\n  ThisParamAnnotationRequired:\n    \"A type annotation is required for the `this` parameter.\",\n  ThisParamBannedInConstructor:\n    \"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.\",\n  ThisParamMayNotBeOptional: \"The `this` parameter cannot be optional.\",\n  ThisParamMustBeFirst:\n    \"The `this` parameter must be the first function parameter.\",\n  ThisParamNoDefault: \"The `this` parameter may not have a default value.\",\n  TypeBeforeInitializer:\n    \"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.\",\n  TypeCastInPattern:\n    \"The type cast expression is expected to be wrapped with parenthesis.\",\n  UnexpectedExplicitInexactInObject:\n    \"Explicit inexact syntax must appear at the end of an inexact object.\",\n  UnexpectedReservedType: ({ reservedType }: { reservedType: string }) =>\n    `Unexpected reserved type ${reservedType}.`,\n  UnexpectedReservedUnderscore:\n    \"`_` is only allowed as a type argument to call or new.\",\n  UnexpectedSpaceBetweenModuloChecks:\n    \"Spaces between `%` and `checks` are not allowed here.\",\n  UnexpectedSpreadType:\n    \"Spread operator cannot appear in class or interface definitions.\",\n  UnexpectedSubtractionOperand:\n    'Unexpected token, expected \"number\" or \"bigint\".',\n  UnexpectedTokenAfterTypeParameter:\n    \"Expected an arrow function after this type parameter declaration.\",\n  UnexpectedTypeParameterBeforeAsyncArrowFunction:\n    \"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.\",\n  UnsupportedDeclareExportKind: ({\n    unsupportedExportKind,\n    suggestion,\n  }: {\n    unsupportedExportKind: string;\n    suggestion: string;\n  }) =>\n    `\\`declare export ${unsupportedExportKind}\\` is not supported. Use \\`${suggestion}\\` instead.`,\n  UnsupportedStatementInDeclareModule:\n    \"Only declares and type imports are allowed inside declare module.\",\n  UnterminatedFlowComment: \"Unterminated flow-comment.\",\n});\n/* eslint-disable sort-keys */\n\nfunction isEsModuleType(bodyElement: N.Node): boolean {\n  return (\n    bodyElement.type === \"DeclareExportAllDeclaration\" ||\n    (bodyElement.type === \"DeclareExportDeclaration\" &&\n      (!bodyElement.declaration ||\n        (bodyElement.declaration.type !== \"TypeAlias\" &&\n          bodyElement.declaration.type !== \"InterfaceDeclaration\")))\n  );\n}\n\nfunction hasTypeImportKind(node: N.Node): boolean {\n  return node.importKind === \"type\" || node.importKind === \"typeof\";\n}\n\nconst exportSuggestions = {\n  const: \"declare export var\",\n  let: \"declare export var\",\n  type: \"export type\",\n  interface: \"export interface\",\n};\n\n// Like Array#filter, but returns a tuple [ acceptedElements, discardedElements ]\nfunction partition<T>(\n  list: T[],\n  test: (c: T, b: number, a: T[]) => boolean | undefined | null,\n): [T[], T[]] {\n  const list1: T[] = [];\n  const list2: T[] = [];\n  for (let i = 0; i < list.length; i++) {\n    (test(list[i], i, list) ? list1 : list2).push(list[i]);\n  }\n  return [list1, list2];\n}\n\nconst FLOW_PRAGMA_REGEX = /\\*?\\s*@((?:no)?flow)\\b/;\n\n// Flow enums types\ntype EnumExplicitType = null | \"boolean\" | \"number\" | \"string\" | \"symbol\";\n\ntype EnumContext = {\n  enumName: string;\n  explicitType: EnumExplicitType;\n  memberName: string;\n};\n\ntype EnumMemberInit =\n  | {\n      type: \"number\";\n      loc: Position;\n      value: N.Node;\n    }\n  | {\n      type: \"string\";\n      loc: Position;\n      value: N.Node;\n    }\n  | {\n      type: \"boolean\";\n      loc: Position;\n      value: N.Node;\n    }\n  | {\n      type: \"invalid\";\n      loc: Position;\n    }\n  | {\n      type: \"none\";\n      loc: Position;\n    };\n\nexport default (superClass: typeof Parser) =>\n  class FlowParserMixin extends superClass implements Parser {\n    // The value of the @flow/@noflow pragma. Initially undefined, transitions\n    // to \"@flow\" or \"@noflow\" if we see a pragma. Transitions to null if we are\n    // past the initial comment.\n    flowPragma: void | null | \"flow\" | \"noflow\" = undefined;\n\n    getScopeHandler(): {\n      new (...args: any): FlowScopeHandler;\n    } {\n      return FlowScopeHandler;\n    }\n\n    shouldParseTypes(): boolean {\n      return this.getPluginOption(\"flow\", \"all\") || this.flowPragma === \"flow\";\n    }\n\n    shouldParseEnums(): boolean {\n      return !!this.getPluginOption(\"flow\", \"enums\");\n    }\n\n    finishToken(type: TokenType, val: any): void {\n      if (\n        type !== tt.string &&\n        type !== tt.semi &&\n        type !== tt.interpreterDirective\n      ) {\n        if (this.flowPragma === undefined) {\n          this.flowPragma = null;\n        }\n      }\n      super.finishToken(type, val);\n    }\n\n    addComment(comment: N.Comment): void {\n      if (this.flowPragma === undefined) {\n        // Try to parse a flow pragma.\n        const matches = FLOW_PRAGMA_REGEX.exec(comment.value);\n        if (!matches) {\n          // do nothing\n        } else if (matches[1] === \"flow\") {\n          this.flowPragma = \"flow\";\n        } else if (matches[1] === \"noflow\") {\n          this.flowPragma = \"noflow\";\n        } else {\n          throw new Error(\"Unexpected flow pragma\");\n        }\n      }\n      super.addComment(comment);\n    }\n\n    flowParseTypeInitialiser(tok?: TokenType): N.FlowType {\n      const oldInType = this.state.inType;\n      this.state.inType = true;\n      this.expect(tok || tt.colon);\n\n      const type = this.flowParseType();\n      this.state.inType = oldInType;\n      return type;\n    }\n\n    flowParsePredicate(): N.FlowPredicate {\n      const node = this.startNode();\n      const moduloLoc = this.state.startLoc;\n      this.next(); // eat `%`\n      this.expectContextual(tt._checks);\n      // Force '%' and 'checks' to be adjacent\n      if (this.state.lastTokStart > moduloLoc.index + 1) {\n        this.raise(FlowErrors.UnexpectedSpaceBetweenModuloChecks, {\n          at: moduloLoc,\n        });\n      }\n      if (this.eat(tt.parenL)) {\n        node.value = super.parseExpression();\n        this.expect(tt.parenR);\n        return this.finishNode(node, \"DeclaredPredicate\");\n      } else {\n        return this.finishNode(node, \"InferredPredicate\");\n      }\n    }\n\n    flowParseTypeAndPredicateInitialiser(): [\n      N.FlowType | undefined | null,\n      N.FlowPredicate | undefined | null,\n    ] {\n      const oldInType = this.state.inType;\n      this.state.inType = true;\n      this.expect(tt.colon);\n      let type = null;\n      let predicate = null;\n      if (this.match(tt.modulo)) {\n        this.state.inType = oldInType;\n        predicate = this.flowParsePredicate();\n      } else {\n        type = this.flowParseType();\n        this.state.inType = oldInType;\n        if (this.match(tt.modulo)) {\n          predicate = this.flowParsePredicate();\n        }\n      }\n      return [type, predicate];\n    }\n\n    flowParseDeclareClass(\n      node: Undone<N.FlowDeclareClass>,\n    ): N.FlowDeclareClass {\n      this.next();\n      this.flowParseInterfaceish(node, /*isClass*/ true);\n      return this.finishNode(node, \"DeclareClass\");\n    }\n\n    flowParseDeclareFunction(\n      node: Undone<N.FlowDeclareFunction>,\n    ): N.FlowDeclareFunction {\n      this.next();\n\n      const id = (node.id = this.parseIdentifier());\n\n      const typeNode = this.startNode();\n      const typeContainer = this.startNode<N.TypeAnnotation>();\n\n      if (this.match(tt.lt)) {\n        typeNode.typeParameters = this.flowParseTypeParameterDeclaration();\n      } else {\n        typeNode.typeParameters = null;\n      }\n\n      this.expect(tt.parenL);\n      const tmp = this.flowParseFunctionTypeParams();\n      typeNode.params = tmp.params;\n      typeNode.rest = tmp.rest;\n      typeNode.this = tmp._this;\n      this.expect(tt.parenR);\n\n      [typeNode.returnType, node.predicate] =\n        this.flowParseTypeAndPredicateInitialiser();\n\n      typeContainer.typeAnnotation = this.finishNode(\n        typeNode,\n        \"FunctionTypeAnnotation\",\n      );\n\n      id.typeAnnotation = this.finishNode(typeContainer, \"TypeAnnotation\");\n\n      this.resetEndLocation(id);\n      this.semicolon();\n\n      this.scope.declareName(\n        node.id.name,\n        BIND_FLOW_DECLARE_FN,\n        node.id.loc.start,\n      );\n\n      return this.finishNode(node, \"DeclareFunction\");\n    }\n\n    flowParseDeclare(\n      node: Undone<N.FlowDeclare>,\n      insideModule?: boolean,\n    ): N.FlowDeclare {\n      if (this.match(tt._class)) {\n        return this.flowParseDeclareClass(node);\n      } else if (this.match(tt._function)) {\n        return this.flowParseDeclareFunction(node);\n      } else if (this.match(tt._var)) {\n        return this.flowParseDeclareVariable(node);\n      } else if (this.eatContextual(tt._module)) {\n        if (this.match(tt.dot)) {\n          return this.flowParseDeclareModuleExports(node);\n        } else {\n          if (insideModule) {\n            this.raise(FlowErrors.NestedDeclareModule, {\n              at: this.state.lastTokStartLoc,\n            });\n          }\n          return this.flowParseDeclareModule(node);\n        }\n      } else if (this.isContextual(tt._type)) {\n        return this.flowParseDeclareTypeAlias(node);\n      } else if (this.isContextual(tt._opaque)) {\n        return this.flowParseDeclareOpaqueType(node);\n      } else if (this.isContextual(tt._interface)) {\n        return this.flowParseDeclareInterface(node);\n      } else if (this.match(tt._export)) {\n        return this.flowParseDeclareExportDeclaration(node, insideModule);\n      } else {\n        this.unexpected();\n      }\n    }\n\n    flowParseDeclareVariable(\n      node: Undone<N.FlowDeclareVariable>,\n    ): N.FlowDeclareVariable {\n      this.next();\n      node.id = this.flowParseTypeAnnotatableIdentifier(\n        /*allowPrimitiveOverride*/ true,\n      );\n      this.scope.declareName(node.id.name, BIND_VAR, node.id.loc.start);\n      this.semicolon();\n      return this.finishNode(node, \"DeclareVariable\");\n    }\n\n    flowParseDeclareModule(\n      node: Undone<N.FlowDeclareModule>,\n    ): N.FlowDeclareModule {\n      this.scope.enter(SCOPE_OTHER);\n\n      if (this.match(tt.string)) {\n        node.id = super.parseExprAtom();\n      } else {\n        node.id = this.parseIdentifier();\n      }\n\n      const bodyNode = (node.body = this.startNode());\n      // @ts-expect-error refine typings\n      const body = (bodyNode.body = []);\n      this.expect(tt.braceL);\n      while (!this.match(tt.braceR)) {\n        let bodyNode = this.startNode<N.ImportDeclaration>();\n\n        if (this.match(tt._import)) {\n          this.next();\n          if (!this.isContextual(tt._type) && !this.match(tt._typeof)) {\n            this.raise(FlowErrors.InvalidNonTypeImportInDeclareModule, {\n              at: this.state.lastTokStartLoc,\n            });\n          }\n          super.parseImport(bodyNode);\n        } else {\n          this.expectContextual(\n            tt._declare,\n            FlowErrors.UnsupportedStatementInDeclareModule,\n          );\n          // @ts-expect-error refine typings\n          bodyNode = this.flowParseDeclare(bodyNode, true);\n        }\n\n        body.push(bodyNode);\n      }\n\n      this.scope.exit();\n\n      this.expect(tt.braceR);\n\n      this.finishNode(bodyNode, \"BlockStatement\");\n\n      let kind: \"CommonJS\" | \"ES\" | null = null;\n      let hasModuleExport = false;\n      body.forEach(bodyElement => {\n        if (isEsModuleType(bodyElement)) {\n          if (kind === \"CommonJS\") {\n            this.raise(FlowErrors.AmbiguousDeclareModuleKind, {\n              at: bodyElement,\n            });\n          }\n          kind = \"ES\";\n        } else if (bodyElement.type === \"DeclareModuleExports\") {\n          if (hasModuleExport) {\n            this.raise(FlowErrors.DuplicateDeclareModuleExports, {\n              at: bodyElement,\n            });\n          }\n          if (kind === \"ES\") {\n            this.raise(FlowErrors.AmbiguousDeclareModuleKind, {\n              at: bodyElement,\n            });\n          }\n          kind = \"CommonJS\";\n          hasModuleExport = true;\n        }\n      });\n\n      node.kind = kind || \"CommonJS\";\n      return this.finishNode(node, \"DeclareModule\");\n    }\n\n    flowParseDeclareExportDeclaration(\n      node: Undone<N.FlowDeclareExportDeclaration>,\n      insideModule?: boolean | null,\n    ): N.FlowDeclareExportDeclaration {\n      this.expect(tt._export);\n\n      if (this.eat(tt._default)) {\n        if (this.match(tt._function) || this.match(tt._class)) {\n          // declare export default class ...\n          // declare export default function ...\n          node.declaration = this.flowParseDeclare(this.startNode());\n        } else {\n          // declare export default [type];\n          node.declaration = this.flowParseType();\n          this.semicolon();\n        }\n        node.default = true;\n\n        return this.finishNode(node, \"DeclareExportDeclaration\");\n      } else {\n        if (\n          this.match(tt._const) ||\n          this.isLet() ||\n          ((this.isContextual(tt._type) || this.isContextual(tt._interface)) &&\n            !insideModule)\n        ) {\n          const label = this.state.value as\n            | \"const\"\n            | \"let\"\n            | \"type\"\n            | \"interface\";\n          throw this.raise(FlowErrors.UnsupportedDeclareExportKind, {\n            at: this.state.startLoc,\n            unsupportedExportKind: label,\n            suggestion: exportSuggestions[label],\n          });\n        }\n\n        if (\n          this.match(tt._var) || // declare export var ...\n          this.match(tt._function) || // declare export function ...\n          this.match(tt._class) || // declare export class ...\n          this.isContextual(tt._opaque) // declare export opaque ..\n        ) {\n          node.declaration = this.flowParseDeclare(this.startNode());\n          node.default = false;\n\n          return this.finishNode(node, \"DeclareExportDeclaration\");\n        } else if (\n          this.match(tt.star) || // declare export * from ''\n          this.match(tt.braceL) || // declare export {} ...\n          this.isContextual(tt._interface) || // declare export interface ...\n          this.isContextual(tt._type) || // declare export type ...\n          this.isContextual(tt._opaque) // declare export opaque type ...\n        ) {\n          node = this.parseExport(\n            node as Undone<N.ExportNamedDeclaration | N.ExportAllDeclaration>,\n            /* decorators */ null,\n          );\n          if (node.type === \"ExportNamedDeclaration\") {\n            node.type = \"ExportDeclaration\";\n            node.default = false;\n            delete node.exportKind;\n          }\n\n          node.type = \"Declare\" + node.type;\n\n          return node as N.Node;\n        }\n      }\n\n      this.unexpected();\n    }\n\n    flowParseDeclareModuleExports(\n      node: Undone<N.FlowDeclareModuleExports>,\n    ): N.FlowDeclareModuleExports {\n      this.next();\n      this.expectContextual(tt._exports);\n      node.typeAnnotation = this.flowParseTypeAnnotation();\n      this.semicolon();\n\n      return this.finishNode(node, \"DeclareModuleExports\");\n    }\n\n    flowParseDeclareTypeAlias(\n      node: Undone<N.FlowDeclareTypeAlias>,\n    ): N.FlowDeclareTypeAlias {\n      this.next();\n      const finished = this.flowParseTypeAlias(node);\n      // Don't do finishNode as we don't want to process comments twice\n      finished.type = \"DeclareTypeAlias\";\n      return finished;\n    }\n\n    flowParseDeclareOpaqueType(\n      node: Undone<N.FlowDeclareOpaqueType>,\n    ): N.FlowDeclareOpaqueType {\n      this.next();\n      const finished = this.flowParseOpaqueType(node, true);\n      // Don't do finishNode as we don't want to process comments twice\n      finished.type = \"DeclareOpaqueType\";\n      return finished;\n    }\n\n    flowParseDeclareInterface(\n      node: Undone<N.FlowDeclareInterface>,\n    ): N.FlowDeclareInterface {\n      this.next();\n      this.flowParseInterfaceish(node, /* isClass */ false);\n      return this.finishNode(node, \"DeclareInterface\");\n    }\n\n    // Interfaces\n\n    flowParseInterfaceish(node: Undone<N.FlowDeclare>, isClass: boolean): void {\n      node.id = this.flowParseRestrictedIdentifier(\n        /* liberal */ !isClass,\n        /* declaration */ true,\n      );\n\n      this.scope.declareName(\n        node.id.name,\n        isClass ? BIND_FUNCTION : BIND_LEXICAL,\n        node.id.loc.start,\n      );\n\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterDeclaration();\n      } else {\n        node.typeParameters = null;\n      }\n\n      node.extends = [];\n\n      if (this.eat(tt._extends)) {\n        do {\n          node.extends.push(this.flowParseInterfaceExtends());\n        } while (!isClass && this.eat(tt.comma));\n      }\n\n      if (isClass) {\n        node.implements = [];\n        node.mixins = [];\n\n        if (this.eatContextual(tt._mixins)) {\n          do {\n            node.mixins.push(this.flowParseInterfaceExtends());\n          } while (this.eat(tt.comma));\n        }\n\n        if (this.eatContextual(tt._implements)) {\n          do {\n            node.implements.push(this.flowParseInterfaceExtends());\n          } while (this.eat(tt.comma));\n        }\n      }\n\n      node.body = this.flowParseObjectType({\n        allowStatic: isClass,\n        allowExact: false,\n        allowSpread: false,\n        allowProto: isClass,\n        allowInexact: false,\n      });\n    }\n\n    flowParseInterfaceExtends(): N.FlowInterfaceExtends {\n      const node = this.startNode();\n\n      node.id = this.flowParseQualifiedTypeIdentifier();\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterInstantiation();\n      } else {\n        node.typeParameters = null;\n      }\n\n      return this.finishNode(node, \"InterfaceExtends\");\n    }\n\n    flowParseInterface(node: Undone<N.FlowInterface>): N.FlowInterface {\n      this.flowParseInterfaceish(node, /* isClass */ false);\n      return this.finishNode(node, \"InterfaceDeclaration\");\n    }\n\n    checkNotUnderscore(word: string) {\n      if (word === \"_\") {\n        this.raise(FlowErrors.UnexpectedReservedUnderscore, {\n          at: this.state.startLoc,\n        });\n      }\n    }\n\n    checkReservedType(word: string, startLoc: Position, declaration?: boolean) {\n      if (!reservedTypes.has(word)) return;\n\n      this.raise(\n        declaration\n          ? FlowErrors.AssignReservedType\n          : FlowErrors.UnexpectedReservedType,\n        {\n          at: startLoc,\n          reservedType: word,\n        },\n      );\n    }\n\n    flowParseRestrictedIdentifier(\n      liberal?: boolean,\n      declaration?: boolean,\n    ): N.Identifier {\n      this.checkReservedType(\n        this.state.value,\n        this.state.startLoc,\n        declaration,\n      );\n      return this.parseIdentifier(liberal);\n    }\n\n    // Type aliases\n\n    flowParseTypeAlias(node: Undone<N.FlowTypeAlias>): N.FlowTypeAlias {\n      node.id = this.flowParseRestrictedIdentifier(\n        /* liberal */ false,\n        /* declaration */ true,\n      );\n      this.scope.declareName(node.id.name, BIND_LEXICAL, node.id.loc.start);\n\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterDeclaration();\n      } else {\n        node.typeParameters = null;\n      }\n\n      node.right = this.flowParseTypeInitialiser(tt.eq);\n      this.semicolon();\n\n      return this.finishNode(node, \"TypeAlias\");\n    }\n\n    flowParseOpaqueType(\n      node: Undone<N.FlowOpaqueType>,\n      declare: boolean,\n    ): N.FlowOpaqueType {\n      this.expectContextual(tt._type);\n      node.id = this.flowParseRestrictedIdentifier(\n        /* liberal */ true,\n        /* declaration */ true,\n      );\n      this.scope.declareName(node.id.name, BIND_LEXICAL, node.id.loc.start);\n\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterDeclaration();\n      } else {\n        node.typeParameters = null;\n      }\n\n      // Parse the supertype\n      node.supertype = null;\n      if (this.match(tt.colon)) {\n        node.supertype = this.flowParseTypeInitialiser(tt.colon);\n      }\n\n      node.impltype = null;\n      if (!declare) {\n        node.impltype = this.flowParseTypeInitialiser(tt.eq);\n      }\n      this.semicolon();\n\n      return this.finishNode(node, \"OpaqueType\");\n    }\n\n    // Type annotations\n\n    flowParseTypeParameter(requireDefault: boolean = false): N.TypeParameter {\n      const nodeStartLoc = this.state.startLoc;\n\n      const node = this.startNode<N.TypeParameter>();\n\n      const variance = this.flowParseVariance();\n\n      const ident = this.flowParseTypeAnnotatableIdentifier();\n      node.name = ident.name;\n      // @ts-expect-error migrate to Babel types\n      node.variance = variance;\n      // @ts-expect-error migrate to Babel types\n      node.bound = ident.typeAnnotation;\n\n      if (this.match(tt.eq)) {\n        this.eat(tt.eq);\n        // @ts-expect-error migrate to Babel types\n        node.default = this.flowParseType();\n      } else {\n        if (requireDefault) {\n          this.raise(FlowErrors.MissingTypeParamDefault, { at: nodeStartLoc });\n        }\n      }\n\n      return this.finishNode(node, \"TypeParameter\");\n    }\n\n    flowParseTypeParameterDeclaration(): N.TypeParameterDeclaration {\n      const oldInType = this.state.inType;\n      const node = this.startNode<N.TypeParameterDeclaration>();\n      node.params = [];\n\n      this.state.inType = true;\n\n      // istanbul ignore else: this condition is already checked at all call sites\n      if (this.match(tt.lt) || this.match(tt.jsxTagStart)) {\n        this.next();\n      } else {\n        this.unexpected();\n      }\n\n      let defaultRequired = false;\n\n      do {\n        const typeParameter = this.flowParseTypeParameter(defaultRequired);\n\n        node.params.push(typeParameter);\n\n        if (typeParameter.default) {\n          defaultRequired = true;\n        }\n\n        if (!this.match(tt.gt)) {\n          this.expect(tt.comma);\n        }\n      } while (!this.match(tt.gt));\n      this.expect(tt.gt);\n\n      this.state.inType = oldInType;\n\n      return this.finishNode(node, \"TypeParameterDeclaration\");\n    }\n\n    flowParseTypeParameterInstantiation(): N.TypeParameterInstantiation {\n      const node = this.startNode<N.TypeParameterInstantiation>();\n      const oldInType = this.state.inType;\n      node.params = [];\n\n      this.state.inType = true;\n\n      this.expect(tt.lt);\n      const oldNoAnonFunctionType = this.state.noAnonFunctionType;\n      this.state.noAnonFunctionType = false;\n      while (!this.match(tt.gt)) {\n        node.params.push(this.flowParseType());\n        if (!this.match(tt.gt)) {\n          this.expect(tt.comma);\n        }\n      }\n      this.state.noAnonFunctionType = oldNoAnonFunctionType;\n      this.expect(tt.gt);\n\n      this.state.inType = oldInType;\n\n      return this.finishNode(node, \"TypeParameterInstantiation\");\n    }\n\n    flowParseTypeParameterInstantiationCallOrNew(): N.TypeParameterInstantiation {\n      const node = this.startNode<N.TypeParameterInstantiation>();\n      const oldInType = this.state.inType;\n      node.params = [];\n\n      this.state.inType = true;\n\n      this.expect(tt.lt);\n      while (!this.match(tt.gt)) {\n        node.params.push(this.flowParseTypeOrImplicitInstantiation());\n        if (!this.match(tt.gt)) {\n          this.expect(tt.comma);\n        }\n      }\n      this.expect(tt.gt);\n\n      this.state.inType = oldInType;\n\n      return this.finishNode(node, \"TypeParameterInstantiation\");\n    }\n\n    flowParseInterfaceType(): N.FlowInterfaceType {\n      const node = this.startNode<N.FlowInterfaceType>();\n      this.expectContextual(tt._interface);\n\n      node.extends = [];\n      if (this.eat(tt._extends)) {\n        do {\n          node.extends.push(this.flowParseInterfaceExtends());\n        } while (this.eat(tt.comma));\n      }\n\n      node.body = this.flowParseObjectType({\n        allowStatic: false,\n        allowExact: false,\n        allowSpread: false,\n        allowProto: false,\n        allowInexact: false,\n      });\n\n      return this.finishNode(node, \"InterfaceTypeAnnotation\");\n    }\n\n    flowParseObjectPropertyKey(): N.Expression {\n      return this.match(tt.num) || this.match(tt.string)\n        ? super.parseExprAtom()\n        : this.parseIdentifier(true);\n    }\n\n    flowParseObjectTypeIndexer(\n      node: Undone<N.FlowObjectTypeIndexer>,\n      isStatic: boolean,\n      variance?: N.FlowVariance | null,\n    ): N.FlowObjectTypeIndexer {\n      node.static = isStatic;\n\n      // Note: bracketL has already been consumed\n      if (this.lookahead().type === tt.colon) {\n        node.id = this.flowParseObjectPropertyKey();\n        node.key = this.flowParseTypeInitialiser();\n      } else {\n        node.id = null;\n        node.key = this.flowParseType();\n      }\n      this.expect(tt.bracketR);\n      node.value = this.flowParseTypeInitialiser();\n      node.variance = variance;\n\n      return this.finishNode(node, \"ObjectTypeIndexer\");\n    }\n\n    flowParseObjectTypeInternalSlot(\n      node: Undone<N.FlowObjectTypeInternalSlot>,\n      isStatic: boolean,\n    ): N.FlowObjectTypeInternalSlot {\n      node.static = isStatic;\n      // Note: both bracketL have already been consumed\n      node.id = this.flowParseObjectPropertyKey();\n      this.expect(tt.bracketR);\n      this.expect(tt.bracketR);\n      if (this.match(tt.lt) || this.match(tt.parenL)) {\n        node.method = true;\n        node.optional = false;\n        node.value = this.flowParseObjectTypeMethodish(\n          this.startNodeAt(node.loc.start),\n        );\n      } else {\n        node.method = false;\n        if (this.eat(tt.question)) {\n          node.optional = true;\n        }\n        node.value = this.flowParseTypeInitialiser();\n      }\n      return this.finishNode(node, \"ObjectTypeInternalSlot\");\n    }\n\n    flowParseObjectTypeMethodish(\n      node: Undone<N.FlowFunctionTypeAnnotation>,\n    ): N.FlowFunctionTypeAnnotation {\n      node.params = [];\n      node.rest = null;\n      node.typeParameters = null;\n      node.this = null;\n\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterDeclaration();\n      }\n\n      this.expect(tt.parenL);\n      if (this.match(tt._this)) {\n        node.this = this.flowParseFunctionTypeParam(/* first */ true);\n        // match Flow parser behavior\n        node.this.name = null;\n        if (!this.match(tt.parenR)) {\n          this.expect(tt.comma);\n        }\n      }\n      while (!this.match(tt.parenR) && !this.match(tt.ellipsis)) {\n        node.params.push(this.flowParseFunctionTypeParam(false));\n        if (!this.match(tt.parenR)) {\n          this.expect(tt.comma);\n        }\n      }\n\n      if (this.eat(tt.ellipsis)) {\n        node.rest = this.flowParseFunctionTypeParam(false);\n      }\n      this.expect(tt.parenR);\n      node.returnType = this.flowParseTypeInitialiser();\n\n      return this.finishNode(node, \"FunctionTypeAnnotation\");\n    }\n\n    flowParseObjectTypeCallProperty(\n      node: Undone<N.FlowObjectTypeCallProperty>,\n      isStatic: boolean,\n    ): N.FlowObjectTypeCallProperty {\n      const valueNode = this.startNode();\n      node.static = isStatic;\n      node.value = this.flowParseObjectTypeMethodish(valueNode);\n      return this.finishNode(node, \"ObjectTypeCallProperty\");\n    }\n\n    flowParseObjectType({\n      allowStatic,\n      allowExact,\n      allowSpread,\n      allowProto,\n      allowInexact,\n    }: {\n      allowStatic: boolean;\n      allowExact: boolean;\n      allowSpread: boolean;\n      allowProto: boolean;\n      allowInexact: boolean;\n    }): N.FlowObjectTypeAnnotation {\n      const oldInType = this.state.inType;\n      this.state.inType = true;\n\n      const nodeStart = this.startNode();\n\n      nodeStart.callProperties = [];\n      nodeStart.properties = [];\n      nodeStart.indexers = [];\n      nodeStart.internalSlots = [];\n\n      let endDelim;\n      let exact;\n      let inexact = false;\n      if (allowExact && this.match(tt.braceBarL)) {\n        this.expect(tt.braceBarL);\n        endDelim = tt.braceBarR;\n        exact = true;\n      } else {\n        this.expect(tt.braceL);\n        endDelim = tt.braceR;\n        exact = false;\n      }\n\n      nodeStart.exact = exact;\n\n      while (!this.match(endDelim)) {\n        let isStatic = false;\n        let protoStartLoc: Position | undefined | null = null;\n        let inexactStartLoc: Position | undefined | null = null;\n        const node = this.startNode();\n\n        if (allowProto && this.isContextual(tt._proto)) {\n          const lookahead = this.lookahead();\n\n          if (lookahead.type !== tt.colon && lookahead.type !== tt.question) {\n            this.next();\n            protoStartLoc = this.state.startLoc;\n            allowStatic = false;\n          }\n        }\n\n        if (allowStatic && this.isContextual(tt._static)) {\n          const lookahead = this.lookahead();\n\n          // static is a valid identifier name\n          if (lookahead.type !== tt.colon && lookahead.type !== tt.question) {\n            this.next();\n            isStatic = true;\n          }\n        }\n\n        const variance = this.flowParseVariance();\n\n        if (this.eat(tt.bracketL)) {\n          if (protoStartLoc != null) {\n            this.unexpected(protoStartLoc);\n          }\n          if (this.eat(tt.bracketL)) {\n            if (variance) {\n              this.unexpected(variance.loc.start);\n            }\n            nodeStart.internalSlots.push(\n              this.flowParseObjectTypeInternalSlot(node, isStatic),\n            );\n          } else {\n            nodeStart.indexers.push(\n              this.flowParseObjectTypeIndexer(node, isStatic, variance),\n            );\n          }\n        } else if (this.match(tt.parenL) || this.match(tt.lt)) {\n          if (protoStartLoc != null) {\n            this.unexpected(protoStartLoc);\n          }\n          if (variance) {\n            this.unexpected(variance.loc.start);\n          }\n          nodeStart.callProperties.push(\n            this.flowParseObjectTypeCallProperty(node, isStatic),\n          );\n        } else {\n          let kind = \"init\";\n\n          if (this.isContextual(tt._get) || this.isContextual(tt._set)) {\n            const lookahead = this.lookahead();\n            if (tokenIsLiteralPropertyName(lookahead.type)) {\n              kind = this.state.value;\n              this.next();\n            }\n          }\n\n          const propOrInexact = this.flowParseObjectTypeProperty(\n            node,\n            isStatic,\n            protoStartLoc,\n            variance,\n            kind,\n            allowSpread,\n            allowInexact ?? !exact,\n          );\n\n          if (propOrInexact === null) {\n            inexact = true;\n            inexactStartLoc = this.state.lastTokStartLoc;\n          } else {\n            nodeStart.properties.push(propOrInexact);\n          }\n        }\n\n        this.flowObjectTypeSemicolon();\n\n        if (\n          inexactStartLoc &&\n          !this.match(tt.braceR) &&\n          !this.match(tt.braceBarR)\n        ) {\n          this.raise(FlowErrors.UnexpectedExplicitInexactInObject, {\n            at: inexactStartLoc,\n          });\n        }\n      }\n\n      this.expect(endDelim);\n\n      /* The inexact flag should only be added on ObjectTypeAnnotations that\n       * are not the body of an interface, declare interface, or declare class.\n       * Since spreads are only allowed in object types, checking that is\n       * sufficient here.\n       */\n      if (allowSpread) {\n        nodeStart.inexact = inexact;\n      }\n\n      const out = this.finishNode(nodeStart, \"ObjectTypeAnnotation\");\n\n      this.state.inType = oldInType;\n\n      return out;\n    }\n\n    flowParseObjectTypeProperty(\n      node: Undone<N.FlowObjectTypeProperty | N.FlowObjectTypeSpreadProperty>,\n      isStatic: boolean,\n      protoStartLoc: Position | undefined | null,\n      variance: N.FlowVariance | undefined | null,\n      kind: string,\n      allowSpread: boolean,\n      allowInexact: boolean,\n    ): N.FlowObjectTypeProperty | N.FlowObjectTypeSpreadProperty | null {\n      if (this.eat(tt.ellipsis)) {\n        const isInexactToken =\n          this.match(tt.comma) ||\n          this.match(tt.semi) ||\n          this.match(tt.braceR) ||\n          this.match(tt.braceBarR);\n\n        if (isInexactToken) {\n          if (!allowSpread) {\n            this.raise(FlowErrors.InexactInsideNonObject, {\n              at: this.state.lastTokStartLoc,\n            });\n          } else if (!allowInexact) {\n            this.raise(FlowErrors.InexactInsideExact, {\n              at: this.state.lastTokStartLoc,\n            });\n          }\n          if (variance) {\n            this.raise(FlowErrors.InexactVariance, { at: variance });\n          }\n\n          return null;\n        }\n\n        if (!allowSpread) {\n          this.raise(FlowErrors.UnexpectedSpreadType, {\n            at: this.state.lastTokStartLoc,\n          });\n        }\n        if (protoStartLoc != null) {\n          this.unexpected(protoStartLoc);\n        }\n        if (variance) {\n          this.raise(FlowErrors.SpreadVariance, { at: variance });\n        }\n\n        node.argument = this.flowParseType();\n        return this.finishNode(node, \"ObjectTypeSpreadProperty\");\n      } else {\n        node.key = this.flowParseObjectPropertyKey();\n        node.static = isStatic;\n        node.proto = protoStartLoc != null;\n        node.kind = kind;\n\n        let optional = false;\n        if (this.match(tt.lt) || this.match(tt.parenL)) {\n          // This is a method property\n          node.method = true;\n\n          if (protoStartLoc != null) {\n            this.unexpected(protoStartLoc);\n          }\n          if (variance) {\n            this.unexpected(variance.loc.start);\n          }\n\n          node.value = this.flowParseObjectTypeMethodish(\n            this.startNodeAt(node.loc.start),\n          );\n          if (kind === \"get\" || kind === \"set\") {\n            this.flowCheckGetterSetterParams(node);\n          }\n          /** Declared classes/interfaces do not allow spread */\n          if (\n            !allowSpread &&\n            node.key.name === \"constructor\" &&\n            node.value.this\n          ) {\n            this.raise(FlowErrors.ThisParamBannedInConstructor, {\n              at: node.value.this,\n            });\n          }\n        } else {\n          if (kind !== \"init\") this.unexpected();\n\n          node.method = false;\n\n          if (this.eat(tt.question)) {\n            optional = true;\n          }\n          node.value = this.flowParseTypeInitialiser();\n          node.variance = variance;\n        }\n\n        node.optional = optional;\n\n        return this.finishNode(node, \"ObjectTypeProperty\");\n      }\n    }\n\n    // This is similar to checkGetterSetterParams, but as\n    // @babel/parser uses non estree properties we cannot reuse it here\n    flowCheckGetterSetterParams(\n      property: Undone<\n        N.FlowObjectTypeProperty | N.FlowObjectTypeSpreadProperty\n      >,\n    ): void {\n      const paramCount = property.kind === \"get\" ? 0 : 1;\n      const length =\n        property.value.params.length + (property.value.rest ? 1 : 0);\n\n      if (property.value.this) {\n        this.raise(\n          property.kind === \"get\"\n            ? FlowErrors.GetterMayNotHaveThisParam\n            : FlowErrors.SetterMayNotHaveThisParam,\n          { at: property.value.this },\n        );\n      }\n\n      if (length !== paramCount) {\n        this.raise(\n          property.kind === \"get\"\n            ? Errors.BadGetterArity\n            : Errors.BadSetterArity,\n          { at: property },\n        );\n      }\n\n      if (property.kind === \"set\" && property.value.rest) {\n        this.raise(Errors.BadSetterRestParameter, { at: property });\n      }\n    }\n\n    flowObjectTypeSemicolon(): void {\n      if (\n        !this.eat(tt.semi) &&\n        !this.eat(tt.comma) &&\n        !this.match(tt.braceR) &&\n        !this.match(tt.braceBarR)\n      ) {\n        this.unexpected();\n      }\n    }\n\n    flowParseQualifiedTypeIdentifier(\n      startLoc?: Position,\n      id?: N.Identifier,\n    ): N.FlowQualifiedTypeIdentifier {\n      startLoc ??= this.state.startLoc;\n      let node: N.Identifier | N.FlowQualifiedTypeIdentifier =\n        id || this.flowParseRestrictedIdentifier(true);\n\n      while (this.eat(tt.dot)) {\n        const node2 = this.startNodeAt<N.FlowQualifiedTypeIdentifier>(startLoc);\n        node2.qualification = node;\n        node2.id = this.flowParseRestrictedIdentifier(true);\n        node = this.finishNode(node2, \"QualifiedTypeIdentifier\");\n      }\n\n      return node;\n    }\n\n    flowParseGenericType(\n      startLoc: Position,\n      id: N.Identifier,\n    ): N.FlowGenericTypeAnnotation {\n      const node = this.startNodeAt(startLoc);\n\n      node.typeParameters = null;\n      node.id = this.flowParseQualifiedTypeIdentifier(startLoc, id);\n\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterInstantiation();\n      }\n\n      return this.finishNode(node, \"GenericTypeAnnotation\");\n    }\n\n    flowParseTypeofType(): N.FlowTypeofTypeAnnotation {\n      const node = this.startNode();\n      this.expect(tt._typeof);\n      node.argument = this.flowParsePrimaryType();\n      return this.finishNode(node, \"TypeofTypeAnnotation\");\n    }\n\n    flowParseTupleType(): N.FlowTupleTypeAnnotation {\n      const node = this.startNode();\n      node.types = [];\n      this.expect(tt.bracketL);\n      // We allow trailing commas\n      while (this.state.pos < this.length && !this.match(tt.bracketR)) {\n        node.types.push(this.flowParseType());\n        if (this.match(tt.bracketR)) break;\n        this.expect(tt.comma);\n      }\n      this.expect(tt.bracketR);\n      return this.finishNode(node, \"TupleTypeAnnotation\");\n    }\n\n    flowParseFunctionTypeParam(first: boolean): N.FlowFunctionTypeParam {\n      let name = null;\n      let optional = false;\n      let typeAnnotation = null;\n      const node = this.startNode<N.FlowFunctionTypeParam>();\n      const lh = this.lookahead();\n      const isThis = this.state.type === tt._this;\n\n      if (lh.type === tt.colon || lh.type === tt.question) {\n        if (isThis && !first) {\n          this.raise(FlowErrors.ThisParamMustBeFirst, { at: node });\n        }\n        name = this.parseIdentifier(isThis);\n        if (this.eat(tt.question)) {\n          optional = true;\n          if (isThis) {\n            this.raise(FlowErrors.ThisParamMayNotBeOptional, { at: node });\n          }\n        }\n        typeAnnotation = this.flowParseTypeInitialiser();\n      } else {\n        typeAnnotation = this.flowParseType();\n      }\n      node.name = name;\n      node.optional = optional;\n      node.typeAnnotation = typeAnnotation;\n      return this.finishNode(node, \"FunctionTypeParam\");\n    }\n\n    reinterpretTypeAsFunctionTypeParam(\n      type: N.FlowType,\n    ): N.FlowFunctionTypeParam {\n      const node = this.startNodeAt(type.loc.start);\n      node.name = null;\n      node.optional = false;\n      node.typeAnnotation = type;\n      return this.finishNode(node, \"FunctionTypeParam\");\n    }\n\n    flowParseFunctionTypeParams(params: N.FlowFunctionTypeParam[] = []): {\n      params: N.FlowFunctionTypeParam[];\n      rest: N.FlowFunctionTypeParam | undefined | null;\n      _this: N.FlowFunctionTypeParam | undefined | null;\n    } {\n      let rest: N.FlowFunctionTypeParam | undefined | null = null;\n      let _this: N.FlowFunctionTypeParam | undefined | null = null;\n      if (this.match(tt._this)) {\n        _this = this.flowParseFunctionTypeParam(/* first */ true);\n        // match Flow parser behavior\n        _this.name = null;\n        if (!this.match(tt.parenR)) {\n          this.expect(tt.comma);\n        }\n      }\n      while (!this.match(tt.parenR) && !this.match(tt.ellipsis)) {\n        params.push(this.flowParseFunctionTypeParam(false));\n        if (!this.match(tt.parenR)) {\n          this.expect(tt.comma);\n        }\n      }\n      if (this.eat(tt.ellipsis)) {\n        rest = this.flowParseFunctionTypeParam(false);\n      }\n      return { params, rest, _this };\n    }\n\n    flowIdentToTypeAnnotation(\n      startLoc: Position,\n      node: Undone<N.FlowTypeAnnotation>,\n      id: N.Identifier,\n    ): N.FlowTypeAnnotation {\n      switch (id.name) {\n        case \"any\":\n          return this.finishNode(node, \"AnyTypeAnnotation\");\n\n        case \"bool\":\n        case \"boolean\":\n          return this.finishNode(node, \"BooleanTypeAnnotation\");\n\n        case \"mixed\":\n          return this.finishNode(node, \"MixedTypeAnnotation\");\n\n        case \"empty\":\n          return this.finishNode(node, \"EmptyTypeAnnotation\");\n\n        case \"number\":\n          return this.finishNode(node, \"NumberTypeAnnotation\");\n\n        case \"string\":\n          return this.finishNode(node, \"StringTypeAnnotation\");\n\n        case \"symbol\":\n          return this.finishNode(node, \"SymbolTypeAnnotation\");\n\n        default:\n          this.checkNotUnderscore(id.name);\n          return this.flowParseGenericType(startLoc, id);\n      }\n    }\n\n    // The parsing of types roughly parallels the parsing of expressions, and\n    // primary types are kind of like primary expressions...they're the\n    // primitives with which other types are constructed.\n    flowParsePrimaryType(): N.FlowTypeAnnotation {\n      const startLoc = this.state.startLoc;\n      const node = this.startNode();\n      let tmp;\n      let type;\n      let isGroupedType = false;\n      const oldNoAnonFunctionType = this.state.noAnonFunctionType;\n\n      switch (this.state.type) {\n        case tt.braceL:\n          return this.flowParseObjectType({\n            allowStatic: false,\n            allowExact: false,\n            allowSpread: true,\n            allowProto: false,\n            allowInexact: true,\n          });\n\n        case tt.braceBarL:\n          return this.flowParseObjectType({\n            allowStatic: false,\n            allowExact: true,\n            allowSpread: true,\n            allowProto: false,\n            allowInexact: false,\n          });\n\n        case tt.bracketL:\n          this.state.noAnonFunctionType = false;\n          type = this.flowParseTupleType();\n          this.state.noAnonFunctionType = oldNoAnonFunctionType;\n          return type;\n\n        case tt.lt:\n          node.typeParameters = this.flowParseTypeParameterDeclaration();\n          this.expect(tt.parenL);\n          tmp = this.flowParseFunctionTypeParams();\n          node.params = tmp.params;\n          node.rest = tmp.rest;\n          node.this = tmp._this;\n          this.expect(tt.parenR);\n\n          this.expect(tt.arrow);\n\n          node.returnType = this.flowParseType();\n\n          return this.finishNode(node, \"FunctionTypeAnnotation\");\n\n        case tt.parenL:\n          this.next();\n\n          // Check to see if this is actually a grouped type\n          if (!this.match(tt.parenR) && !this.match(tt.ellipsis)) {\n            if (tokenIsIdentifier(this.state.type) || this.match(tt._this)) {\n              const token = this.lookahead().type;\n              isGroupedType = token !== tt.question && token !== tt.colon;\n            } else {\n              isGroupedType = true;\n            }\n          }\n\n          if (isGroupedType) {\n            this.state.noAnonFunctionType = false;\n            type = this.flowParseType();\n            this.state.noAnonFunctionType = oldNoAnonFunctionType;\n\n            // A `,` or a `) =>` means this is an anonymous function type\n            if (\n              this.state.noAnonFunctionType ||\n              !(\n                this.match(tt.comma) ||\n                (this.match(tt.parenR) && this.lookahead().type === tt.arrow)\n              )\n            ) {\n              this.expect(tt.parenR);\n              return type;\n            } else {\n              // Eat a comma if there is one\n              this.eat(tt.comma);\n            }\n          }\n\n          if (type) {\n            tmp = this.flowParseFunctionTypeParams([\n              this.reinterpretTypeAsFunctionTypeParam(type),\n            ]);\n          } else {\n            tmp = this.flowParseFunctionTypeParams();\n          }\n\n          node.params = tmp.params;\n          node.rest = tmp.rest;\n          node.this = tmp._this;\n\n          this.expect(tt.parenR);\n\n          this.expect(tt.arrow);\n\n          node.returnType = this.flowParseType();\n\n          node.typeParameters = null;\n\n          return this.finishNode(node, \"FunctionTypeAnnotation\");\n\n        case tt.string:\n          return this.parseLiteral<N.StringLiteralTypeAnnotation>(\n            this.state.value,\n            \"StringLiteralTypeAnnotation\",\n          );\n\n        case tt._true:\n        case tt._false:\n          node.value = this.match(tt._true);\n          this.next();\n          return this.finishNode(\n            node as Undone<N.BooleanLiteralTypeAnnotation>,\n            \"BooleanLiteralTypeAnnotation\",\n          );\n\n        case tt.plusMin:\n          if (this.state.value === \"-\") {\n            this.next();\n            if (this.match(tt.num)) {\n              return this.parseLiteralAtNode<N.NumberLiteralTypeAnnotation>(\n                -this.state.value,\n                \"NumberLiteralTypeAnnotation\",\n                node,\n              );\n            }\n\n            if (this.match(tt.bigint)) {\n              return this.parseLiteralAtNode<N.BigIntLiteralTypeAnnotation>(\n                -this.state.value,\n                \"BigIntLiteralTypeAnnotation\",\n                node,\n              );\n            }\n\n            throw this.raise(FlowErrors.UnexpectedSubtractionOperand, {\n              at: this.state.startLoc,\n            });\n          }\n          this.unexpected();\n          return;\n        case tt.num:\n          return this.parseLiteral(\n            this.state.value,\n            \"NumberLiteralTypeAnnotation\",\n          );\n\n        case tt.bigint:\n          return this.parseLiteral(\n            this.state.value,\n            \"BigIntLiteralTypeAnnotation\",\n          );\n\n        case tt._void:\n          this.next();\n          return this.finishNode(node, \"VoidTypeAnnotation\");\n\n        case tt._null:\n          this.next();\n          return this.finishNode(node, \"NullLiteralTypeAnnotation\");\n\n        case tt._this:\n          this.next();\n          return this.finishNode(node, \"ThisTypeAnnotation\");\n\n        case tt.star:\n          this.next();\n          return this.finishNode(node, \"ExistsTypeAnnotation\");\n\n        case tt._typeof:\n          return this.flowParseTypeofType();\n\n        default:\n          if (tokenIsKeyword(this.state.type)) {\n            const label = tokenLabelName(this.state.type);\n            this.next();\n            return super.createIdentifier(node as Undone<N.Identifier>, label);\n          } else if (tokenIsIdentifier(this.state.type)) {\n            if (this.isContextual(tt._interface)) {\n              return this.flowParseInterfaceType();\n            }\n\n            return this.flowIdentToTypeAnnotation(\n              startLoc,\n              node,\n              this.parseIdentifier(),\n            );\n          }\n      }\n\n      this.unexpected();\n    }\n\n    flowParsePostfixType(): N.FlowTypeAnnotation {\n      const startLoc = this.state.startLoc;\n      let type = this.flowParsePrimaryType();\n      let seenOptionalIndexedAccess = false;\n      while (\n        (this.match(tt.bracketL) || this.match(tt.questionDot)) &&\n        !this.canInsertSemicolon()\n      ) {\n        const node = this.startNodeAt(startLoc);\n        const optional = this.eat(tt.questionDot);\n        seenOptionalIndexedAccess = seenOptionalIndexedAccess || optional;\n        this.expect(tt.bracketL);\n        if (!optional && this.match(tt.bracketR)) {\n          node.elementType = type;\n          this.next(); // eat `]`\n          type = this.finishNode(node, \"ArrayTypeAnnotation\");\n        } else {\n          node.objectType = type;\n          node.indexType = this.flowParseType();\n          this.expect(tt.bracketR);\n          if (seenOptionalIndexedAccess) {\n            node.optional = optional;\n            type = this.finishNode<N.FlowOptionalIndexedAccessType>(\n              // @ts-expect-error todo(flow->ts)\n              node,\n              \"OptionalIndexedAccessType\",\n            );\n          } else {\n            type = this.finishNode<N.FlowIndexedAccessType>(\n              // @ts-expect-error todo(flow->ts)\n              node,\n              \"IndexedAccessType\",\n            );\n          }\n        }\n      }\n      return type;\n    }\n\n    flowParsePrefixType(): N.FlowTypeAnnotation {\n      const node = this.startNode();\n      if (this.eat(tt.question)) {\n        node.typeAnnotation = this.flowParsePrefixType();\n        return this.finishNode(node, \"NullableTypeAnnotation\");\n      } else {\n        return this.flowParsePostfixType();\n      }\n    }\n\n    flowParseAnonFunctionWithoutParens(): N.FlowTypeAnnotation {\n      const param = this.flowParsePrefixType();\n      if (!this.state.noAnonFunctionType && this.eat(tt.arrow)) {\n        // TODO: This should be a type error. Passing in a SourceLocation, and it expects a Position.\n        const node = this.startNodeAt(param.loc.start);\n        node.params = [this.reinterpretTypeAsFunctionTypeParam(param)];\n        node.rest = null;\n        node.this = null;\n        node.returnType = this.flowParseType();\n        node.typeParameters = null;\n        return this.finishNode(node, \"FunctionTypeAnnotation\");\n      }\n      return param;\n    }\n\n    flowParseIntersectionType(): N.FlowTypeAnnotation {\n      const node = this.startNode();\n      this.eat(tt.bitwiseAND);\n      const type = this.flowParseAnonFunctionWithoutParens();\n      node.types = [type];\n      while (this.eat(tt.bitwiseAND)) {\n        node.types.push(this.flowParseAnonFunctionWithoutParens());\n      }\n      return node.types.length === 1\n        ? type\n        : this.finishNode(node, \"IntersectionTypeAnnotation\");\n    }\n\n    flowParseUnionType(): N.FlowTypeAnnotation {\n      const node = this.startNode();\n      this.eat(tt.bitwiseOR);\n      const type = this.flowParseIntersectionType();\n      node.types = [type];\n      while (this.eat(tt.bitwiseOR)) {\n        node.types.push(this.flowParseIntersectionType());\n      }\n      return node.types.length === 1\n        ? type\n        : this.finishNode(node, \"UnionTypeAnnotation\");\n    }\n\n    flowParseType(): N.FlowTypeAnnotation {\n      const oldInType = this.state.inType;\n      this.state.inType = true;\n      const type = this.flowParseUnionType();\n      this.state.inType = oldInType;\n      return type;\n    }\n\n    flowParseTypeOrImplicitInstantiation(): N.FlowTypeAnnotation {\n      if (this.state.type === tt.name && this.state.value === \"_\") {\n        const startLoc = this.state.startLoc;\n        const node = this.parseIdentifier();\n        return this.flowParseGenericType(startLoc, node);\n      } else {\n        return this.flowParseType();\n      }\n    }\n\n    flowParseTypeAnnotation(): N.FlowTypeAnnotation {\n      const node = this.startNode<N.FlowTypeAnnotation>();\n      node.typeAnnotation = this.flowParseTypeInitialiser();\n      return this.finishNode(node, \"TypeAnnotation\");\n    }\n\n    flowParseTypeAnnotatableIdentifier(\n      allowPrimitiveOverride?: boolean,\n    ): N.Identifier {\n      const ident = allowPrimitiveOverride\n        ? this.parseIdentifier()\n        : this.flowParseRestrictedIdentifier();\n      if (this.match(tt.colon)) {\n        // @ts-expect-error: refine typings\n        ident.typeAnnotation = this.flowParseTypeAnnotation();\n        this.resetEndLocation(ident);\n      }\n      return ident;\n    }\n\n    typeCastToParameter(node: N.Node): N.Node {\n      node.expression.typeAnnotation = node.typeAnnotation;\n\n      this.resetEndLocation(node.expression, node.typeAnnotation.loc.end);\n\n      return node.expression;\n    }\n\n    flowParseVariance(): N.FlowVariance | undefined | null {\n      let variance = null;\n      if (this.match(tt.plusMin)) {\n        variance = this.startNode<N.FlowVariance>();\n        if (this.state.value === \"+\") {\n          variance.kind = \"plus\";\n        } else {\n          variance.kind = \"minus\";\n        }\n        this.next();\n        return this.finishNode(variance, \"Variance\");\n      }\n      return variance;\n    }\n\n    // ==================================\n    // Overrides\n    // ==================================\n\n    parseFunctionBody(\n      node: N.Function,\n      allowExpressionBody?: boolean | null,\n      isMethod: boolean = false,\n    ): void {\n      if (allowExpressionBody) {\n        this.forwardNoArrowParamsConversionAt(node, () =>\n          super.parseFunctionBody(node, true, isMethod),\n        );\n        return;\n      }\n\n      super.parseFunctionBody(node, false, isMethod);\n    }\n\n    parseFunctionBodyAndFinish<\n      T extends\n        | N.Function\n        | N.TSDeclareMethod\n        | N.TSDeclareFunction\n        | N.ClassPrivateMethod,\n    >(node: Undone<T>, type: T[\"type\"], isMethod: boolean = false): T {\n      if (this.match(tt.colon)) {\n        const typeNode = this.startNode<N.TypeAnnotation>();\n\n        [\n          typeNode.typeAnnotation,\n          // @ts-expect-error predicate may not exist\n          node.predicate,\n        ] = this.flowParseTypeAndPredicateInitialiser();\n\n        node.returnType = typeNode.typeAnnotation\n          ? this.finishNode(typeNode, \"TypeAnnotation\")\n          : null;\n      }\n\n      return super.parseFunctionBodyAndFinish(node, type, isMethod);\n    }\n\n    // interfaces and enums\n    parseStatementLike(flags: ParseStatementFlag): N.Statement {\n      // strict mode handling of `interface` since it's a reserved word\n      if (this.state.strict && this.isContextual(tt._interface)) {\n        const lookahead = this.lookahead();\n        if (tokenIsKeywordOrIdentifier(lookahead.type)) {\n          const node = this.startNode<N.FlowInterface>();\n          this.next();\n          return this.flowParseInterface(node);\n        }\n      } else if (this.shouldParseEnums() && this.isContextual(tt._enum)) {\n        const node = this.startNode();\n        this.next();\n        return this.flowParseEnumDeclaration(node);\n      }\n      const stmt = super.parseStatementLike(flags);\n      // We will parse a flow pragma in any comment before the first statement.\n      if (this.flowPragma === undefined && !this.isValidDirective(stmt)) {\n        this.flowPragma = null;\n      }\n      return stmt;\n    }\n\n    // declares, interfaces and type aliases\n    parseExpressionStatement(\n      node: N.ExpressionStatement,\n      expr: N.Expression,\n      decorators: N.Decorator[] | null,\n    ): N.ExpressionStatement {\n      if (expr.type === \"Identifier\") {\n        if (expr.name === \"declare\") {\n          if (\n            this.match(tt._class) ||\n            tokenIsIdentifier(this.state.type) ||\n            this.match(tt._function) ||\n            this.match(tt._var) ||\n            this.match(tt._export)\n          ) {\n            // @ts-expect-error: refine typings\n            return this.flowParseDeclare(node);\n          }\n        } else if (tokenIsIdentifier(this.state.type)) {\n          if (expr.name === \"interface\") {\n            // @ts-expect-error: refine typings\n            return this.flowParseInterface(node);\n          } else if (expr.name === \"type\") {\n            // @ts-expect-error: refine typings\n            return this.flowParseTypeAlias(node);\n          } else if (expr.name === \"opaque\") {\n            // @ts-expect-error: refine typings\n            return this.flowParseOpaqueType(node, false);\n          }\n        }\n      }\n\n      return super.parseExpressionStatement(node, expr, decorators);\n    }\n\n    // export type\n    shouldParseExportDeclaration(): boolean {\n      const { type } = this.state;\n      if (\n        tokenIsFlowInterfaceOrTypeOrOpaque(type) ||\n        (this.shouldParseEnums() && type === tt._enum)\n      ) {\n        return !this.state.containsEsc;\n      }\n      return super.shouldParseExportDeclaration();\n    }\n\n    isExportDefaultSpecifier(): boolean {\n      const { type } = this.state;\n      if (\n        tokenIsFlowInterfaceOrTypeOrOpaque(type) ||\n        (this.shouldParseEnums() && type === tt._enum)\n      ) {\n        return this.state.containsEsc;\n      }\n\n      return super.isExportDefaultSpecifier();\n    }\n\n    parseExportDefaultExpression(): N.Expression | N.Declaration {\n      if (this.shouldParseEnums() && this.isContextual(tt._enum)) {\n        const node = this.startNode();\n        this.next();\n        return this.flowParseEnumDeclaration(node);\n      }\n      return super.parseExportDefaultExpression();\n    }\n\n    parseConditional(\n      expr: N.Expression,\n\n      startLoc: Position,\n      refExpressionErrors?: ExpressionErrors | null,\n    ): N.Expression {\n      if (!this.match(tt.question)) return expr;\n\n      if (this.state.maybeInArrowParameters) {\n        const nextCh = this.lookaheadCharCode();\n        // These tokens cannot start an expression, so if one of them follows\n        // ? then we are probably in an arrow function parameters list and we\n        // don't parse the conditional expression.\n        if (\n          nextCh === charCodes.comma || // (a?, b) => c\n          nextCh === charCodes.equalsTo || // (a? = b) => c\n          nextCh === charCodes.colon || // (a?: b) => c\n          nextCh === charCodes.rightParenthesis // (a?) => c\n        ) {\n          /*:: invariant(refExpressionErrors != null) */\n          this.setOptionalParametersError(refExpressionErrors);\n          return expr;\n        }\n      }\n\n      this.expect(tt.question);\n      const state = this.state.clone();\n      const originalNoArrowAt = this.state.noArrowAt;\n      const node = this.startNodeAt(startLoc);\n      let { consequent, failed } = this.tryParseConditionalConsequent();\n      let [valid, invalid] = this.getArrowLikeExpressions(consequent);\n\n      if (failed || invalid.length > 0) {\n        const noArrowAt = [...originalNoArrowAt];\n\n        if (invalid.length > 0) {\n          this.state = state;\n          this.state.noArrowAt = noArrowAt;\n\n          for (let i = 0; i < invalid.length; i++) {\n            noArrowAt.push(invalid[i].start);\n          }\n\n          ({ consequent, failed } = this.tryParseConditionalConsequent());\n          [valid, invalid] = this.getArrowLikeExpressions(consequent);\n        }\n\n        if (failed && valid.length > 1) {\n          // if there are two or more possible correct ways of parsing, throw an\n          // error.\n          // e.g.   Source: a ? (b): c => (d): e => f\n          //      Result 1: a ? b : (c => ((d): e => f))\n          //      Result 2: a ? ((b): c => d) : (e => f)\n          this.raise(FlowErrors.AmbiguousConditionalArrow, {\n            at: state.startLoc,\n          });\n        }\n\n        if (failed && valid.length === 1) {\n          this.state = state;\n          noArrowAt.push(valid[0].start);\n          this.state.noArrowAt = noArrowAt;\n          ({ consequent, failed } = this.tryParseConditionalConsequent());\n        }\n      }\n\n      this.getArrowLikeExpressions(consequent, true);\n\n      this.state.noArrowAt = originalNoArrowAt;\n      this.expect(tt.colon);\n\n      node.test = expr;\n      node.consequent = consequent;\n      node.alternate = this.forwardNoArrowParamsConversionAt(node, () =>\n        this.parseMaybeAssign(undefined, undefined),\n      );\n\n      return this.finishNode(node, \"ConditionalExpression\");\n    }\n\n    tryParseConditionalConsequent(): {\n      consequent: N.Expression;\n      failed: boolean;\n    } {\n      this.state.noArrowParamsConversionAt.push(this.state.start);\n\n      const consequent = this.parseMaybeAssignAllowIn();\n      const failed = !this.match(tt.colon);\n\n      this.state.noArrowParamsConversionAt.pop();\n\n      return { consequent, failed };\n    }\n\n    // Given an expression, walks through out its arrow functions whose body is\n    // an expression and through out conditional expressions. It returns every\n    // function which has been parsed with a return type but could have been\n    // parenthesized expressions.\n    // These functions are separated into two arrays: one containing the ones\n    // whose parameters can be converted to assignable lists, one containing the\n    // others.\n    getArrowLikeExpressions(\n      node: N.Expression,\n      disallowInvalid?: boolean,\n    ): [N.ArrowFunctionExpression[], N.ArrowFunctionExpression[]] {\n      const stack = [node];\n      const arrows: N.ArrowFunctionExpression[] = [];\n\n      while (stack.length !== 0) {\n        const node = stack.pop();\n        if (node.type === \"ArrowFunctionExpression\") {\n          if (node.typeParameters || !node.returnType) {\n            // This is an arrow expression without ambiguity, so check its parameters\n            // @ts-expect-error: refine typings\n            this.finishArrowValidation(node);\n          } else {\n            // @ts-expect-error: refine typings\n            arrows.push(node);\n          }\n          stack.push(node.body);\n        } else if (node.type === \"ConditionalExpression\") {\n          stack.push(node.consequent);\n          stack.push(node.alternate);\n        }\n      }\n\n      if (disallowInvalid) {\n        arrows.forEach(node => this.finishArrowValidation(node));\n        return [arrows, []];\n      }\n\n      return partition(arrows, node =>\n        node.params.every(param => this.isAssignable(param, true)),\n      );\n    }\n\n    finishArrowValidation(node: N.ArrowFunctionExpression) {\n      this.toAssignableList(\n        // node.params is Expression[] instead of $ReadOnlyArray<Pattern> because it\n        // has not been converted yet.\n        node.params as any as N.Expression[],\n        node.extra?.trailingCommaLoc,\n        /* isLHS */ false,\n      );\n      // Enter scope, as checkParams defines bindings\n      this.scope.enter(SCOPE_FUNCTION | SCOPE_ARROW);\n      // Use super's method to force the parameters to be checked\n      super.checkParams(node, false, true);\n      this.scope.exit();\n    }\n\n    forwardNoArrowParamsConversionAt<T>(\n      node: Undone<N.Node>,\n      parse: () => T,\n    ): T {\n      let result: T;\n      if (this.state.noArrowParamsConversionAt.indexOf(node.start) !== -1) {\n        this.state.noArrowParamsConversionAt.push(this.state.start);\n        result = parse();\n        this.state.noArrowParamsConversionAt.pop();\n      } else {\n        result = parse();\n      }\n\n      return result;\n    }\n\n    parseParenItem(\n      node: N.Expression,\n\n      startLoc: Position,\n    ): N.Expression {\n      node = super.parseParenItem(node, startLoc);\n      if (this.eat(tt.question)) {\n        node.optional = true;\n        // Include questionmark in location of node\n        // Don't use this.finishNode() as otherwise we might process comments twice and\n        // include already consumed parens\n        this.resetEndLocation(node);\n      }\n\n      if (this.match(tt.colon)) {\n        const typeCastNode = this.startNodeAt(startLoc);\n        typeCastNode.expression = node;\n        typeCastNode.typeAnnotation = this.flowParseTypeAnnotation();\n\n        return this.finishNode(typeCastNode, \"TypeCastExpression\");\n      }\n\n      return node;\n    }\n\n    assertModuleNodeAllowed(node: N.Node) {\n      if (\n        (node.type === \"ImportDeclaration\" &&\n          (node.importKind === \"type\" || node.importKind === \"typeof\")) ||\n        (node.type === \"ExportNamedDeclaration\" &&\n          node.exportKind === \"type\") ||\n        (node.type === \"ExportAllDeclaration\" && node.exportKind === \"type\")\n      ) {\n        // Allow Flowtype imports and exports in all conditions because\n        // Flow itself does not care about 'sourceType'.\n        return;\n      }\n\n      super.assertModuleNodeAllowed(node);\n    }\n\n    parseExportDeclaration(\n      node: N.ExportNamedDeclaration,\n    ): N.Declaration | undefined | null {\n      if (this.isContextual(tt._type)) {\n        node.exportKind = \"type\";\n\n        const declarationNode = this.startNode();\n        this.next();\n\n        if (this.match(tt.braceL)) {\n          // export type { foo, bar };\n          node.specifiers = this.parseExportSpecifiers(\n            /* isInTypeExport */ true,\n          );\n          super.parseExportFrom(node);\n          return null;\n        } else {\n          // export type Foo = Bar;\n          // @ts-expect-error: refine typings\n          return this.flowParseTypeAlias(declarationNode);\n        }\n      } else if (this.isContextual(tt._opaque)) {\n        node.exportKind = \"type\";\n\n        const declarationNode = this.startNode();\n        this.next();\n        // export opaque type Foo = Bar;\n        // @ts-expect-error: refine typings\n        return this.flowParseOpaqueType(declarationNode, false);\n      } else if (this.isContextual(tt._interface)) {\n        node.exportKind = \"type\";\n        const declarationNode = this.startNode();\n        this.next();\n        // @ts-expect-error: refine typings\n        return this.flowParseInterface(declarationNode);\n      } else if (this.shouldParseEnums() && this.isContextual(tt._enum)) {\n        node.exportKind = \"value\";\n        const declarationNode = this.startNode();\n        this.next();\n        // @ts-expect-error: refine typings\n        return this.flowParseEnumDeclaration(declarationNode);\n      } else {\n        return super.parseExportDeclaration(node);\n      }\n    }\n\n    eatExportStar(node: N.Node): boolean {\n      if (super.eatExportStar(node)) return true;\n\n      if (this.isContextual(tt._type) && this.lookahead().type === tt.star) {\n        node.exportKind = \"type\";\n        this.next();\n        this.next();\n        return true;\n      }\n\n      return false;\n    }\n\n    maybeParseExportNamespaceSpecifier(node: N.Node): boolean {\n      const { startLoc } = this.state;\n      const hasNamespace = super.maybeParseExportNamespaceSpecifier(node);\n      if (hasNamespace && node.exportKind === \"type\") {\n        this.unexpected(startLoc);\n      }\n      return hasNamespace;\n    }\n\n    parseClassId(\n      node: N.Class,\n      isStatement: boolean,\n      optionalId?: boolean | null,\n    ) {\n      super.parseClassId(node, isStatement, optionalId);\n      if (this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterDeclaration();\n      }\n    }\n\n    parseClassMember(\n      classBody: N.ClassBody,\n      member: any,\n      state: N.ParseClassMemberState,\n    ): void {\n      const { startLoc } = this.state;\n      if (this.isContextual(tt._declare)) {\n        if (super.parseClassMemberFromModifier(classBody, member)) {\n          // 'declare' is a class element name\n          return;\n        }\n\n        member.declare = true;\n      }\n\n      super.parseClassMember(classBody, member, state);\n\n      if (member.declare) {\n        if (\n          member.type !== \"ClassProperty\" &&\n          member.type !== \"ClassPrivateProperty\" &&\n          member.type !== \"PropertyDefinition\" // Used by estree plugin\n        ) {\n          this.raise(FlowErrors.DeclareClassElement, { at: startLoc });\n        } else if (member.value) {\n          this.raise(FlowErrors.DeclareClassFieldInitializer, {\n            at: member.value,\n          });\n        }\n      }\n    }\n\n    isIterator(word: string): boolean {\n      return word === \"iterator\" || word === \"asyncIterator\";\n    }\n\n    readIterator(): void {\n      const word = super.readWord1();\n      const fullWord = \"@@\" + word;\n\n      // Allow @@iterator and @@asyncIterator as a identifier only inside type\n      if (!this.isIterator(word) || !this.state.inType) {\n        this.raise(Errors.InvalidIdentifier, {\n          at: this.state.curPosition(),\n          identifierName: fullWord,\n        });\n      }\n\n      this.finishToken(tt.name, fullWord);\n    }\n\n    // ensure that inside flow types, we bypass the jsx parser plugin\n    getTokenFromCode(code: number): void {\n      const next = this.input.charCodeAt(this.state.pos + 1);\n      if (code === charCodes.leftCurlyBrace && next === charCodes.verticalBar) {\n        this.finishOp(tt.braceBarL, 2);\n      } else if (\n        this.state.inType &&\n        (code === charCodes.greaterThan || code === charCodes.lessThan)\n      ) {\n        this.finishOp(code === charCodes.greaterThan ? tt.gt : tt.lt, 1);\n      } else if (this.state.inType && code === charCodes.questionMark) {\n        if (next === charCodes.dot) {\n          this.finishOp(tt.questionDot, 2);\n        } else {\n          // allow double nullable types in Flow: ??string\n          this.finishOp(tt.question, 1);\n        }\n      } else if (\n        isIteratorStart(code, next, this.input.charCodeAt(this.state.pos + 2))\n      ) {\n        this.state.pos += 2; // eat \"@@\"\n        this.readIterator();\n      } else {\n        super.getTokenFromCode(code);\n      }\n    }\n\n    isAssignable(node: N.Node, isBinding?: boolean): boolean {\n      if (node.type === \"TypeCastExpression\") {\n        return this.isAssignable(node.expression, isBinding);\n      } else {\n        return super.isAssignable(node, isBinding);\n      }\n    }\n\n    toAssignable(node: N.Node, isLHS: boolean = false): void {\n      if (\n        !isLHS &&\n        node.type === \"AssignmentExpression\" &&\n        node.left.type === \"TypeCastExpression\"\n      ) {\n        node.left = this.typeCastToParameter(node.left);\n      }\n      super.toAssignable(node, isLHS);\n    }\n\n    // turn type casts that we found in function parameter head into type annotated params\n    toAssignableList(\n      exprList: N.Expression[],\n      trailingCommaLoc: Position | undefined | null,\n      isLHS: boolean,\n    ): void {\n      for (let i = 0; i < exprList.length; i++) {\n        const expr = exprList[i];\n        if (expr?.type === \"TypeCastExpression\") {\n          exprList[i] = this.typeCastToParameter(expr);\n        }\n      }\n      super.toAssignableList(exprList, trailingCommaLoc, isLHS);\n    }\n\n    // this is a list of nodes, from something like a call expression, we need to filter the\n    // type casts that we've found that are illegal in this context\n    toReferencedList(\n      exprList: ReadonlyArray<N.Expression | undefined | null>,\n      isParenthesizedExpr?: boolean,\n    ): ReadonlyArray<N.Expression | undefined | null> {\n      for (let i = 0; i < exprList.length; i++) {\n        const expr = exprList[i];\n        if (\n          expr &&\n          expr.type === \"TypeCastExpression\" &&\n          !expr.extra?.parenthesized &&\n          (exprList.length > 1 || !isParenthesizedExpr)\n        ) {\n          this.raise(FlowErrors.TypeCastInPattern, {\n            at: expr.typeAnnotation,\n          });\n        }\n      }\n\n      return exprList;\n    }\n\n    parseArrayLike(\n      close: TokenType,\n      canBePattern: boolean,\n      isTuple: boolean,\n      refExpressionErrors?: ExpressionErrors | null,\n    ): N.ArrayExpression | N.TupleExpression {\n      const node = super.parseArrayLike(\n        close,\n        canBePattern,\n        isTuple,\n        refExpressionErrors,\n      );\n\n      // This could be an array pattern:\n      //   ([a: string, b: string]) => {}\n      // In this case, we don't have to call toReferencedList. We will\n      // call it, if needed, when we are sure that it is a parenthesized\n      // expression by calling toReferencedListDeep.\n      if (canBePattern && !this.state.maybeInArrowParameters) {\n        this.toReferencedList(node.elements);\n      }\n\n      return node;\n    }\n\n    isValidLVal(type: string, isParenthesized: boolean, binding: BindingTypes) {\n      return (\n        type === \"TypeCastExpression\" ||\n        super.isValidLVal(type, isParenthesized, binding)\n      );\n    }\n\n    // parse class property type annotations\n    parseClassProperty(node: N.ClassProperty): N.ClassProperty {\n      if (this.match(tt.colon)) {\n        // @ts-expect-error refine typings\n        node.typeAnnotation = this.flowParseTypeAnnotation();\n      }\n      return super.parseClassProperty(node);\n    }\n\n    parseClassPrivateProperty(\n      node: N.ClassPrivateProperty,\n    ): N.ClassPrivateProperty {\n      if (this.match(tt.colon)) {\n        // @ts-expect-error refine typings\n        node.typeAnnotation = this.flowParseTypeAnnotation();\n      }\n      return super.parseClassPrivateProperty(node);\n    }\n\n    // determine whether or not we're currently in the position where a class method would appear\n    isClassMethod(): boolean {\n      return this.match(tt.lt) || super.isClassMethod();\n    }\n\n    // determine whether or not we're currently in the position where a class property would appear\n    isClassProperty(): boolean {\n      return this.match(tt.colon) || super.isClassProperty();\n    }\n\n    isNonstaticConstructor(method: N.ClassMethod | N.ClassProperty): boolean {\n      return !this.match(tt.colon) && super.isNonstaticConstructor(method);\n    }\n\n    // parse type parameters for class methods\n    pushClassMethod(\n      classBody: N.ClassBody,\n      method: N.ClassMethod,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isConstructor: boolean,\n      allowsDirectSuper: boolean,\n    ): void {\n      if ((method as any).variance) {\n        this.unexpected((method as any).variance.loc.start);\n      }\n      delete (method as any).variance;\n      if (this.match(tt.lt)) {\n        method.typeParameters = this.flowParseTypeParameterDeclaration();\n      }\n\n      super.pushClassMethod(\n        classBody,\n        method,\n        isGenerator,\n        isAsync,\n        isConstructor,\n        allowsDirectSuper,\n      );\n\n      if (method.params && isConstructor) {\n        const params = method.params;\n        if (params.length > 0 && this.isThisParam(params[0])) {\n          this.raise(FlowErrors.ThisParamBannedInConstructor, { at: method });\n        }\n        // estree support\n      } else if (\n        // @ts-expect-error TS does not know about the face that estree can replace ClassMethod with MethodDefinition\n        method.type === \"MethodDefinition\" &&\n        isConstructor &&\n        // @ts-expect-error estree\n        method.value.params\n      ) {\n        // @ts-expect-error estree\n        const params = method.value.params;\n        if (params.length > 0 && this.isThisParam(params[0])) {\n          this.raise(FlowErrors.ThisParamBannedInConstructor, { at: method });\n        }\n      }\n    }\n\n    pushClassPrivateMethod(\n      classBody: N.ClassBody,\n      method: N.ClassPrivateMethod,\n      isGenerator: boolean,\n      isAsync: boolean,\n    ): void {\n      if ((method as any).variance) {\n        this.unexpected((method as any).variance.loc.start);\n      }\n      delete (method as any).variance;\n      if (this.match(tt.lt)) {\n        method.typeParameters = this.flowParseTypeParameterDeclaration();\n      }\n\n      super.pushClassPrivateMethod(classBody, method, isGenerator, isAsync);\n    }\n\n    // parse a the super class type parameters and implements\n    parseClassSuper(node: N.Class): void {\n      super.parseClassSuper(node);\n      if (node.superClass && this.match(tt.lt)) {\n        node.superTypeParameters = this.flowParseTypeParameterInstantiation();\n      }\n      if (this.isContextual(tt._implements)) {\n        this.next();\n        const implemented: N.FlowClassImplements[] = (node.implements = []);\n        do {\n          const node = this.startNode();\n          node.id = this.flowParseRestrictedIdentifier(/*liberal*/ true);\n          if (this.match(tt.lt)) {\n            node.typeParameters = this.flowParseTypeParameterInstantiation();\n          } else {\n            node.typeParameters = null;\n          }\n          implemented.push(this.finishNode(node, \"ClassImplements\"));\n        } while (this.eat(tt.comma));\n      }\n    }\n\n    checkGetterSetterParams(method: N.ObjectMethod | N.ClassMethod): void {\n      super.checkGetterSetterParams(method);\n      const params = this.getObjectOrClassMethodParams(method);\n      if (params.length > 0) {\n        const param = params[0];\n        if (this.isThisParam(param) && method.kind === \"get\") {\n          this.raise(FlowErrors.GetterMayNotHaveThisParam, { at: param });\n        } else if (this.isThisParam(param)) {\n          this.raise(FlowErrors.SetterMayNotHaveThisParam, { at: param });\n        }\n      }\n    }\n\n    parsePropertyNamePrefixOperator(\n      node: N.ObjectOrClassMember | N.ClassMember,\n    ): void {\n      node.variance = this.flowParseVariance();\n    }\n\n    // parse type parameters for object method shorthand\n    parseObjPropValue(\n      prop: Undone<N.ObjectMethod | N.ObjectProperty>,\n      startLoc: Position | undefined | null,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isPattern: boolean,\n      isAccessor: boolean,\n      refExpressionErrors?: ExpressionErrors | null,\n    ): N.ObjectMethod | N.ObjectProperty {\n      if ((prop as any).variance) {\n        this.unexpected((prop as any).variance.loc.start);\n      }\n      delete (prop as any).variance;\n\n      let typeParameters;\n\n      // method shorthand\n      if (this.match(tt.lt) && !isAccessor) {\n        typeParameters = this.flowParseTypeParameterDeclaration();\n        if (!this.match(tt.parenL)) this.unexpected();\n      }\n\n      const result = super.parseObjPropValue(\n        prop,\n        startLoc,\n        isGenerator,\n        isAsync,\n        isPattern,\n        isAccessor,\n        refExpressionErrors,\n      );\n\n      // add typeParameters if we found them\n      if (typeParameters) {\n        (result.value || result).typeParameters = typeParameters;\n      }\n      return result;\n    }\n\n    parseAssignableListItemTypes(param: N.Pattern): N.Pattern {\n      if (this.eat(tt.question)) {\n        if (param.type !== \"Identifier\") {\n          this.raise(FlowErrors.PatternIsOptional, { at: param });\n        }\n        if (this.isThisParam(param)) {\n          this.raise(FlowErrors.ThisParamMayNotBeOptional, { at: param });\n        }\n\n        (param as any as N.Identifier).optional = true;\n      }\n      if (this.match(tt.colon)) {\n        // @ts-expect-error: refine typings\n        param.typeAnnotation = this.flowParseTypeAnnotation();\n      } else if (this.isThisParam(param)) {\n        this.raise(FlowErrors.ThisParamAnnotationRequired, { at: param });\n      }\n\n      if (this.match(tt.eq) && this.isThisParam(param)) {\n        this.raise(FlowErrors.ThisParamNoDefault, { at: param });\n      }\n\n      this.resetEndLocation(param);\n      return param;\n    }\n\n    parseMaybeDefault(\n      startLoc?: Position | null,\n      left?: N.Pattern | null,\n    ): N.Pattern {\n      const node = super.parseMaybeDefault(startLoc, left);\n\n      if (\n        node.type === \"AssignmentPattern\" &&\n        node.typeAnnotation &&\n        node.right.start < node.typeAnnotation.start\n      ) {\n        this.raise(FlowErrors.TypeBeforeInitializer, {\n          at: node.typeAnnotation,\n        });\n      }\n\n      return node;\n    }\n\n    checkImportReflection(node: Undone<N.ImportDeclaration>) {\n      super.checkImportReflection(node);\n      if (node.module && node.importKind !== \"value\") {\n        this.raise(FlowErrors.ImportReflectionHasImportType, {\n          at: node.specifiers[0].loc.start,\n        });\n      }\n    }\n\n    parseImportSpecifierLocal<\n      T extends\n        | N.ImportSpecifier\n        | N.ImportDefaultSpecifier\n        | N.ImportNamespaceSpecifier,\n    >(node: N.ImportDeclaration, specifier: Undone<T>, type: T[\"type\"]): void {\n      specifier.local = hasTypeImportKind(node)\n        ? this.flowParseRestrictedIdentifier(\n            /* liberal */ true,\n            /* declaration */ true,\n          )\n        : this.parseIdentifier();\n\n      node.specifiers.push(this.finishImportSpecifier(specifier, type));\n    }\n\n    isPotentialImportPhase(isExport: boolean): boolean {\n      if (super.isPotentialImportPhase(isExport)) return true;\n      if (this.isContextual(tt._type)) {\n        if (!isExport) return true;\n        const ch = this.lookaheadCharCode();\n        return ch === charCodes.leftCurlyBrace || ch === charCodes.asterisk;\n      }\n      return !isExport && this.isContextual(tt._typeof);\n    }\n\n    applyImportPhase(\n      node: Undone<N.ImportDeclaration | N.ExportNamedDeclaration>,\n      isExport: boolean,\n      phase: string | null,\n      loc?: Position,\n    ): void {\n      super.applyImportPhase(node, isExport, phase, loc);\n      if (isExport) {\n        if (!phase && this.match(tt._default)) {\n          // TODO: Align with our TS AST and always add .exportKind\n          return;\n        }\n        (node as N.ExportNamedDeclaration).exportKind =\n          phase === \"type\" ? phase : \"value\";\n      } else {\n        if (phase === \"type\" && this.match(tt.star)) this.unexpected();\n        (node as N.ImportDeclaration).importKind =\n          phase === \"type\" || phase === \"typeof\" ? phase : \"value\";\n      }\n    }\n\n    // parse import-type/typeof shorthand\n    parseImportSpecifier(\n      specifier: any,\n      importedIsString: boolean,\n      isInTypeOnlyImport: boolean,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      isMaybeTypeOnly: boolean,\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      bindingType: BindingTypes | undefined,\n    ): N.ImportSpecifier {\n      const firstIdent = specifier.imported;\n\n      let specifierTypeKind = null;\n      if (firstIdent.type === \"Identifier\") {\n        if (firstIdent.name === \"type\") {\n          specifierTypeKind = \"type\";\n        } else if (firstIdent.name === \"typeof\") {\n          specifierTypeKind = \"typeof\";\n        }\n      }\n\n      let isBinding = false;\n      if (this.isContextual(tt._as) && !this.isLookaheadContextual(\"as\")) {\n        const as_ident = this.parseIdentifier(true);\n        if (\n          specifierTypeKind !== null &&\n          !tokenIsKeywordOrIdentifier(this.state.type)\n        ) {\n          // `import {type as ,` or `import {type as }`\n          specifier.imported = as_ident;\n          specifier.importKind = specifierTypeKind;\n          specifier.local = cloneIdentifier(as_ident);\n        } else {\n          // `import {type as foo`\n          specifier.imported = firstIdent;\n          specifier.importKind = null;\n          specifier.local = this.parseIdentifier();\n        }\n      } else {\n        if (\n          specifierTypeKind !== null &&\n          tokenIsKeywordOrIdentifier(this.state.type)\n        ) {\n          // `import {type foo`\n          specifier.imported = this.parseIdentifier(true);\n          specifier.importKind = specifierTypeKind;\n        } else {\n          if (importedIsString) {\n            /*:: invariant(firstIdent instanceof N.StringLiteral) */\n            throw this.raise(Errors.ImportBindingIsString, {\n              at: specifier,\n              importName: firstIdent.value,\n            });\n          }\n          /*:: invariant(firstIdent instanceof N.Node) */\n          specifier.imported = firstIdent;\n          specifier.importKind = null;\n        }\n\n        if (this.eatContextual(tt._as)) {\n          specifier.local = this.parseIdentifier();\n        } else {\n          isBinding = true;\n          specifier.local = cloneIdentifier(specifier.imported);\n        }\n      }\n\n      const specifierIsTypeImport = hasTypeImportKind(specifier);\n\n      if (isInTypeOnlyImport && specifierIsTypeImport) {\n        this.raise(FlowErrors.ImportTypeShorthandOnlyInPureImport, {\n          at: specifier,\n        });\n      }\n\n      if (isInTypeOnlyImport || specifierIsTypeImport) {\n        this.checkReservedType(\n          specifier.local.name,\n          specifier.local.loc.start,\n          /* declaration */ true,\n        );\n      }\n\n      if (isBinding && !isInTypeOnlyImport && !specifierIsTypeImport) {\n        this.checkReservedWord(\n          specifier.local.name,\n          specifier.loc.start,\n          true,\n          true,\n        );\n      }\n\n      return this.finishImportSpecifier(specifier, \"ImportSpecifier\");\n    }\n\n    parseBindingAtom(): N.Pattern {\n      switch (this.state.type) {\n        case tt._this:\n          // \"this\" may be the name of a parameter, so allow it.\n          return this.parseIdentifier(/* liberal */ true);\n        default:\n          return super.parseBindingAtom();\n      }\n    }\n\n    // parse function type parameters - function foo<T>() {}\n    parseFunctionParams(\n      node: Undone<N.Function>,\n      isConstructor: boolean,\n    ): void {\n      // @ts-expect-error kind may not index node\n      const kind = node.kind;\n      if (kind !== \"get\" && kind !== \"set\" && this.match(tt.lt)) {\n        node.typeParameters = this.flowParseTypeParameterDeclaration();\n      }\n      super.parseFunctionParams(node, isConstructor);\n    }\n\n    // parse flow type annotations on variable declarator heads - let foo: string = bar\n    parseVarId(\n      decl: N.VariableDeclarator,\n      kind: \"var\" | \"let\" | \"const\",\n    ): void {\n      super.parseVarId(decl, kind);\n      if (this.match(tt.colon)) {\n        // @ts-expect-error: refine typings\n        decl.id.typeAnnotation = this.flowParseTypeAnnotation();\n        this.resetEndLocation(decl.id); // set end position to end of type\n      }\n    }\n\n    // parse the return type of an async arrow function - let foo = (async (): number => {});\n    parseAsyncArrowFromCallExpression(\n      node: N.ArrowFunctionExpression,\n      call: N.CallExpression,\n    ): N.ArrowFunctionExpression {\n      if (this.match(tt.colon)) {\n        const oldNoAnonFunctionType = this.state.noAnonFunctionType;\n        this.state.noAnonFunctionType = true;\n        // @ts-expect-error refine typings\n        node.returnType = this.flowParseTypeAnnotation();\n        this.state.noAnonFunctionType = oldNoAnonFunctionType;\n      }\n\n      return super.parseAsyncArrowFromCallExpression(node, call);\n    }\n\n    // todo description\n    shouldParseAsyncArrow(): boolean {\n      return this.match(tt.colon) || super.shouldParseAsyncArrow();\n    }\n\n    // We need to support type parameter declarations for arrow functions. This\n    // is tricky. There are three situations we need to handle\n    //\n    // 1. This is either JSX or an arrow function. We'll try JSX first. If that\n    //    fails, we'll try an arrow function. If that fails, we'll throw the JSX\n    //    error.\n    // 2. This is an arrow function. We'll parse the type parameter declaration,\n    //    parse the rest, make sure the rest is an arrow function, and go from\n    //    there\n    // 3. This is neither. Just call the super method\n    parseMaybeAssign(\n      refExpressionErrors?: ExpressionErrors | null,\n      afterLeftParse?: Function,\n    ): N.Expression {\n      let state = null;\n\n      let jsx;\n\n      if (\n        this.hasPlugin(\"jsx\") &&\n        (this.match(tt.jsxTagStart) || this.match(tt.lt))\n      ) {\n        state = this.state.clone();\n\n        jsx = this.tryParse(\n          () => super.parseMaybeAssign(refExpressionErrors, afterLeftParse),\n          state,\n        );\n\n        /*:: invariant(!jsx.aborted) */\n        /*:: invariant(jsx.node != null) */\n        if (!jsx.error) return jsx.node;\n\n        // Remove `tc.j_expr` and `tc.j_oTag` from context added\n        // by parsing `jsxTagStart` to stop the JSX plugin from\n        // messing with the tokens\n        const { context } = this.state;\n        const currentContext = context[context.length - 1];\n        if (currentContext === tc.j_oTag || currentContext === tc.j_expr) {\n          context.pop();\n        }\n      }\n\n      if (jsx?.error || this.match(tt.lt)) {\n        state = state || this.state.clone();\n\n        let typeParameters: N.TypeParameterDeclaration;\n\n        const arrow = this.tryParse(abort => {\n          typeParameters = this.flowParseTypeParameterDeclaration();\n\n          const arrowExpression = this.forwardNoArrowParamsConversionAt(\n            typeParameters,\n            () => {\n              const result = super.parseMaybeAssign(\n                refExpressionErrors,\n                afterLeftParse,\n              );\n\n              this.resetStartLocationFromNode(result, typeParameters);\n\n              return result;\n            },\n          );\n\n          // <T>(() => {});\n          // <T>(() => {}: any);\n          if (arrowExpression.extra?.parenthesized) abort();\n\n          // The above can return a TypeCastExpression when the arrow\n          // expression is not wrapped in parens. See also `this.parseParenItem`.\n          // (<T>() => {}: any);\n          const expr = this.maybeUnwrapTypeCastExpression(arrowExpression);\n\n          if (expr.type !== \"ArrowFunctionExpression\") abort();\n\n          expr.typeParameters = typeParameters;\n          this.resetStartLocationFromNode(expr, typeParameters);\n\n          return arrowExpression;\n        }, state);\n\n        let arrowExpression:\n          | N.ArrowFunctionExpression\n          | N.TypeCastExpression\n          | undefined\n          | null = null;\n\n        if (\n          arrow.node &&\n          // @ts-expect-error: refine tryParse typings\n          this.maybeUnwrapTypeCastExpression(arrow.node).type ===\n            \"ArrowFunctionExpression\"\n        ) {\n          if (!arrow.error && !arrow.aborted) {\n            // <T> async () => {}\n            // @ts-expect-error: refine tryParse typings\n            if (arrow.node.async) {\n              /*:: invariant(typeParameters) */\n              this.raise(\n                FlowErrors.UnexpectedTypeParameterBeforeAsyncArrowFunction,\n                { at: typeParameters },\n              );\n            }\n            // @ts-expect-error: refine tryParse typings\n            return arrow.node;\n          }\n\n          // @ts-expect-error: refine typings\n          arrowExpression = arrow.node;\n        }\n\n        // If we are here, both JSX and Flow parsing attempts failed.\n        // Give the precedence to the JSX error, except if JSX had an\n        // unrecoverable error while Flow didn't.\n        // If the error is recoverable, we can only re-report it if there is\n        // a node we can return.\n\n        if (jsx?.node) {\n          /*:: invariant(jsx.failState) */\n          this.state = jsx.failState;\n          return jsx.node;\n        }\n\n        if (arrowExpression) {\n          /*:: invariant(arrow.failState) */\n          this.state = arrow.failState;\n          return arrowExpression;\n        }\n\n        if (jsx?.thrown) throw jsx.error;\n        if (arrow.thrown) throw arrow.error;\n\n        /*:: invariant(typeParameters) */\n        throw this.raise(FlowErrors.UnexpectedTokenAfterTypeParameter, {\n          at: typeParameters,\n        });\n      }\n\n      return super.parseMaybeAssign(refExpressionErrors, afterLeftParse);\n    }\n\n    // handle return types for arrow functions\n    parseArrow(\n      node: Undone<N.ArrowFunctionExpression>,\n    ): Undone<N.ArrowFunctionExpression> | undefined | null {\n      if (this.match(tt.colon)) {\n        // @ts-expect-error todo(flow->ts)\n        const result = this.tryParse(() => {\n          const oldNoAnonFunctionType = this.state.noAnonFunctionType;\n          this.state.noAnonFunctionType = true;\n\n          const typeNode = this.startNode<N.TypeAnnotation>();\n\n          [\n            typeNode.typeAnnotation,\n            // @ts-expect-error (destructuring not supported yet)\n            node.predicate,\n          ] = this.flowParseTypeAndPredicateInitialiser();\n\n          this.state.noAnonFunctionType = oldNoAnonFunctionType;\n\n          if (this.canInsertSemicolon()) this.unexpected();\n          if (!this.match(tt.arrow)) this.unexpected();\n\n          return typeNode;\n        });\n\n        if (result.thrown) return null;\n        /*:: invariant(result.node) */\n\n        if (result.error) this.state = result.failState;\n\n        // assign after it is clear it is an arrow\n        // @ts-expect-error todo(flow->ts)\n        node.returnType = result.node.typeAnnotation\n          ? this.finishNode(result.node, \"TypeAnnotation\")\n          : null;\n      }\n\n      return super.parseArrow(node);\n    }\n\n    shouldParseArrow(params: Array<N.Node>): boolean {\n      return this.match(tt.colon) || super.shouldParseArrow(params);\n    }\n\n    setArrowFunctionParameters(\n      node: N.ArrowFunctionExpression,\n      params: N.Pattern[],\n    ): void {\n      if (this.state.noArrowParamsConversionAt.indexOf(node.start) !== -1) {\n        node.params = params;\n      } else {\n        super.setArrowFunctionParameters(node, params);\n      }\n    }\n\n    checkParams(\n      node: N.Function,\n      allowDuplicates: boolean,\n      isArrowFunction?: boolean | null,\n      strictModeChanged: boolean = true,\n    ): void {\n      if (\n        isArrowFunction &&\n        this.state.noArrowParamsConversionAt.indexOf(node.start) !== -1\n      ) {\n        return;\n      }\n\n      // ensure the `this` param is first, if it exists\n      for (let i = 0; i < node.params.length; i++) {\n        if (this.isThisParam(node.params[i]) && i > 0) {\n          this.raise(FlowErrors.ThisParamMustBeFirst, { at: node.params[i] });\n        }\n      }\n\n      super.checkParams(\n        node,\n        allowDuplicates,\n        isArrowFunction,\n        strictModeChanged,\n      );\n    }\n\n    parseParenAndDistinguishExpression(canBeArrow: boolean): N.Expression {\n      return super.parseParenAndDistinguishExpression(\n        canBeArrow && this.state.noArrowAt.indexOf(this.state.start) === -1,\n      );\n    }\n\n    parseSubscripts(\n      base: N.Expression,\n\n      startLoc: Position,\n      noCalls?: boolean | null,\n    ): N.Expression {\n      if (\n        base.type === \"Identifier\" &&\n        base.name === \"async\" &&\n        this.state.noArrowAt.indexOf(startLoc.index) !== -1\n      ) {\n        this.next();\n\n        const node = this.startNodeAt(startLoc);\n        node.callee = base;\n        node.arguments = super.parseCallExpressionArguments(tt.parenR, false);\n        base = this.finishNode(node, \"CallExpression\");\n      } else if (\n        base.type === \"Identifier\" &&\n        base.name === \"async\" &&\n        this.match(tt.lt)\n      ) {\n        const state = this.state.clone();\n        const arrow = this.tryParse(\n          abort => this.parseAsyncArrowWithTypeParameters(startLoc) || abort(),\n          state,\n        );\n\n        /*:: invariant(arrow.node != null) */\n        // @ts-expect-error: refine tryParse typings\n        if (!arrow.error && !arrow.aborted) return arrow.node;\n\n        const result = this.tryParse(\n          () => super.parseSubscripts(base, startLoc, noCalls),\n          state,\n        );\n\n        if (result.node && !result.error) return result.node;\n\n        if (arrow.node) {\n          this.state = arrow.failState;\n          // @ts-expect-error: refine tryParse typings\n          return arrow.node;\n        }\n\n        if (result.node) {\n          this.state = result.failState;\n          return result.node;\n        }\n\n        throw arrow.error || result.error;\n      }\n\n      return super.parseSubscripts(base, startLoc, noCalls);\n    }\n\n    parseSubscript(\n      base: N.Expression,\n\n      startLoc: Position,\n      noCalls: boolean | undefined | null,\n      subscriptState: N.ParseSubscriptState,\n    ): N.Expression {\n      if (this.match(tt.questionDot) && this.isLookaheadToken_lt()) {\n        subscriptState.optionalChainMember = true;\n        if (noCalls) {\n          subscriptState.stop = true;\n          return base;\n        }\n        this.next();\n        const node = this.startNodeAt<N.OptionalCallExpression>(startLoc);\n        node.callee = base;\n        node.typeArguments = this.flowParseTypeParameterInstantiation();\n        this.expect(tt.parenL);\n        node.arguments = this.parseCallExpressionArguments(tt.parenR, false);\n        node.optional = true;\n        return this.finishCallExpression(node, /* optional */ true);\n      } else if (!noCalls && this.shouldParseTypes() && this.match(tt.lt)) {\n        const node = this.startNodeAt<\n          N.OptionalCallExpression | N.CallExpression\n        >(startLoc);\n        node.callee = base;\n\n        const result = this.tryParse(() => {\n          node.typeArguments =\n            this.flowParseTypeParameterInstantiationCallOrNew();\n          this.expect(tt.parenL);\n          node.arguments = super.parseCallExpressionArguments(tt.parenR, false);\n          if (subscriptState.optionalChainMember) {\n            (node as Undone<N.OptionalCallExpression>).optional = false;\n          }\n          return this.finishCallExpression(\n            node,\n            subscriptState.optionalChainMember,\n          );\n        });\n\n        if (result.node) {\n          if (result.error) this.state = result.failState;\n          return result.node;\n        }\n      }\n\n      return super.parseSubscript(\n        base,\n\n        startLoc,\n        noCalls,\n        subscriptState,\n      );\n    }\n\n    parseNewCallee(node: N.NewExpression): void {\n      super.parseNewCallee(node);\n\n      let targs = null;\n      if (this.shouldParseTypes() && this.match(tt.lt)) {\n        targs = this.tryParse(() =>\n          this.flowParseTypeParameterInstantiationCallOrNew(),\n        ).node;\n      }\n      node.typeArguments = targs;\n    }\n\n    parseAsyncArrowWithTypeParameters(\n      startLoc: Position,\n    ): N.ArrowFunctionExpression | undefined | null {\n      const node = this.startNodeAt<N.ArrowFunctionExpression>(startLoc);\n      this.parseFunctionParams(node, false);\n      if (!this.parseArrow(node)) return;\n      return super.parseArrowExpression(\n        node,\n        /* params */ undefined,\n        /* isAsync */ true,\n      );\n    }\n\n    readToken_mult_modulo(code: number): void {\n      const next = this.input.charCodeAt(this.state.pos + 1);\n      if (\n        code === charCodes.asterisk &&\n        next === charCodes.slash &&\n        this.state.hasFlowComment\n      ) {\n        this.state.hasFlowComment = false;\n        this.state.pos += 2;\n        this.nextToken();\n        return;\n      }\n\n      super.readToken_mult_modulo(code);\n    }\n\n    readToken_pipe_amp(code: number): void {\n      const next = this.input.charCodeAt(this.state.pos + 1);\n      if (\n        code === charCodes.verticalBar &&\n        next === charCodes.rightCurlyBrace\n      ) {\n        // '|}'\n        this.finishOp(tt.braceBarR, 2);\n        return;\n      }\n\n      super.readToken_pipe_amp(code);\n    }\n\n    parseTopLevel(file: N.File, program: N.Program): N.File {\n      const fileNode = super.parseTopLevel(file, program);\n      if (this.state.hasFlowComment) {\n        this.raise(FlowErrors.UnterminatedFlowComment, {\n          at: this.state.curPosition(),\n        });\n      }\n      return fileNode;\n    }\n\n    skipBlockComment(): N.CommentBlock | undefined {\n      if (this.hasPlugin(\"flowComments\") && this.skipFlowComment()) {\n        if (this.state.hasFlowComment) {\n          throw this.raise(FlowErrors.NestedFlowComment, {\n            at: this.state.startLoc,\n          });\n        }\n        this.hasFlowCommentCompletion();\n        const commentSkip = this.skipFlowComment();\n        if (commentSkip) {\n          this.state.pos += commentSkip;\n          this.state.hasFlowComment = true;\n        }\n        return;\n      }\n\n      return super.skipBlockComment(this.state.hasFlowComment ? \"*-/\" : \"*/\");\n    }\n\n    skipFlowComment(): number | false {\n      const { pos } = this.state;\n      let shiftToFirstNonWhiteSpace = 2;\n      while (\n        [charCodes.space, charCodes.tab].includes(\n          // @ts-expect-error testing whether a number is included\n          this.input.charCodeAt(pos + shiftToFirstNonWhiteSpace),\n        )\n      ) {\n        shiftToFirstNonWhiteSpace++;\n      }\n\n      const ch2 = this.input.charCodeAt(shiftToFirstNonWhiteSpace + pos);\n      const ch3 = this.input.charCodeAt(shiftToFirstNonWhiteSpace + pos + 1);\n\n      if (ch2 === charCodes.colon && ch3 === charCodes.colon) {\n        return shiftToFirstNonWhiteSpace + 2; // check for /*::\n      }\n      if (\n        this.input.slice(\n          shiftToFirstNonWhiteSpace + pos,\n          shiftToFirstNonWhiteSpace + pos + 12,\n        ) === \"flow-include\"\n      ) {\n        return shiftToFirstNonWhiteSpace + 12; // check for /*flow-include\n      }\n      if (ch2 === charCodes.colon && ch3 !== charCodes.colon) {\n        return shiftToFirstNonWhiteSpace; // check for /*:, advance up to :\n      }\n      return false;\n    }\n\n    hasFlowCommentCompletion(): void {\n      const end = this.input.indexOf(\"*/\", this.state.pos);\n      if (end === -1) {\n        throw this.raise(Errors.UnterminatedComment, {\n          at: this.state.curPosition(),\n        });\n      }\n    }\n\n    // Flow enum parsing\n\n    flowEnumErrorBooleanMemberNotInitialized(\n      loc: Position,\n      {\n        enumName,\n        memberName,\n      }: {\n        enumName: string;\n        memberName: string;\n      },\n    ): void {\n      this.raise(FlowErrors.EnumBooleanMemberNotInitialized, {\n        at: loc,\n        memberName,\n        enumName,\n      });\n    }\n\n    flowEnumErrorInvalidMemberInitializer(\n      loc: Position,\n      enumContext: EnumContext,\n    ) {\n      return this.raise(\n        !enumContext.explicitType\n          ? FlowErrors.EnumInvalidMemberInitializerUnknownType\n          : enumContext.explicitType === \"symbol\"\n          ? FlowErrors.EnumInvalidMemberInitializerSymbolType\n          : FlowErrors.EnumInvalidMemberInitializerPrimaryType,\n        {\n          at: loc,\n          ...enumContext,\n        },\n      );\n    }\n\n    flowEnumErrorNumberMemberNotInitialized(\n      loc: Position,\n      {\n        enumName,\n        memberName,\n      }: {\n        enumName: string;\n        memberName: string;\n      },\n    ): void {\n      this.raise(FlowErrors.EnumNumberMemberNotInitialized, {\n        at: loc,\n        enumName,\n        memberName,\n      });\n    }\n\n    flowEnumErrorStringMemberInconsistentlyInitailized(\n      node: N.Node,\n      {\n        enumName,\n      }: {\n        enumName: string;\n      },\n    ): void {\n      this.raise(FlowErrors.EnumStringMemberInconsistentlyInitailized, {\n        at: node,\n        enumName,\n      });\n    }\n\n    flowEnumMemberInit(): EnumMemberInit {\n      const startLoc = this.state.startLoc;\n      const endOfInit = () => this.match(tt.comma) || this.match(tt.braceR);\n      switch (this.state.type) {\n        case tt.num: {\n          const literal = this.parseNumericLiteral(this.state.value);\n          if (endOfInit()) {\n            return { type: \"number\", loc: literal.loc.start, value: literal };\n          }\n          return { type: \"invalid\", loc: startLoc };\n        }\n        case tt.string: {\n          const literal = this.parseStringLiteral(this.state.value);\n          if (endOfInit()) {\n            return { type: \"string\", loc: literal.loc.start, value: literal };\n          }\n          return { type: \"invalid\", loc: startLoc };\n        }\n        case tt._true:\n        case tt._false: {\n          const literal = this.parseBooleanLiteral(this.match(tt._true));\n          if (endOfInit()) {\n            return {\n              type: \"boolean\",\n              loc: literal.loc.start,\n              value: literal,\n            };\n          }\n          return { type: \"invalid\", loc: startLoc };\n        }\n        default:\n          return { type: \"invalid\", loc: startLoc };\n      }\n    }\n\n    flowEnumMemberRaw(): {\n      id: N.Node;\n      init: EnumMemberInit;\n    } {\n      const loc = this.state.startLoc;\n      const id = this.parseIdentifier(true);\n      const init = this.eat(tt.eq)\n        ? this.flowEnumMemberInit()\n        : { type: \"none\" as const, loc };\n      return { id, init };\n    }\n\n    flowEnumCheckExplicitTypeMismatch(\n      loc: Position,\n      context: EnumContext,\n      expectedType: EnumExplicitType,\n    ): void {\n      const { explicitType } = context;\n      if (explicitType === null) {\n        return;\n      }\n      if (explicitType !== expectedType) {\n        this.flowEnumErrorInvalidMemberInitializer(loc, context);\n      }\n    }\n\n    flowEnumMembers({\n      enumName,\n      explicitType,\n    }: {\n      enumName: string;\n      explicitType: EnumExplicitType;\n    }): {\n      members: {\n        booleanMembers: Array<N.Node>;\n        numberMembers: Array<N.Node>;\n        stringMembers: Array<N.Node>;\n        defaultedMembers: Array<N.Node>;\n      };\n      hasUnknownMembers: boolean;\n    } {\n      const seenNames = new Set();\n      const members = {\n        // @ts-expect-error: migrate to Babel types\n        booleanMembers: [],\n        // @ts-expect-error: migrate to Babel types\n        numberMembers: [],\n        // @ts-expect-error: migrate to Babel types\n        stringMembers: [],\n        // @ts-expect-error: migrate to Babel types\n        defaultedMembers: [],\n      };\n      let hasUnknownMembers = false;\n      while (!this.match(tt.braceR)) {\n        if (this.eat(tt.ellipsis)) {\n          hasUnknownMembers = true;\n          break;\n        }\n        const memberNode = this.startNode();\n        const { id, init } = this.flowEnumMemberRaw();\n        const memberName = id.name;\n        if (memberName === \"\") {\n          continue;\n        }\n        if (/^[a-z]/.test(memberName)) {\n          this.raise(FlowErrors.EnumInvalidMemberName, {\n            at: id,\n            memberName,\n            suggestion: memberName[0].toUpperCase() + memberName.slice(1),\n            enumName,\n          });\n        }\n        if (seenNames.has(memberName)) {\n          this.raise(FlowErrors.EnumDuplicateMemberName, {\n            at: id,\n            memberName,\n            enumName,\n          });\n        }\n        seenNames.add(memberName);\n        const context = { enumName, explicitType, memberName };\n        memberNode.id = id;\n        switch (init.type) {\n          case \"boolean\": {\n            this.flowEnumCheckExplicitTypeMismatch(\n              init.loc,\n              context,\n              \"boolean\",\n            );\n            memberNode.init = init.value;\n            members.booleanMembers.push(\n              this.finishNode(memberNode, \"EnumBooleanMember\"),\n            );\n            break;\n          }\n          case \"number\": {\n            this.flowEnumCheckExplicitTypeMismatch(init.loc, context, \"number\");\n            memberNode.init = init.value;\n            members.numberMembers.push(\n              this.finishNode(memberNode, \"EnumNumberMember\"),\n            );\n            break;\n          }\n          case \"string\": {\n            this.flowEnumCheckExplicitTypeMismatch(init.loc, context, \"string\");\n            memberNode.init = init.value;\n            members.stringMembers.push(\n              this.finishNode(memberNode, \"EnumStringMember\"),\n            );\n            break;\n          }\n          case \"invalid\": {\n            throw this.flowEnumErrorInvalidMemberInitializer(init.loc, context);\n          }\n          case \"none\": {\n            switch (explicitType) {\n              case \"boolean\":\n                this.flowEnumErrorBooleanMemberNotInitialized(\n                  init.loc,\n                  context,\n                );\n                break;\n              case \"number\":\n                this.flowEnumErrorNumberMemberNotInitialized(init.loc, context);\n                break;\n              default:\n                members.defaultedMembers.push(\n                  this.finishNode(memberNode, \"EnumDefaultedMember\"),\n                );\n            }\n          }\n        }\n\n        if (!this.match(tt.braceR)) {\n          this.expect(tt.comma);\n        }\n      }\n      return { members, hasUnknownMembers };\n    }\n\n    flowEnumStringMembers(\n      initializedMembers: Array<N.Node>,\n      defaultedMembers: Array<N.Node>,\n      {\n        enumName,\n      }: {\n        enumName: string;\n      },\n    ): Array<N.Node> {\n      if (initializedMembers.length === 0) {\n        return defaultedMembers;\n      } else if (defaultedMembers.length === 0) {\n        return initializedMembers;\n      } else if (defaultedMembers.length > initializedMembers.length) {\n        for (const member of initializedMembers) {\n          this.flowEnumErrorStringMemberInconsistentlyInitailized(member, {\n            enumName,\n          });\n        }\n        return defaultedMembers;\n      } else {\n        for (const member of defaultedMembers) {\n          this.flowEnumErrorStringMemberInconsistentlyInitailized(member, {\n            enumName,\n          });\n        }\n        return initializedMembers;\n      }\n    }\n\n    flowEnumParseExplicitType({\n      enumName,\n    }: {\n      enumName: string;\n    }): EnumExplicitType {\n      if (!this.eatContextual(tt._of)) return null;\n\n      if (!tokenIsIdentifier(this.state.type)) {\n        throw this.raise(FlowErrors.EnumInvalidExplicitTypeUnknownSupplied, {\n          at: this.state.startLoc,\n          enumName,\n        });\n      }\n\n      const { value } = this.state;\n      this.next();\n\n      if (\n        value !== \"boolean\" &&\n        value !== \"number\" &&\n        value !== \"string\" &&\n        value !== \"symbol\"\n      ) {\n        this.raise(FlowErrors.EnumInvalidExplicitType, {\n          at: this.state.startLoc,\n          enumName,\n          invalidEnumType: value,\n        });\n      }\n\n      return value;\n    }\n\n    flowEnumBody(node: Undone<N.Node>, id: N.Node): N.Node {\n      const enumName = id.name;\n      const nameLoc = id.loc.start;\n      const explicitType = this.flowEnumParseExplicitType({ enumName });\n      this.expect(tt.braceL);\n      const { members, hasUnknownMembers } = this.flowEnumMembers({\n        enumName,\n        explicitType,\n      });\n      node.hasUnknownMembers = hasUnknownMembers;\n\n      switch (explicitType) {\n        case \"boolean\":\n          node.explicitType = true;\n          node.members = members.booleanMembers;\n          this.expect(tt.braceR);\n          return this.finishNode(node, \"EnumBooleanBody\");\n        case \"number\":\n          node.explicitType = true;\n          node.members = members.numberMembers;\n          this.expect(tt.braceR);\n          return this.finishNode(node, \"EnumNumberBody\");\n        case \"string\":\n          node.explicitType = true;\n          node.members = this.flowEnumStringMembers(\n            members.stringMembers,\n            members.defaultedMembers,\n            { enumName },\n          );\n          this.expect(tt.braceR);\n          return this.finishNode(node, \"EnumStringBody\");\n        case \"symbol\":\n          node.members = members.defaultedMembers;\n          this.expect(tt.braceR);\n          return this.finishNode(node, \"EnumSymbolBody\");\n        default: {\n          // `explicitType` is `null`\n          const empty = () => {\n            node.members = [];\n            this.expect(tt.braceR);\n            return this.finishNode(node, \"EnumStringBody\");\n          };\n          node.explicitType = false;\n\n          const boolsLen = members.booleanMembers.length;\n          const numsLen = members.numberMembers.length;\n          const strsLen = members.stringMembers.length;\n          const defaultedLen = members.defaultedMembers.length;\n\n          if (!boolsLen && !numsLen && !strsLen && !defaultedLen) {\n            return empty();\n          } else if (!boolsLen && !numsLen) {\n            node.members = this.flowEnumStringMembers(\n              members.stringMembers,\n              members.defaultedMembers,\n              { enumName },\n            );\n            this.expect(tt.braceR);\n            return this.finishNode(node, \"EnumStringBody\");\n          } else if (!numsLen && !strsLen && boolsLen >= defaultedLen) {\n            for (const member of members.defaultedMembers) {\n              this.flowEnumErrorBooleanMemberNotInitialized(member.loc.start, {\n                enumName,\n                memberName: member.id.name,\n              });\n            }\n            node.members = members.booleanMembers;\n            this.expect(tt.braceR);\n            return this.finishNode(node, \"EnumBooleanBody\");\n          } else if (!boolsLen && !strsLen && numsLen >= defaultedLen) {\n            for (const member of members.defaultedMembers) {\n              this.flowEnumErrorNumberMemberNotInitialized(member.loc.start, {\n                enumName,\n                memberName: member.id.name,\n              });\n            }\n            node.members = members.numberMembers;\n            this.expect(tt.braceR);\n            return this.finishNode(node, \"EnumNumberBody\");\n          } else {\n            this.raise(FlowErrors.EnumInconsistentMemberValues, {\n              at: nameLoc,\n              enumName,\n            });\n            return empty();\n          }\n        }\n      }\n    }\n\n    flowParseEnumDeclaration(node: Undone<N.Node>): N.Node {\n      const id = this.parseIdentifier();\n      node.id = id;\n      node.body = this.flowEnumBody(this.startNode(), id);\n      return this.finishNode(node, \"EnumDeclaration\");\n    }\n\n    // check if the next token is a tt.lt\n    isLookaheadToken_lt(): boolean {\n      const next = this.nextTokenStart();\n      if (this.input.charCodeAt(next) === charCodes.lessThan) {\n        const afterNext = this.input.charCodeAt(next + 1);\n        return (\n          afterNext !== charCodes.lessThan && afterNext !== charCodes.equalsTo\n        );\n      }\n      return false;\n    }\n\n    maybeUnwrapTypeCastExpression(node: N.Node) {\n      return node.type === \"TypeCastExpression\" ? node.expression : node;\n    }\n  };\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAYA,IAAAC,QAAA,GAAAD,OAAA;AAEA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAYA,IAAAK,WAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AAEA,MAAMO,aAAa,GAAG,IAAIC,GAAG,CAAC,CAC5B,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,OAAO,EACP,SAAS,EACT,OAAO,EACP,WAAW,EACX,OAAO,EACP,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,CACP,CAAC;AAIF,MAAMC,UAAU,GAAG,IAAAC,0BAAc,CAAC,MAAK,CAAC;EACtCC,yBAAyB,EACvB,gFAAgF;EAClFC,0BAA0B,EACxB,wKAAwK;EAG1KC,kBAAkB,EAAEA,CAAC;IAAEC;EAAuC,CAAC,KAC5D,kCAAiCA,YAAa,GAAE;EACnDC,mBAAmB,EACjB,yDAAyD;EAC3DC,4BAA4B,EAC1B,qEAAqE;EACvEC,6BAA6B,EAC3B,+CAA+C;EACjDC,+BAA+B,EAAEA,CAAC;IAChCC,UAAU;IACVC;EAIF,CAAC,KACE,6DAA4DD,UAAW,mBAAkBA,UAAW,yBAAwBC,QAAS,KAAI;EAC5IC,uBAAuB,EAAEA,CAAC;IACxBF,UAAU;IACVC;EAIF,CAAC,KACE,uDAAsDD,UAAW,6CAA4CC,QAAS,KAAI;EAC7HE,4BAA4B,EAAEA,CAAC;IAAEF;EAA+B,CAAC,KAC9D,UAASA,QAAS,uKAAsK;EAC3LG,uBAAuB,EAAEA,CAAC;IACxBC,eAAe;IACfJ;EAIF,CAAC,KACE,eAAcI,eAAgB,4FAA2FJ,QAAS,KAAI;EACzIK,sCAAsC,EAAEA,CAAC;IACvCL;EAGF,CAAC,KACE,4GAA2GA,QAAS,KAAI;EAa3HM,uCAAuC,EAAEA,CAAC;IACxCN,QAAQ;IACRD,UAAU;IACVQ;EAKF,CAAC,KACE,UAASP,QAAS,iBAAgBO,YAAa,+BAA8BR,UAAW,oBAAmBQ,YAAa,WAAU;EACrIC,sCAAsC,EAAEA,CAAC;IACvCR,QAAQ;IACRD;EAKF,CAAC,KACE,oDAAmDA,UAAW,iBAAgBC,QAAS,KAAI;EAC9FS,uCAAuC,EAAEA,CAAC;IACxCT,QAAQ;IACRD;EAKF,CAAC,KACE,qCAAoCA,UAAW,4EAA2EC,QAAS,KAAI;EAC1IU,qBAAqB,EAAEA,CAAC;IACtBV,QAAQ;IACRD,UAAU;IACVY;EAKF,CAAC,KACE,qFAAoFZ,UAAW,wBAAuBY,UAAW,iBAAgBX,QAAS,KAAI;EACjKY,8BAA8B,EAAEA,CAAC;IAC/BZ,QAAQ;IACRD;EAIF,CAAC,KACE,sDAAqDA,UAAW,oBAAmBC,QAAS,KAAI;EACnGa,yCAAyC,EAAEA,CAAC;IAC1Cb;EAGF,CAAC,KACE,2GAA0GA,QAAS,KAAI;EAC1Hc,yBAAyB,EAAE,0CAA0C;EACrEC,6BAA6B,EAC3B,wEAAwE;EAC1EC,mCAAmC,EACjC,wKAAwK;EAC1KC,kBAAkB,EAChB,6EAA6E;EAC/EC,sBAAsB,EACpB,0EAA0E;EAC5EC,eAAe,EAAE,+CAA+C;EAChEC,mCAAmC,EACjC,yFAAyF;EAC3FC,uBAAuB,EACrB,yGAAyG;EAC3GC,mBAAmB,EACjB,kEAAkE;EACpEC,iBAAiB,EAAE,yDAAyD;EAC5EC,iBAAiB,EAAAC,MAAA,CAAAC,MAAA;IACfC,OAAO,EACL;EAAgF,GAG9E;IAAEC,UAAU,EAAE;EAAyB,CAAC,CAE7C;EACDC,yBAAyB,EAAE,0CAA0C;EACrEC,cAAc,EAAE,yCAAyC;EACzDC,2BAA2B,EACzB,yDAAyD;EAC3DC,4BAA4B,EAC1B,mGAAmG;EACrGC,yBAAyB,EAAE,0CAA0C;EACrEC,oBAAoB,EAClB,4DAA4D;EAC9DC,kBAAkB,EAAE,oDAAoD;EACxEC,qBAAqB,EACnB,mHAAmH;EACrHC,iBAAiB,EACf,sEAAsE;EACxEC,iCAAiC,EAC/B,sEAAsE;EACxEC,sBAAsB,EAAEA,CAAC;IAAE7C;EAAuC,CAAC,KAChE,4BAA2BA,YAAa,GAAE;EAC7C8C,4BAA4B,EAC1B,wDAAwD;EAC1DC,kCAAkC,EAChC,uDAAuD;EACzDC,oBAAoB,EAClB,kEAAkE;EACpEC,4BAA4B,EAC1B,kDAAkD;EACpDC,iCAAiC,EAC/B,mEAAmE;EACrEC,+CAA+C,EAC7C,mHAAmH;EACrHC,4BAA4B,EAAEA,CAAC;IAC7BC,qBAAqB;IACrBpC;EAIF,CAAC,KACE,oBAAmBoC,qBAAsB,8BAA6BpC,UAAW,aAAY;EAChGqC,mCAAmC,EACjC,mEAAmE;EACrEC,uBAAuB,EAAE;AAC3B,CAAC,CAAC;AAGF,SAASC,cAAcA,CAACC,WAAmB,EAAW;EACpD,OACEA,WAAW,CAACC,IAAI,KAAK,6BAA6B,IACjDD,WAAW,CAACC,IAAI,KAAK,0BAA0B,KAC7C,CAACD,WAAW,CAACE,WAAW,IACtBF,WAAW,CAACE,WAAW,CAACD,IAAI,KAAK,WAAW,IAC3CD,WAAW,CAACE,WAAW,CAACD,IAAI,KAAK,sBAAuB,CAAE;AAEpE;AAEA,SAASE,iBAAiBA,CAACC,IAAY,EAAW;EAChD,OAAOA,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ;AACnE;AAEA,MAAMC,iBAAiB,GAAG;EACxBC,KAAK,EAAE,oBAAoB;EAC3BC,GAAG,EAAE,oBAAoB;EACzBP,IAAI,EAAE,aAAa;EACnBQ,SAAS,EAAE;AACb,CAAC;AAGD,SAASC,SAASA,CAChBC,IAAS,EACTC,IAA6D,EACjD;EACZ,MAAMC,KAAU,GAAG,EAAE;EACrB,MAAMC,KAAU,GAAG,EAAE;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,CAACH,IAAI,CAACD,IAAI,CAACI,CAAC,CAAC,EAAEA,CAAC,EAAEJ,IAAI,CAAC,GAAGE,KAAK,GAAGC,KAAK,EAAEG,IAAI,CAACN,IAAI,CAACI,CAAC,CAAC,CAAC;EACxD;EACA,OAAO,CAACF,KAAK,EAAEC,KAAK,CAAC;AACvB;AAEA,MAAMI,iBAAiB,GAAG,wBAAwB;AAAC,IAAAC,QAAA,GAoCnCC,UAAyB,IACvC,MAAMC,eAAe,SAASD,UAAU,CAAmB;EAAAE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAIzDC,UAAU,GAAoCC,SAAS;EAAA;EAEvDC,eAAeA,CAAA,EAEb;IACA,OAAOC,cAAgB;EACzB;EAEAC,gBAAgBA,CAAA,EAAY;IAC1B,OAAO,IAAI,CAACC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAACL,UAAU,KAAK,MAAM;EAC1E;EAEAM,gBAAgBA,CAAA,EAAY;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACD,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC;EAChD;EAEAE,WAAWA,CAAC9B,IAAe,EAAE+B,GAAQ,EAAQ;IAC3C,IACE/B,IAAI,QAAc,IAClBA,IAAI,OAAY,IAChBA,IAAI,OAA4B,EAChC;MACA,IAAI,IAAI,CAACuB,UAAU,KAAKC,SAAS,EAAE;QACjC,IAAI,CAACD,UAAU,GAAG,IAAI;MACxB;IACF;IACA,KAAK,CAACO,WAAW,CAAC9B,IAAI,EAAE+B,GAAG,CAAC;EAC9B;EAEAC,UAAUA,CAACC,OAAkB,EAAQ;IACnC,IAAI,IAAI,CAACV,UAAU,KAAKC,SAAS,EAAE;MAEjC,MAAMU,OAAO,GAAGjB,iBAAiB,CAACkB,IAAI,CAACF,OAAO,CAACG,KAAK,CAAC;MACrD,IAAI,CAACF,OAAO,EAAE,CAEd,CAAC,MAAM,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;QAChC,IAAI,CAACX,UAAU,GAAG,MAAM;MAC1B,CAAC,MAAM,IAAIW,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAClC,IAAI,CAACX,UAAU,GAAG,QAAQ;MAC5B,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAAC,wBAAwB,CAAC;MAC3C;IACF;IACA,KAAK,CAACL,UAAU,CAACC,OAAO,CAAC;EAC3B;EAEAK,wBAAwBA,CAACC,GAAe,EAAc;IACpD,MAAMC,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;IACnC,IAAI,CAACD,KAAK,CAACC,MAAM,GAAG,IAAI;IACxB,IAAI,CAACC,MAAM,CAACJ,GAAG,MAAY,CAAC;IAE5B,MAAMvC,IAAI,GAAG,IAAI,CAAC4C,aAAa,CAAC,CAAC;IACjC,IAAI,CAACH,KAAK,CAACC,MAAM,GAAGF,SAAS;IAC7B,OAAOxC,IAAI;EACb;EAEA6C,kBAAkBA,CAAA,EAAoB;IACpC,MAAM1C,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAC7B,MAAMC,SAAS,GAAG,IAAI,CAACN,KAAK,CAACO,QAAQ;IACrC,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAI,CAACC,gBAAgB,IAAW,CAAC;IAEjC,IAAI,IAAI,CAACT,KAAK,CAACU,YAAY,GAAGJ,SAAS,CAACK,KAAK,GAAG,CAAC,EAAE;MACjD,IAAI,CAACC,KAAK,CAACpH,UAAU,CAACoD,kCAAkC,EAAE;QACxDiE,EAAE,EAAEP;MACN,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAACQ,GAAG,GAAU,CAAC,EAAE;MACvBpD,IAAI,CAACiC,KAAK,GAAG,KAAK,CAACoB,eAAe,CAAC,CAAC;MACpC,IAAI,CAACb,MAAM,GAAU,CAAC;MACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,mBAAmB,CAAC;IACnD,CAAC,MAAM;MACL,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,mBAAmB,CAAC;IACnD;EACF;EAEAuD,oCAAoCA,CAAA,EAGlC;IACA,MAAMlB,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;IACnC,IAAI,CAACD,KAAK,CAACC,MAAM,GAAG,IAAI;IACxB,IAAI,CAACC,MAAM,GAAS,CAAC;IACrB,IAAI3C,IAAI,GAAG,IAAI;IACf,IAAI2D,SAAS,GAAG,IAAI;IACpB,IAAI,IAAI,CAACC,KAAK,GAAU,CAAC,EAAE;MACzB,IAAI,CAACnB,KAAK,CAACC,MAAM,GAAGF,SAAS;MAC7BmB,SAAS,GAAG,IAAI,CAACd,kBAAkB,CAAC,CAAC;IACvC,CAAC,MAAM;MACL7C,IAAI,GAAG,IAAI,CAAC4C,aAAa,CAAC,CAAC;MAC3B,IAAI,CAACH,KAAK,CAACC,MAAM,GAAGF,SAAS;MAC7B,IAAI,IAAI,CAACoB,KAAK,GAAU,CAAC,EAAE;QACzBD,SAAS,GAAG,IAAI,CAACd,kBAAkB,CAAC,CAAC;MACvC;IACF;IACA,OAAO,CAAC7C,IAAI,EAAE2D,SAAS,CAAC;EAC1B;EAEAE,qBAAqBA,CACnB1D,IAAgC,EACZ;IACpB,IAAI,CAAC8C,IAAI,CAAC,CAAC;IACX,IAAI,CAACa,qBAAqB,CAAC3D,IAAI,EAAc,IAAI,CAAC;IAClD,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,cAAc,CAAC;EAC9C;EAEA4D,wBAAwBA,CACtB5D,IAAmC,EACZ;IACvB,IAAI,CAAC8C,IAAI,CAAC,CAAC;IAEX,MAAMe,EAAE,GAAI7D,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACC,eAAe,CAAC,CAAE;IAE7C,MAAMC,QAAQ,GAAG,IAAI,CAACpB,SAAS,CAAC,CAAC;IACjC,MAAMqB,aAAa,GAAG,IAAI,CAACrB,SAAS,CAAmB,CAAC;IAExD,IAAI,IAAI,CAACc,KAAK,GAAM,CAAC,EAAE;MACrBM,QAAQ,CAACE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IACpE,CAAC,MAAM;MACLH,QAAQ,CAACE,cAAc,GAAG,IAAI;IAChC;IAEA,IAAI,CAACzB,MAAM,GAAU,CAAC;IACtB,MAAM2B,GAAG,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;IAC9CL,QAAQ,CAACM,MAAM,GAAGF,GAAG,CAACE,MAAM;IAC5BN,QAAQ,CAACO,IAAI,GAAGH,GAAG,CAACG,IAAI;IACxBP,QAAQ,CAACQ,IAAI,GAAGJ,GAAG,CAACK,KAAK;IACzB,IAAI,CAAChC,MAAM,GAAU,CAAC;IAEtB,CAACuB,QAAQ,CAACU,UAAU,EAAEzE,IAAI,CAACwD,SAAS,CAAC,GACnC,IAAI,CAACD,oCAAoC,CAAC,CAAC;IAE7CS,aAAa,CAACU,cAAc,GAAG,IAAI,CAACpB,UAAU,CAC5CS,QAAQ,EACR,wBACF,CAAC;IAEDF,EAAE,CAACa,cAAc,GAAG,IAAI,CAACpB,UAAU,CAACU,aAAa,EAAE,gBAAgB,CAAC;IAEpE,IAAI,CAACW,gBAAgB,CAACd,EAAE,CAAC;IACzB,IAAI,CAACe,SAAS,CAAC,CAAC;IAEhB,IAAI,CAACC,KAAK,CAACC,WAAW,CACpB9E,IAAI,CAAC6D,EAAE,CAACkB,IAAI,EACZC,gCAAoB,EACpBhF,IAAI,CAAC6D,EAAE,CAACoB,GAAG,CAACC,KACd,CAAC;IAED,OAAO,IAAI,CAAC5B,UAAU,CAACtD,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAmF,gBAAgBA,CACdnF,IAA2B,EAC3BoF,YAAsB,EACP;IACf,IAAI,IAAI,CAAC3B,KAAK,GAAU,CAAC,EAAE;MACzB,OAAO,IAAI,CAACC,qBAAqB,CAAC1D,IAAI,CAAC;IACzC,CAAC,MAAM,IAAI,IAAI,CAACyD,KAAK,GAAa,CAAC,EAAE;MACnC,OAAO,IAAI,CAACG,wBAAwB,CAAC5D,IAAI,CAAC;IAC5C,CAAC,MAAM,IAAI,IAAI,CAACyD,KAAK,GAAQ,CAAC,EAAE;MAC9B,OAAO,IAAI,CAAC4B,wBAAwB,CAACrF,IAAI,CAAC;IAC5C,CAAC,MAAM,IAAI,IAAI,CAACsF,aAAa,IAAW,CAAC,EAAE;MACzC,IAAI,IAAI,CAAC7B,KAAK,GAAO,CAAC,EAAE;QACtB,OAAO,IAAI,CAAC8B,6BAA6B,CAACvF,IAAI,CAAC;MACjD,CAAC,MAAM;QACL,IAAIoF,YAAY,EAAE;UAChB,IAAI,CAAClC,KAAK,CAACpH,UAAU,CAACiC,mBAAmB,EAAE;YACzCoF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkD;UACjB,CAAC,CAAC;QACJ;QACA,OAAO,IAAI,CAACC,sBAAsB,CAACzF,IAAI,CAAC;MAC1C;IACF,CAAC,MAAM,IAAI,IAAI,CAAC0F,YAAY,IAAS,CAAC,EAAE;MACtC,OAAO,IAAI,CAACC,yBAAyB,CAAC3F,IAAI,CAAC;IAC7C,CAAC,MAAM,IAAI,IAAI,CAAC0F,YAAY,IAAW,CAAC,EAAE;MACxC,OAAO,IAAI,CAACE,0BAA0B,CAAC5F,IAAI,CAAC;IAC9C,CAAC,MAAM,IAAI,IAAI,CAAC0F,YAAY,IAAc,CAAC,EAAE;MAC3C,OAAO,IAAI,CAACG,yBAAyB,CAAC7F,IAAI,CAAC;IAC7C,CAAC,MAAM,IAAI,IAAI,CAACyD,KAAK,GAAW,CAAC,EAAE;MACjC,OAAO,IAAI,CAACqC,iCAAiC,CAAC9F,IAAI,EAAEoF,YAAY,CAAC;IACnE,CAAC,MAAM;MACL,IAAI,CAACW,UAAU,CAAC,CAAC;IACnB;EACF;EAEAV,wBAAwBA,CACtBrF,IAAmC,EACZ;IACvB,IAAI,CAAC8C,IAAI,CAAC,CAAC;IACX9C,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACmC,kCAAkC,CACpB,IAC7B,CAAC;IACD,IAAI,CAACnB,KAAK,CAACC,WAAW,CAAC9E,IAAI,CAAC6D,EAAE,CAACkB,IAAI,EAAEkB,oBAAQ,EAAEjG,IAAI,CAAC6D,EAAE,CAACoB,GAAG,CAACC,KAAK,CAAC;IACjE,IAAI,CAACN,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAACtB,UAAU,CAACtD,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAyF,sBAAsBA,CACpBzF,IAAiC,EACZ;IACrB,IAAI,CAAC6E,KAAK,CAACqB,KAAK,CAACC,uBAAW,CAAC;IAE7B,IAAI,IAAI,CAAC1C,KAAK,IAAU,CAAC,EAAE;MACzBzD,IAAI,CAAC6D,EAAE,GAAG,KAAK,CAACuC,aAAa,CAAC,CAAC;IACjC,CAAC,MAAM;MACLpG,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAClC;IAEA,MAAMuC,QAAQ,GAAIrG,IAAI,CAACsG,IAAI,GAAG,IAAI,CAAC3D,SAAS,CAAC,CAAE;IAE/C,MAAM2D,IAAI,GAAID,QAAQ,CAACC,IAAI,GAAG,EAAG;IACjC,IAAI,CAAC9D,MAAM,EAAU,CAAC;IACtB,OAAO,CAAC,IAAI,CAACiB,KAAK,EAAU,CAAC,EAAE;MAC7B,IAAI4C,QAAQ,GAAG,IAAI,CAAC1D,SAAS,CAAsB,CAAC;MAEpD,IAAI,IAAI,CAACc,KAAK,GAAW,CAAC,EAAE;QAC1B,IAAI,CAACX,IAAI,CAAC,CAAC;QACX,IAAI,CAAC,IAAI,CAAC4C,YAAY,IAAS,CAAC,IAAI,CAAC,IAAI,CAACjC,KAAK,GAAW,CAAC,EAAE;UAC3D,IAAI,CAACP,KAAK,CAACpH,UAAU,CAAC+B,mCAAmC,EAAE;YACzDsF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkD;UACjB,CAAC,CAAC;QACJ;QACA,KAAK,CAACe,WAAW,CAACF,QAAQ,CAAC;MAC7B,CAAC,MAAM;QACL,IAAI,CAACtD,gBAAgB,MAEnBjH,UAAU,CAAC2D,mCACb,CAAC;QAED4G,QAAQ,GAAG,IAAI,CAAClB,gBAAgB,CAACkB,QAAQ,EAAE,IAAI,CAAC;MAClD;MAEAC,IAAI,CAACzF,IAAI,CAACwF,QAAQ,CAAC;IACrB;IAEA,IAAI,CAACxB,KAAK,CAAC2B,IAAI,CAAC,CAAC;IAEjB,IAAI,CAAChE,MAAM,EAAU,CAAC;IAEtB,IAAI,CAACc,UAAU,CAAC+C,QAAQ,EAAE,gBAAgB,CAAC;IAE3C,IAAII,IAA8B,GAAG,IAAI;IACzC,IAAIC,eAAe,GAAG,KAAK;IAC3BJ,IAAI,CAACK,OAAO,CAAC/G,WAAW,IAAI;MAC1B,IAAID,cAAc,CAACC,WAAW,CAAC,EAAE;QAC/B,IAAI6G,IAAI,KAAK,UAAU,EAAE;UACvB,IAAI,CAACvD,KAAK,CAACpH,UAAU,CAACG,0BAA0B,EAAE;YAChDkH,EAAE,EAAEvD;UACN,CAAC,CAAC;QACJ;QACA6G,IAAI,GAAG,IAAI;MACb,CAAC,MAAM,IAAI7G,WAAW,CAACC,IAAI,KAAK,sBAAsB,EAAE;QACtD,IAAI6G,eAAe,EAAE;UACnB,IAAI,CAACxD,KAAK,CAACpH,UAAU,CAACQ,6BAA6B,EAAE;YACnD6G,EAAE,EAAEvD;UACN,CAAC,CAAC;QACJ;QACA,IAAI6G,IAAI,KAAK,IAAI,EAAE;UACjB,IAAI,CAACvD,KAAK,CAACpH,UAAU,CAACG,0BAA0B,EAAE;YAChDkH,EAAE,EAAEvD;UACN,CAAC,CAAC;QACJ;QACA6G,IAAI,GAAG,UAAU;QACjBC,eAAe,GAAG,IAAI;MACxB;IACF,CAAC,CAAC;IAEF1G,IAAI,CAACyG,IAAI,GAAGA,IAAI,IAAI,UAAU;IAC9B,OAAO,IAAI,CAACnD,UAAU,CAACtD,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEA8F,iCAAiCA,CAC/B9F,IAA4C,EAC5CoF,YAA6B,EACG;IAChC,IAAI,CAAC5C,MAAM,GAAW,CAAC;IAEvB,IAAI,IAAI,CAACY,GAAG,GAAY,CAAC,EAAE;MACzB,IAAI,IAAI,CAACK,KAAK,GAAa,CAAC,IAAI,IAAI,CAACA,KAAK,GAAU,CAAC,EAAE;QAGrDzD,IAAI,CAACF,WAAW,GAAG,IAAI,CAACqF,gBAAgB,CAAC,IAAI,CAACxC,SAAS,CAAC,CAAC,CAAC;MAC5D,CAAC,MAAM;QAEL3C,IAAI,CAACF,WAAW,GAAG,IAAI,CAAC2C,aAAa,CAAC,CAAC;QACvC,IAAI,CAACmC,SAAS,CAAC,CAAC;MAClB;MACA5E,IAAI,CAAC4G,OAAO,GAAG,IAAI;MAEnB,OAAO,IAAI,CAACtD,UAAU,CAACtD,IAAI,EAAE,0BAA0B,CAAC;IAC1D,CAAC,MAAM;MACL,IACE,IAAI,CAACyD,KAAK,GAAU,CAAC,IACrB,IAAI,CAACoD,KAAK,CAAC,CAAC,IACX,CAAC,IAAI,CAACnB,YAAY,IAAS,CAAC,IAAI,IAAI,CAACA,YAAY,IAAc,CAAC,KAC/D,CAACN,YAAa,EAChB;QACA,MAAM0B,KAAK,GAAG,IAAI,CAACxE,KAAK,CAACL,KAIV;QACf,MAAM,IAAI,CAACiB,KAAK,CAACpH,UAAU,CAACyD,4BAA4B,EAAE;UACxD4D,EAAE,EAAE,IAAI,CAACb,KAAK,CAACO,QAAQ;UACvBrD,qBAAqB,EAAEsH,KAAK;UAC5B1J,UAAU,EAAE8C,iBAAiB,CAAC4G,KAAK;QACrC,CAAC,CAAC;MACJ;MAEA,IACE,IAAI,CAACrD,KAAK,GAAQ,CAAC,IACnB,IAAI,CAACA,KAAK,GAAa,CAAC,IACxB,IAAI,CAACA,KAAK,GAAU,CAAC,IACrB,IAAI,CAACiC,YAAY,IAAW,CAAC,EAC7B;QACA1F,IAAI,CAACF,WAAW,GAAG,IAAI,CAACqF,gBAAgB,CAAC,IAAI,CAACxC,SAAS,CAAC,CAAC,CAAC;QAC1D3C,IAAI,CAAC4G,OAAO,GAAG,KAAK;QAEpB,OAAO,IAAI,CAACtD,UAAU,CAACtD,IAAI,EAAE,0BAA0B,CAAC;MAC1D,CAAC,MAAM,IACL,IAAI,CAACyD,KAAK,GAAQ,CAAC,IACnB,IAAI,CAACA,KAAK,EAAU,CAAC,IACrB,IAAI,CAACiC,YAAY,IAAc,CAAC,IAChC,IAAI,CAACA,YAAY,IAAS,CAAC,IAC3B,IAAI,CAACA,YAAY,IAAW,CAAC,EAC7B;QACA1F,IAAI,GAAG,IAAI,CAAC+G,WAAW,CACrB/G,IAAI,EACa,IACnB,CAAC;QACD,IAAIA,IAAI,CAACH,IAAI,KAAK,wBAAwB,EAAE;UAC1CG,IAAI,CAACH,IAAI,GAAG,mBAAmB;UAC/BG,IAAI,CAAC4G,OAAO,GAAG,KAAK;UACpB,OAAO5G,IAAI,CAACgH,UAAU;QACxB;QAEAhH,IAAI,CAACH,IAAI,GAAG,SAAS,GAAGG,IAAI,CAACH,IAAI;QAEjC,OAAOG,IAAI;MACb;IACF;IAEA,IAAI,CAAC+F,UAAU,CAAC,CAAC;EACnB;EAEAR,6BAA6BA,CAC3BvF,IAAwC,EACZ;IAC5B,IAAI,CAAC8C,IAAI,CAAC,CAAC;IACX,IAAI,CAACC,gBAAgB,IAAY,CAAC;IAClC/C,IAAI,CAAC0E,cAAc,GAAG,IAAI,CAACuC,uBAAuB,CAAC,CAAC;IACpD,IAAI,CAACrC,SAAS,CAAC,CAAC;IAEhB,OAAO,IAAI,CAACtB,UAAU,CAACtD,IAAI,EAAE,sBAAsB,CAAC;EACtD;EAEA2F,yBAAyBA,CACvB3F,IAAoC,EACZ;IACxB,IAAI,CAAC8C,IAAI,CAAC,CAAC;IACX,MAAMoE,QAAQ,GAAG,IAAI,CAACC,kBAAkB,CAACnH,IAAI,CAAC;IAE9CkH,QAAQ,CAACrH,IAAI,GAAG,kBAAkB;IAClC,OAAOqH,QAAQ;EACjB;EAEAtB,0BAA0BA,CACxB5F,IAAqC,EACZ;IACzB,IAAI,CAAC8C,IAAI,CAAC,CAAC;IACX,MAAMoE,QAAQ,GAAG,IAAI,CAACE,mBAAmB,CAACpH,IAAI,EAAE,IAAI,CAAC;IAErDkH,QAAQ,CAACrH,IAAI,GAAG,mBAAmB;IACnC,OAAOqH,QAAQ;EACjB;EAEArB,yBAAyBA,CACvB7F,IAAoC,EACZ;IACxB,IAAI,CAAC8C,IAAI,CAAC,CAAC;IACX,IAAI,CAACa,qBAAqB,CAAC3D,IAAI,EAAgB,KAAK,CAAC;IACrD,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,kBAAkB,CAAC;EAClD;EAIA2D,qBAAqBA,CAAC3D,IAA2B,EAAEqH,OAAgB,EAAQ;IACzErH,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACyD,6BAA6B,CAC5B,CAACD,OAAO,EACJ,IACpB,CAAC;IAED,IAAI,CAACxC,KAAK,CAACC,WAAW,CACpB9E,IAAI,CAAC6D,EAAE,CAACkB,IAAI,EACZsC,OAAO,GAAGE,yBAAa,GAAGC,wBAAY,EACtCxH,IAAI,CAAC6D,EAAE,CAACoB,GAAG,CAACC,KACd,CAAC;IAED,IAAI,IAAI,CAACzB,KAAK,GAAM,CAAC,EAAE;MACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAChE,CAAC,MAAM;MACLlE,IAAI,CAACiE,cAAc,GAAG,IAAI;IAC5B;IAEAjE,IAAI,CAACyH,OAAO,GAAG,EAAE;IAEjB,IAAI,IAAI,CAACrE,GAAG,GAAY,CAAC,EAAE;MACzB,GAAG;QACDpD,IAAI,CAACyH,OAAO,CAAC5G,IAAI,CAAC,IAAI,CAAC6G,yBAAyB,CAAC,CAAC,CAAC;MACrD,CAAC,QAAQ,CAACL,OAAO,IAAI,IAAI,CAACjE,GAAG,GAAS,CAAC;IACzC;IAEA,IAAIiE,OAAO,EAAE;MACXrH,IAAI,CAAC2H,UAAU,GAAG,EAAE;MACpB3H,IAAI,CAAC4H,MAAM,GAAG,EAAE;MAEhB,IAAI,IAAI,CAACtC,aAAa,IAAW,CAAC,EAAE;QAClC,GAAG;UACDtF,IAAI,CAAC4H,MAAM,CAAC/G,IAAI,CAAC,IAAI,CAAC6G,yBAAyB,CAAC,CAAC,CAAC;QACpD,CAAC,QAAQ,IAAI,CAACtE,GAAG,GAAS,CAAC;MAC7B;MAEA,IAAI,IAAI,CAACkC,aAAa,IAAe,CAAC,EAAE;QACtC,GAAG;UACDtF,IAAI,CAAC2H,UAAU,CAAC9G,IAAI,CAAC,IAAI,CAAC6G,yBAAyB,CAAC,CAAC,CAAC;QACxD,CAAC,QAAQ,IAAI,CAACtE,GAAG,GAAS,CAAC;MAC7B;IACF;IAEApD,IAAI,CAACsG,IAAI,GAAG,IAAI,CAACuB,mBAAmB,CAAC;MACnCC,WAAW,EAAET,OAAO;MACpBU,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAEZ,OAAO;MACnBa,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAR,yBAAyBA,CAAA,EAA2B;IAClD,MAAM1H,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAE7B3C,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACsE,gCAAgC,CAAC,CAAC;IACjD,IAAI,IAAI,CAAC1E,KAAK,GAAM,CAAC,EAAE;MACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACmE,mCAAmC,CAAC,CAAC;IAClE,CAAC,MAAM;MACLpI,IAAI,CAACiE,cAAc,GAAG,IAAI;IAC5B;IAEA,OAAO,IAAI,CAACX,UAAU,CAACtD,IAAI,EAAE,kBAAkB,CAAC;EAClD;EAEAqI,kBAAkBA,CAACrI,IAA6B,EAAmB;IACjE,IAAI,CAAC2D,qBAAqB,CAAC3D,IAAI,EAAgB,KAAK,CAAC;IACrD,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,sBAAsB,CAAC;EACtD;EAEAsI,kBAAkBA,CAACC,IAAY,EAAE;IAC/B,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,IAAI,CAACrF,KAAK,CAACpH,UAAU,CAACmD,4BAA4B,EAAE;QAClDkE,EAAE,EAAE,IAAI,CAACb,KAAK,CAACO;MACjB,CAAC,CAAC;IACJ;EACF;EAEA2F,iBAAiBA,CAACD,IAAY,EAAE1F,QAAkB,EAAE/C,WAAqB,EAAE;IACzE,IAAI,CAAClE,aAAa,CAAC6M,GAAG,CAACF,IAAI,CAAC,EAAE;IAE9B,IAAI,CAACrF,KAAK,CACRpD,WAAW,GACPhE,UAAU,CAACI,kBAAkB,GAC7BJ,UAAU,CAACkD,sBAAsB,EACrC;MACEmE,EAAE,EAAEN,QAAQ;MACZ1G,YAAY,EAAEoM;IAChB,CACF,CAAC;EACH;EAEAjB,6BAA6BA,CAC3BoB,OAAiB,EACjB5I,WAAqB,EACP;IACd,IAAI,CAAC0I,iBAAiB,CACpB,IAAI,CAAClG,KAAK,CAACL,KAAK,EAChB,IAAI,CAACK,KAAK,CAACO,QAAQ,EACnB/C,WACF,CAAC;IACD,OAAO,IAAI,CAACgE,eAAe,CAAC4E,OAAO,CAAC;EACtC;EAIAvB,kBAAkBA,CAACnH,IAA6B,EAAmB;IACjEA,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACyD,6BAA6B,CAC5B,KAAK,EACD,IACpB,CAAC;IACD,IAAI,CAACzC,KAAK,CAACC,WAAW,CAAC9E,IAAI,CAAC6D,EAAE,CAACkB,IAAI,EAAEyC,wBAAY,EAAExH,IAAI,CAAC6D,EAAE,CAACoB,GAAG,CAACC,KAAK,CAAC;IAErE,IAAI,IAAI,CAACzB,KAAK,GAAM,CAAC,EAAE;MACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAChE,CAAC,MAAM;MACLlE,IAAI,CAACiE,cAAc,GAAG,IAAI;IAC5B;IAEAjE,IAAI,CAAC2I,KAAK,GAAG,IAAI,CAACxG,wBAAwB,GAAM,CAAC;IACjD,IAAI,CAACyC,SAAS,CAAC,CAAC;IAEhB,OAAO,IAAI,CAACtB,UAAU,CAACtD,IAAI,EAAE,WAAW,CAAC;EAC3C;EAEAoH,mBAAmBA,CACjBpH,IAA8B,EAC9B4I,OAAgB,EACE;IAClB,IAAI,CAAC7F,gBAAgB,IAAS,CAAC;IAC/B/C,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACyD,6BAA6B,CAC5B,IAAI,EACA,IACpB,CAAC;IACD,IAAI,CAACzC,KAAK,CAACC,WAAW,CAAC9E,IAAI,CAAC6D,EAAE,CAACkB,IAAI,EAAEyC,wBAAY,EAAExH,IAAI,CAAC6D,EAAE,CAACoB,GAAG,CAACC,KAAK,CAAC;IAErE,IAAI,IAAI,CAACzB,KAAK,GAAM,CAAC,EAAE;MACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAChE,CAAC,MAAM;MACLlE,IAAI,CAACiE,cAAc,GAAG,IAAI;IAC5B;IAGAjE,IAAI,CAAC6I,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACpF,KAAK,GAAS,CAAC,EAAE;MACxBzD,IAAI,CAAC6I,SAAS,GAAG,IAAI,CAAC1G,wBAAwB,GAAS,CAAC;IAC1D;IAEAnC,IAAI,CAAC8I,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACF,OAAO,EAAE;MACZ5I,IAAI,CAAC8I,QAAQ,GAAG,IAAI,CAAC3G,wBAAwB,GAAM,CAAC;IACtD;IACA,IAAI,CAACyC,SAAS,CAAC,CAAC;IAEhB,OAAO,IAAI,CAACtB,UAAU,CAACtD,IAAI,EAAE,YAAY,CAAC;EAC5C;EAIA+I,sBAAsBA,CAACC,cAAuB,GAAG,KAAK,EAAmB;IACvE,MAAMC,YAAY,GAAG,IAAI,CAAC3G,KAAK,CAACO,QAAQ;IAExC,MAAM7C,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAkB,CAAC;IAE9C,MAAMuG,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAEzC,MAAMC,KAAK,GAAG,IAAI,CAACpD,kCAAkC,CAAC,CAAC;IACvDhG,IAAI,CAAC+E,IAAI,GAAGqE,KAAK,CAACrE,IAAI;IAEtB/E,IAAI,CAACkJ,QAAQ,GAAGA,QAAQ;IAExBlJ,IAAI,CAACqJ,KAAK,GAAGD,KAAK,CAAC1E,cAAc;IAEjC,IAAI,IAAI,CAACjB,KAAK,GAAM,CAAC,EAAE;MACrB,IAAI,CAACL,GAAG,GAAM,CAAC;MAEfpD,IAAI,CAAC4G,OAAO,GAAG,IAAI,CAACnE,aAAa,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,IAAIuG,cAAc,EAAE;QAClB,IAAI,CAAC9F,KAAK,CAACpH,UAAU,CAACgC,uBAAuB,EAAE;UAAEqF,EAAE,EAAE8F;QAAa,CAAC,CAAC;MACtE;IACF;IAEA,OAAO,IAAI,CAAC3F,UAAU,CAACtD,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEAkE,iCAAiCA,CAAA,EAA+B;IAC9D,MAAM7B,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;IACnC,MAAMvC,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAA6B,CAAC;IACzD3C,IAAI,CAACqE,MAAM,GAAG,EAAE;IAEhB,IAAI,CAAC/B,KAAK,CAACC,MAAM,GAAG,IAAI;IAGxB,IAAI,IAAI,CAACkB,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,IAAe,CAAC,EAAE;MACnD,IAAI,CAACX,IAAI,CAAC,CAAC;IACb,CAAC,MAAM;MACL,IAAI,CAACiD,UAAU,CAAC,CAAC;IACnB;IAEA,IAAIuD,eAAe,GAAG,KAAK;IAE3B,GAAG;MACD,MAAMC,aAAa,GAAG,IAAI,CAACR,sBAAsB,CAACO,eAAe,CAAC;MAElEtJ,IAAI,CAACqE,MAAM,CAACxD,IAAI,CAAC0I,aAAa,CAAC;MAE/B,IAAIA,aAAa,CAAC3C,OAAO,EAAE;QACzB0C,eAAe,GAAG,IAAI;MACxB;MAEA,IAAI,CAAC,IAAI,CAAC7F,KAAK,GAAM,CAAC,EAAE;QACtB,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF,CAAC,QAAQ,CAAC,IAAI,CAACiB,KAAK,GAAM,CAAC;IAC3B,IAAI,CAACjB,MAAM,GAAM,CAAC;IAElB,IAAI,CAACF,KAAK,CAACC,MAAM,GAAGF,SAAS;IAE7B,OAAO,IAAI,CAACiB,UAAU,CAACtD,IAAI,EAAE,0BAA0B,CAAC;EAC1D;EAEAoI,mCAAmCA,CAAA,EAAiC;IAClE,MAAMpI,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAA+B,CAAC;IAC3D,MAAMN,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;IACnCvC,IAAI,CAACqE,MAAM,GAAG,EAAE;IAEhB,IAAI,CAAC/B,KAAK,CAACC,MAAM,GAAG,IAAI;IAExB,IAAI,CAACC,MAAM,GAAM,CAAC;IAClB,MAAMgH,qBAAqB,GAAG,IAAI,CAAClH,KAAK,CAACmH,kBAAkB;IAC3D,IAAI,CAACnH,KAAK,CAACmH,kBAAkB,GAAG,KAAK;IACrC,OAAO,CAAC,IAAI,CAAChG,KAAK,GAAM,CAAC,EAAE;MACzBzD,IAAI,CAACqE,MAAM,CAACxD,IAAI,CAAC,IAAI,CAAC4B,aAAa,CAAC,CAAC,CAAC;MACtC,IAAI,CAAC,IAAI,CAACgB,KAAK,GAAM,CAAC,EAAE;QACtB,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF;IACA,IAAI,CAACF,KAAK,CAACmH,kBAAkB,GAAGD,qBAAqB;IACrD,IAAI,CAAChH,MAAM,GAAM,CAAC;IAElB,IAAI,CAACF,KAAK,CAACC,MAAM,GAAGF,SAAS;IAE7B,OAAO,IAAI,CAACiB,UAAU,CAACtD,IAAI,EAAE,4BAA4B,CAAC;EAC5D;EAEA0J,4CAA4CA,CAAA,EAAiC;IAC3E,MAAM1J,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAA+B,CAAC;IAC3D,MAAMN,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;IACnCvC,IAAI,CAACqE,MAAM,GAAG,EAAE;IAEhB,IAAI,CAAC/B,KAAK,CAACC,MAAM,GAAG,IAAI;IAExB,IAAI,CAACC,MAAM,GAAM,CAAC;IAClB,OAAO,CAAC,IAAI,CAACiB,KAAK,GAAM,CAAC,EAAE;MACzBzD,IAAI,CAACqE,MAAM,CAACxD,IAAI,CAAC,IAAI,CAAC8I,oCAAoC,CAAC,CAAC,CAAC;MAC7D,IAAI,CAAC,IAAI,CAAClG,KAAK,GAAM,CAAC,EAAE;QACtB,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF;IACA,IAAI,CAACA,MAAM,GAAM,CAAC;IAElB,IAAI,CAACF,KAAK,CAACC,MAAM,GAAGF,SAAS;IAE7B,OAAO,IAAI,CAACiB,UAAU,CAACtD,IAAI,EAAE,4BAA4B,CAAC;EAC5D;EAEA4J,sBAAsBA,CAAA,EAAwB;IAC5C,MAAM5J,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAsB,CAAC;IAClD,IAAI,CAACI,gBAAgB,IAAc,CAAC;IAEpC/C,IAAI,CAACyH,OAAO,GAAG,EAAE;IACjB,IAAI,IAAI,CAACrE,GAAG,GAAY,CAAC,EAAE;MACzB,GAAG;QACDpD,IAAI,CAACyH,OAAO,CAAC5G,IAAI,CAAC,IAAI,CAAC6G,yBAAyB,CAAC,CAAC,CAAC;MACrD,CAAC,QAAQ,IAAI,CAACtE,GAAG,GAAS,CAAC;IAC7B;IAEApD,IAAI,CAACsG,IAAI,GAAG,IAAI,CAACuB,mBAAmB,CAAC;MACnCC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE;IAChB,CAAC,CAAC;IAEF,OAAO,IAAI,CAAC5E,UAAU,CAACtD,IAAI,EAAE,yBAAyB,CAAC;EACzD;EAEA6J,0BAA0BA,CAAA,EAAiB;IACzC,OAAO,IAAI,CAACpG,KAAK,IAAO,CAAC,IAAI,IAAI,CAACA,KAAK,IAAU,CAAC,GAC9C,KAAK,CAAC2C,aAAa,CAAC,CAAC,GACrB,IAAI,CAACtC,eAAe,CAAC,IAAI,CAAC;EAChC;EAEAgG,0BAA0BA,CACxB9J,IAAqC,EACrC+J,QAAiB,EACjBb,QAAgC,EACP;IACzBlJ,IAAI,CAACgK,MAAM,GAAGD,QAAQ;IAGtB,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC,CAACpK,IAAI,OAAa,EAAE;MACtCG,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACgG,0BAA0B,CAAC,CAAC;MAC3C7J,IAAI,CAACkK,GAAG,GAAG,IAAI,CAAC/H,wBAAwB,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLnC,IAAI,CAAC6D,EAAE,GAAG,IAAI;MACd7D,IAAI,CAACkK,GAAG,GAAG,IAAI,CAACzH,aAAa,CAAC,CAAC;IACjC;IACA,IAAI,CAACD,MAAM,EAAY,CAAC;IACxBxC,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACE,wBAAwB,CAAC,CAAC;IAC5CnC,IAAI,CAACkJ,QAAQ,GAAGA,QAAQ;IAExB,OAAO,IAAI,CAAC5F,UAAU,CAACtD,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAmK,+BAA+BA,CAC7BnK,IAA0C,EAC1C+J,QAAiB,EACa;IAC9B/J,IAAI,CAACgK,MAAM,GAAGD,QAAQ;IAEtB/J,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACgG,0BAA0B,CAAC,CAAC;IAC3C,IAAI,CAACrH,MAAM,EAAY,CAAC;IACxB,IAAI,CAACA,MAAM,EAAY,CAAC;IACxB,IAAI,IAAI,CAACiB,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,GAAU,CAAC,EAAE;MAC9CzD,IAAI,CAACoK,MAAM,GAAG,IAAI;MAClBpK,IAAI,CAACqK,QAAQ,GAAG,KAAK;MACrBrK,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACqI,4BAA4B,CAC5C,IAAI,CAACC,WAAW,CAACvK,IAAI,CAACiF,GAAG,CAACC,KAAK,CACjC,CAAC;IACH,CAAC,MAAM;MACLlF,IAAI,CAACoK,MAAM,GAAG,KAAK;MACnB,IAAI,IAAI,CAAChH,GAAG,GAAY,CAAC,EAAE;QACzBpD,IAAI,CAACqK,QAAQ,GAAG,IAAI;MACtB;MACArK,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACE,wBAAwB,CAAC,CAAC;IAC9C;IACA,OAAO,IAAI,CAACmB,UAAU,CAACtD,IAAI,EAAE,wBAAwB,CAAC;EACxD;EAEAsK,4BAA4BA,CAC1BtK,IAA0C,EACZ;IAC9BA,IAAI,CAACqE,MAAM,GAAG,EAAE;IAChBrE,IAAI,CAACsE,IAAI,GAAG,IAAI;IAChBtE,IAAI,CAACiE,cAAc,GAAG,IAAI;IAC1BjE,IAAI,CAACuE,IAAI,GAAG,IAAI;IAEhB,IAAI,IAAI,CAACd,KAAK,GAAM,CAAC,EAAE;MACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAChE;IAEA,IAAI,CAAC1B,MAAM,GAAU,CAAC;IACtB,IAAI,IAAI,CAACiB,KAAK,GAAS,CAAC,EAAE;MACxBzD,IAAI,CAACuE,IAAI,GAAG,IAAI,CAACiG,0BAA0B,CAAa,IAAI,CAAC;MAE7DxK,IAAI,CAACuE,IAAI,CAACQ,IAAI,GAAG,IAAI;MACrB,IAAI,CAAC,IAAI,CAACtB,KAAK,GAAU,CAAC,EAAE;QAC1B,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF;IACA,OAAO,CAAC,IAAI,CAACiB,KAAK,GAAU,CAAC,IAAI,CAAC,IAAI,CAACA,KAAK,GAAY,CAAC,EAAE;MACzDzD,IAAI,CAACqE,MAAM,CAACxD,IAAI,CAAC,IAAI,CAAC2J,0BAA0B,CAAC,KAAK,CAAC,CAAC;MACxD,IAAI,CAAC,IAAI,CAAC/G,KAAK,GAAU,CAAC,EAAE;QAC1B,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF;IAEA,IAAI,IAAI,CAACY,GAAG,GAAY,CAAC,EAAE;MACzBpD,IAAI,CAACsE,IAAI,GAAG,IAAI,CAACkG,0BAA0B,CAAC,KAAK,CAAC;IACpD;IACA,IAAI,CAAChI,MAAM,GAAU,CAAC;IACtBxC,IAAI,CAACyE,UAAU,GAAG,IAAI,CAACtC,wBAAwB,CAAC,CAAC;IAEjD,OAAO,IAAI,CAACmB,UAAU,CAACtD,IAAI,EAAE,wBAAwB,CAAC;EACxD;EAEAyK,+BAA+BA,CAC7BzK,IAA0C,EAC1C+J,QAAiB,EACa;IAC9B,MAAMW,SAAS,GAAG,IAAI,CAAC/H,SAAS,CAAC,CAAC;IAClC3C,IAAI,CAACgK,MAAM,GAAGD,QAAQ;IACtB/J,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACqI,4BAA4B,CAACI,SAAS,CAAC;IACzD,OAAO,IAAI,CAACpH,UAAU,CAACtD,IAAI,EAAE,wBAAwB,CAAC;EACxD;EAEA6H,mBAAmBA,CAAC;IAClBC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,UAAU;IACVC;EAOF,CAAC,EAA8B;IAC7B,MAAM7F,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;IACnC,IAAI,CAACD,KAAK,CAACC,MAAM,GAAG,IAAI;IAExB,MAAMoI,SAAS,GAAG,IAAI,CAAChI,SAAS,CAAC,CAAC;IAElCgI,SAAS,CAACC,cAAc,GAAG,EAAE;IAC7BD,SAAS,CAACE,UAAU,GAAG,EAAE;IACzBF,SAAS,CAACG,QAAQ,GAAG,EAAE;IACvBH,SAAS,CAACI,aAAa,GAAG,EAAE;IAE5B,IAAIC,QAAQ;IACZ,IAAIC,KAAK;IACT,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAInD,UAAU,IAAI,IAAI,CAACtE,KAAK,EAAa,CAAC,EAAE;MAC1C,IAAI,CAACjB,MAAM,EAAa,CAAC;MACzBwI,QAAQ,IAAe;MACvBC,KAAK,GAAG,IAAI;IACd,CAAC,MAAM;MACL,IAAI,CAACzI,MAAM,EAAU,CAAC;MACtBwI,QAAQ,IAAY;MACpBC,KAAK,GAAG,KAAK;IACf;IAEAN,SAAS,CAACM,KAAK,GAAGA,KAAK;IAEvB,OAAO,CAAC,IAAI,CAACxH,KAAK,CAACuH,QAAQ,CAAC,EAAE;MAC5B,IAAIjB,QAAQ,GAAG,KAAK;MACpB,IAAIoB,aAA0C,GAAG,IAAI;MACrD,IAAIC,eAA4C,GAAG,IAAI;MACvD,MAAMpL,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;MAE7B,IAAIsF,UAAU,IAAI,IAAI,CAACvC,YAAY,IAAU,CAAC,EAAE;QAC9C,MAAMuE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;QAElC,IAAIA,SAAS,CAACpK,IAAI,OAAa,IAAIoK,SAAS,CAACpK,IAAI,OAAgB,EAAE;UACjE,IAAI,CAACiD,IAAI,CAAC,CAAC;UACXqI,aAAa,GAAG,IAAI,CAAC7I,KAAK,CAACO,QAAQ;UACnCiF,WAAW,GAAG,KAAK;QACrB;MACF;MAEA,IAAIA,WAAW,IAAI,IAAI,CAACpC,YAAY,IAAW,CAAC,EAAE;QAChD,MAAMuE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;QAGlC,IAAIA,SAAS,CAACpK,IAAI,OAAa,IAAIoK,SAAS,CAACpK,IAAI,OAAgB,EAAE;UACjE,IAAI,CAACiD,IAAI,CAAC,CAAC;UACXiH,QAAQ,GAAG,IAAI;QACjB;MACF;MAEA,MAAMb,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAEzC,IAAI,IAAI,CAAC/F,GAAG,EAAY,CAAC,EAAE;QACzB,IAAI+H,aAAa,IAAI,IAAI,EAAE;UACzB,IAAI,CAACpF,UAAU,CAACoF,aAAa,CAAC;QAChC;QACA,IAAI,IAAI,CAAC/H,GAAG,EAAY,CAAC,EAAE;UACzB,IAAI8F,QAAQ,EAAE;YACZ,IAAI,CAACnD,UAAU,CAACmD,QAAQ,CAACjE,GAAG,CAACC,KAAK,CAAC;UACrC;UACAyF,SAAS,CAACI,aAAa,CAAClK,IAAI,CAC1B,IAAI,CAACsJ,+BAA+B,CAACnK,IAAI,EAAE+J,QAAQ,CACrD,CAAC;QACH,CAAC,MAAM;UACLY,SAAS,CAACG,QAAQ,CAACjK,IAAI,CACrB,IAAI,CAACiJ,0BAA0B,CAAC9J,IAAI,EAAE+J,QAAQ,EAAEb,QAAQ,CAC1D,CAAC;QACH;MACF,CAAC,MAAM,IAAI,IAAI,CAACzF,KAAK,GAAU,CAAC,IAAI,IAAI,CAACA,KAAK,GAAM,CAAC,EAAE;QACrD,IAAI0H,aAAa,IAAI,IAAI,EAAE;UACzB,IAAI,CAACpF,UAAU,CAACoF,aAAa,CAAC;QAChC;QACA,IAAIjC,QAAQ,EAAE;UACZ,IAAI,CAACnD,UAAU,CAACmD,QAAQ,CAACjE,GAAG,CAACC,KAAK,CAAC;QACrC;QACAyF,SAAS,CAACC,cAAc,CAAC/J,IAAI,CAC3B,IAAI,CAAC4J,+BAA+B,CAACzK,IAAI,EAAE+J,QAAQ,CACrD,CAAC;MACH,CAAC,MAAM;QACL,IAAItD,IAAI,GAAG,MAAM;QAEjB,IAAI,IAAI,CAACf,YAAY,GAAQ,CAAC,IAAI,IAAI,CAACA,YAAY,IAAQ,CAAC,EAAE;UAC5D,MAAMuE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;UAClC,IAAI,IAAAoB,iCAA0B,EAACpB,SAAS,CAACpK,IAAI,CAAC,EAAE;YAC9C4G,IAAI,GAAG,IAAI,CAACnE,KAAK,CAACL,KAAK;YACvB,IAAI,CAACa,IAAI,CAAC,CAAC;UACb;QACF;QAEA,MAAMwI,aAAa,GAAG,IAAI,CAACC,2BAA2B,CACpDvL,IAAI,EACJ+J,QAAQ,EACRoB,aAAa,EACbjC,QAAQ,EACRzC,IAAI,EACJuB,WAAW,EACXE,YAAY,WAAZA,YAAY,GAAI,CAAC+C,KACnB,CAAC;QAED,IAAIK,aAAa,KAAK,IAAI,EAAE;UAC1BJ,OAAO,GAAG,IAAI;UACdE,eAAe,GAAG,IAAI,CAAC9I,KAAK,CAACkD,eAAe;QAC9C,CAAC,MAAM;UACLmF,SAAS,CAACE,UAAU,CAAChK,IAAI,CAACyK,aAAa,CAAC;QAC1C;MACF;MAEA,IAAI,CAACE,uBAAuB,CAAC,CAAC;MAE9B,IACEJ,eAAe,IACf,CAAC,IAAI,CAAC3H,KAAK,EAAU,CAAC,IACtB,CAAC,IAAI,CAACA,KAAK,EAAa,CAAC,EACzB;QACA,IAAI,CAACP,KAAK,CAACpH,UAAU,CAACiD,iCAAiC,EAAE;UACvDoE,EAAE,EAAEiI;QACN,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAAC5I,MAAM,CAACwI,QAAQ,CAAC;IAOrB,IAAIhD,WAAW,EAAE;MACf2C,SAAS,CAACO,OAAO,GAAGA,OAAO;IAC7B;IAEA,MAAMO,GAAG,GAAG,IAAI,CAACnI,UAAU,CAACqH,SAAS,EAAE,sBAAsB,CAAC;IAE9D,IAAI,CAACrI,KAAK,CAACC,MAAM,GAAGF,SAAS;IAE7B,OAAOoJ,GAAG;EACZ;EAEAF,2BAA2BA,CACzBvL,IAAuE,EACvE+J,QAAiB,EACjBoB,aAA0C,EAC1CjC,QAA2C,EAC3CzC,IAAY,EACZuB,WAAoB,EACpBE,YAAqB,EAC6C;IAClE,IAAI,IAAI,CAAC9E,GAAG,GAAY,CAAC,EAAE;MACzB,MAAMsI,cAAc,GAClB,IAAI,CAACjI,KAAK,GAAS,CAAC,IACpB,IAAI,CAACA,KAAK,GAAQ,CAAC,IACnB,IAAI,CAACA,KAAK,EAAU,CAAC,IACrB,IAAI,CAACA,KAAK,EAAa,CAAC;MAE1B,IAAIiI,cAAc,EAAE;QAClB,IAAI,CAAC1D,WAAW,EAAE;UAChB,IAAI,CAAC9E,KAAK,CAACpH,UAAU,CAAC6B,sBAAsB,EAAE;YAC5CwF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkD;UACjB,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI,CAAC0C,YAAY,EAAE;UACxB,IAAI,CAAChF,KAAK,CAACpH,UAAU,CAAC4B,kBAAkB,EAAE;YACxCyF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkD;UACjB,CAAC,CAAC;QACJ;QACA,IAAI0D,QAAQ,EAAE;UACZ,IAAI,CAAChG,KAAK,CAACpH,UAAU,CAAC8B,eAAe,EAAE;YAAEuF,EAAE,EAAE+F;UAAS,CAAC,CAAC;QAC1D;QAEA,OAAO,IAAI;MACb;MAEA,IAAI,CAAClB,WAAW,EAAE;QAChB,IAAI,CAAC9E,KAAK,CAACpH,UAAU,CAACqD,oBAAoB,EAAE;UAC1CgE,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkD;QACjB,CAAC,CAAC;MACJ;MACA,IAAI2F,aAAa,IAAI,IAAI,EAAE;QACzB,IAAI,CAACpF,UAAU,CAACoF,aAAa,CAAC;MAChC;MACA,IAAIjC,QAAQ,EAAE;QACZ,IAAI,CAAChG,KAAK,CAACpH,UAAU,CAACyC,cAAc,EAAE;UAAE4E,EAAE,EAAE+F;QAAS,CAAC,CAAC;MACzD;MAEAlJ,IAAI,CAAC2L,QAAQ,GAAG,IAAI,CAAClJ,aAAa,CAAC,CAAC;MACpC,OAAO,IAAI,CAACa,UAAU,CAACtD,IAAI,EAAE,0BAA0B,CAAC;IAC1D,CAAC,MAAM;MACLA,IAAI,CAACkK,GAAG,GAAG,IAAI,CAACL,0BAA0B,CAAC,CAAC;MAC5C7J,IAAI,CAACgK,MAAM,GAAGD,QAAQ;MACtB/J,IAAI,CAAC4L,KAAK,GAAGT,aAAa,IAAI,IAAI;MAClCnL,IAAI,CAACyG,IAAI,GAAGA,IAAI;MAEhB,IAAI4D,QAAQ,GAAG,KAAK;MACpB,IAAI,IAAI,CAAC5G,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,GAAU,CAAC,EAAE;QAE9CzD,IAAI,CAACoK,MAAM,GAAG,IAAI;QAElB,IAAIe,aAAa,IAAI,IAAI,EAAE;UACzB,IAAI,CAACpF,UAAU,CAACoF,aAAa,CAAC;QAChC;QACA,IAAIjC,QAAQ,EAAE;UACZ,IAAI,CAACnD,UAAU,CAACmD,QAAQ,CAACjE,GAAG,CAACC,KAAK,CAAC;QACrC;QAEAlF,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACqI,4BAA4B,CAC5C,IAAI,CAACC,WAAW,CAACvK,IAAI,CAACiF,GAAG,CAACC,KAAK,CACjC,CAAC;QACD,IAAIuB,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;UACpC,IAAI,CAACoF,2BAA2B,CAAC7L,IAAI,CAAC;QACxC;QAEA,IACE,CAACgI,WAAW,IACZhI,IAAI,CAACkK,GAAG,CAACnF,IAAI,KAAK,aAAa,IAC/B/E,IAAI,CAACiC,KAAK,CAACsC,IAAI,EACf;UACA,IAAI,CAACrB,KAAK,CAACpH,UAAU,CAAC2C,4BAA4B,EAAE;YAClD0E,EAAE,EAAEnD,IAAI,CAACiC,KAAK,CAACsC;UACjB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAIkC,IAAI,KAAK,MAAM,EAAE,IAAI,CAACV,UAAU,CAAC,CAAC;QAEtC/F,IAAI,CAACoK,MAAM,GAAG,KAAK;QAEnB,IAAI,IAAI,CAAChH,GAAG,GAAY,CAAC,EAAE;UACzBiH,QAAQ,GAAG,IAAI;QACjB;QACArK,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACE,wBAAwB,CAAC,CAAC;QAC5CnC,IAAI,CAACkJ,QAAQ,GAAGA,QAAQ;MAC1B;MAEAlJ,IAAI,CAACqK,QAAQ,GAAGA,QAAQ;MAExB,OAAO,IAAI,CAAC/G,UAAU,CAACtD,IAAI,EAAE,oBAAoB,CAAC;IACpD;EACF;EAIA6L,2BAA2BA,CACzBC,QAEC,EACK;IACN,MAAMC,UAAU,GAAGD,QAAQ,CAACrF,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC;IAClD,MAAM7F,MAAM,GACVkL,QAAQ,CAAC7J,KAAK,CAACoC,MAAM,CAACzD,MAAM,IAAIkL,QAAQ,CAAC7J,KAAK,CAACqC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE9D,IAAIwH,QAAQ,CAAC7J,KAAK,CAACsC,IAAI,EAAE;MACvB,IAAI,CAACrB,KAAK,CACR4I,QAAQ,CAACrF,IAAI,KAAK,KAAK,GACnB3K,UAAU,CAACyB,yBAAyB,GACpCzB,UAAU,CAACwC,yBAAyB,EACxC;QAAE6E,EAAE,EAAE2I,QAAQ,CAAC7J,KAAK,CAACsC;MAAK,CAC5B,CAAC;IACH;IAEA,IAAI3D,MAAM,KAAKmL,UAAU,EAAE;MACzB,IAAI,CAAC7I,KAAK,CACR4I,QAAQ,CAACrF,IAAI,KAAK,KAAK,GACnBuF,kBAAM,CAACC,cAAc,GACrBD,kBAAM,CAACE,cAAc,EACzB;QAAE/I,EAAE,EAAE2I;MAAS,CACjB,CAAC;IACH;IAEA,IAAIA,QAAQ,CAACrF,IAAI,KAAK,KAAK,IAAIqF,QAAQ,CAAC7J,KAAK,CAACqC,IAAI,EAAE;MAClD,IAAI,CAACpB,KAAK,CAAC8I,kBAAM,CAACG,sBAAsB,EAAE;QAAEhJ,EAAE,EAAE2I;MAAS,CAAC,CAAC;IAC7D;EACF;EAEAN,uBAAuBA,CAAA,EAAS;IAC9B,IACE,CAAC,IAAI,CAACpI,GAAG,GAAQ,CAAC,IAClB,CAAC,IAAI,CAACA,GAAG,GAAS,CAAC,IACnB,CAAC,IAAI,CAACK,KAAK,EAAU,CAAC,IACtB,CAAC,IAAI,CAACA,KAAK,EAAa,CAAC,EACzB;MACA,IAAI,CAACsC,UAAU,CAAC,CAAC;IACnB;EACF;EAEAoC,gCAAgCA,CAC9BtF,QAAmB,EACnBgB,EAAiB,EACc;IAAA,IAAAuI,SAAA;IAC/B,CAAAA,SAAA,GAAAvJ,QAAQ,YAAAuJ,SAAA,GAARvJ,QAAQ,GAAK,IAAI,CAACP,KAAK,CAACO,QAAQ;IAChC,IAAI7C,IAAkD,GACpD6D,EAAE,IAAI,IAAI,CAACyD,6BAA6B,CAAC,IAAI,CAAC;IAEhD,OAAO,IAAI,CAAClE,GAAG,GAAO,CAAC,EAAE;MACvB,MAAMiJ,KAAK,GAAG,IAAI,CAAC9B,WAAW,CAAgC1H,QAAQ,CAAC;MACvEwJ,KAAK,CAACC,aAAa,GAAGtM,IAAI;MAC1BqM,KAAK,CAACxI,EAAE,GAAG,IAAI,CAACyD,6BAA6B,CAAC,IAAI,CAAC;MACnDtH,IAAI,GAAG,IAAI,CAACsD,UAAU,CAAC+I,KAAK,EAAE,yBAAyB,CAAC;IAC1D;IAEA,OAAOrM,IAAI;EACb;EAEAuM,oBAAoBA,CAClB1J,QAAkB,EAClBgB,EAAgB,EACa;IAC7B,MAAM7D,IAAI,GAAG,IAAI,CAACuK,WAAW,CAAC1H,QAAQ,CAAC;IAEvC7C,IAAI,CAACiE,cAAc,GAAG,IAAI;IAC1BjE,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACsE,gCAAgC,CAACtF,QAAQ,EAAEgB,EAAE,CAAC;IAE7D,IAAI,IAAI,CAACJ,KAAK,GAAM,CAAC,EAAE;MACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACmE,mCAAmC,CAAC,CAAC;IAClE;IAEA,OAAO,IAAI,CAAC9E,UAAU,CAACtD,IAAI,EAAE,uBAAuB,CAAC;EACvD;EAEAwM,mBAAmBA,CAAA,EAA+B;IAChD,MAAMxM,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACH,MAAM,GAAW,CAAC;IACvBxC,IAAI,CAAC2L,QAAQ,GAAG,IAAI,CAACc,oBAAoB,CAAC,CAAC;IAC3C,OAAO,IAAI,CAACnJ,UAAU,CAACtD,IAAI,EAAE,sBAAsB,CAAC;EACtD;EAEA0M,kBAAkBA,CAAA,EAA8B;IAC9C,MAAM1M,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAC7B3C,IAAI,CAAC2M,KAAK,GAAG,EAAE;IACf,IAAI,CAACnK,MAAM,EAAY,CAAC;IAExB,OAAO,IAAI,CAACF,KAAK,CAACsK,GAAG,GAAG,IAAI,CAAChM,MAAM,IAAI,CAAC,IAAI,CAAC6C,KAAK,EAAY,CAAC,EAAE;MAC/DzD,IAAI,CAAC2M,KAAK,CAAC9L,IAAI,CAAC,IAAI,CAAC4B,aAAa,CAAC,CAAC,CAAC;MACrC,IAAI,IAAI,CAACgB,KAAK,EAAY,CAAC,EAAE;MAC7B,IAAI,CAACjB,MAAM,GAAS,CAAC;IACvB;IACA,IAAI,CAACA,MAAM,EAAY,CAAC;IACxB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,qBAAqB,CAAC;EACrD;EAEAwK,0BAA0BA,CAACqC,KAAc,EAA2B;IAClE,IAAI9H,IAAI,GAAG,IAAI;IACf,IAAIsF,QAAQ,GAAG,KAAK;IACpB,IAAI3F,cAAc,GAAG,IAAI;IACzB,MAAM1E,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAA0B,CAAC;IACtD,MAAMmK,EAAE,GAAG,IAAI,CAAC7C,SAAS,CAAC,CAAC;IAC3B,MAAM8C,MAAM,GAAG,IAAI,CAACzK,KAAK,CAACzC,IAAI,OAAa;IAE3C,IAAIiN,EAAE,CAACjN,IAAI,OAAa,IAAIiN,EAAE,CAACjN,IAAI,OAAgB,EAAE;MACnD,IAAIkN,MAAM,IAAI,CAACF,KAAK,EAAE;QACpB,IAAI,CAAC3J,KAAK,CAACpH,UAAU,CAAC6C,oBAAoB,EAAE;UAAEwE,EAAE,EAAEnD;QAAK,CAAC,CAAC;MAC3D;MACA+E,IAAI,GAAG,IAAI,CAACjB,eAAe,CAACiJ,MAAM,CAAC;MACnC,IAAI,IAAI,CAAC3J,GAAG,GAAY,CAAC,EAAE;QACzBiH,QAAQ,GAAG,IAAI;QACf,IAAI0C,MAAM,EAAE;UACV,IAAI,CAAC7J,KAAK,CAACpH,UAAU,CAAC4C,yBAAyB,EAAE;YAAEyE,EAAE,EAAEnD;UAAK,CAAC,CAAC;QAChE;MACF;MACA0E,cAAc,GAAG,IAAI,CAACvC,wBAAwB,CAAC,CAAC;IAClD,CAAC,MAAM;MACLuC,cAAc,GAAG,IAAI,CAACjC,aAAa,CAAC,CAAC;IACvC;IACAzC,IAAI,CAAC+E,IAAI,GAAGA,IAAI;IAChB/E,IAAI,CAACqK,QAAQ,GAAGA,QAAQ;IACxBrK,IAAI,CAAC0E,cAAc,GAAGA,cAAc;IACpC,OAAO,IAAI,CAACpB,UAAU,CAACtD,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAgN,kCAAkCA,CAChCnN,IAAgB,EACS;IACzB,MAAMG,IAAI,GAAG,IAAI,CAACuK,WAAW,CAAC1K,IAAI,CAACoF,GAAG,CAACC,KAAK,CAAC;IAC7ClF,IAAI,CAAC+E,IAAI,GAAG,IAAI;IAChB/E,IAAI,CAACqK,QAAQ,GAAG,KAAK;IACrBrK,IAAI,CAAC0E,cAAc,GAAG7E,IAAI;IAC1B,OAAO,IAAI,CAACyD,UAAU,CAACtD,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAoE,2BAA2BA,CAACC,MAAiC,GAAG,EAAE,EAIhE;IACA,IAAIC,IAAgD,GAAG,IAAI;IAC3D,IAAIE,KAAiD,GAAG,IAAI;IAC5D,IAAI,IAAI,CAACf,KAAK,GAAS,CAAC,EAAE;MACxBe,KAAK,GAAG,IAAI,CAACgG,0BAA0B,CAAa,IAAI,CAAC;MAEzDhG,KAAK,CAACO,IAAI,GAAG,IAAI;MACjB,IAAI,CAAC,IAAI,CAACtB,KAAK,GAAU,CAAC,EAAE;QAC1B,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF;IACA,OAAO,CAAC,IAAI,CAACiB,KAAK,GAAU,CAAC,IAAI,CAAC,IAAI,CAACA,KAAK,GAAY,CAAC,EAAE;MACzDY,MAAM,CAACxD,IAAI,CAAC,IAAI,CAAC2J,0BAA0B,CAAC,KAAK,CAAC,CAAC;MACnD,IAAI,CAAC,IAAI,CAAC/G,KAAK,GAAU,CAAC,EAAE;QAC1B,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF;IACA,IAAI,IAAI,CAACY,GAAG,GAAY,CAAC,EAAE;MACzBkB,IAAI,GAAG,IAAI,CAACkG,0BAA0B,CAAC,KAAK,CAAC;IAC/C;IACA,OAAO;MAAEnG,MAAM;MAAEC,IAAI;MAAEE;IAAM,CAAC;EAChC;EAEAyI,yBAAyBA,CACvBpK,QAAkB,EAClB7C,IAAkC,EAClC6D,EAAgB,EACM;IACtB,QAAQA,EAAE,CAACkB,IAAI;MACb,KAAK,KAAK;QACR,OAAO,IAAI,CAACzB,UAAU,CAACtD,IAAI,EAAE,mBAAmB,CAAC;MAEnD,KAAK,MAAM;MACX,KAAK,SAAS;QACZ,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,uBAAuB,CAAC;MAEvD,KAAK,OAAO;QACV,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,qBAAqB,CAAC;MAErD,KAAK,OAAO;QACV,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,qBAAqB,CAAC;MAErD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,sBAAsB,CAAC;MAEtD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,sBAAsB,CAAC;MAEtD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACsD,UAAU,CAACtD,IAAI,EAAE,sBAAsB,CAAC;MAEtD;QACE,IAAI,CAACsI,kBAAkB,CAACzE,EAAE,CAACkB,IAAI,CAAC;QAChC,OAAO,IAAI,CAACwH,oBAAoB,CAAC1J,QAAQ,EAAEgB,EAAE,CAAC;IAClD;EACF;EAKA4I,oBAAoBA,CAAA,EAAyB;IAC3C,MAAM5J,QAAQ,GAAG,IAAI,CAACP,KAAK,CAACO,QAAQ;IACpC,MAAM7C,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAC7B,IAAIwB,GAAG;IACP,IAAItE,IAAI;IACR,IAAIqN,aAAa,GAAG,KAAK;IACzB,MAAM1D,qBAAqB,GAAG,IAAI,CAAClH,KAAK,CAACmH,kBAAkB;IAE3D,QAAQ,IAAI,CAACnH,KAAK,CAACzC,IAAI;MACrB;QACE,OAAO,IAAI,CAACgI,mBAAmB,CAAC;UAC9BC,WAAW,EAAE,KAAK;UAClBC,UAAU,EAAE,KAAK;UACjBC,WAAW,EAAE,IAAI;UACjBC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE;QAChB,CAAC,CAAC;MAEJ;QACE,OAAO,IAAI,CAACL,mBAAmB,CAAC;UAC9BC,WAAW,EAAE,KAAK;UAClBC,UAAU,EAAE,IAAI;UAChBC,WAAW,EAAE,IAAI;UACjBC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE;QAChB,CAAC,CAAC;MAEJ;QACE,IAAI,CAAC5F,KAAK,CAACmH,kBAAkB,GAAG,KAAK;QACrC5J,IAAI,GAAG,IAAI,CAAC6M,kBAAkB,CAAC,CAAC;QAChC,IAAI,CAACpK,KAAK,CAACmH,kBAAkB,GAAGD,qBAAqB;QACrD,OAAO3J,IAAI;MAEb;QACEG,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;QAC9D,IAAI,CAAC1B,MAAM,GAAU,CAAC;QACtB2B,GAAG,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;QACxCpE,IAAI,CAACqE,MAAM,GAAGF,GAAG,CAACE,MAAM;QACxBrE,IAAI,CAACsE,IAAI,GAAGH,GAAG,CAACG,IAAI;QACpBtE,IAAI,CAACuE,IAAI,GAAGJ,GAAG,CAACK,KAAK;QACrB,IAAI,CAAChC,MAAM,GAAU,CAAC;QAEtB,IAAI,CAACA,MAAM,GAAS,CAAC;QAErBxC,IAAI,CAACyE,UAAU,GAAG,IAAI,CAAChC,aAAa,CAAC,CAAC;QAEtC,OAAO,IAAI,CAACa,UAAU,CAACtD,IAAI,EAAE,wBAAwB,CAAC;MAExD;QACE,IAAI,CAAC8C,IAAI,CAAC,CAAC;QAGX,IAAI,CAAC,IAAI,CAACW,KAAK,GAAU,CAAC,IAAI,CAAC,IAAI,CAACA,KAAK,GAAY,CAAC,EAAE;UACtD,IAAI,IAAA0J,wBAAiB,EAAC,IAAI,CAAC7K,KAAK,CAACzC,IAAI,CAAC,IAAI,IAAI,CAAC4D,KAAK,GAAS,CAAC,EAAE;YAC9D,MAAM2J,KAAK,GAAG,IAAI,CAACnD,SAAS,CAAC,CAAC,CAACpK,IAAI;YACnCqN,aAAa,GAAGE,KAAK,OAAgB,IAAIA,KAAK,OAAa;UAC7D,CAAC,MAAM;YACLF,aAAa,GAAG,IAAI;UACtB;QACF;QAEA,IAAIA,aAAa,EAAE;UACjB,IAAI,CAAC5K,KAAK,CAACmH,kBAAkB,GAAG,KAAK;UACrC5J,IAAI,GAAG,IAAI,CAAC4C,aAAa,CAAC,CAAC;UAC3B,IAAI,CAACH,KAAK,CAACmH,kBAAkB,GAAGD,qBAAqB;UAGrD,IACE,IAAI,CAAClH,KAAK,CAACmH,kBAAkB,IAC7B,EACE,IAAI,CAAChG,KAAK,GAAS,CAAC,IACnB,IAAI,CAACA,KAAK,GAAU,CAAC,IAAI,IAAI,CAACwG,SAAS,CAAC,CAAC,CAACpK,IAAI,OAAc,CAC9D,EACD;YACA,IAAI,CAAC2C,MAAM,GAAU,CAAC;YACtB,OAAO3C,IAAI;UACb,CAAC,MAAM;YAEL,IAAI,CAACuD,GAAG,GAAS,CAAC;UACpB;QACF;QAEA,IAAIvD,IAAI,EAAE;UACRsE,GAAG,GAAG,IAAI,CAACC,2BAA2B,CAAC,CACrC,IAAI,CAAC4I,kCAAkC,CAACnN,IAAI,CAAC,CAC9C,CAAC;QACJ,CAAC,MAAM;UACLsE,GAAG,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;QAC1C;QAEApE,IAAI,CAACqE,MAAM,GAAGF,GAAG,CAACE,MAAM;QACxBrE,IAAI,CAACsE,IAAI,GAAGH,GAAG,CAACG,IAAI;QACpBtE,IAAI,CAACuE,IAAI,GAAGJ,GAAG,CAACK,KAAK;QAErB,IAAI,CAAChC,MAAM,GAAU,CAAC;QAEtB,IAAI,CAACA,MAAM,GAAS,CAAC;QAErBxC,IAAI,CAACyE,UAAU,GAAG,IAAI,CAAChC,aAAa,CAAC,CAAC;QAEtCzC,IAAI,CAACiE,cAAc,GAAG,IAAI;QAE1B,OAAO,IAAI,CAACX,UAAU,CAACtD,IAAI,EAAE,wBAAwB,CAAC;MAExD;QACE,OAAO,IAAI,CAACqN,YAAY,CACtB,IAAI,CAAC/K,KAAK,CAACL,KAAK,EAChB,6BACF,CAAC;MAEH;MACA;QACEjC,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACwB,KAAK,GAAS,CAAC;QACjC,IAAI,CAACX,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAACQ,UAAU,CACpBtD,IAAI,EACJ,8BACF,CAAC;MAEH;QACE,IAAI,IAAI,CAACsC,KAAK,CAACL,KAAK,KAAK,GAAG,EAAE;UAC5B,IAAI,CAACa,IAAI,CAAC,CAAC;UACX,IAAI,IAAI,CAACW,KAAK,IAAO,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC6J,kBAAkB,CAC5B,CAAC,IAAI,CAAChL,KAAK,CAACL,KAAK,EACjB,6BAA6B,EAC7BjC,IACF,CAAC;UACH;UAEA,IAAI,IAAI,CAACyD,KAAK,IAAU,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC6J,kBAAkB,CAC5B,CAAC,IAAI,CAAChL,KAAK,CAACL,KAAK,EACjB,6BAA6B,EAC7BjC,IACF,CAAC;UACH;UAEA,MAAM,IAAI,CAACkD,KAAK,CAACpH,UAAU,CAACsD,4BAA4B,EAAE;YACxD+D,EAAE,EAAE,IAAI,CAACb,KAAK,CAACO;UACjB,CAAC,CAAC;QACJ;QACA,IAAI,CAACkD,UAAU,CAAC,CAAC;QACjB;MACF;QACE,OAAO,IAAI,CAACsH,YAAY,CACtB,IAAI,CAAC/K,KAAK,CAACL,KAAK,EAChB,6BACF,CAAC;MAEH;QACE,OAAO,IAAI,CAACoL,YAAY,CACtB,IAAI,CAAC/K,KAAK,CAACL,KAAK,EAChB,6BACF,CAAC;MAEH;QACE,IAAI,CAACa,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAACQ,UAAU,CAACtD,IAAI,EAAE,oBAAoB,CAAC;MAEpD;QACE,IAAI,CAAC8C,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAACQ,UAAU,CAACtD,IAAI,EAAE,2BAA2B,CAAC;MAE3D;QACE,IAAI,CAAC8C,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAACQ,UAAU,CAACtD,IAAI,EAAE,oBAAoB,CAAC;MAEpD;QACE,IAAI,CAAC8C,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAACQ,UAAU,CAACtD,IAAI,EAAE,sBAAsB,CAAC;MAEtD;QACE,OAAO,IAAI,CAACwM,mBAAmB,CAAC,CAAC;MAEnC;QACE,IAAI,IAAAe,qBAAc,EAAC,IAAI,CAACjL,KAAK,CAACzC,IAAI,CAAC,EAAE;UACnC,MAAMiH,KAAK,GAAG,IAAA0G,qBAAc,EAAC,IAAI,CAAClL,KAAK,CAACzC,IAAI,CAAC;UAC7C,IAAI,CAACiD,IAAI,CAAC,CAAC;UACX,OAAO,KAAK,CAAC2K,gBAAgB,CAACzN,IAAI,EAA0B8G,KAAK,CAAC;QACpE,CAAC,MAAM,IAAI,IAAAqG,wBAAiB,EAAC,IAAI,CAAC7K,KAAK,CAACzC,IAAI,CAAC,EAAE;UAC7C,IAAI,IAAI,CAAC6F,YAAY,IAAc,CAAC,EAAE;YACpC,OAAO,IAAI,CAACkE,sBAAsB,CAAC,CAAC;UACtC;UAEA,OAAO,IAAI,CAACqD,yBAAyB,CACnCpK,QAAQ,EACR7C,IAAI,EACJ,IAAI,CAAC8D,eAAe,CAAC,CACvB,CAAC;QACH;IACJ;IAEA,IAAI,CAACiC,UAAU,CAAC,CAAC;EACnB;EAEA2H,oBAAoBA,CAAA,EAAyB;IAC3C,MAAM7K,QAAQ,GAAG,IAAI,CAACP,KAAK,CAACO,QAAQ;IACpC,IAAIhD,IAAI,GAAG,IAAI,CAAC4M,oBAAoB,CAAC,CAAC;IACtC,IAAIkB,yBAAyB,GAAG,KAAK;IACrC,OACE,CAAC,IAAI,CAAClK,KAAK,EAAY,CAAC,IAAI,IAAI,CAACA,KAAK,GAAe,CAAC,KACtD,CAAC,IAAI,CAACmK,kBAAkB,CAAC,CAAC,EAC1B;MACA,MAAM5N,IAAI,GAAG,IAAI,CAACuK,WAAW,CAAC1H,QAAQ,CAAC;MACvC,MAAMwH,QAAQ,GAAG,IAAI,CAACjH,GAAG,GAAe,CAAC;MACzCuK,yBAAyB,GAAGA,yBAAyB,IAAItD,QAAQ;MACjE,IAAI,CAAC7H,MAAM,EAAY,CAAC;MACxB,IAAI,CAAC6H,QAAQ,IAAI,IAAI,CAAC5G,KAAK,EAAY,CAAC,EAAE;QACxCzD,IAAI,CAAC6N,WAAW,GAAGhO,IAAI;QACvB,IAAI,CAACiD,IAAI,CAAC,CAAC;QACXjD,IAAI,GAAG,IAAI,CAACyD,UAAU,CAACtD,IAAI,EAAE,qBAAqB,CAAC;MACrD,CAAC,MAAM;QACLA,IAAI,CAAC8N,UAAU,GAAGjO,IAAI;QACtBG,IAAI,CAAC+N,SAAS,GAAG,IAAI,CAACtL,aAAa,CAAC,CAAC;QACrC,IAAI,CAACD,MAAM,EAAY,CAAC;QACxB,IAAImL,yBAAyB,EAAE;UAC7B3N,IAAI,CAACqK,QAAQ,GAAGA,QAAQ;UACxBxK,IAAI,GAAG,IAAI,CAACyD,UAAU,CAEpBtD,IAAI,EACJ,2BACF,CAAC;QACH,CAAC,MAAM;UACLH,IAAI,GAAG,IAAI,CAACyD,UAAU,CAEpBtD,IAAI,EACJ,mBACF,CAAC;QACH;MACF;IACF;IACA,OAAOH,IAAI;EACb;EAEAmO,mBAAmBA,CAAA,EAAyB;IAC1C,MAAMhO,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACS,GAAG,GAAY,CAAC,EAAE;MACzBpD,IAAI,CAAC0E,cAAc,GAAG,IAAI,CAACsJ,mBAAmB,CAAC,CAAC;MAChD,OAAO,IAAI,CAAC1K,UAAU,CAACtD,IAAI,EAAE,wBAAwB,CAAC;IACxD,CAAC,MAAM;MACL,OAAO,IAAI,CAAC0N,oBAAoB,CAAC,CAAC;IACpC;EACF;EAEAO,kCAAkCA,CAAA,EAAyB;IACzD,MAAMC,KAAK,GAAG,IAAI,CAACF,mBAAmB,CAAC,CAAC;IACxC,IAAI,CAAC,IAAI,CAAC1L,KAAK,CAACmH,kBAAkB,IAAI,IAAI,CAACrG,GAAG,GAAS,CAAC,EAAE;MAExD,MAAMpD,IAAI,GAAG,IAAI,CAACuK,WAAW,CAAC2D,KAAK,CAACjJ,GAAG,CAACC,KAAK,CAAC;MAC9ClF,IAAI,CAACqE,MAAM,GAAG,CAAC,IAAI,CAAC2I,kCAAkC,CAACkB,KAAK,CAAC,CAAC;MAC9DlO,IAAI,CAACsE,IAAI,GAAG,IAAI;MAChBtE,IAAI,CAACuE,IAAI,GAAG,IAAI;MAChBvE,IAAI,CAACyE,UAAU,GAAG,IAAI,CAAChC,aAAa,CAAC,CAAC;MACtCzC,IAAI,CAACiE,cAAc,GAAG,IAAI;MAC1B,OAAO,IAAI,CAACX,UAAU,CAACtD,IAAI,EAAE,wBAAwB,CAAC;IACxD;IACA,OAAOkO,KAAK;EACd;EAEAC,yBAAyBA,CAAA,EAAyB;IAChD,MAAMnO,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACS,GAAG,GAAc,CAAC;IACvB,MAAMvD,IAAI,GAAG,IAAI,CAACoO,kCAAkC,CAAC,CAAC;IACtDjO,IAAI,CAAC2M,KAAK,GAAG,CAAC9M,IAAI,CAAC;IACnB,OAAO,IAAI,CAACuD,GAAG,GAAc,CAAC,EAAE;MAC9BpD,IAAI,CAAC2M,KAAK,CAAC9L,IAAI,CAAC,IAAI,CAACoN,kCAAkC,CAAC,CAAC,CAAC;IAC5D;IACA,OAAOjO,IAAI,CAAC2M,KAAK,CAAC/L,MAAM,KAAK,CAAC,GAC1Bf,IAAI,GACJ,IAAI,CAACyD,UAAU,CAACtD,IAAI,EAAE,4BAA4B,CAAC;EACzD;EAEAoO,kBAAkBA,CAAA,EAAyB;IACzC,MAAMpO,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAC7B,IAAI,CAACS,GAAG,GAAa,CAAC;IACtB,MAAMvD,IAAI,GAAG,IAAI,CAACsO,yBAAyB,CAAC,CAAC;IAC7CnO,IAAI,CAAC2M,KAAK,GAAG,CAAC9M,IAAI,CAAC;IACnB,OAAO,IAAI,CAACuD,GAAG,GAAa,CAAC,EAAE;MAC7BpD,IAAI,CAAC2M,KAAK,CAAC9L,IAAI,CAAC,IAAI,CAACsN,yBAAyB,CAAC,CAAC,CAAC;IACnD;IACA,OAAOnO,IAAI,CAAC2M,KAAK,CAAC/L,MAAM,KAAK,CAAC,GAC1Bf,IAAI,GACJ,IAAI,CAACyD,UAAU,CAACtD,IAAI,EAAE,qBAAqB,CAAC;EAClD;EAEAyC,aAAaA,CAAA,EAAyB;IACpC,MAAMJ,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,MAAM;IACnC,IAAI,CAACD,KAAK,CAACC,MAAM,GAAG,IAAI;IACxB,MAAM1C,IAAI,GAAG,IAAI,CAACuO,kBAAkB,CAAC,CAAC;IACtC,IAAI,CAAC9L,KAAK,CAACC,MAAM,GAAGF,SAAS;IAC7B,OAAOxC,IAAI;EACb;EAEA8J,oCAAoCA,CAAA,EAAyB;IAC3D,IAAI,IAAI,CAACrH,KAAK,CAACzC,IAAI,QAAY,IAAI,IAAI,CAACyC,KAAK,CAACL,KAAK,KAAK,GAAG,EAAE;MAC3D,MAAMY,QAAQ,GAAG,IAAI,CAACP,KAAK,CAACO,QAAQ;MACpC,MAAM7C,IAAI,GAAG,IAAI,CAAC8D,eAAe,CAAC,CAAC;MACnC,OAAO,IAAI,CAACyI,oBAAoB,CAAC1J,QAAQ,EAAE7C,IAAI,CAAC;IAClD,CAAC,MAAM;MACL,OAAO,IAAI,CAACyC,aAAa,CAAC,CAAC;IAC7B;EACF;EAEAwE,uBAAuBA,CAAA,EAAyB;IAC9C,MAAMjH,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAuB,CAAC;IACnD3C,IAAI,CAAC0E,cAAc,GAAG,IAAI,CAACvC,wBAAwB,CAAC,CAAC;IACrD,OAAO,IAAI,CAACmB,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAEAgG,kCAAkCA,CAChCqI,sBAAgC,EAClB;IACd,MAAMjF,KAAK,GAAGiF,sBAAsB,GAChC,IAAI,CAACvK,eAAe,CAAC,CAAC,GACtB,IAAI,CAACwD,6BAA6B,CAAC,CAAC;IACxC,IAAI,IAAI,CAAC7D,KAAK,GAAS,CAAC,EAAE;MAExB2F,KAAK,CAAC1E,cAAc,GAAG,IAAI,CAACuC,uBAAuB,CAAC,CAAC;MACrD,IAAI,CAACtC,gBAAgB,CAACyE,KAAK,CAAC;IAC9B;IACA,OAAOA,KAAK;EACd;EAEAkF,mBAAmBA,CAACtO,IAAY,EAAU;IACxCA,IAAI,CAACuO,UAAU,CAAC7J,cAAc,GAAG1E,IAAI,CAAC0E,cAAc;IAEpD,IAAI,CAACC,gBAAgB,CAAC3E,IAAI,CAACuO,UAAU,EAAEvO,IAAI,CAAC0E,cAAc,CAACO,GAAG,CAACuJ,GAAG,CAAC;IAEnE,OAAOxO,IAAI,CAACuO,UAAU;EACxB;EAEApF,iBAAiBA,CAAA,EAAsC;IACrD,IAAID,QAAQ,GAAG,IAAI;IACnB,IAAI,IAAI,CAACzF,KAAK,GAAW,CAAC,EAAE;MAC1ByF,QAAQ,GAAG,IAAI,CAACvG,SAAS,CAAiB,CAAC;MAC3C,IAAI,IAAI,CAACL,KAAK,CAACL,KAAK,KAAK,GAAG,EAAE;QAC5BiH,QAAQ,CAACzC,IAAI,GAAG,MAAM;MACxB,CAAC,MAAM;QACLyC,QAAQ,CAACzC,IAAI,GAAG,OAAO;MACzB;MACA,IAAI,CAAC3D,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACQ,UAAU,CAAC4F,QAAQ,EAAE,UAAU,CAAC;IAC9C;IACA,OAAOA,QAAQ;EACjB;EAMAuF,iBAAiBA,CACfzO,IAAgB,EAChB0O,mBAAoC,EACpCC,QAAiB,GAAG,KAAK,EACnB;IACN,IAAID,mBAAmB,EAAE;MACvB,IAAI,CAACE,gCAAgC,CAAC5O,IAAI,EAAE,MAC1C,KAAK,CAACyO,iBAAiB,CAACzO,IAAI,EAAE,IAAI,EAAE2O,QAAQ,CAC9C,CAAC;MACD;IACF;IAEA,KAAK,CAACF,iBAAiB,CAACzO,IAAI,EAAE,KAAK,EAAE2O,QAAQ,CAAC;EAChD;EAEAE,0BAA0BA,CAMxB7O,IAAe,EAAEH,IAAe,EAAE8O,QAAiB,GAAG,KAAK,EAAK;IAChE,IAAI,IAAI,CAAClL,KAAK,GAAS,CAAC,EAAE;MACxB,MAAMM,QAAQ,GAAG,IAAI,CAACpB,SAAS,CAAmB,CAAC;MAEnD,CACEoB,QAAQ,CAACW,cAAc,EAEvB1E,IAAI,CAACwD,SAAS,CACf,GAAG,IAAI,CAACD,oCAAoC,CAAC,CAAC;MAE/CvD,IAAI,CAACyE,UAAU,GAAGV,QAAQ,CAACW,cAAc,GACrC,IAAI,CAACpB,UAAU,CAACS,QAAQ,EAAE,gBAAgB,CAAC,GAC3C,IAAI;IACV;IAEA,OAAO,KAAK,CAAC8K,0BAA0B,CAAC7O,IAAI,EAAEH,IAAI,EAAE8O,QAAQ,CAAC;EAC/D;EAGAG,kBAAkBA,CAACC,KAAyB,EAAe;IAEzD,IAAI,IAAI,CAACzM,KAAK,CAAC0M,MAAM,IAAI,IAAI,CAACtJ,YAAY,IAAc,CAAC,EAAE;MACzD,MAAMuE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;MAClC,IAAI,IAAAgF,iCAA0B,EAAChF,SAAS,CAACpK,IAAI,CAAC,EAAE;QAC9C,MAAMG,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAkB,CAAC;QAC9C,IAAI,CAACG,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAACuF,kBAAkB,CAACrI,IAAI,CAAC;MACtC;IACF,CAAC,MAAM,IAAI,IAAI,CAAC0B,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACgE,YAAY,IAAS,CAAC,EAAE;MACjE,MAAM1F,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;MAC7B,IAAI,CAACG,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACoM,wBAAwB,CAAClP,IAAI,CAAC;IAC5C;IACA,MAAMmP,IAAI,GAAG,KAAK,CAACL,kBAAkB,CAACC,KAAK,CAAC;IAE5C,IAAI,IAAI,CAAC3N,UAAU,KAAKC,SAAS,IAAI,CAAC,IAAI,CAAC+N,gBAAgB,CAACD,IAAI,CAAC,EAAE;MACjE,IAAI,CAAC/N,UAAU,GAAG,IAAI;IACxB;IACA,OAAO+N,IAAI;EACb;EAGAE,wBAAwBA,CACtBrP,IAA2B,EAC3BsP,IAAkB,EAClBC,UAAgC,EACT;IACvB,IAAID,IAAI,CAACzP,IAAI,KAAK,YAAY,EAAE;MAC9B,IAAIyP,IAAI,CAACvK,IAAI,KAAK,SAAS,EAAE;QAC3B,IACE,IAAI,CAACtB,KAAK,GAAU,CAAC,IACrB,IAAA0J,wBAAiB,EAAC,IAAI,CAAC7K,KAAK,CAACzC,IAAI,CAAC,IAClC,IAAI,CAAC4D,KAAK,GAAa,CAAC,IACxB,IAAI,CAACA,KAAK,GAAQ,CAAC,IACnB,IAAI,CAACA,KAAK,GAAW,CAAC,EACtB;UAEA,OAAO,IAAI,CAAC0B,gBAAgB,CAACnF,IAAI,CAAC;QACpC;MACF,CAAC,MAAM,IAAI,IAAAmN,wBAAiB,EAAC,IAAI,CAAC7K,KAAK,CAACzC,IAAI,CAAC,EAAE;QAC7C,IAAIyP,IAAI,CAACvK,IAAI,KAAK,WAAW,EAAE;UAE7B,OAAO,IAAI,CAACsD,kBAAkB,CAACrI,IAAI,CAAC;QACtC,CAAC,MAAM,IAAIsP,IAAI,CAACvK,IAAI,KAAK,MAAM,EAAE;UAE/B,OAAO,IAAI,CAACoC,kBAAkB,CAACnH,IAAI,CAAC;QACtC,CAAC,MAAM,IAAIsP,IAAI,CAACvK,IAAI,KAAK,QAAQ,EAAE;UAEjC,OAAO,IAAI,CAACqC,mBAAmB,CAACpH,IAAI,EAAE,KAAK,CAAC;QAC9C;MACF;IACF;IAEA,OAAO,KAAK,CAACqP,wBAAwB,CAACrP,IAAI,EAAEsP,IAAI,EAAEC,UAAU,CAAC;EAC/D;EAGAC,4BAA4BA,CAAA,EAAY;IACtC,MAAM;MAAE3P;IAAK,CAAC,GAAG,IAAI,CAACyC,KAAK;IAC3B,IACE,IAAAmN,yCAAkC,EAAC5P,IAAI,CAAC,IACvC,IAAI,CAAC6B,gBAAgB,CAAC,CAAC,IAAI7B,IAAI,QAAc,EAC9C;MACA,OAAO,CAAC,IAAI,CAACyC,KAAK,CAACoN,WAAW;IAChC;IACA,OAAO,KAAK,CAACF,4BAA4B,CAAC,CAAC;EAC7C;EAEAG,wBAAwBA,CAAA,EAAY;IAClC,MAAM;MAAE9P;IAAK,CAAC,GAAG,IAAI,CAACyC,KAAK;IAC3B,IACE,IAAAmN,yCAAkC,EAAC5P,IAAI,CAAC,IACvC,IAAI,CAAC6B,gBAAgB,CAAC,CAAC,IAAI7B,IAAI,QAAc,EAC9C;MACA,OAAO,IAAI,CAACyC,KAAK,CAACoN,WAAW;IAC/B;IAEA,OAAO,KAAK,CAACC,wBAAwB,CAAC,CAAC;EACzC;EAEAC,4BAA4BA,CAAA,EAAiC;IAC3D,IAAI,IAAI,CAAClO,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACgE,YAAY,IAAS,CAAC,EAAE;MAC1D,MAAM1F,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;MAC7B,IAAI,CAACG,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACoM,wBAAwB,CAAClP,IAAI,CAAC;IAC5C;IACA,OAAO,KAAK,CAAC4P,4BAA4B,CAAC,CAAC;EAC7C;EAEAC,gBAAgBA,CACdP,IAAkB,EAElBzM,QAAkB,EAClBiN,mBAA6C,EAC/B;IACd,IAAI,CAAC,IAAI,CAACrM,KAAK,GAAY,CAAC,EAAE,OAAO6L,IAAI;IAEzC,IAAI,IAAI,CAAChN,KAAK,CAACyN,sBAAsB,EAAE;MACrC,MAAMC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAIvC,IACED,MAAM,OAAoB,IAC1BA,MAAM,OAAuB,IAC7BA,MAAM,OAAoB,IAC1BA,MAAM,OAA+B,EACrC;QAEA,IAAI,CAACE,0BAA0B,CAACJ,mBAAmB,CAAC;QACpD,OAAOR,IAAI;MACb;IACF;IAEA,IAAI,CAAC9M,MAAM,GAAY,CAAC;IACxB,MAAMF,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC6N,KAAK,CAAC,CAAC;IAChC,MAAMC,iBAAiB,GAAG,IAAI,CAAC9N,KAAK,CAAC+N,SAAS;IAC9C,MAAMrQ,IAAI,GAAG,IAAI,CAACuK,WAAW,CAAC1H,QAAQ,CAAC;IACvC,IAAI;MAAEyN,UAAU;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACC,6BAA6B,CAAC,CAAC;IACjE,IAAI,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAACL,UAAU,CAAC;IAE/D,IAAIC,MAAM,IAAIG,OAAO,CAAC9P,MAAM,GAAG,CAAC,EAAE;MAChC,MAAMyP,SAAS,GAAG,CAAC,GAAGD,iBAAiB,CAAC;MAExC,IAAIM,OAAO,CAAC9P,MAAM,GAAG,CAAC,EAAE;QACtB,IAAI,CAAC0B,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACA,KAAK,CAAC+N,SAAS,GAAGA,SAAS;QAEhC,KAAK,IAAI1P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+P,OAAO,CAAC9P,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC0P,SAAS,CAACxP,IAAI,CAAC6P,OAAO,CAAC/P,CAAC,CAAC,CAACuE,KAAK,CAAC;QAClC;QAEA,CAAC;UAAEoL,UAAU;UAAEC;QAAO,CAAC,GAAG,IAAI,CAACC,6BAA6B,CAAC,CAAC;QAC9D,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAACL,UAAU,CAAC;MAC7D;MAEA,IAAIC,MAAM,IAAIE,KAAK,CAAC7P,MAAM,GAAG,CAAC,EAAE;QAM9B,IAAI,CAACsC,KAAK,CAACpH,UAAU,CAACE,yBAAyB,EAAE;UAC/CmH,EAAE,EAAEb,KAAK,CAACO;QACZ,CAAC,CAAC;MACJ;MAEA,IAAI0N,MAAM,IAAIE,KAAK,CAAC7P,MAAM,KAAK,CAAC,EAAE;QAChC,IAAI,CAAC0B,KAAK,GAAGA,KAAK;QAClB+N,SAAS,CAACxP,IAAI,CAAC4P,KAAK,CAAC,CAAC,CAAC,CAACvL,KAAK,CAAC;QAC9B,IAAI,CAAC5C,KAAK,CAAC+N,SAAS,GAAGA,SAAS;QAChC,CAAC;UAAEC,UAAU;UAAEC;QAAO,CAAC,GAAG,IAAI,CAACC,6BAA6B,CAAC,CAAC;MAChE;IACF;IAEA,IAAI,CAACG,uBAAuB,CAACL,UAAU,EAAE,IAAI,CAAC;IAE9C,IAAI,CAAChO,KAAK,CAAC+N,SAAS,GAAGD,iBAAiB;IACxC,IAAI,CAAC5N,MAAM,GAAS,CAAC;IAErBxC,IAAI,CAACQ,IAAI,GAAG8O,IAAI;IAChBtP,IAAI,CAACsQ,UAAU,GAAGA,UAAU;IAC5BtQ,IAAI,CAAC4Q,SAAS,GAAG,IAAI,CAAChC,gCAAgC,CAAC5O,IAAI,EAAE,MAC3D,IAAI,CAAC6Q,gBAAgB,CAACxP,SAAS,EAAEA,SAAS,CAC5C,CAAC;IAED,OAAO,IAAI,CAACiC,UAAU,CAACtD,IAAI,EAAE,uBAAuB,CAAC;EACvD;EAEAwQ,6BAA6BA,CAAA,EAG3B;IACA,IAAI,CAAClO,KAAK,CAACwO,yBAAyB,CAACjQ,IAAI,CAAC,IAAI,CAACyB,KAAK,CAAC4C,KAAK,CAAC;IAE3D,MAAMoL,UAAU,GAAG,IAAI,CAACS,uBAAuB,CAAC,CAAC;IACjD,MAAMR,MAAM,GAAG,CAAC,IAAI,CAAC9M,KAAK,GAAS,CAAC;IAEpC,IAAI,CAACnB,KAAK,CAACwO,yBAAyB,CAACE,GAAG,CAAC,CAAC;IAE1C,OAAO;MAAEV,UAAU;MAAEC;IAAO,CAAC;EAC/B;EASAI,uBAAuBA,CACrB3Q,IAAkB,EAClBiR,eAAyB,EACmC;IAC5D,MAAMC,KAAK,GAAG,CAAClR,IAAI,CAAC;IACpB,MAAMmR,MAAmC,GAAG,EAAE;IAE9C,OAAOD,KAAK,CAACtQ,MAAM,KAAK,CAAC,EAAE;MACzB,MAAMZ,IAAI,GAAGkR,KAAK,CAACF,GAAG,CAAC,CAAC;MACxB,IAAIhR,IAAI,CAACH,IAAI,KAAK,yBAAyB,EAAE;QAC3C,IAAIG,IAAI,CAACiE,cAAc,IAAI,CAACjE,IAAI,CAACyE,UAAU,EAAE;UAG3C,IAAI,CAAC2M,qBAAqB,CAACpR,IAAI,CAAC;QAClC,CAAC,MAAM;UAELmR,MAAM,CAACtQ,IAAI,CAACb,IAAI,CAAC;QACnB;QACAkR,KAAK,CAACrQ,IAAI,CAACb,IAAI,CAACsG,IAAI,CAAC;MACvB,CAAC,MAAM,IAAItG,IAAI,CAACH,IAAI,KAAK,uBAAuB,EAAE;QAChDqR,KAAK,CAACrQ,IAAI,CAACb,IAAI,CAACsQ,UAAU,CAAC;QAC3BY,KAAK,CAACrQ,IAAI,CAACb,IAAI,CAAC4Q,SAAS,CAAC;MAC5B;IACF;IAEA,IAAIK,eAAe,EAAE;MACnBE,MAAM,CAACxK,OAAO,CAAC3G,IAAI,IAAI,IAAI,CAACoR,qBAAqB,CAACpR,IAAI,CAAC,CAAC;MACxD,OAAO,CAACmR,MAAM,EAAE,EAAE,CAAC;IACrB;IAEA,OAAO7Q,SAAS,CAAC6Q,MAAM,EAAEnR,IAAI,IAC3BA,IAAI,CAACqE,MAAM,CAACgN,KAAK,CAACnD,KAAK,IAAI,IAAI,CAACoD,YAAY,CAACpD,KAAK,EAAE,IAAI,CAAC,CAC3D,CAAC;EACH;EAEAkD,qBAAqBA,CAACpR,IAA+B,EAAE;IAAA,IAAAuR,WAAA;IACrD,IAAI,CAACC,gBAAgB,CAGnBxR,IAAI,CAACqE,MAAM,GAAAkN,WAAA,GACXvR,IAAI,CAACyR,KAAK,qBAAVF,WAAA,CAAYG,gBAAgB,EAChB,KACd,CAAC;IAED,IAAI,CAAC7M,KAAK,CAACqB,KAAK,CAACyL,0BAAc,GAAGC,uBAAW,CAAC;IAE9C,KAAK,CAACC,WAAW,CAAC7R,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IACpC,IAAI,CAAC6E,KAAK,CAAC2B,IAAI,CAAC,CAAC;EACnB;EAEAoI,gCAAgCA,CAC9B5O,IAAoB,EACpB8R,KAAc,EACX;IACH,IAAIC,MAAS;IACb,IAAI,IAAI,CAACzP,KAAK,CAACwO,yBAAyB,CAACkB,OAAO,CAAChS,IAAI,CAACkF,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;MACnE,IAAI,CAAC5C,KAAK,CAACwO,yBAAyB,CAACjQ,IAAI,CAAC,IAAI,CAACyB,KAAK,CAAC4C,KAAK,CAAC;MAC3D6M,MAAM,GAAGD,KAAK,CAAC,CAAC;MAChB,IAAI,CAACxP,KAAK,CAACwO,yBAAyB,CAACE,GAAG,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLe,MAAM,GAAGD,KAAK,CAAC,CAAC;IAClB;IAEA,OAAOC,MAAM;EACf;EAEAE,cAAcA,CACZjS,IAAkB,EAElB6C,QAAkB,EACJ;IACd7C,IAAI,GAAG,KAAK,CAACiS,cAAc,CAACjS,IAAI,EAAE6C,QAAQ,CAAC;IAC3C,IAAI,IAAI,CAACO,GAAG,GAAY,CAAC,EAAE;MACzBpD,IAAI,CAACqK,QAAQ,GAAG,IAAI;MAIpB,IAAI,CAAC1F,gBAAgB,CAAC3E,IAAI,CAAC;IAC7B;IAEA,IAAI,IAAI,CAACyD,KAAK,GAAS,CAAC,EAAE;MACxB,MAAMyO,YAAY,GAAG,IAAI,CAAC3H,WAAW,CAAC1H,QAAQ,CAAC;MAC/CqP,YAAY,CAAC3D,UAAU,GAAGvO,IAAI;MAC9BkS,YAAY,CAACxN,cAAc,GAAG,IAAI,CAACuC,uBAAuB,CAAC,CAAC;MAE5D,OAAO,IAAI,CAAC3D,UAAU,CAAC4O,YAAY,EAAE,oBAAoB,CAAC;IAC5D;IAEA,OAAOlS,IAAI;EACb;EAEAmS,uBAAuBA,CAACnS,IAAY,EAAE;IACpC,IACGA,IAAI,CAACH,IAAI,KAAK,mBAAmB,KAC/BG,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ,CAAC,IAC7DD,IAAI,CAACH,IAAI,KAAK,wBAAwB,IACrCG,IAAI,CAACgH,UAAU,KAAK,MAAO,IAC5BhH,IAAI,CAACH,IAAI,KAAK,sBAAsB,IAAIG,IAAI,CAACgH,UAAU,KAAK,MAAO,EACpE;MAGA;IACF;IAEA,KAAK,CAACmL,uBAAuB,CAACnS,IAAI,CAAC;EACrC;EAEAoS,sBAAsBA,CACpBpS,IAA8B,EACI;IAClC,IAAI,IAAI,CAAC0F,YAAY,IAAS,CAAC,EAAE;MAC/B1F,IAAI,CAACgH,UAAU,GAAG,MAAM;MAExB,MAAMqL,eAAe,GAAG,IAAI,CAAC1P,SAAS,CAAC,CAAC;MACxC,IAAI,CAACG,IAAI,CAAC,CAAC;MAEX,IAAI,IAAI,CAACW,KAAK,EAAU,CAAC,EAAE;QAEzBzD,IAAI,CAACsS,UAAU,GAAG,IAAI,CAACC,qBAAqB,CACrB,IACvB,CAAC;QACD,KAAK,CAACC,eAAe,CAACxS,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb,CAAC,MAAM;QAGL,OAAO,IAAI,CAACmH,kBAAkB,CAACkL,eAAe,CAAC;MACjD;IACF,CAAC,MAAM,IAAI,IAAI,CAAC3M,YAAY,IAAW,CAAC,EAAE;MACxC1F,IAAI,CAACgH,UAAU,GAAG,MAAM;MAExB,MAAMqL,eAAe,GAAG,IAAI,CAAC1P,SAAS,CAAC,CAAC;MACxC,IAAI,CAACG,IAAI,CAAC,CAAC;MAGX,OAAO,IAAI,CAACsE,mBAAmB,CAACiL,eAAe,EAAE,KAAK,CAAC;IACzD,CAAC,MAAM,IAAI,IAAI,CAAC3M,YAAY,IAAc,CAAC,EAAE;MAC3C1F,IAAI,CAACgH,UAAU,GAAG,MAAM;MACxB,MAAMqL,eAAe,GAAG,IAAI,CAAC1P,SAAS,CAAC,CAAC;MACxC,IAAI,CAACG,IAAI,CAAC,CAAC;MAEX,OAAO,IAAI,CAACuF,kBAAkB,CAACgK,eAAe,CAAC;IACjD,CAAC,MAAM,IAAI,IAAI,CAAC3Q,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACgE,YAAY,IAAS,CAAC,EAAE;MACjE1F,IAAI,CAACgH,UAAU,GAAG,OAAO;MACzB,MAAMqL,eAAe,GAAG,IAAI,CAAC1P,SAAS,CAAC,CAAC;MACxC,IAAI,CAACG,IAAI,CAAC,CAAC;MAEX,OAAO,IAAI,CAACoM,wBAAwB,CAACmD,eAAe,CAAC;IACvD,CAAC,MAAM;MACL,OAAO,KAAK,CAACD,sBAAsB,CAACpS,IAAI,CAAC;IAC3C;EACF;EAEAyS,aAAaA,CAACzS,IAAY,EAAW;IACnC,IAAI,KAAK,CAACyS,aAAa,CAACzS,IAAI,CAAC,EAAE,OAAO,IAAI;IAE1C,IAAI,IAAI,CAAC0F,YAAY,IAAS,CAAC,IAAI,IAAI,CAACuE,SAAS,CAAC,CAAC,CAACpK,IAAI,OAAY,EAAE;MACpEG,IAAI,CAACgH,UAAU,GAAG,MAAM;MACxB,IAAI,CAAClE,IAAI,CAAC,CAAC;MACX,IAAI,CAACA,IAAI,CAAC,CAAC;MACX,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA4P,kCAAkCA,CAAC1S,IAAY,EAAW;IACxD,MAAM;MAAE6C;IAAS,CAAC,GAAG,IAAI,CAACP,KAAK;IAC/B,MAAMqQ,YAAY,GAAG,KAAK,CAACD,kCAAkC,CAAC1S,IAAI,CAAC;IACnE,IAAI2S,YAAY,IAAI3S,IAAI,CAACgH,UAAU,KAAK,MAAM,EAAE;MAC9C,IAAI,CAACjB,UAAU,CAAClD,QAAQ,CAAC;IAC3B;IACA,OAAO8P,YAAY;EACrB;EAEAC,YAAYA,CACV5S,IAAa,EACb6S,WAAoB,EACpBC,UAA2B,EAC3B;IACA,KAAK,CAACF,YAAY,CAAC5S,IAAI,EAAE6S,WAAW,EAAEC,UAAU,CAAC;IACjD,IAAI,IAAI,CAACrP,KAAK,GAAM,CAAC,EAAE;MACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAChE;EACF;EAEA6O,gBAAgBA,CACdC,SAAsB,EACtBC,MAAW,EACX3Q,KAA8B,EACxB;IACN,MAAM;MAAEO;IAAS,CAAC,GAAG,IAAI,CAACP,KAAK;IAC/B,IAAI,IAAI,CAACoD,YAAY,IAAY,CAAC,EAAE;MAClC,IAAI,KAAK,CAACwN,4BAA4B,CAACF,SAAS,EAAEC,MAAM,CAAC,EAAE;QAEzD;MACF;MAEAA,MAAM,CAACrK,OAAO,GAAG,IAAI;IACvB;IAEA,KAAK,CAACmK,gBAAgB,CAACC,SAAS,EAAEC,MAAM,EAAE3Q,KAAK,CAAC;IAEhD,IAAI2Q,MAAM,CAACrK,OAAO,EAAE;MAClB,IACEqK,MAAM,CAACpT,IAAI,KAAK,eAAe,IAC/BoT,MAAM,CAACpT,IAAI,KAAK,sBAAsB,IACtCoT,MAAM,CAACpT,IAAI,KAAK,oBAAoB,EACpC;QACA,IAAI,CAACqD,KAAK,CAACpH,UAAU,CAACM,mBAAmB,EAAE;UAAE+G,EAAE,EAAEN;QAAS,CAAC,CAAC;MAC9D,CAAC,MAAM,IAAIoQ,MAAM,CAAChR,KAAK,EAAE;QACvB,IAAI,CAACiB,KAAK,CAACpH,UAAU,CAACO,4BAA4B,EAAE;UAClD8G,EAAE,EAAE8P,MAAM,CAAChR;QACb,CAAC,CAAC;MACJ;IACF;EACF;EAEAkR,UAAUA,CAAC5K,IAAY,EAAW;IAChC,OAAOA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,eAAe;EACxD;EAEA6K,YAAYA,CAAA,EAAS;IACnB,MAAM7K,IAAI,GAAG,KAAK,CAAC8K,SAAS,CAAC,CAAC;IAC9B,MAAMC,QAAQ,GAAG,IAAI,GAAG/K,IAAI;IAG5B,IAAI,CAAC,IAAI,CAAC4K,UAAU,CAAC5K,IAAI,CAAC,IAAI,CAAC,IAAI,CAACjG,KAAK,CAACC,MAAM,EAAE;MAChD,IAAI,CAACW,KAAK,CAAC8I,kBAAM,CAACuH,iBAAiB,EAAE;QACnCpQ,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkR,WAAW,CAAC,CAAC;QAC5BC,cAAc,EAAEH;MAClB,CAAC,CAAC;IACJ;IAEA,IAAI,CAAC3R,WAAW,MAAU2R,QAAQ,CAAC;EACrC;EAGAI,gBAAgBA,CAACC,IAAY,EAAQ;IACnC,MAAM7Q,IAAI,GAAG,IAAI,CAAC8Q,KAAK,CAACC,UAAU,CAAC,IAAI,CAACvR,KAAK,CAACsK,GAAG,GAAG,CAAC,CAAC;IACtD,IAAI+G,IAAI,QAA6B,IAAI7Q,IAAI,QAA0B,EAAE;MACvE,IAAI,CAACgR,QAAQ,IAAe,CAAC,CAAC;IAChC,CAAC,MAAM,IACL,IAAI,CAACxR,KAAK,CAACC,MAAM,KAChBoR,IAAI,OAA0B,IAAIA,IAAI,OAAuB,CAAC,EAC/D;MACA,IAAI,CAACG,QAAQ,CAACH,IAAI,OAA0B,UAAgB,EAAE,CAAC,CAAC;IAClE,CAAC,MAAM,IAAI,IAAI,CAACrR,KAAK,CAACC,MAAM,IAAIoR,IAAI,OAA2B,EAAE;MAC/D,IAAI7Q,IAAI,OAAkB,EAAE;QAC1B,IAAI,CAACgR,QAAQ,KAAiB,CAAC,CAAC;MAClC,CAAC,MAAM;QAEL,IAAI,CAACA,QAAQ,KAAc,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM,IACL,IAAAC,2BAAe,EAACJ,IAAI,EAAE7Q,IAAI,EAAE,IAAI,CAAC8Q,KAAK,CAACC,UAAU,CAAC,IAAI,CAACvR,KAAK,CAACsK,GAAG,GAAG,CAAC,CAAC,CAAC,EACtE;MACA,IAAI,CAACtK,KAAK,CAACsK,GAAG,IAAI,CAAC;MACnB,IAAI,CAACwG,YAAY,CAAC,CAAC;IACrB,CAAC,MAAM;MACL,KAAK,CAACM,gBAAgB,CAACC,IAAI,CAAC;IAC9B;EACF;EAEArC,YAAYA,CAACtR,IAAY,EAAEgU,SAAmB,EAAW;IACvD,IAAIhU,IAAI,CAACH,IAAI,KAAK,oBAAoB,EAAE;MACtC,OAAO,IAAI,CAACyR,YAAY,CAACtR,IAAI,CAACuO,UAAU,EAAEyF,SAAS,CAAC;IACtD,CAAC,MAAM;MACL,OAAO,KAAK,CAAC1C,YAAY,CAACtR,IAAI,EAAEgU,SAAS,CAAC;IAC5C;EACF;EAEAC,YAAYA,CAACjU,IAAY,EAAEkU,KAAc,GAAG,KAAK,EAAQ;IACvD,IACE,CAACA,KAAK,IACNlU,IAAI,CAACH,IAAI,KAAK,sBAAsB,IACpCG,IAAI,CAACmU,IAAI,CAACtU,IAAI,KAAK,oBAAoB,EACvC;MACAG,IAAI,CAACmU,IAAI,GAAG,IAAI,CAAC7F,mBAAmB,CAACtO,IAAI,CAACmU,IAAI,CAAC;IACjD;IACA,KAAK,CAACF,YAAY,CAACjU,IAAI,EAAEkU,KAAK,CAAC;EACjC;EAGA1C,gBAAgBA,CACd4C,QAAwB,EACxB1C,gBAA6C,EAC7CwC,KAAc,EACR;IACN,KAAK,IAAIvT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyT,QAAQ,CAACxT,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,MAAM2O,IAAI,GAAG8E,QAAQ,CAACzT,CAAC,CAAC;MACxB,IAAI,CAAA2O,IAAI,oBAAJA,IAAI,CAAEzP,IAAI,MAAK,oBAAoB,EAAE;QACvCuU,QAAQ,CAACzT,CAAC,CAAC,GAAG,IAAI,CAAC2N,mBAAmB,CAACgB,IAAI,CAAC;MAC9C;IACF;IACA,KAAK,CAACkC,gBAAgB,CAAC4C,QAAQ,EAAE1C,gBAAgB,EAAEwC,KAAK,CAAC;EAC3D;EAIAG,gBAAgBA,CACdD,QAAwD,EACxDE,mBAA6B,EACmB;IAChD,KAAK,IAAI3T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyT,QAAQ,CAACxT,MAAM,EAAED,CAAC,EAAE,EAAE;MAAA,IAAA4T,WAAA;MACxC,MAAMjF,IAAI,GAAG8E,QAAQ,CAACzT,CAAC,CAAC;MACxB,IACE2O,IAAI,IACJA,IAAI,CAACzP,IAAI,KAAK,oBAAoB,IAClC,GAAA0U,WAAA,GAACjF,IAAI,CAACmC,KAAK,aAAV8C,WAAA,CAAYC,aAAa,MACzBJ,QAAQ,CAACxT,MAAM,GAAG,CAAC,IAAI,CAAC0T,mBAAmB,CAAC,EAC7C;QACA,IAAI,CAACpR,KAAK,CAACpH,UAAU,CAACgD,iBAAiB,EAAE;UACvCqE,EAAE,EAAEmM,IAAI,CAAC5K;QACX,CAAC,CAAC;MACJ;IACF;IAEA,OAAO0P,QAAQ;EACjB;EAEAK,cAAcA,CACZC,KAAgB,EAChBC,YAAqB,EACrBC,OAAgB,EAChB9E,mBAA6C,EACN;IACvC,MAAM9P,IAAI,GAAG,KAAK,CAACyU,cAAc,CAC/BC,KAAK,EACLC,YAAY,EACZC,OAAO,EACP9E,mBACF,CAAC;IAOD,IAAI6E,YAAY,IAAI,CAAC,IAAI,CAACrS,KAAK,CAACyN,sBAAsB,EAAE;MACtD,IAAI,CAACsE,gBAAgB,CAACrU,IAAI,CAAC6U,QAAQ,CAAC;IACtC;IAEA,OAAO7U,IAAI;EACb;EAEA8U,WAAWA,CAACjV,IAAY,EAAEkV,eAAwB,EAAEC,OAAqB,EAAE;IACzE,OACEnV,IAAI,KAAK,oBAAoB,IAC7B,KAAK,CAACiV,WAAW,CAACjV,IAAI,EAAEkV,eAAe,EAAEC,OAAO,CAAC;EAErD;EAGAC,kBAAkBA,CAACjV,IAAqB,EAAmB;IACzD,IAAI,IAAI,CAACyD,KAAK,GAAS,CAAC,EAAE;MAExBzD,IAAI,CAAC0E,cAAc,GAAG,IAAI,CAACuC,uBAAuB,CAAC,CAAC;IACtD;IACA,OAAO,KAAK,CAACgO,kBAAkB,CAACjV,IAAI,CAAC;EACvC;EAEAkV,yBAAyBA,CACvBlV,IAA4B,EACJ;IACxB,IAAI,IAAI,CAACyD,KAAK,GAAS,CAAC,EAAE;MAExBzD,IAAI,CAAC0E,cAAc,GAAG,IAAI,CAACuC,uBAAuB,CAAC,CAAC;IACtD;IACA,OAAO,KAAK,CAACiO,yBAAyB,CAAClV,IAAI,CAAC;EAC9C;EAGAmV,aAAaA,CAAA,EAAY;IACvB,OAAO,IAAI,CAAC1R,KAAK,GAAM,CAAC,IAAI,KAAK,CAAC0R,aAAa,CAAC,CAAC;EACnD;EAGAC,eAAeA,CAAA,EAAY;IACzB,OAAO,IAAI,CAAC3R,KAAK,GAAS,CAAC,IAAI,KAAK,CAAC2R,eAAe,CAAC,CAAC;EACxD;EAEAC,sBAAsBA,CAACjL,MAAuC,EAAW;IACvE,OAAO,CAAC,IAAI,CAAC3G,KAAK,GAAS,CAAC,IAAI,KAAK,CAAC4R,sBAAsB,CAACjL,MAAM,CAAC;EACtE;EAGAkL,eAAeA,CACbtC,SAAsB,EACtB5I,MAAqB,EACrBmL,WAAoB,EACpBC,OAAgB,EAChBC,aAAsB,EACtBC,iBAA0B,EACpB;IACN,IAAKtL,MAAM,CAASlB,QAAQ,EAAE;MAC5B,IAAI,CAACnD,UAAU,CAAEqE,MAAM,CAASlB,QAAQ,CAACjE,GAAG,CAACC,KAAK,CAAC;IACrD;IACA,OAAQkF,MAAM,CAASlB,QAAQ;IAC/B,IAAI,IAAI,CAACzF,KAAK,GAAM,CAAC,EAAE;MACrB2G,MAAM,CAACnG,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAClE;IAEA,KAAK,CAACoR,eAAe,CACnBtC,SAAS,EACT5I,MAAM,EACNmL,WAAW,EACXC,OAAO,EACPC,aAAa,EACbC,iBACF,CAAC;IAED,IAAItL,MAAM,CAAC/F,MAAM,IAAIoR,aAAa,EAAE;MAClC,MAAMpR,MAAM,GAAG+F,MAAM,CAAC/F,MAAM;MAC5B,IAAIA,MAAM,CAACzD,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC+U,WAAW,CAACtR,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QACpD,IAAI,CAACnB,KAAK,CAACpH,UAAU,CAAC2C,4BAA4B,EAAE;UAAE0E,EAAE,EAAEiH;QAAO,CAAC,CAAC;MACrE;IAEF,CAAC,MAAM,IAELA,MAAM,CAACvK,IAAI,KAAK,kBAAkB,IAClC4V,aAAa,IAEbrL,MAAM,CAACnI,KAAK,CAACoC,MAAM,EACnB;MAEA,MAAMA,MAAM,GAAG+F,MAAM,CAACnI,KAAK,CAACoC,MAAM;MAClC,IAAIA,MAAM,CAACzD,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC+U,WAAW,CAACtR,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QACpD,IAAI,CAACnB,KAAK,CAACpH,UAAU,CAAC2C,4BAA4B,EAAE;UAAE0E,EAAE,EAAEiH;QAAO,CAAC,CAAC;MACrE;IACF;EACF;EAEAwL,sBAAsBA,CACpB5C,SAAsB,EACtB5I,MAA4B,EAC5BmL,WAAoB,EACpBC,OAAgB,EACV;IACN,IAAKpL,MAAM,CAASlB,QAAQ,EAAE;MAC5B,IAAI,CAACnD,UAAU,CAAEqE,MAAM,CAASlB,QAAQ,CAACjE,GAAG,CAACC,KAAK,CAAC;IACrD;IACA,OAAQkF,MAAM,CAASlB,QAAQ;IAC/B,IAAI,IAAI,CAACzF,KAAK,GAAM,CAAC,EAAE;MACrB2G,MAAM,CAACnG,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAClE;IAEA,KAAK,CAAC0R,sBAAsB,CAAC5C,SAAS,EAAE5I,MAAM,EAAEmL,WAAW,EAAEC,OAAO,CAAC;EACvE;EAGAK,eAAeA,CAAC7V,IAAa,EAAQ;IACnC,KAAK,CAAC6V,eAAe,CAAC7V,IAAI,CAAC;IAC3B,IAAIA,IAAI,CAACgB,UAAU,IAAI,IAAI,CAACyC,KAAK,GAAM,CAAC,EAAE;MACxCzD,IAAI,CAAC8V,mBAAmB,GAAG,IAAI,CAAC1N,mCAAmC,CAAC,CAAC;IACvE;IACA,IAAI,IAAI,CAAC1C,YAAY,IAAe,CAAC,EAAE;MACrC,IAAI,CAAC5C,IAAI,CAAC,CAAC;MACX,MAAMiT,WAAoC,GAAI/V,IAAI,CAAC2H,UAAU,GAAG,EAAG;MACnE,GAAG;QACD,MAAM3H,IAAI,GAAG,IAAI,CAAC2C,SAAS,CAAC,CAAC;QAC7B3C,IAAI,CAAC6D,EAAE,GAAG,IAAI,CAACyD,6BAA6B,CAAa,IAAI,CAAC;QAC9D,IAAI,IAAI,CAAC7D,KAAK,GAAM,CAAC,EAAE;UACrBzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACmE,mCAAmC,CAAC,CAAC;QAClE,CAAC,MAAM;UACLpI,IAAI,CAACiE,cAAc,GAAG,IAAI;QAC5B;QACA8R,WAAW,CAAClV,IAAI,CAAC,IAAI,CAACyC,UAAU,CAACtD,IAAI,EAAE,iBAAiB,CAAC,CAAC;MAC5D,CAAC,QAAQ,IAAI,CAACoD,GAAG,GAAS,CAAC;IAC7B;EACF;EAEA4S,uBAAuBA,CAAC5L,MAAsC,EAAQ;IACpE,KAAK,CAAC4L,uBAAuB,CAAC5L,MAAM,CAAC;IACrC,MAAM/F,MAAM,GAAG,IAAI,CAAC4R,4BAA4B,CAAC7L,MAAM,CAAC;IACxD,IAAI/F,MAAM,CAACzD,MAAM,GAAG,CAAC,EAAE;MACrB,MAAMsN,KAAK,GAAG7J,MAAM,CAAC,CAAC,CAAC;MACvB,IAAI,IAAI,CAACsR,WAAW,CAACzH,KAAK,CAAC,IAAI9D,MAAM,CAAC3D,IAAI,KAAK,KAAK,EAAE;QACpD,IAAI,CAACvD,KAAK,CAACpH,UAAU,CAACyB,yBAAyB,EAAE;UAAE4F,EAAE,EAAE+K;QAAM,CAAC,CAAC;MACjE,CAAC,MAAM,IAAI,IAAI,CAACyH,WAAW,CAACzH,KAAK,CAAC,EAAE;QAClC,IAAI,CAAChL,KAAK,CAACpH,UAAU,CAACwC,yBAAyB,EAAE;UAAE6E,EAAE,EAAE+K;QAAM,CAAC,CAAC;MACjE;IACF;EACF;EAEAgI,+BAA+BA,CAC7BlW,IAA2C,EACrC;IACNA,IAAI,CAACkJ,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1C;EAGAgN,iBAAiBA,CACfC,IAA+C,EAC/CvT,QAAqC,EACrC0S,WAAoB,EACpBC,OAAgB,EAChBa,SAAkB,EAClBC,UAAmB,EACnBxG,mBAA6C,EACV;IACnC,IAAKsG,IAAI,CAASlN,QAAQ,EAAE;MAC1B,IAAI,CAACnD,UAAU,CAAEqQ,IAAI,CAASlN,QAAQ,CAACjE,GAAG,CAACC,KAAK,CAAC;IACnD;IACA,OAAQkR,IAAI,CAASlN,QAAQ;IAE7B,IAAIjF,cAAc;IAGlB,IAAI,IAAI,CAACR,KAAK,GAAM,CAAC,IAAI,CAAC6S,UAAU,EAAE;MACpCrS,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;MACzD,IAAI,CAAC,IAAI,CAACT,KAAK,GAAU,CAAC,EAAE,IAAI,CAACsC,UAAU,CAAC,CAAC;IAC/C;IAEA,MAAMgM,MAAM,GAAG,KAAK,CAACoE,iBAAiB,CACpCC,IAAI,EACJvT,QAAQ,EACR0S,WAAW,EACXC,OAAO,EACPa,SAAS,EACTC,UAAU,EACVxG,mBACF,CAAC;IAGD,IAAI7L,cAAc,EAAE;MAClB,CAAC8N,MAAM,CAAC9P,KAAK,IAAI8P,MAAM,EAAE9N,cAAc,GAAGA,cAAc;IAC1D;IACA,OAAO8N,MAAM;EACf;EAEAwE,4BAA4BA,CAACrI,KAAgB,EAAa;IACxD,IAAI,IAAI,CAAC9K,GAAG,GAAY,CAAC,EAAE;MACzB,IAAI8K,KAAK,CAACrO,IAAI,KAAK,YAAY,EAAE;QAC/B,IAAI,CAACqD,KAAK,CAACpH,UAAU,CAACmC,iBAAiB,EAAE;UAAEkF,EAAE,EAAE+K;QAAM,CAAC,CAAC;MACzD;MACA,IAAI,IAAI,CAACyH,WAAW,CAACzH,KAAK,CAAC,EAAE;QAC3B,IAAI,CAAChL,KAAK,CAACpH,UAAU,CAAC4C,yBAAyB,EAAE;UAAEyE,EAAE,EAAE+K;QAAM,CAAC,CAAC;MACjE;MAECA,KAAK,CAAyB7D,QAAQ,GAAG,IAAI;IAChD;IACA,IAAI,IAAI,CAAC5G,KAAK,GAAS,CAAC,EAAE;MAExByK,KAAK,CAACxJ,cAAc,GAAG,IAAI,CAACuC,uBAAuB,CAAC,CAAC;IACvD,CAAC,MAAM,IAAI,IAAI,CAAC0O,WAAW,CAACzH,KAAK,CAAC,EAAE;MAClC,IAAI,CAAChL,KAAK,CAACpH,UAAU,CAAC0C,2BAA2B,EAAE;QAAE2E,EAAE,EAAE+K;MAAM,CAAC,CAAC;IACnE;IAEA,IAAI,IAAI,CAACzK,KAAK,GAAM,CAAC,IAAI,IAAI,CAACkS,WAAW,CAACzH,KAAK,CAAC,EAAE;MAChD,IAAI,CAAChL,KAAK,CAACpH,UAAU,CAAC8C,kBAAkB,EAAE;QAAEuE,EAAE,EAAE+K;MAAM,CAAC,CAAC;IAC1D;IAEA,IAAI,CAACvJ,gBAAgB,CAACuJ,KAAK,CAAC;IAC5B,OAAOA,KAAK;EACd;EAEAsI,iBAAiBA,CACf3T,QAA0B,EAC1BsR,IAAuB,EACZ;IACX,MAAMnU,IAAI,GAAG,KAAK,CAACwW,iBAAiB,CAAC3T,QAAQ,EAAEsR,IAAI,CAAC;IAEpD,IACEnU,IAAI,CAACH,IAAI,KAAK,mBAAmB,IACjCG,IAAI,CAAC0E,cAAc,IACnB1E,IAAI,CAAC2I,KAAK,CAACzD,KAAK,GAAGlF,IAAI,CAAC0E,cAAc,CAACQ,KAAK,EAC5C;MACA,IAAI,CAAChC,KAAK,CAACpH,UAAU,CAAC+C,qBAAqB,EAAE;QAC3CsE,EAAE,EAAEnD,IAAI,CAAC0E;MACX,CAAC,CAAC;IACJ;IAEA,OAAO1E,IAAI;EACb;EAEAyW,qBAAqBA,CAACzW,IAAiC,EAAE;IACvD,KAAK,CAACyW,qBAAqB,CAACzW,IAAI,CAAC;IACjC,IAAIA,IAAI,CAAC0W,MAAM,IAAI1W,IAAI,CAACC,UAAU,KAAK,OAAO,EAAE;MAC9C,IAAI,CAACiD,KAAK,CAACpH,UAAU,CAAC0B,6BAA6B,EAAE;QACnD2F,EAAE,EAAEnD,IAAI,CAACsS,UAAU,CAAC,CAAC,CAAC,CAACrN,GAAG,CAACC;MAC7B,CAAC,CAAC;IACJ;EACF;EAEAyR,yBAAyBA,CAKvB3W,IAAyB,EAAE4W,SAAoB,EAAE/W,IAAe,EAAQ;IACxE+W,SAAS,CAACC,KAAK,GAAG9W,iBAAiB,CAACC,IAAI,CAAC,GACrC,IAAI,CAACsH,6BAA6B,CAClB,IAAI,EACA,IACpB,CAAC,GACD,IAAI,CAACxD,eAAe,CAAC,CAAC;IAE1B9D,IAAI,CAACsS,UAAU,CAACzR,IAAI,CAAC,IAAI,CAACiW,qBAAqB,CAACF,SAAS,EAAE/W,IAAI,CAAC,CAAC;EACnE;EAEAkX,sBAAsBA,CAACC,QAAiB,EAAW;IACjD,IAAI,KAAK,CAACD,sBAAsB,CAACC,QAAQ,CAAC,EAAE,OAAO,IAAI;IACvD,IAAI,IAAI,CAACtR,YAAY,IAAS,CAAC,EAAE;MAC/B,IAAI,CAACsR,QAAQ,EAAE,OAAO,IAAI;MAC1B,MAAMC,EAAE,GAAG,IAAI,CAAChH,iBAAiB,CAAC,CAAC;MACnC,OAAOgH,EAAE,QAA6B,IAAIA,EAAE,OAAuB;IACrE;IACA,OAAO,CAACD,QAAQ,IAAI,IAAI,CAACtR,YAAY,GAAW,CAAC;EACnD;EAEAwR,gBAAgBA,CACdlX,IAA4D,EAC5DgX,QAAiB,EACjBG,KAAoB,EACpBlS,GAAc,EACR;IACN,KAAK,CAACiS,gBAAgB,CAAClX,IAAI,EAAEgX,QAAQ,EAAEG,KAAK,EAAElS,GAAG,CAAC;IAClD,IAAI+R,QAAQ,EAAE;MACZ,IAAI,CAACG,KAAK,IAAI,IAAI,CAAC1T,KAAK,GAAY,CAAC,EAAE;QAErC;MACF;MACCzD,IAAI,CAA8BgH,UAAU,GAC3CmQ,KAAK,KAAK,MAAM,GAAGA,KAAK,GAAG,OAAO;IACtC,CAAC,MAAM;MACL,IAAIA,KAAK,KAAK,MAAM,IAAI,IAAI,CAAC1T,KAAK,GAAQ,CAAC,EAAE,IAAI,CAACsC,UAAU,CAAC,CAAC;MAC7D/F,IAAI,CAAyBC,UAAU,GACtCkX,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,OAAO;IAC5D;EACF;EAGAC,oBAAoBA,CAClBR,SAAc,EACdS,gBAAyB,EACzBC,kBAA2B,EAE3BC,eAAwB,EAExBC,WAAqC,EAClB;IACnB,MAAMC,UAAU,GAAGb,SAAS,CAACc,QAAQ;IAErC,IAAIC,iBAAiB,GAAG,IAAI;IAC5B,IAAIF,UAAU,CAAC5X,IAAI,KAAK,YAAY,EAAE;MACpC,IAAI4X,UAAU,CAAC1S,IAAI,KAAK,MAAM,EAAE;QAC9B4S,iBAAiB,GAAG,MAAM;MAC5B,CAAC,MAAM,IAAIF,UAAU,CAAC1S,IAAI,KAAK,QAAQ,EAAE;QACvC4S,iBAAiB,GAAG,QAAQ;MAC9B;IACF;IAEA,IAAI3D,SAAS,GAAG,KAAK;IACrB,IAAI,IAAI,CAACtO,YAAY,GAAO,CAAC,IAAI,CAAC,IAAI,CAACkS,qBAAqB,CAAC,IAAI,CAAC,EAAE;MAClE,MAAMC,QAAQ,GAAG,IAAI,CAAC/T,eAAe,CAAC,IAAI,CAAC;MAC3C,IACE6T,iBAAiB,KAAK,IAAI,IAC1B,CAAC,IAAA1I,iCAA0B,EAAC,IAAI,CAAC3M,KAAK,CAACzC,IAAI,CAAC,EAC5C;QAEA+W,SAAS,CAACc,QAAQ,GAAGG,QAAQ;QAC7BjB,SAAS,CAAC3W,UAAU,GAAG0X,iBAAiB;QACxCf,SAAS,CAACC,KAAK,GAAG,IAAAiB,qBAAe,EAACD,QAAQ,CAAC;MAC7C,CAAC,MAAM;QAELjB,SAAS,CAACc,QAAQ,GAAGD,UAAU;QAC/Bb,SAAS,CAAC3W,UAAU,GAAG,IAAI;QAC3B2W,SAAS,CAACC,KAAK,GAAG,IAAI,CAAC/S,eAAe,CAAC,CAAC;MAC1C;IACF,CAAC,MAAM;MACL,IACE6T,iBAAiB,KAAK,IAAI,IAC1B,IAAA1I,iCAA0B,EAAC,IAAI,CAAC3M,KAAK,CAACzC,IAAI,CAAC,EAC3C;QAEA+W,SAAS,CAACc,QAAQ,GAAG,IAAI,CAAC5T,eAAe,CAAC,IAAI,CAAC;QAC/C8S,SAAS,CAAC3W,UAAU,GAAG0X,iBAAiB;MAC1C,CAAC,MAAM;QACL,IAAIN,gBAAgB,EAAE;UAEpB,MAAM,IAAI,CAACnU,KAAK,CAAC8I,kBAAM,CAAC+L,qBAAqB,EAAE;YAC7C5U,EAAE,EAAEyT,SAAS;YACboB,UAAU,EAAEP,UAAU,CAACxV;UACzB,CAAC,CAAC;QACJ;QAEA2U,SAAS,CAACc,QAAQ,GAAGD,UAAU;QAC/Bb,SAAS,CAAC3W,UAAU,GAAG,IAAI;MAC7B;MAEA,IAAI,IAAI,CAACqF,aAAa,GAAO,CAAC,EAAE;QAC9BsR,SAAS,CAACC,KAAK,GAAG,IAAI,CAAC/S,eAAe,CAAC,CAAC;MAC1C,CAAC,MAAM;QACLkQ,SAAS,GAAG,IAAI;QAChB4C,SAAS,CAACC,KAAK,GAAG,IAAAiB,qBAAe,EAAClB,SAAS,CAACc,QAAQ,CAAC;MACvD;IACF;IAEA,MAAMO,qBAAqB,GAAGlY,iBAAiB,CAAC6W,SAAS,CAAC;IAE1D,IAAIU,kBAAkB,IAAIW,qBAAqB,EAAE;MAC/C,IAAI,CAAC/U,KAAK,CAACpH,UAAU,CAAC2B,mCAAmC,EAAE;QACzD0F,EAAE,EAAEyT;MACN,CAAC,CAAC;IACJ;IAEA,IAAIU,kBAAkB,IAAIW,qBAAqB,EAAE;MAC/C,IAAI,CAACzP,iBAAiB,CACpBoO,SAAS,CAACC,KAAK,CAAC9R,IAAI,EACpB6R,SAAS,CAACC,KAAK,CAAC5R,GAAG,CAACC,KAAK,EACP,IACpB,CAAC;IACH;IAEA,IAAI8O,SAAS,IAAI,CAACsD,kBAAkB,IAAI,CAACW,qBAAqB,EAAE;MAC9D,IAAI,CAACC,iBAAiB,CACpBtB,SAAS,CAACC,KAAK,CAAC9R,IAAI,EACpB6R,SAAS,CAAC3R,GAAG,CAACC,KAAK,EACnB,IAAI,EACJ,IACF,CAAC;IACH;IAEA,OAAO,IAAI,CAAC4R,qBAAqB,CAACF,SAAS,EAAE,iBAAiB,CAAC;EACjE;EAEAuB,gBAAgBA,CAAA,EAAc;IAC5B,QAAQ,IAAI,CAAC7V,KAAK,CAACzC,IAAI;MACrB;QAEE,OAAO,IAAI,CAACiE,eAAe,CAAe,IAAI,CAAC;MACjD;QACE,OAAO,KAAK,CAACqU,gBAAgB,CAAC,CAAC;IACnC;EACF;EAGAC,mBAAmBA,CACjBpY,IAAwB,EACxByV,aAAsB,EAChB;IAEN,MAAMhP,IAAI,GAAGzG,IAAI,CAACyG,IAAI;IACtB,IAAIA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,IAAI,IAAI,CAAChD,KAAK,GAAM,CAAC,EAAE;MACzDzD,IAAI,CAACiE,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;IAChE;IACA,KAAK,CAACkU,mBAAmB,CAACpY,IAAI,EAAEyV,aAAa,CAAC;EAChD;EAGA4C,UAAUA,CACRC,IAA0B,EAC1B7R,IAA6B,EACvB;IACN,KAAK,CAAC4R,UAAU,CAACC,IAAI,EAAE7R,IAAI,CAAC;IAC5B,IAAI,IAAI,CAAChD,KAAK,GAAS,CAAC,EAAE;MAExB6U,IAAI,CAACzU,EAAE,CAACa,cAAc,GAAG,IAAI,CAACuC,uBAAuB,CAAC,CAAC;MACvD,IAAI,CAACtC,gBAAgB,CAAC2T,IAAI,CAACzU,EAAE,CAAC;IAChC;EACF;EAGA0U,iCAAiCA,CAC/BvY,IAA+B,EAC/BwY,IAAsB,EACK;IAC3B,IAAI,IAAI,CAAC/U,KAAK,GAAS,CAAC,EAAE;MACxB,MAAM+F,qBAAqB,GAAG,IAAI,CAAClH,KAAK,CAACmH,kBAAkB;MAC3D,IAAI,CAACnH,KAAK,CAACmH,kBAAkB,GAAG,IAAI;MAEpCzJ,IAAI,CAACyE,UAAU,GAAG,IAAI,CAACwC,uBAAuB,CAAC,CAAC;MAChD,IAAI,CAAC3E,KAAK,CAACmH,kBAAkB,GAAGD,qBAAqB;IACvD;IAEA,OAAO,KAAK,CAAC+O,iCAAiC,CAACvY,IAAI,EAAEwY,IAAI,CAAC;EAC5D;EAGAC,qBAAqBA,CAAA,EAAY;IAC/B,OAAO,IAAI,CAAChV,KAAK,GAAS,CAAC,IAAI,KAAK,CAACgV,qBAAqB,CAAC,CAAC;EAC9D;EAYA5H,gBAAgBA,CACdf,mBAA6C,EAC7C4I,cAAyB,EACX;IAAA,IAAAC,IAAA;IACd,IAAIrW,KAAK,GAAG,IAAI;IAEhB,IAAIsW,GAAG;IAEP,IACE,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC,KACpB,IAAI,CAACpV,KAAK,IAAe,CAAC,IAAI,IAAI,CAACA,KAAK,GAAM,CAAC,CAAC,EACjD;MACAnB,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC6N,KAAK,CAAC,CAAC;MAE1ByI,GAAG,GAAG,IAAI,CAACE,QAAQ,CACjB,MAAM,KAAK,CAACjI,gBAAgB,CAACf,mBAAmB,EAAE4I,cAAc,CAAC,EACjEpW,KACF,CAAC;MAID,IAAI,CAACsW,GAAG,CAACG,KAAK,EAAE,OAAOH,GAAG,CAAC5Y,IAAI;MAK/B,MAAM;QAAEgZ;MAAQ,CAAC,GAAG,IAAI,CAAC1W,KAAK;MAC9B,MAAM2W,cAAc,GAAGD,OAAO,CAACA,OAAO,CAACpY,MAAM,GAAG,CAAC,CAAC;MAClD,IAAIqY,cAAc,KAAKC,cAAE,CAACC,MAAM,IAAIF,cAAc,KAAKC,cAAE,CAACE,MAAM,EAAE;QAChEJ,OAAO,CAAChI,GAAG,CAAC,CAAC;MACf;IACF;IAEA,IAAI,CAAA2H,IAAA,GAAAC,GAAG,aAAHD,IAAA,CAAKI,KAAK,IAAI,IAAI,CAACtV,KAAK,GAAM,CAAC,EAAE;MAAA,IAAA4V,KAAA,EAAAC,KAAA;MACnChX,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC6N,KAAK,CAAC,CAAC;MAEnC,IAAIlM,cAA0C;MAE9C,MAAMsV,KAAK,GAAG,IAAI,CAACT,QAAQ,CAACU,KAAK,IAAI;QAAA,IAAAC,qBAAA;QACnCxV,cAAc,GAAG,IAAI,CAACC,iCAAiC,CAAC,CAAC;QAEzD,MAAMwV,eAAe,GAAG,IAAI,CAAC9K,gCAAgC,CAC3D3K,cAAc,EACd,MAAM;UACJ,MAAM8N,MAAM,GAAG,KAAK,CAAClB,gBAAgB,CACnCf,mBAAmB,EACnB4I,cACF,CAAC;UAED,IAAI,CAACiB,0BAA0B,CAAC5H,MAAM,EAAE9N,cAAc,CAAC;UAEvD,OAAO8N,MAAM;QACf,CACF,CAAC;QAID,KAAA0H,qBAAA,GAAIC,eAAe,CAACjI,KAAK,aAArBgI,qBAAA,CAAuBjF,aAAa,EAAEgF,KAAK,CAAC,CAAC;QAKjD,MAAMlK,IAAI,GAAG,IAAI,CAACsK,6BAA6B,CAACF,eAAe,CAAC;QAEhE,IAAIpK,IAAI,CAACzP,IAAI,KAAK,yBAAyB,EAAE2Z,KAAK,CAAC,CAAC;QAEpDlK,IAAI,CAACrL,cAAc,GAAGA,cAAc;QACpC,IAAI,CAAC0V,0BAA0B,CAACrK,IAAI,EAAErL,cAAc,CAAC;QAErD,OAAOyV,eAAe;MACxB,CAAC,EAAEpX,KAAK,CAAC;MAET,IAAIoX,eAII,GAAG,IAAI;MAEf,IACEH,KAAK,CAACvZ,IAAI,IAEV,IAAI,CAAC4Z,6BAA6B,CAACL,KAAK,CAACvZ,IAAI,CAAC,CAACH,IAAI,KACjD,yBAAyB,EAC3B;QACA,IAAI,CAAC0Z,KAAK,CAACR,KAAK,IAAI,CAACQ,KAAK,CAACM,OAAO,EAAE;UAGlC,IAAIN,KAAK,CAACvZ,IAAI,CAAC8Z,KAAK,EAAE;YAEpB,IAAI,CAAC5W,KAAK,CACRpH,UAAU,CAACwD,+CAA+C,EAC1D;cAAE6D,EAAE,EAAEc;YAAe,CACvB,CAAC;UACH;UAEA,OAAOsV,KAAK,CAACvZ,IAAI;QACnB;QAGA0Z,eAAe,GAAGH,KAAK,CAACvZ,IAAI;MAC9B;MAQA,KAAAqZ,KAAA,GAAIT,GAAG,aAAHS,KAAA,CAAKrZ,IAAI,EAAE;QAEb,IAAI,CAACsC,KAAK,GAAGsW,GAAG,CAACmB,SAAS;QAC1B,OAAOnB,GAAG,CAAC5Y,IAAI;MACjB;MAEA,IAAI0Z,eAAe,EAAE;QAEnB,IAAI,CAACpX,KAAK,GAAGiX,KAAK,CAACQ,SAAS;QAC5B,OAAOL,eAAe;MACxB;MAEA,KAAAJ,KAAA,GAAIV,GAAG,aAAHU,KAAA,CAAKU,MAAM,EAAE,MAAMpB,GAAG,CAACG,KAAK;MAChC,IAAIQ,KAAK,CAACS,MAAM,EAAE,MAAMT,KAAK,CAACR,KAAK;MAGnC,MAAM,IAAI,CAAC7V,KAAK,CAACpH,UAAU,CAACuD,iCAAiC,EAAE;QAC7D8D,EAAE,EAAEc;MACN,CAAC,CAAC;IACJ;IAEA,OAAO,KAAK,CAAC4M,gBAAgB,CAACf,mBAAmB,EAAE4I,cAAc,CAAC;EACpE;EAGAuB,UAAUA,CACRja,IAAuC,EACe;IACtD,IAAI,IAAI,CAACyD,KAAK,GAAS,CAAC,EAAE;MAExB,MAAMsO,MAAM,GAAG,IAAI,CAAC+G,QAAQ,CAAC,MAAM;QACjC,MAAMtP,qBAAqB,GAAG,IAAI,CAAClH,KAAK,CAACmH,kBAAkB;QAC3D,IAAI,CAACnH,KAAK,CAACmH,kBAAkB,GAAG,IAAI;QAEpC,MAAM1F,QAAQ,GAAG,IAAI,CAACpB,SAAS,CAAmB,CAAC;QAEnD,CACEoB,QAAQ,CAACW,cAAc,EAEvB1E,IAAI,CAACwD,SAAS,CACf,GAAG,IAAI,CAACD,oCAAoC,CAAC,CAAC;QAE/C,IAAI,CAACjB,KAAK,CAACmH,kBAAkB,GAAGD,qBAAqB;QAErD,IAAI,IAAI,CAACoE,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC7H,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,CAACtC,KAAK,GAAS,CAAC,EAAE,IAAI,CAACsC,UAAU,CAAC,CAAC;QAE5C,OAAOhC,QAAQ;MACjB,CAAC,CAAC;MAEF,IAAIgO,MAAM,CAACiI,MAAM,EAAE,OAAO,IAAI;MAG9B,IAAIjI,MAAM,CAACgH,KAAK,EAAE,IAAI,CAACzW,KAAK,GAAGyP,MAAM,CAACgI,SAAS;MAI/C/Z,IAAI,CAACyE,UAAU,GAAGsN,MAAM,CAAC/R,IAAI,CAAC0E,cAAc,GACxC,IAAI,CAACpB,UAAU,CAACyO,MAAM,CAAC/R,IAAI,EAAE,gBAAgB,CAAC,GAC9C,IAAI;IACV;IAEA,OAAO,KAAK,CAACia,UAAU,CAACja,IAAI,CAAC;EAC/B;EAEAka,gBAAgBA,CAAC7V,MAAqB,EAAW;IAC/C,OAAO,IAAI,CAACZ,KAAK,GAAS,CAAC,IAAI,KAAK,CAACyW,gBAAgB,CAAC7V,MAAM,CAAC;EAC/D;EAEA8V,0BAA0BA,CACxBna,IAA+B,EAC/BqE,MAAmB,EACb;IACN,IAAI,IAAI,CAAC/B,KAAK,CAACwO,yBAAyB,CAACkB,OAAO,CAAChS,IAAI,CAACkF,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;MACnElF,IAAI,CAACqE,MAAM,GAAGA,MAAM;IACtB,CAAC,MAAM;MACL,KAAK,CAAC8V,0BAA0B,CAACna,IAAI,EAAEqE,MAAM,CAAC;IAChD;EACF;EAEAwN,WAAWA,CACT7R,IAAgB,EAChBoa,eAAwB,EACxBC,eAAgC,EAChCC,iBAA0B,GAAG,IAAI,EAC3B;IACN,IACED,eAAe,IACf,IAAI,CAAC/X,KAAK,CAACwO,yBAAyB,CAACkB,OAAO,CAAChS,IAAI,CAACkF,KAAK,CAAC,KAAK,CAAC,CAAC,EAC/D;MACA;IACF;IAGA,KAAK,IAAIvE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACqE,MAAM,CAACzD,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,IAAI,IAAI,CAACgV,WAAW,CAAC3V,IAAI,CAACqE,MAAM,CAAC1D,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;QAC7C,IAAI,CAACuC,KAAK,CAACpH,UAAU,CAAC6C,oBAAoB,EAAE;UAAEwE,EAAE,EAAEnD,IAAI,CAACqE,MAAM,CAAC1D,CAAC;QAAE,CAAC,CAAC;MACrE;IACF;IAEA,KAAK,CAACkR,WAAW,CACf7R,IAAI,EACJoa,eAAe,EACfC,eAAe,EACfC,iBACF,CAAC;EACH;EAEAC,kCAAkCA,CAACC,UAAmB,EAAgB;IACpE,OAAO,KAAK,CAACD,kCAAkC,CAC7CC,UAAU,IAAI,IAAI,CAAClY,KAAK,CAAC+N,SAAS,CAAC2B,OAAO,CAAC,IAAI,CAAC1P,KAAK,CAAC4C,KAAK,CAAC,KAAK,CAAC,CACpE,CAAC;EACH;EAEAuV,eAAeA,CACbC,IAAkB,EAElB7X,QAAkB,EAClB8X,OAAwB,EACV;IACd,IACED,IAAI,CAAC7a,IAAI,KAAK,YAAY,IAC1B6a,IAAI,CAAC3V,IAAI,KAAK,OAAO,IACrB,IAAI,CAACzC,KAAK,CAAC+N,SAAS,CAAC2B,OAAO,CAACnP,QAAQ,CAACI,KAAK,CAAC,KAAK,CAAC,CAAC,EACnD;MACA,IAAI,CAACH,IAAI,CAAC,CAAC;MAEX,MAAM9C,IAAI,GAAG,IAAI,CAACuK,WAAW,CAAC1H,QAAQ,CAAC;MACvC7C,IAAI,CAAC4a,MAAM,GAAGF,IAAI;MAClB1a,IAAI,CAAC6a,SAAS,GAAG,KAAK,CAACC,4BAA4B,KAAY,KAAK,CAAC;MACrEJ,IAAI,GAAG,IAAI,CAACpX,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;IAChD,CAAC,MAAM,IACL0a,IAAI,CAAC7a,IAAI,KAAK,YAAY,IAC1B6a,IAAI,CAAC3V,IAAI,KAAK,OAAO,IACrB,IAAI,CAACtB,KAAK,GAAM,CAAC,EACjB;MACA,MAAMnB,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC6N,KAAK,CAAC,CAAC;MAChC,MAAMoJ,KAAK,GAAG,IAAI,CAACT,QAAQ,CACzBU,KAAK,IAAI,IAAI,CAACuB,iCAAiC,CAAClY,QAAQ,CAAC,IAAI2W,KAAK,CAAC,CAAC,EACpElX,KACF,CAAC;MAID,IAAI,CAACiX,KAAK,CAACR,KAAK,IAAI,CAACQ,KAAK,CAACM,OAAO,EAAE,OAAON,KAAK,CAACvZ,IAAI;MAErD,MAAM+R,MAAM,GAAG,IAAI,CAAC+G,QAAQ,CAC1B,MAAM,KAAK,CAAC2B,eAAe,CAACC,IAAI,EAAE7X,QAAQ,EAAE8X,OAAO,CAAC,EACpDrY,KACF,CAAC;MAED,IAAIyP,MAAM,CAAC/R,IAAI,IAAI,CAAC+R,MAAM,CAACgH,KAAK,EAAE,OAAOhH,MAAM,CAAC/R,IAAI;MAEpD,IAAIuZ,KAAK,CAACvZ,IAAI,EAAE;QACd,IAAI,CAACsC,KAAK,GAAGiX,KAAK,CAACQ,SAAS;QAE5B,OAAOR,KAAK,CAACvZ,IAAI;MACnB;MAEA,IAAI+R,MAAM,CAAC/R,IAAI,EAAE;QACf,IAAI,CAACsC,KAAK,GAAGyP,MAAM,CAACgI,SAAS;QAC7B,OAAOhI,MAAM,CAAC/R,IAAI;MACpB;MAEA,MAAMuZ,KAAK,CAACR,KAAK,IAAIhH,MAAM,CAACgH,KAAK;IACnC;IAEA,OAAO,KAAK,CAAC0B,eAAe,CAACC,IAAI,EAAE7X,QAAQ,EAAE8X,OAAO,CAAC;EACvD;EAEAK,cAAcA,CACZN,IAAkB,EAElB7X,QAAkB,EAClB8X,OAAmC,EACnCM,cAAqC,EACvB;IACd,IAAI,IAAI,CAACxX,KAAK,GAAe,CAAC,IAAI,IAAI,CAACyX,mBAAmB,CAAC,CAAC,EAAE;MAC5DD,cAAc,CAACE,mBAAmB,GAAG,IAAI;MACzC,IAAIR,OAAO,EAAE;QACXM,cAAc,CAACG,IAAI,GAAG,IAAI;QAC1B,OAAOV,IAAI;MACb;MACA,IAAI,CAAC5X,IAAI,CAAC,CAAC;MACX,MAAM9C,IAAI,GAAG,IAAI,CAACuK,WAAW,CAA2B1H,QAAQ,CAAC;MACjE7C,IAAI,CAAC4a,MAAM,GAAGF,IAAI;MAClB1a,IAAI,CAACqb,aAAa,GAAG,IAAI,CAACjT,mCAAmC,CAAC,CAAC;MAC/D,IAAI,CAAC5F,MAAM,GAAU,CAAC;MACtBxC,IAAI,CAAC6a,SAAS,GAAG,IAAI,CAACC,4BAA4B,KAAY,KAAK,CAAC;MACpE9a,IAAI,CAACqK,QAAQ,GAAG,IAAI;MACpB,OAAO,IAAI,CAACiR,oBAAoB,CAACtb,IAAI,EAAiB,IAAI,CAAC;IAC7D,CAAC,MAAM,IAAI,CAAC2a,OAAO,IAAI,IAAI,CAACnZ,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACiC,KAAK,GAAM,CAAC,EAAE;MACnE,MAAMzD,IAAI,GAAG,IAAI,CAACuK,WAAW,CAE3B1H,QAAQ,CAAC;MACX7C,IAAI,CAAC4a,MAAM,GAAGF,IAAI;MAElB,MAAM3I,MAAM,GAAG,IAAI,CAAC+G,QAAQ,CAAC,MAAM;QACjC9Y,IAAI,CAACqb,aAAa,GAChB,IAAI,CAAC3R,4CAA4C,CAAC,CAAC;QACrD,IAAI,CAAClH,MAAM,GAAU,CAAC;QACtBxC,IAAI,CAAC6a,SAAS,GAAG,KAAK,CAACC,4BAA4B,KAAY,KAAK,CAAC;QACrE,IAAIG,cAAc,CAACE,mBAAmB,EAAE;UACrCnb,IAAI,CAAsCqK,QAAQ,GAAG,KAAK;QAC7D;QACA,OAAO,IAAI,CAACiR,oBAAoB,CAC9Btb,IAAI,EACJib,cAAc,CAACE,mBACjB,CAAC;MACH,CAAC,CAAC;MAEF,IAAIpJ,MAAM,CAAC/R,IAAI,EAAE;QACf,IAAI+R,MAAM,CAACgH,KAAK,EAAE,IAAI,CAACzW,KAAK,GAAGyP,MAAM,CAACgI,SAAS;QAC/C,OAAOhI,MAAM,CAAC/R,IAAI;MACpB;IACF;IAEA,OAAO,KAAK,CAACgb,cAAc,CACzBN,IAAI,EAEJ7X,QAAQ,EACR8X,OAAO,EACPM,cACF,CAAC;EACH;EAEAM,cAAcA,CAACvb,IAAqB,EAAQ;IAC1C,KAAK,CAACub,cAAc,CAACvb,IAAI,CAAC;IAE1B,IAAIwb,KAAK,GAAG,IAAI;IAChB,IAAI,IAAI,CAACha,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACiC,KAAK,GAAM,CAAC,EAAE;MAChD+X,KAAK,GAAG,IAAI,CAAC1C,QAAQ,CAAC,MACpB,IAAI,CAACpP,4CAA4C,CAAC,CACpD,CAAC,CAAC1J,IAAI;IACR;IACAA,IAAI,CAACqb,aAAa,GAAGG,KAAK;EAC5B;EAEAT,iCAAiCA,CAC/BlY,QAAkB,EAC4B;IAC9C,MAAM7C,IAAI,GAAG,IAAI,CAACuK,WAAW,CAA4B1H,QAAQ,CAAC;IAClE,IAAI,CAACuV,mBAAmB,CAACpY,IAAI,EAAE,KAAK,CAAC;IACrC,IAAI,CAAC,IAAI,CAACia,UAAU,CAACja,IAAI,CAAC,EAAE;IAC5B,OAAO,KAAK,CAACyb,oBAAoB,CAC/Bzb,IAAI,EACSqB,SAAS,EACR,IAChB,CAAC;EACH;EAEAqa,qBAAqBA,CAAC/H,IAAY,EAAQ;IACxC,MAAM7Q,IAAI,GAAG,IAAI,CAAC8Q,KAAK,CAACC,UAAU,CAAC,IAAI,CAACvR,KAAK,CAACsK,GAAG,GAAG,CAAC,CAAC;IACtD,IACE+G,IAAI,OAAuB,IAC3B7Q,IAAI,OAAoB,IACxB,IAAI,CAACR,KAAK,CAACqZ,cAAc,EACzB;MACA,IAAI,CAACrZ,KAAK,CAACqZ,cAAc,GAAG,KAAK;MACjC,IAAI,CAACrZ,KAAK,CAACsK,GAAG,IAAI,CAAC;MACnB,IAAI,CAACgP,SAAS,CAAC,CAAC;MAChB;IACF;IAEA,KAAK,CAACF,qBAAqB,CAAC/H,IAAI,CAAC;EACnC;EAEAkI,kBAAkBA,CAAClI,IAAY,EAAQ;IACrC,MAAM7Q,IAAI,GAAG,IAAI,CAAC8Q,KAAK,CAACC,UAAU,CAAC,IAAI,CAACvR,KAAK,CAACsK,GAAG,GAAG,CAAC,CAAC;IACtD,IACE+G,IAAI,QAA0B,IAC9B7Q,IAAI,QAA8B,EAClC;MAEA,IAAI,CAACgR,QAAQ,IAAe,CAAC,CAAC;MAC9B;IACF;IAEA,KAAK,CAAC+H,kBAAkB,CAAClI,IAAI,CAAC;EAChC;EAEAmI,aAAaA,CAACC,IAAY,EAAEC,OAAkB,EAAU;IACtD,MAAMC,QAAQ,GAAG,KAAK,CAACH,aAAa,CAACC,IAAI,EAAEC,OAAO,CAAC;IACnD,IAAI,IAAI,CAAC1Z,KAAK,CAACqZ,cAAc,EAAE;MAC7B,IAAI,CAACzY,KAAK,CAACpH,UAAU,CAAC4D,uBAAuB,EAAE;QAC7CyD,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkR,WAAW,CAAC;MAC7B,CAAC,CAAC;IACJ;IACA,OAAOyI,QAAQ;EACjB;EAEAC,gBAAgBA,CAAA,EAA+B;IAC7C,IAAI,IAAI,CAACrD,SAAS,CAAC,cAAc,CAAC,IAAI,IAAI,CAACsD,eAAe,CAAC,CAAC,EAAE;MAC5D,IAAI,IAAI,CAAC7Z,KAAK,CAACqZ,cAAc,EAAE;QAC7B,MAAM,IAAI,CAACzY,KAAK,CAACpH,UAAU,CAACkC,iBAAiB,EAAE;UAC7CmF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACO;QACjB,CAAC,CAAC;MACJ;MACA,IAAI,CAACuZ,wBAAwB,CAAC,CAAC;MAC/B,MAAMC,WAAW,GAAG,IAAI,CAACF,eAAe,CAAC,CAAC;MAC1C,IAAIE,WAAW,EAAE;QACf,IAAI,CAAC/Z,KAAK,CAACsK,GAAG,IAAIyP,WAAW;QAC7B,IAAI,CAAC/Z,KAAK,CAACqZ,cAAc,GAAG,IAAI;MAClC;MACA;IACF;IAEA,OAAO,KAAK,CAACO,gBAAgB,CAAC,IAAI,CAAC5Z,KAAK,CAACqZ,cAAc,GAAG,KAAK,GAAG,IAAI,CAAC;EACzE;EAEAQ,eAAeA,CAAA,EAAmB;IAChC,MAAM;MAAEvP;IAAI,CAAC,GAAG,IAAI,CAACtK,KAAK;IAC1B,IAAIga,yBAAyB,GAAG,CAAC;IACjC,OACE,OAAgC,CAACC,QAAQ,CAEvC,IAAI,CAAC3I,KAAK,CAACC,UAAU,CAACjH,GAAG,GAAG0P,yBAAyB,CACvD,CAAC,EACD;MACAA,yBAAyB,EAAE;IAC7B;IAEA,MAAME,GAAG,GAAG,IAAI,CAAC5I,KAAK,CAACC,UAAU,CAACyI,yBAAyB,GAAG1P,GAAG,CAAC;IAClE,MAAM6P,GAAG,GAAG,IAAI,CAAC7I,KAAK,CAACC,UAAU,CAACyI,yBAAyB,GAAG1P,GAAG,GAAG,CAAC,CAAC;IAEtE,IAAI4P,GAAG,OAAoB,IAAIC,GAAG,OAAoB,EAAE;MACtD,OAAOH,yBAAyB,GAAG,CAAC;IACtC;IACA,IACE,IAAI,CAAC1I,KAAK,CAAC8I,KAAK,CACdJ,yBAAyB,GAAG1P,GAAG,EAC/B0P,yBAAyB,GAAG1P,GAAG,GAAG,EACpC,CAAC,KAAK,cAAc,EACpB;MACA,OAAO0P,yBAAyB,GAAG,EAAE;IACvC;IACA,IAAIE,GAAG,OAAoB,IAAIC,GAAG,OAAoB,EAAE;MACtD,OAAOH,yBAAyB;IAClC;IACA,OAAO,KAAK;EACd;EAEAF,wBAAwBA,CAAA,EAAS;IAC/B,MAAM5N,GAAG,GAAG,IAAI,CAACoF,KAAK,CAAC5B,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC1P,KAAK,CAACsK,GAAG,CAAC;IACpD,IAAI4B,GAAG,KAAK,CAAC,CAAC,EAAE;MACd,MAAM,IAAI,CAACtL,KAAK,CAAC8I,kBAAM,CAAC2Q,mBAAmB,EAAE;QAC3CxZ,EAAE,EAAE,IAAI,CAACb,KAAK,CAACkR,WAAW,CAAC;MAC7B,CAAC,CAAC;IACJ;EACF;EAIAoJ,wCAAwCA,CACtC3X,GAAa,EACb;IACExI,QAAQ;IACRD;EAIF,CAAC,EACK;IACN,IAAI,CAAC0G,KAAK,CAACpH,UAAU,CAACS,+BAA+B,EAAE;MACrD4G,EAAE,EAAE8B,GAAG;MACPzI,UAAU;MACVC;IACF,CAAC,CAAC;EACJ;EAEAogB,qCAAqCA,CACnC5X,GAAa,EACb6X,WAAwB,EACxB;IACA,OAAO,IAAI,CAAC5Z,KAAK,CACf,CAAC4Z,WAAW,CAAC9f,YAAY,GACrBlB,UAAU,CAACoB,uCAAuC,GAClD4f,WAAW,CAAC9f,YAAY,KAAK,QAAQ,GACrClB,UAAU,CAACmB,sCAAsC,GACjDnB,UAAU,CAACiB,uCAAuC,EAAAmB,MAAA,CAAAC,MAAA;MAEpDgF,EAAE,EAAE8B;IAAG,GACJ6X,WAAW,CAElB,CAAC;EACH;EAEAC,uCAAuCA,CACrC9X,GAAa,EACb;IACExI,QAAQ;IACRD;EAIF,CAAC,EACK;IACN,IAAI,CAAC0G,KAAK,CAACpH,UAAU,CAACuB,8BAA8B,EAAE;MACpD8F,EAAE,EAAE8B,GAAG;MACPxI,QAAQ;MACRD;IACF,CAAC,CAAC;EACJ;EAEAwgB,kDAAkDA,CAChDhd,IAAY,EACZ;IACEvD;EAGF,CAAC,EACK;IACN,IAAI,CAACyG,KAAK,CAACpH,UAAU,CAACwB,yCAAyC,EAAE;MAC/D6F,EAAE,EAAEnD,IAAI;MACRvD;IACF,CAAC,CAAC;EACJ;EAEAwgB,kBAAkBA,CAAA,EAAmB;IACnC,MAAMpa,QAAQ,GAAG,IAAI,CAACP,KAAK,CAACO,QAAQ;IACpC,MAAMqa,SAAS,GAAGA,CAAA,KAAM,IAAI,CAACzZ,KAAK,GAAS,CAAC,IAAI,IAAI,CAACA,KAAK,EAAU,CAAC;IACrE,QAAQ,IAAI,CAACnB,KAAK,CAACzC,IAAI;MACrB;QAAa;UACX,MAAMsd,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC9a,KAAK,CAACL,KAAK,CAAC;UAC1D,IAAIib,SAAS,CAAC,CAAC,EAAE;YACf,OAAO;cAAErd,IAAI,EAAE,QAAQ;cAAEoF,GAAG,EAAEkY,OAAO,CAAClY,GAAG,CAACC,KAAK;cAAEjD,KAAK,EAAEkb;YAAQ,CAAC;UACnE;UACA,OAAO;YAAEtd,IAAI,EAAE,SAAS;YAAEoF,GAAG,EAAEpC;UAAS,CAAC;QAC3C;MACA;QAAgB;UACd,MAAMsa,OAAO,GAAG,IAAI,CAACE,kBAAkB,CAAC,IAAI,CAAC/a,KAAK,CAACL,KAAK,CAAC;UACzD,IAAIib,SAAS,CAAC,CAAC,EAAE;YACf,OAAO;cAAErd,IAAI,EAAE,QAAQ;cAAEoF,GAAG,EAAEkY,OAAO,CAAClY,GAAG,CAACC,KAAK;cAAEjD,KAAK,EAAEkb;YAAQ,CAAC;UACnE;UACA,OAAO;YAAEtd,IAAI,EAAE,SAAS;YAAEoF,GAAG,EAAEpC;UAAS,CAAC;QAC3C;MACA;MACA;QAAgB;UACd,MAAMsa,OAAO,GAAG,IAAI,CAACG,mBAAmB,CAAC,IAAI,CAAC7Z,KAAK,GAAS,CAAC,CAAC;UAC9D,IAAIyZ,SAAS,CAAC,CAAC,EAAE;YACf,OAAO;cACLrd,IAAI,EAAE,SAAS;cACfoF,GAAG,EAAEkY,OAAO,CAAClY,GAAG,CAACC,KAAK;cACtBjD,KAAK,EAAEkb;YACT,CAAC;UACH;UACA,OAAO;YAAEtd,IAAI,EAAE,SAAS;YAAEoF,GAAG,EAAEpC;UAAS,CAAC;QAC3C;MACA;QACE,OAAO;UAAEhD,IAAI,EAAE,SAAS;UAAEoF,GAAG,EAAEpC;QAAS,CAAC;IAC7C;EACF;EAEA0a,iBAAiBA,CAAA,EAGf;IACA,MAAMtY,GAAG,GAAG,IAAI,CAAC3C,KAAK,CAACO,QAAQ;IAC/B,MAAMgB,EAAE,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC;IACrC,MAAM0Z,IAAI,GAAG,IAAI,CAACpa,GAAG,GAAM,CAAC,GACxB,IAAI,CAAC6Z,kBAAkB,CAAC,CAAC,GACzB;MAAEpd,IAAI,EAAE,MAAe;MAAEoF;IAAI,CAAC;IAClC,OAAO;MAAEpB,EAAE;MAAE2Z;IAAK,CAAC;EACrB;EAEAC,iCAAiCA,CAC/BxY,GAAa,EACb+T,OAAoB,EACpB0E,YAA8B,EACxB;IACN,MAAM;MAAE1gB;IAAa,CAAC,GAAGgc,OAAO;IAChC,IAAIhc,YAAY,KAAK,IAAI,EAAE;MACzB;IACF;IACA,IAAIA,YAAY,KAAK0gB,YAAY,EAAE;MACjC,IAAI,CAACb,qCAAqC,CAAC5X,GAAG,EAAE+T,OAAO,CAAC;IAC1D;EACF;EAEA2E,eAAeA,CAAC;IACdlhB,QAAQ;IACRO;EAIF,CAAC,EAQC;IACA,MAAM4gB,SAAS,GAAG,IAAI/hB,GAAG,CAAC,CAAC;IAC3B,MAAMgiB,OAAO,GAAG;MAEdC,cAAc,EAAE,EAAE;MAElBC,aAAa,EAAE,EAAE;MAEjBC,aAAa,EAAE,EAAE;MAEjBC,gBAAgB,EAAE;IACpB,CAAC;IACD,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,OAAO,CAAC,IAAI,CAACza,KAAK,EAAU,CAAC,EAAE;MAC7B,IAAI,IAAI,CAACL,GAAG,GAAY,CAAC,EAAE;QACzB8a,iBAAiB,GAAG,IAAI;QACxB;MACF;MACA,MAAMC,UAAU,GAAG,IAAI,CAACxb,SAAS,CAAC,CAAC;MACnC,MAAM;QAAEkB,EAAE;QAAE2Z;MAAK,CAAC,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;MAC7C,MAAM/gB,UAAU,GAAGqH,EAAE,CAACkB,IAAI;MAC1B,IAAIvI,UAAU,KAAK,EAAE,EAAE;QACrB;MACF;MACA,IAAI,QAAQ,CAACgE,IAAI,CAAChE,UAAU,CAAC,EAAE;QAC7B,IAAI,CAAC0G,KAAK,CAACpH,UAAU,CAACqB,qBAAqB,EAAE;UAC3CgG,EAAE,EAAEU,EAAE;UACNrH,UAAU;UACVY,UAAU,EAAEZ,UAAU,CAAC,CAAC,CAAC,CAAC4hB,WAAW,CAAC,CAAC,GAAG5hB,UAAU,CAACkgB,KAAK,CAAC,CAAC,CAAC;UAC7DjgB;QACF,CAAC,CAAC;MACJ;MACA,IAAImhB,SAAS,CAACnV,GAAG,CAACjM,UAAU,CAAC,EAAE;QAC7B,IAAI,CAAC0G,KAAK,CAACpH,UAAU,CAACY,uBAAuB,EAAE;UAC7CyG,EAAE,EAAEU,EAAE;UACNrH,UAAU;UACVC;QACF,CAAC,CAAC;MACJ;MACAmhB,SAAS,CAACS,GAAG,CAAC7hB,UAAU,CAAC;MACzB,MAAMwc,OAAO,GAAG;QAAEvc,QAAQ;QAAEO,YAAY;QAAER;MAAW,CAAC;MACtD2hB,UAAU,CAACta,EAAE,GAAGA,EAAE;MAClB,QAAQ2Z,IAAI,CAAC3d,IAAI;QACf,KAAK,SAAS;UAAE;YACd,IAAI,CAAC4d,iCAAiC,CACpCD,IAAI,CAACvY,GAAG,EACR+T,OAAO,EACP,SACF,CAAC;YACDmF,UAAU,CAACX,IAAI,GAAGA,IAAI,CAACvb,KAAK;YAC5B4b,OAAO,CAACC,cAAc,CAACjd,IAAI,CACzB,IAAI,CAACyC,UAAU,CAAC6a,UAAU,EAAE,mBAAmB,CACjD,CAAC;YACD;UACF;QACA,KAAK,QAAQ;UAAE;YACb,IAAI,CAACV,iCAAiC,CAACD,IAAI,CAACvY,GAAG,EAAE+T,OAAO,EAAE,QAAQ,CAAC;YACnEmF,UAAU,CAACX,IAAI,GAAGA,IAAI,CAACvb,KAAK;YAC5B4b,OAAO,CAACE,aAAa,CAACld,IAAI,CACxB,IAAI,CAACyC,UAAU,CAAC6a,UAAU,EAAE,kBAAkB,CAChD,CAAC;YACD;UACF;QACA,KAAK,QAAQ;UAAE;YACb,IAAI,CAACV,iCAAiC,CAACD,IAAI,CAACvY,GAAG,EAAE+T,OAAO,EAAE,QAAQ,CAAC;YACnEmF,UAAU,CAACX,IAAI,GAAGA,IAAI,CAACvb,KAAK;YAC5B4b,OAAO,CAACG,aAAa,CAACnd,IAAI,CACxB,IAAI,CAACyC,UAAU,CAAC6a,UAAU,EAAE,kBAAkB,CAChD,CAAC;YACD;UACF;QACA,KAAK,SAAS;UAAE;YACd,MAAM,IAAI,CAACtB,qCAAqC,CAACW,IAAI,CAACvY,GAAG,EAAE+T,OAAO,CAAC;UACrE;QACA,KAAK,MAAM;UAAE;YACX,QAAQhc,YAAY;cAClB,KAAK,SAAS;gBACZ,IAAI,CAAC4f,wCAAwC,CAC3CY,IAAI,CAACvY,GAAG,EACR+T,OACF,CAAC;gBACD;cACF,KAAK,QAAQ;gBACX,IAAI,CAAC+D,uCAAuC,CAACS,IAAI,CAACvY,GAAG,EAAE+T,OAAO,CAAC;gBAC/D;cACF;gBACE6E,OAAO,CAACI,gBAAgB,CAACpd,IAAI,CAC3B,IAAI,CAACyC,UAAU,CAAC6a,UAAU,EAAE,qBAAqB,CACnD,CAAC;YACL;UACF;MACF;MAEA,IAAI,CAAC,IAAI,CAAC1a,KAAK,EAAU,CAAC,EAAE;QAC1B,IAAI,CAACjB,MAAM,GAAS,CAAC;MACvB;IACF;IACA,OAAO;MAAEqb,OAAO;MAAEK;IAAkB,CAAC;EACvC;EAEAI,qBAAqBA,CACnBC,kBAAiC,EACjCN,gBAA+B,EAC/B;IACExhB;EAGF,CAAC,EACc;IACf,IAAI8hB,kBAAkB,CAAC3d,MAAM,KAAK,CAAC,EAAE;MACnC,OAAOqd,gBAAgB;IACzB,CAAC,MAAM,IAAIA,gBAAgB,CAACrd,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO2d,kBAAkB;IAC3B,CAAC,MAAM,IAAIN,gBAAgB,CAACrd,MAAM,GAAG2d,kBAAkB,CAAC3d,MAAM,EAAE;MAC9D,KAAK,MAAMqS,MAAM,IAAIsL,kBAAkB,EAAE;QACvC,IAAI,CAACvB,kDAAkD,CAAC/J,MAAM,EAAE;UAC9DxW;QACF,CAAC,CAAC;MACJ;MACA,OAAOwhB,gBAAgB;IACzB,CAAC,MAAM;MACL,KAAK,MAAMhL,MAAM,IAAIgL,gBAAgB,EAAE;QACrC,IAAI,CAACjB,kDAAkD,CAAC/J,MAAM,EAAE;UAC9DxW;QACF,CAAC,CAAC;MACJ;MACA,OAAO8hB,kBAAkB;IAC3B;EACF;EAEAC,yBAAyBA,CAAC;IACxB/hB;EAGF,CAAC,EAAoB;IACnB,IAAI,CAAC,IAAI,CAAC6I,aAAa,IAAO,CAAC,EAAE,OAAO,IAAI;IAE5C,IAAI,CAAC,IAAA6H,wBAAiB,EAAC,IAAI,CAAC7K,KAAK,CAACzC,IAAI,CAAC,EAAE;MACvC,MAAM,IAAI,CAACqD,KAAK,CAACpH,UAAU,CAACgB,sCAAsC,EAAE;QAClEqG,EAAE,EAAE,IAAI,CAACb,KAAK,CAACO,QAAQ;QACvBpG;MACF,CAAC,CAAC;IACJ;IAEA,MAAM;MAAEwF;IAAM,CAAC,GAAG,IAAI,CAACK,KAAK;IAC5B,IAAI,CAACQ,IAAI,CAAC,CAAC;IAEX,IACEb,KAAK,KAAK,SAAS,IACnBA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,QAAQ,EAClB;MACA,IAAI,CAACiB,KAAK,CAACpH,UAAU,CAACc,uBAAuB,EAAE;QAC7CuG,EAAE,EAAE,IAAI,CAACb,KAAK,CAACO,QAAQ;QACvBpG,QAAQ;QACRI,eAAe,EAAEoF;MACnB,CAAC,CAAC;IACJ;IAEA,OAAOA,KAAK;EACd;EAEAwc,YAAYA,CAACze,IAAoB,EAAE6D,EAAU,EAAU;IACrD,MAAMpH,QAAQ,GAAGoH,EAAE,CAACkB,IAAI;IACxB,MAAM2Z,OAAO,GAAG7a,EAAE,CAACoB,GAAG,CAACC,KAAK;IAC5B,MAAMlI,YAAY,GAAG,IAAI,CAACwhB,yBAAyB,CAAC;MAAE/hB;IAAS,CAAC,CAAC;IACjE,IAAI,CAAC+F,MAAM,EAAU,CAAC;IACtB,MAAM;MAAEqb,OAAO;MAAEK;IAAkB,CAAC,GAAG,IAAI,CAACP,eAAe,CAAC;MAC1DlhB,QAAQ;MACRO;IACF,CAAC,CAAC;IACFgD,IAAI,CAACke,iBAAiB,GAAGA,iBAAiB;IAE1C,QAAQlhB,YAAY;MAClB,KAAK,SAAS;QACZgD,IAAI,CAAChD,YAAY,GAAG,IAAI;QACxBgD,IAAI,CAAC6d,OAAO,GAAGA,OAAO,CAACC,cAAc;QACrC,IAAI,CAACtb,MAAM,EAAU,CAAC;QACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,iBAAiB,CAAC;MACjD,KAAK,QAAQ;QACXA,IAAI,CAAChD,YAAY,GAAG,IAAI;QACxBgD,IAAI,CAAC6d,OAAO,GAAGA,OAAO,CAACE,aAAa;QACpC,IAAI,CAACvb,MAAM,EAAU,CAAC;QACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;MAChD,KAAK,QAAQ;QACXA,IAAI,CAAChD,YAAY,GAAG,IAAI;QACxBgD,IAAI,CAAC6d,OAAO,GAAG,IAAI,CAACS,qBAAqB,CACvCT,OAAO,CAACG,aAAa,EACrBH,OAAO,CAACI,gBAAgB,EACxB;UAAExhB;QAAS,CACb,CAAC;QACD,IAAI,CAAC+F,MAAM,EAAU,CAAC;QACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;MAChD,KAAK,QAAQ;QACXA,IAAI,CAAC6d,OAAO,GAAGA,OAAO,CAACI,gBAAgB;QACvC,IAAI,CAACzb,MAAM,EAAU,CAAC;QACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;MAChD;QAAS;UAEP,MAAM2e,KAAK,GAAGA,CAAA,KAAM;YAClB3e,IAAI,CAAC6d,OAAO,GAAG,EAAE;YACjB,IAAI,CAACrb,MAAM,EAAU,CAAC;YACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;UAChD,CAAC;UACDA,IAAI,CAAChD,YAAY,GAAG,KAAK;UAEzB,MAAM4hB,QAAQ,GAAGf,OAAO,CAACC,cAAc,CAACld,MAAM;UAC9C,MAAMie,OAAO,GAAGhB,OAAO,CAACE,aAAa,CAACnd,MAAM;UAC5C,MAAMke,OAAO,GAAGjB,OAAO,CAACG,aAAa,CAACpd,MAAM;UAC5C,MAAMme,YAAY,GAAGlB,OAAO,CAACI,gBAAgB,CAACrd,MAAM;UAEpD,IAAI,CAACge,QAAQ,IAAI,CAACC,OAAO,IAAI,CAACC,OAAO,IAAI,CAACC,YAAY,EAAE;YACtD,OAAOJ,KAAK,CAAC,CAAC;UAChB,CAAC,MAAM,IAAI,CAACC,QAAQ,IAAI,CAACC,OAAO,EAAE;YAChC7e,IAAI,CAAC6d,OAAO,GAAG,IAAI,CAACS,qBAAqB,CACvCT,OAAO,CAACG,aAAa,EACrBH,OAAO,CAACI,gBAAgB,EACxB;cAAExhB;YAAS,CACb,CAAC;YACD,IAAI,CAAC+F,MAAM,EAAU,CAAC;YACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;UAChD,CAAC,MAAM,IAAI,CAAC6e,OAAO,IAAI,CAACC,OAAO,IAAIF,QAAQ,IAAIG,YAAY,EAAE;YAC3D,KAAK,MAAM9L,MAAM,IAAI4K,OAAO,CAACI,gBAAgB,EAAE;cAC7C,IAAI,CAACrB,wCAAwC,CAAC3J,MAAM,CAAChO,GAAG,CAACC,KAAK,EAAE;gBAC9DzI,QAAQ;gBACRD,UAAU,EAAEyW,MAAM,CAACpP,EAAE,CAACkB;cACxB,CAAC,CAAC;YACJ;YACA/E,IAAI,CAAC6d,OAAO,GAAGA,OAAO,CAACC,cAAc;YACrC,IAAI,CAACtb,MAAM,EAAU,CAAC;YACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,iBAAiB,CAAC;UACjD,CAAC,MAAM,IAAI,CAAC4e,QAAQ,IAAI,CAACE,OAAO,IAAID,OAAO,IAAIE,YAAY,EAAE;YAC3D,KAAK,MAAM9L,MAAM,IAAI4K,OAAO,CAACI,gBAAgB,EAAE;cAC7C,IAAI,CAAClB,uCAAuC,CAAC9J,MAAM,CAAChO,GAAG,CAACC,KAAK,EAAE;gBAC7DzI,QAAQ;gBACRD,UAAU,EAAEyW,MAAM,CAACpP,EAAE,CAACkB;cACxB,CAAC,CAAC;YACJ;YACA/E,IAAI,CAAC6d,OAAO,GAAGA,OAAO,CAACE,aAAa;YACpC,IAAI,CAACvb,MAAM,EAAU,CAAC;YACtB,OAAO,IAAI,CAACc,UAAU,CAACtD,IAAI,EAAE,gBAAgB,CAAC;UAChD,CAAC,MAAM;YACL,IAAI,CAACkD,KAAK,CAACpH,UAAU,CAACa,4BAA4B,EAAE;cAClDwG,EAAE,EAAEub,OAAO;cACXjiB;YACF,CAAC,CAAC;YACF,OAAOkiB,KAAK,CAAC,CAAC;UAChB;QACF;IACF;EACF;EAEAzP,wBAAwBA,CAAClP,IAAoB,EAAU;IACrD,MAAM6D,EAAE,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACjC9D,IAAI,CAAC6D,EAAE,GAAGA,EAAE;IACZ7D,IAAI,CAACsG,IAAI,GAAG,IAAI,CAACmY,YAAY,CAAC,IAAI,CAAC9b,SAAS,CAAC,CAAC,EAAEkB,EAAE,CAAC;IACnD,OAAO,IAAI,CAACP,UAAU,CAACtD,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAGAkb,mBAAmBA,CAAA,EAAY;IAC7B,MAAMpY,IAAI,GAAG,IAAI,CAACkc,cAAc,CAAC,CAAC;IAClC,IAAI,IAAI,CAACpL,KAAK,CAACC,UAAU,CAAC/Q,IAAI,CAAC,OAAuB,EAAE;MACtD,MAAMmc,SAAS,GAAG,IAAI,CAACrL,KAAK,CAACC,UAAU,CAAC/Q,IAAI,GAAG,CAAC,CAAC;MACjD,OACEmc,SAAS,OAAuB,IAAIA,SAAS,OAAuB;IAExE;IACA,OAAO,KAAK;EACd;EAEArF,6BAA6BA,CAAC5Z,IAAY,EAAE;IAC1C,OAAOA,IAAI,CAACH,IAAI,KAAK,oBAAoB,GAAGG,IAAI,CAACuO,UAAU,GAAGvO,IAAI;EACpE;AACF,CAAC;AAAAkf,OAAA,CAAAtY,OAAA,GAAA7F,QAAA"}