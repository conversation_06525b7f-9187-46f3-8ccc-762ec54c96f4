//this will affect all the git repos
git config --global core.excludesfile ~/.gitignore


//update files since .ignore won't if already tracked
git rm --cached <file>

# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs and databases #
######################
*.log
*.sql
*.sqlite

# OS generated files #
######################
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
# Icon?
ehthumbs.db
Thumbs.db
.cache
.project
.settings
.tmproj
*.esproj
nbproject

# Numerous always-ignore extensions #
#####################################
*.diff
*.err
*.orig
*.rej
*.swn
*.swo
*.swp
*.vi
*~
*.sass-cache
*.grunt
*.tmp

# Dreamweaver added files #
###########################
_notes
dwsync.xml

# Komodo #
###########################
*.komodoproject
.komodotools

# Node #
#####################
node_modules

# Bower #
#####################
bower_components

# Folders to ignore #
#####################
.hg
.svn
.CVS
intermediate
publish
.idea
.graphics
_test
_archive
uploads
tmp

# Vim files to ignore #
#######################
.VimballRecord
.netrwhist

bundle.*

_demo