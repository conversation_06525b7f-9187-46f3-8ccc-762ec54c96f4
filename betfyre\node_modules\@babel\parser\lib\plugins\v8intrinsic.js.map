{"version": 3, "names": ["_types", "require", "_default", "superClass", "V8IntrinsicMixin", "parseV8Intrinsic", "match", "v8IntrinsicStartLoc", "state", "startLoc", "node", "startNode", "next", "tokenIsIdentifier", "type", "name", "parseIdentifierName", "identifier", "createIdentifier", "unexpected", "parseExprAtom", "refExpressionErrors", "exports", "default"], "sources": ["../../src/plugins/v8intrinsic.ts"], "sourcesContent": ["import type Parser from \"../parser\";\nimport { tokenIsIdentifier, tt } from \"../tokenizer/types\";\nimport type * as N from \"../types\";\nimport type { ExpressionErrors } from \"../parser/util\";\n\nexport default (superClass: typeof Parser) =>\n  class V8IntrinsicMixin extends superClass implements Parser {\n    parseV8Intrinsic(): N.Expression {\n      if (this.match(tt.modulo)) {\n        const v8IntrinsicStartLoc = this.state.startLoc;\n        // let the `loc` of Identifier starts from `%`\n        const node = this.startNode<N.Identifier>();\n        this.next(); // eat '%'\n        if (tokenIsIdentifier(this.state.type)) {\n          const name = this.parseIdentifierName();\n          const identifier = this.createIdentifier(node, name);\n          // @ts-expect-error: avoid mutating AST types\n          identifier.type = \"V8IntrinsicIdentifier\";\n          if (this.match(tt.parenL)) {\n            return identifier;\n          }\n        }\n        this.unexpected(v8IntrinsicStartLoc);\n      }\n    }\n\n    /* ============================================================ *\n     * parser/expression.js                                         *\n     * ============================================================ */\n\n    parseExprAtom(refExpressionErrors?: ExpressionErrors | null): N.Expression {\n      return (\n        this.parseV8Intrinsic() || super.parseExprAtom(refExpressionErrors)\n      );\n    }\n  };\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAA2D,IAAAC,QAAA,GAI3CC,UAAyB,IACvC,MAAMC,gBAAgB,SAASD,UAAU,CAAmB;EAC1DE,gBAAgBA,CAAA,EAAiB;IAC/B,IAAI,IAAI,CAACC,KAAK,GAAU,CAAC,EAAE;MACzB,MAAMC,mBAAmB,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ;MAE/C,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAe,CAAC;MAC3C,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,IAAAC,wBAAiB,EAAC,IAAI,CAACL,KAAK,CAACM,IAAI,CAAC,EAAE;QACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;QACvC,MAAMC,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACR,IAAI,EAAEK,IAAI,CAAC;QAEpDE,UAAU,CAACH,IAAI,GAAG,uBAAuB;QACzC,IAAI,IAAI,CAACR,KAAK,GAAU,CAAC,EAAE;UACzB,OAAOW,UAAU;QACnB;MACF;MACA,IAAI,CAACE,UAAU,CAACZ,mBAAmB,CAAC;IACtC;EACF;EAMAa,aAAaA,CAACC,mBAA6C,EAAgB;IACzE,OACE,IAAI,CAAChB,gBAAgB,CAAC,CAAC,IAAI,KAAK,CAACe,aAAa,CAACC,mBAAmB,CAAC;EAEvE;AACF,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAArB,QAAA"}