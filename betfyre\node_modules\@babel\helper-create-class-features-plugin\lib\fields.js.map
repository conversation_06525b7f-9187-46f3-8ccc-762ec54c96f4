{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "_helperEnvironmentVisitor", "_helperMemberExpressionToFunctions", "_helperOptimiseCallExpression", "_helperAnnotateAsPure", "_helperSkipTransparentExpressionWrappers", "ts", "buildPrivateNamesMap", "props", "privateNamesMap", "Map", "prop", "isPrivate", "name", "node", "key", "id", "update", "has", "get", "scope", "generateUidIdentifier", "static", "method", "isProperty", "isClassPrivateMethod", "kind", "getId", "setId", "methodId", "set", "buildPrivateNamesNodes", "privateFieldsAsProperties", "privateFieldsAsSymbols", "state", "initNodes", "value", "isStatic", "isMethod", "isAccessor", "t", "cloneNode", "init", "callExpression", "addHelper", "stringLiteral", "identifier", "newExpression", "annotateAsPure", "push", "template", "statement", "ast", "privateNameVisitorFactory", "visitor", "nestedVisitor", "traverse", "visitors", "merge", "Object", "assign", "environmentVisitor", "privateNameVisitor", "Class", "path", "body", "visiblePrivateNames", "redeclared", "delete", "length", "<PERSON><PERSON><PERSON>", "PrivateName", "noDocumentAll", "parentPath", "isMemberExpression", "property", "isOptionalMemberExpression", "includes", "handle", "unshadow", "innerBinding", "_scope", "hasBinding", "bindingIdentifierEquals", "rename", "parent", "buildCheckInRHS", "rhs", "file", "inRHSIsObject", "availableHelper", "privateInVisitor", "BinaryExpression", "operator", "left", "right", "isPrivateName", "classRef", "replaceWith", "expression", "privateNameHandlerSpec", "memoise", "member", "count", "object", "memo", "maybeGenerateMemoised", "memoiser", "receiver", "helper<PERSON><PERSON>", "sequenceExpression", "console", "warn", "boundGet", "memberExpression", "destructureSet", "helper", "_unused", "Error", "call", "args", "optimiseCall", "optionalCall", "privateNameHandlerLoose", "BASE", "REF", "PROP", "simpleSet", "optionalCallExpression", "transformPrivateNamesUsage", "ref", "size", "handler", "memberExpressionToFunctions", "buildPrivateFieldInitLoose", "buildUndefinedNode", "inheritPropComments", "buildPrivateInstanceFieldInitSpec", "thisExpression", "buildPrivateStaticFieldInitSpec", "privateName", "initAdded", "buildPrivateMethodInitLoose", "buildPrivateInstanceMethodInitSpec", "buildPrivateAccessorInitialization", "buildPrivateInstanceMethodInitialization", "buildPublicFieldInitLoose", "computed", "expressionStatement", "assignmentExpression", "isLiteral", "buildPublicFieldInitSpec", "buildPrivateStaticMethodInitLoose", "buildPrivateMethodDeclaration", "getterDeclared", "setter<PERSON><PERSON><PERSON>ed", "params", "generator", "async", "isGetter", "isSetter", "declId", "functionDeclaration", "thisContextVisitor", "ThisExpression", "findParent", "isTransparentExprWrapper", "isUnaryExpression", "booleanLiteral", "needsClassRef", "MetaProperty", "meta", "isIdentifier", "innerReferencesVisitor", "ReferencedIdentifier", "replaceThisContext", "getSuperRef", "isStaticBlock", "constant<PERSON>uper", "innerBindingRef", "_state$classRef", "replacer", "ReplaceSupers", "methodPath", "refToPreserve", "getObjectRef", "replace", "isNameOrLength", "type", "inheritLeadingComments", "inheritInnerComments", "buildFieldsInitNodes", "superRef", "setPublicClassFields", "injectSuperRef", "staticNodes", "instanceNodes", "pureStaticNodes", "_injectSuperRef", "generateUidIdentifierBasedOnNode", "isClassProperty", "assertFieldTransformed", "isInstance", "isPublic", "isField", "replaced", "blockBody", "isExpressionStatement", "inheritsComments", "unshift", "filter", "Boolean", "wrapClass", "leadingComments", "remove", "superClass", "isClassExpression"], "sources": ["../src/fields.ts"], "sourcesContent": ["import { template, traverse, types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { NodePath, Visitor, Scope } from \"@babel/traverse\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport memberExpressionToFunctions from \"@babel/helper-member-expression-to-functions\";\nimport type {\n  Hand<PERSON>,\n  HandlerState,\n} from \"@babel/helper-member-expression-to-functions\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { isTransparentExprWrapper } from \"@babel/helper-skip-transparent-expression-wrappers\";\n\nimport * as ts from \"./typescript\";\n\ninterface PrivateNameMetadata {\n  id: t.Identifier;\n  static: boolean;\n  method: boolean;\n  getId?: t.Identifier;\n  setId?: t.Identifier;\n  methodId?: t.Identifier;\n  initAdded?: boolean;\n  getterDeclared?: boolean;\n  setterDeclared?: boolean;\n}\n\ntype PrivateNamesMap = Map<string, PrivateNameMetadata>;\n\nexport function buildPrivateNamesMap(props: PropPath[]) {\n  const privateNamesMap: PrivateNamesMap = new Map();\n  for (const prop of props) {\n    if (prop.isPrivate()) {\n      const { name } = prop.node.key.id;\n      const update: PrivateNameMetadata = privateNamesMap.has(name)\n        ? privateNamesMap.get(name)\n        : {\n            id: prop.scope.generateUidIdentifier(name),\n            static: prop.node.static,\n            method: !prop.isProperty(),\n          };\n      if (prop.isClassPrivateMethod()) {\n        if (prop.node.kind === \"get\") {\n          update.getId = prop.scope.generateUidIdentifier(`get_${name}`);\n        } else if (prop.node.kind === \"set\") {\n          update.setId = prop.scope.generateUidIdentifier(`set_${name}`);\n        } else if (prop.node.kind === \"method\") {\n          update.methodId = prop.scope.generateUidIdentifier(name);\n        }\n      }\n      privateNamesMap.set(name, update);\n    }\n  }\n  return privateNamesMap;\n}\n\nexport function buildPrivateNamesNodes(\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsProperties: boolean,\n  privateFieldsAsSymbols: boolean,\n  state: File,\n) {\n  const initNodes: t.Statement[] = [];\n\n  for (const [name, value] of privateNamesMap) {\n    // - When the privateFieldsAsProperties assumption is enabled,\n    //   both static and instance fields are transpiled using a\n    //   secret non-enumerable property. Hence, we also need to generate that\n    //   key (using the classPrivateFieldLooseKey helper).\n    // - When the privateFieldsAsSymbols assumption is enabled,\n    //   both static and instance fields are transpiled using a\n    //   unique Symbol to define a non-enumerable property.\n    // - In spec mode, only instance fields need a \"private name\" initializer\n    //   because static fields are directly assigned to a variable in the\n    //   buildPrivateStaticFieldInitSpec function.\n    const { static: isStatic, method: isMethod, getId, setId } = value;\n    const isAccessor = getId || setId;\n    const id = t.cloneNode(value.id);\n\n    let init: t.Expression;\n\n    if (privateFieldsAsProperties) {\n      init = t.callExpression(state.addHelper(\"classPrivateFieldLooseKey\"), [\n        t.stringLiteral(name),\n      ]);\n    } else if (privateFieldsAsSymbols) {\n      init = t.callExpression(t.identifier(\"Symbol\"), [t.stringLiteral(name)]);\n    } else if (!isStatic) {\n      init = t.newExpression(\n        t.identifier(!isMethod || isAccessor ? \"WeakMap\" : \"WeakSet\"),\n        [],\n      );\n    }\n\n    if (init) {\n      annotateAsPure(init);\n      initNodes.push(template.statement.ast`var ${id} = ${init}`);\n    }\n  }\n\n  return initNodes;\n}\n\ninterface PrivateNameVisitorState {\n  privateNamesMap: PrivateNamesMap;\n  privateFieldsAsProperties: boolean;\n  redeclared?: string[];\n}\n\n// Traverses the class scope, handling private name references. If an inner\n// class redeclares the same private name, it will hand off traversal to the\n// restricted visitor (which doesn't traverse the inner class's inner scope).\nfunction privateNameVisitorFactory<S>(\n  visitor: Visitor<PrivateNameVisitorState & S>,\n) {\n  // Traverses the outer portion of a class, without touching the class's inner\n  // scope, for private names.\n  const nestedVisitor = traverse.visitors.merge([\n    {\n      ...visitor,\n    },\n    environmentVisitor,\n  ]);\n\n  const privateNameVisitor: Visitor<PrivateNameVisitorState & S> = {\n    ...visitor,\n\n    Class(path) {\n      const { privateNamesMap } = this;\n      const body = path.get(\"body.body\");\n\n      const visiblePrivateNames = new Map(privateNamesMap);\n      const redeclared = [];\n      for (const prop of body) {\n        if (!prop.isPrivate()) continue;\n        const { name } = prop.node.key.id;\n        visiblePrivateNames.delete(name);\n        redeclared.push(name);\n      }\n\n      // If the class doesn't redeclare any private fields, we can continue with\n      // our overall traversal.\n      if (!redeclared.length) {\n        return;\n      }\n\n      // This class redeclares some private field. We need to process the outer\n      // environment with access to all the outer privates, then we can process\n      // the inner environment with only the still-visible outer privates.\n      path.get(\"body\").traverse(nestedVisitor, {\n        ...this,\n        redeclared,\n      });\n      path.traverse(privateNameVisitor, {\n        ...this,\n        privateNamesMap: visiblePrivateNames,\n      });\n\n      // We'll eventually hit this class node again with the overall Class\n      // Features visitor, which'll process the redeclared privates.\n      path.skipKey(\"body\");\n    },\n  };\n\n  return privateNameVisitor;\n}\n\ninterface PrivateNameState {\n  privateNamesMap: PrivateNamesMap;\n  classRef: t.Identifier;\n  file: File;\n  noDocumentAll: boolean;\n  innerBinding?: t.Identifier;\n}\n\nconst privateNameVisitor = privateNameVisitorFactory<\n  HandlerState<PrivateNameState> & PrivateNameState\n>({\n  PrivateName(path, { noDocumentAll }) {\n    const { privateNamesMap, redeclared } = this;\n    const { node, parentPath } = path;\n\n    if (\n      !parentPath.isMemberExpression({ property: node }) &&\n      !parentPath.isOptionalMemberExpression({ property: node })\n    ) {\n      return;\n    }\n    const { name } = node.id;\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared && redeclared.includes(name)) return;\n\n    this.handle(parentPath, noDocumentAll);\n  },\n});\n\n// rename all bindings that shadows innerBinding\nfunction unshadow(\n  name: string,\n  scope: Scope,\n  innerBinding: t.Identifier | undefined,\n) {\n  // in some cases, scope.getBinding(name) === undefined\n  // so we check hasBinding to avoid keeping looping\n  // see: https://github.com/babel/babel/pull/13656#discussion_r686030715\n  while (\n    scope?.hasBinding(name) &&\n    !scope.bindingIdentifierEquals(name, innerBinding)\n  ) {\n    scope.rename(name);\n    scope = scope.parent;\n  }\n}\n\nexport function buildCheckInRHS(\n  rhs: t.Expression,\n  file: File,\n  inRHSIsObject?: boolean,\n) {\n  if (inRHSIsObject || !file.availableHelper?.(\"checkInRHS\")) return rhs;\n  return t.callExpression(file.addHelper(\"checkInRHS\"), [rhs]);\n}\n\nconst privateInVisitor = privateNameVisitorFactory<{\n  classRef: t.Identifier;\n  file: File;\n  innerBinding?: t.Identifier;\n}>({\n  BinaryExpression(path, { file }) {\n    const { operator, left, right } = path.node;\n    if (operator !== \"in\") return;\n    if (!t.isPrivateName(left)) return;\n\n    const { privateFieldsAsProperties, privateNamesMap, redeclared } = this;\n\n    const { name } = left.id;\n\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared && redeclared.includes(name)) return;\n\n    // if there are any local variable shadowing classRef, unshadow it\n    // see #12960\n    unshadow(this.classRef.name, path.scope, this.innerBinding);\n\n    if (privateFieldsAsProperties) {\n      const { id } = privateNamesMap.get(name);\n      path.replaceWith(template.expression.ast`\n        Object.prototype.hasOwnProperty.call(${buildCheckInRHS(\n          right,\n          file,\n        )}, ${t.cloneNode(id)})\n      `);\n      return;\n    }\n\n    const { id, static: isStatic } = privateNamesMap.get(name);\n\n    if (isStatic) {\n      path.replaceWith(\n        template.expression.ast`${buildCheckInRHS(\n          right,\n          file,\n        )} === ${t.cloneNode(this.classRef)}`,\n      );\n      return;\n    }\n\n    path.replaceWith(\n      template.expression.ast`${t.cloneNode(id)}.has(${buildCheckInRHS(\n        right,\n        file,\n      )})`,\n    );\n  },\n});\n\ninterface Receiver {\n  receiver(\n    this: HandlerState<PrivateNameState> & PrivateNameState,\n    member: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n  ): t.Expression;\n}\n\nconst privateNameHandlerSpec: Handler<PrivateNameState & Receiver> & Receiver =\n  {\n    memoise(member, count) {\n      const { scope } = member;\n      const { object } = member.node as { object: t.Expression };\n\n      const memo = scope.maybeGenerateMemoised(object);\n      if (!memo) {\n        return;\n      }\n\n      this.memoiser.set(object, memo, count);\n    },\n\n    receiver(member) {\n      const { object } = member.node as { object: t.Expression };\n\n      if (this.memoiser.has(object)) {\n        return t.cloneNode(this.memoiser.get(object));\n      }\n\n      return t.cloneNode(object);\n    },\n\n    get(member) {\n      const { classRef, privateNamesMap, file, innerBinding } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        methodId,\n        getId,\n        setId,\n      } = privateNamesMap.get(name);\n      const isAccessor = getId || setId;\n\n      if (isStatic) {\n        const helperName =\n          isMethod && !isAccessor\n            ? \"classStaticPrivateMethodGet\"\n            : \"classStaticPrivateFieldSpecGet\";\n\n        // if there are any local variable shadowing classRef, unshadow it\n        // see #12960\n        unshadow(classRef.name, member.scope, innerBinding);\n\n        return t.callExpression(file.addHelper(helperName), [\n          this.receiver(member),\n          t.cloneNode(classRef),\n          t.cloneNode(id),\n        ]);\n      }\n\n      if (isMethod) {\n        if (isAccessor) {\n          if (!getId && setId) {\n            if (file.availableHelper(\"writeOnlyError\")) {\n              return t.sequenceExpression([\n                this.receiver(member),\n                t.callExpression(file.addHelper(\"writeOnlyError\"), [\n                  t.stringLiteral(`#${name}`),\n                ]),\n              ]);\n            }\n            console.warn(\n              `@babel/helpers is outdated, update it to silence this warning.`,\n            );\n          }\n          return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n            this.receiver(member),\n            t.cloneNode(id),\n          ]);\n        }\n        return t.callExpression(file.addHelper(\"classPrivateMethodGet\"), [\n          this.receiver(member),\n          t.cloneNode(id),\n          t.cloneNode(methodId),\n        ]);\n      }\n      return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n        this.receiver(member),\n        t.cloneNode(id),\n      ]);\n    },\n\n    boundGet(member) {\n      this.memoise(member, 1);\n\n      return t.callExpression(\n        t.memberExpression(this.get(member), t.identifier(\"bind\")),\n        [this.receiver(member)],\n      );\n    },\n\n    set(member, value) {\n      const { classRef, privateNamesMap, file } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        setId,\n        getId,\n      } = privateNamesMap.get(name);\n      const isAccessor = getId || setId;\n\n      if (isStatic) {\n        const helperName =\n          isMethod && !isAccessor\n            ? \"classStaticPrivateMethodSet\"\n            : \"classStaticPrivateFieldSpecSet\";\n\n        return t.callExpression(file.addHelper(helperName), [\n          this.receiver(member),\n          t.cloneNode(classRef),\n          t.cloneNode(id),\n          value,\n        ]);\n      }\n      if (isMethod) {\n        if (setId) {\n          return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n            this.receiver(member),\n            t.cloneNode(id),\n            value,\n          ]);\n        }\n        return t.sequenceExpression([\n          this.receiver(member),\n          value,\n          t.callExpression(file.addHelper(\"readOnlyError\"), [\n            t.stringLiteral(`#${name}`),\n          ]),\n        ]);\n      }\n      return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n        this.receiver(member),\n        t.cloneNode(id),\n        value,\n      ]);\n    },\n\n    destructureSet(member) {\n      const { classRef, privateNamesMap, file } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const { id, static: isStatic } = privateNamesMap.get(name);\n      if (isStatic) {\n        try {\n          // classStaticPrivateFieldDestructureSet was introduced in 7.13.10\n          // eslint-disable-next-line no-var\n          var helper = file.addHelper(\"classStaticPrivateFieldDestructureSet\");\n        } catch {\n          throw new Error(\n            \"Babel can not transpile `[C.#p] = [0]` with @babel/helpers < 7.13.10, \\n\" +\n              \"please update @babel/helpers to the latest version.\",\n          );\n        }\n        return t.memberExpression(\n          t.callExpression(helper, [\n            this.receiver(member),\n            t.cloneNode(classRef),\n            t.cloneNode(id),\n          ]),\n          t.identifier(\"value\"),\n        );\n      }\n\n      return t.memberExpression(\n        t.callExpression(file.addHelper(\"classPrivateFieldDestructureSet\"), [\n          this.receiver(member),\n          t.cloneNode(id),\n        ]),\n        t.identifier(\"value\"),\n      );\n    },\n\n    call(member, args: (t.Expression | t.SpreadElement)[]) {\n      // The first access (the get) should do the memo assignment.\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, false);\n    },\n\n    optionalCall(member, args: (t.Expression | t.SpreadElement)[]) {\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, true);\n    },\n\n    delete() {\n      throw new Error(\n        \"Internal Babel error: deleting private elements is a parsing error.\",\n      );\n    },\n  };\n\nconst privateNameHandlerLoose: Handler<PrivateNameState> = {\n  get(member) {\n    const { privateNamesMap, file } = this;\n    const { object } = member.node;\n    const { name } = (member.node.property as t.PrivateName).id;\n\n    return template.expression`BASE(REF, PROP)[PROP]`({\n      BASE: file.addHelper(\"classPrivateFieldLooseBase\"),\n      REF: t.cloneNode(object),\n      PROP: t.cloneNode(privateNamesMap.get(name).id),\n    });\n  },\n\n  set() {\n    // noop\n    throw new Error(\"private name handler with loose = true don't need set()\");\n  },\n\n  boundGet(member) {\n    return t.callExpression(\n      t.memberExpression(this.get(member), t.identifier(\"bind\")),\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n      [t.cloneNode(member.node.object as t.Expression)],\n    );\n  },\n\n  simpleSet(member) {\n    return this.get(member);\n  },\n\n  destructureSet(member) {\n    return this.get(member);\n  },\n\n  call(member, args) {\n    return t.callExpression(this.get(member), args);\n  },\n\n  optionalCall(member, args) {\n    return t.optionalCallExpression(this.get(member), args, true);\n  },\n\n  delete() {\n    throw new Error(\n      \"Internal Babel error: deleting private elements is a parsing error.\",\n    );\n  },\n};\n\nexport function transformPrivateNamesUsage(\n  ref: t.Identifier,\n  path: NodePath<t.Class>,\n  privateNamesMap: PrivateNamesMap,\n  {\n    privateFieldsAsProperties,\n    noDocumentAll,\n    innerBinding,\n  }: {\n    privateFieldsAsProperties: boolean;\n    noDocumentAll: boolean;\n    innerBinding: t.Identifier;\n  },\n  state: File,\n) {\n  if (!privateNamesMap.size) return;\n\n  const body = path.get(\"body\");\n  const handler = privateFieldsAsProperties\n    ? privateNameHandlerLoose\n    : privateNameHandlerSpec;\n\n  memberExpressionToFunctions<PrivateNameState>(body, privateNameVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    ...handler,\n    noDocumentAll,\n    innerBinding,\n  });\n  body.traverse(privateInVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    privateFieldsAsProperties,\n    innerBinding,\n  });\n}\n\nfunction buildPrivateFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    template.statement.ast`\n      Object.defineProperty(${ref}, ${t.cloneNode(id)}, {\n        // configurable is false by default\n        // enumerable is false by default\n        writable: true,\n        value: ${value}\n      });\n    `,\n    prop,\n  );\n}\n\nfunction buildPrivateInstanceFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`${t.cloneNode(id)}.set(${ref}, {\n          // configurable is always false for private elements\n          // enumerable is always false for private elements\n          writable: true,\n          value: ${value},\n        })`,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return inheritPropComments(\n    template.statement.ast`${helper}(\n      ${t.thisExpression()},\n      ${t.cloneNode(id)},\n      {\n        writable: true,\n        value: ${value}\n      },\n    )`,\n    prop,\n  );\n}\n\nfunction buildPrivateStaticFieldInitSpec(\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, getId, setId, initAdded } = privateName;\n  const isAccessor = getId || setId;\n\n  if (!prop.isProperty() && (initAdded || !isAccessor)) return;\n\n  if (isAccessor) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return inheritPropComments(\n      template.statement.ast`\n        var ${t.cloneNode(id)} = {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n          set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n        }\n      `,\n      prop,\n    );\n  }\n\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n  return inheritPropComments(\n    template.statement.ast`\n      var ${t.cloneNode(id)} = {\n        // configurable is false by default\n        // enumerable is false by default\n        writable: true,\n        value: ${value}\n      };\n    `,\n    prop,\n  );\n}\n\nfunction buildPrivateMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { methodId, id, getId, setId, initAdded } = privateName;\n  if (initAdded) return;\n\n  if (methodId) {\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          value: ${methodId.name}\n        });\n      `,\n      prop,\n    );\n  }\n  const isAccessor = getId || setId;\n  if (isAccessor) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n          set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n        });\n      `,\n      prop,\n    );\n  }\n}\n\nfunction buildPrivateInstanceMethodInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { getId, setId, initAdded } = privateName;\n\n  if (initAdded) return;\n\n  const isAccessor = getId || setId;\n  if (isAccessor) {\n    return buildPrivateAccessorInitialization(\n      ref,\n      prop,\n      privateNamesMap,\n      state,\n    );\n  }\n\n  return buildPrivateInstanceMethodInitialization(\n    ref,\n    prop,\n    privateNamesMap,\n    state,\n  );\n}\n\nfunction buildPrivateAccessorInitialization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, getId, setId } = privateName;\n\n  privateNamesMap.set(prop.node.key.id.name, {\n    ...privateName,\n    initAdded: true,\n  });\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`\n          ${id}.set(${ref}, {\n            get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n            set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n          });\n        `,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return inheritPropComments(\n    template.statement.ast`${helper}(\n      ${t.thisExpression()},\n      ${t.cloneNode(id)},\n      {\n        get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n        set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n      },\n    )`,\n    prop,\n  );\n}\n\nfunction buildPrivateInstanceMethodInitialization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id } = privateName;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateMethodInitSpec\")) {\n      return inheritPropComments(\n        template.statement.ast`${id}.add(${ref})`,\n        prop,\n      );\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateMethodInitSpec\");\n  return inheritPropComments(\n    template.statement.ast`${helper}(\n      ${t.thisExpression()},\n      ${t.cloneNode(id)}\n    )`,\n    prop,\n  );\n}\n\nfunction buildPublicFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(ref, key, computed || t.isLiteral(key)),\n        value,\n      ),\n    ),\n    prop,\n  );\n}\n\nfunction buildPublicFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n  state: File,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return inheritPropComments(\n    t.expressionStatement(\n      t.callExpression(state.addHelper(\"defineProperty\"), [\n        ref,\n        computed || t.isLiteral(key)\n          ? key\n          : t.stringLiteral((key as t.Identifier).name),\n        value,\n      ]),\n    ),\n    prop,\n  );\n}\n\nfunction buildPrivateStaticMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  state: File,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, methodId, getId, setId, initAdded } = privateName;\n\n  if (initAdded) return;\n\n  const isAccessor = getId || setId;\n  if (isAccessor) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return inheritPropComments(\n      template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n          set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n        })\n      `,\n      prop,\n    );\n  }\n\n  return inheritPropComments(\n    template.statement.ast`\n      Object.defineProperty(${ref}, ${id}, {\n        // configurable is false by default\n        // enumerable is false by default\n        // writable is false by default\n        value: ${methodId.name}\n      });\n    `,\n    prop,\n  );\n}\n\nfunction buildPrivateMethodDeclaration(\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsProperties = false,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const {\n    id,\n    methodId,\n    getId,\n    setId,\n    getterDeclared,\n    setterDeclared,\n    static: isStatic,\n  } = privateName;\n  const { params, body, generator, async } = prop.node;\n  const isGetter = getId && !getterDeclared && params.length === 0;\n  const isSetter = setId && !setterDeclared && params.length > 0;\n\n  let declId = methodId;\n\n  if (isGetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      getterDeclared: true,\n    });\n    declId = getId;\n  } else if (isSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      setterDeclared: true,\n    });\n    declId = setId;\n  } else if (isStatic && !privateFieldsAsProperties) {\n    declId = id;\n  }\n\n  return inheritPropComments(\n    t.functionDeclaration(\n      t.cloneNode(declId),\n      // @ts-expect-error params for ClassMethod has TSParameterProperty\n      params,\n      body,\n      generator,\n      async,\n    ),\n    prop,\n  );\n}\n\ntype ReplaceThisState = {\n  classRef: t.Identifier;\n  needsClassRef: boolean;\n  innerBinding: t.Identifier | null;\n};\n\nconst thisContextVisitor = traverse.visitors.merge<ReplaceThisState>([\n  {\n    ThisExpression(path, state) {\n      // Replace `delete this` with `true`\n      const parent = path.findParent(\n        path => !isTransparentExprWrapper(path.node),\n      );\n      if (t.isUnaryExpression(parent.node, { operator: \"delete\" })) {\n        path.parentPath.replaceWith(t.booleanLiteral(true));\n        return;\n      }\n\n      state.needsClassRef = true;\n      path.replaceWith(t.cloneNode(state.classRef));\n    },\n    MetaProperty(path) {\n      const meta = path.get(\"meta\");\n      const property = path.get(\"property\");\n      const { scope } = path;\n      // if there are `new.target` in static field\n      // we should replace it with `undefined`\n      if (\n        meta.isIdentifier({ name: \"new\" }) &&\n        property.isIdentifier({ name: \"target\" })\n      ) {\n        path.replaceWith(scope.buildUndefinedNode());\n      }\n    },\n  },\n  environmentVisitor,\n]);\n\nconst innerReferencesVisitor: Visitor<ReplaceThisState> = {\n  ReferencedIdentifier(path, state) {\n    if (\n      path.scope.bindingIdentifierEquals(path.node.name, state.innerBinding)\n    ) {\n      state.needsClassRef = true;\n      path.node.name = state.classRef.name;\n    }\n  },\n};\n\nfunction replaceThisContext(\n  path: PropPath,\n  ref: t.Identifier,\n  getSuperRef: () => t.Identifier,\n  file: File,\n  isStaticBlock: boolean,\n  constantSuper: boolean,\n  innerBindingRef: t.Identifier | null,\n) {\n  const state: ReplaceThisState = {\n    classRef: ref,\n    needsClassRef: false,\n    innerBinding: innerBindingRef,\n  };\n\n  const replacer = new ReplaceSupers({\n    methodPath: path,\n    constantSuper,\n    file,\n    refToPreserve: ref,\n    getSuperRef,\n    getObjectRef() {\n      state.needsClassRef = true;\n      // @ts-expect-error: TS doesn't infer that path.node is not a StaticBlock\n      return t.isStaticBlock?.(path.node) || path.node.static\n        ? ref\n        : t.memberExpression(ref, t.identifier(\"prototype\"));\n    },\n  });\n  replacer.replace();\n  if (isStaticBlock || path.isProperty()) {\n    path.traverse(thisContextVisitor, state);\n  }\n\n  // todo: use innerBinding.referencePaths to avoid full traversal\n  if (\n    innerBindingRef != null &&\n    state.classRef?.name &&\n    state.classRef.name !== innerBindingRef?.name\n  ) {\n    path.traverse(innerReferencesVisitor, state);\n  }\n\n  return state.needsClassRef;\n}\n\nexport type PropNode =\n  | t.ClassProperty\n  | t.ClassPrivateMethod\n  | t.ClassPrivateProperty\n  | t.StaticBlock;\nexport type PropPath = NodePath<PropNode>;\n\nfunction isNameOrLength({ key, computed }: t.ClassProperty) {\n  if (key.type === \"Identifier\") {\n    return !computed && (key.name === \"name\" || key.name === \"length\");\n  }\n  if (key.type === \"StringLiteral\") {\n    return key.value === \"name\" || key.value === \"length\";\n  }\n  return false;\n}\n\n/**\n * Inherit comments from class members. This is a reduced version of\n * t.inheritsComments: the trailing comments are not inherited because\n * for most class members except the last one, their trailing comments are\n * the next sibling's leading comments.\n *\n * @template T transformed class member type\n * @param {T} node transformed class member\n * @param {PropPath} prop class member\n * @returns transformed class member type with comments inherited\n */\nfunction inheritPropComments<T extends t.Node>(node: T, prop: PropPath) {\n  t.inheritLeadingComments(node, prop.node);\n  t.inheritInnerComments(node, prop.node);\n  return node;\n}\n\nexport function buildFieldsInitNodes(\n  ref: t.Identifier,\n  superRef: t.Expression | undefined,\n  props: PropPath[],\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n  setPublicClassFields: boolean,\n  privateFieldsAsProperties: boolean,\n  constantSuper: boolean,\n  innerBindingRef: t.Identifier,\n) {\n  let needsClassRef = false;\n  let injectSuperRef: t.Identifier;\n  const staticNodes: t.Statement[] = [];\n  const instanceNodes: t.Statement[] = [];\n  // These nodes are pure and can be moved to the closest statement position\n  const pureStaticNodes: t.FunctionDeclaration[] = [];\n\n  const getSuperRef = t.isIdentifier(superRef)\n    ? () => superRef\n    : () => {\n        injectSuperRef ??=\n          props[0].scope.generateUidIdentifierBasedOnNode(superRef);\n        return injectSuperRef;\n      };\n\n  for (const prop of props) {\n    prop.isClassProperty() && ts.assertFieldTransformed(prop);\n\n    // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n    const isStatic = !t.isStaticBlock?.(prop.node) && prop.node.static;\n    const isInstance = !isStatic;\n    const isPrivate = prop.isPrivate();\n    const isPublic = !isPrivate;\n    const isField = prop.isProperty();\n    const isMethod = !isField;\n    const isStaticBlock = prop.isStaticBlock?.();\n\n    if (isStatic || (isMethod && isPrivate) || isStaticBlock) {\n      const replaced = replaceThisContext(\n        prop,\n        ref,\n        getSuperRef,\n        state,\n        isStaticBlock,\n        constantSuper,\n        innerBindingRef,\n      );\n      needsClassRef = needsClassRef || replaced;\n    }\n\n    // TODO(ts): there are so many `ts-expect-error` inside cases since\n    // ts can not infer type from pre-computed values (or a case test)\n    // even change `isStaticBlock` to `t.isStaticBlock(prop)` will not make prop\n    // a `NodePath<t.StaticBlock>`\n    // this maybe a bug for ts\n    switch (true) {\n      case isStaticBlock: {\n        const blockBody = (prop.node as t.StaticBlock).body;\n        // We special-case the single expression case to avoid the iife, since\n        // it's common.\n        if (blockBody.length === 1 && t.isExpressionStatement(blockBody[0])) {\n          staticNodes.push(inheritPropComments(blockBody[0], prop));\n        } else {\n          staticNodes.push(\n            t.inheritsComments(\n              template.statement.ast`(() => { ${blockBody} })()`,\n              prop.node,\n            ),\n          );\n        }\n        break;\n      }\n      case isStatic && isPrivate && isField && privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.push(\n          // @ts-expect-error checked in switch\n          buildPrivateFieldInitLoose(t.cloneNode(ref), prop, privateNamesMap),\n        );\n        break;\n      case isStatic && isPrivate && isField && !privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.push(\n          // @ts-expect-error checked in switch\n          buildPrivateStaticFieldInitSpec(prop, privateNamesMap),\n        );\n        break;\n      case isStatic && isPublic && isField && setPublicClassFields:\n        // Functions always have non-writable .name and .length properties,\n        // so we must always use [[Define]] for them.\n        // It might still be possible to a computed static fields whose resulting\n        // key is \"name\" or \"length\", but the assumption is telling us that it's\n        // not going to happen.\n        // @ts-expect-error checked in switch\n        if (!isNameOrLength(prop.node)) {\n          needsClassRef = true;\n          // @ts-expect-error checked in switch\n          staticNodes.push(buildPublicFieldInitLoose(t.cloneNode(ref), prop));\n          break;\n        }\n      // falls through\n      case isStatic && isPublic && isField && !setPublicClassFields:\n        needsClassRef = true;\n        staticNodes.push(\n          // @ts-expect-error checked in switch\n          buildPublicFieldInitSpec(t.cloneNode(ref), prop, state),\n        );\n        break;\n      case isInstance && isPrivate && isField && privateFieldsAsProperties:\n        instanceNodes.push(\n          // @ts-expect-error checked in switch\n          buildPrivateFieldInitLoose(t.thisExpression(), prop, privateNamesMap),\n        );\n        break;\n      case isInstance && isPrivate && isField && !privateFieldsAsProperties:\n        instanceNodes.push(\n          buildPrivateInstanceFieldInitSpec(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            state,\n          ),\n        );\n        break;\n      case isInstance && isPrivate && isMethod && privateFieldsAsProperties:\n        instanceNodes.unshift(\n          buildPrivateMethodInitLoose(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isInstance && isPrivate && isMethod && !privateFieldsAsProperties:\n        instanceNodes.unshift(\n          buildPrivateInstanceMethodInitSpec(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            state,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isStatic && isPrivate && isMethod && !privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.unshift(\n          // @ts-expect-error checked in switch\n          buildPrivateStaticFieldInitSpec(prop, privateNamesMap),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isStatic && isPrivate && isMethod && privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.unshift(\n          buildPrivateStaticMethodInitLoose(\n            t.cloneNode(ref),\n            // @ts-expect-error checked in switch\n            prop,\n            state,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isInstance && isPublic && isField && setPublicClassFields:\n        // @ts-expect-error checked in switch\n        instanceNodes.push(buildPublicFieldInitLoose(t.thisExpression(), prop));\n        break;\n      case isInstance && isPublic && isField && !setPublicClassFields:\n        instanceNodes.push(\n          // @ts-expect-error checked in switch\n          buildPublicFieldInitSpec(t.thisExpression(), prop, state),\n        );\n        break;\n      default:\n        throw new Error(\"Unreachable.\");\n    }\n  }\n\n  return {\n    staticNodes: staticNodes.filter(Boolean),\n    instanceNodes: instanceNodes.filter(Boolean),\n    pureStaticNodes: pureStaticNodes.filter(Boolean),\n    wrapClass(path: NodePath<t.Class>) {\n      for (const prop of props) {\n        // Delete leading comments so that they don't get attached as\n        // trailing comments of the previous sibling.\n        // When transforming props, we explicitly attach their leading\n        // comments to the transformed node with `inheritPropComments`\n        // above.\n        prop.node.leadingComments = null;\n        prop.remove();\n      }\n\n      if (injectSuperRef) {\n        path.scope.push({ id: t.cloneNode(injectSuperRef) });\n        path.set(\n          \"superClass\",\n          t.assignmentExpression(\"=\", injectSuperRef, path.node.superClass),\n        );\n      }\n\n      if (!needsClassRef) return path;\n\n      if (path.isClassExpression()) {\n        path.scope.push({ id: ref });\n        path.replaceWith(\n          t.assignmentExpression(\"=\", t.cloneNode(ref), path.node),\n        );\n      } else if (!path.node.id) {\n        // Anonymous class declaration\n        path.node.id = ref;\n      }\n\n      return path;\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,kCAAA,GAAAH,OAAA;AAKA,IAAAI,6BAAA,GAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AACA,IAAAM,wCAAA,GAAAN,OAAA;AAEA,IAAAO,EAAA,GAAAP,OAAA;AAgBO,SAASQ,oBAAoBA,CAACC,KAAiB,EAAE;EACtD,MAAMC,eAAgC,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClD,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;IACxB,IAAIG,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;MACpB,MAAM;QAAEC;MAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE;MACjC,MAAMC,MAA2B,GAAGR,eAAe,CAACS,GAAG,CAACL,IAAI,CAAC,GACzDJ,eAAe,CAACU,GAAG,CAACN,IAAI,CAAC,GACzB;QACEG,EAAE,EAAEL,IAAI,CAACS,KAAK,CAACC,qBAAqB,CAACR,IAAI,CAAC;QAC1CS,MAAM,EAAEX,IAAI,CAACG,IAAI,CAACQ,MAAM;QACxBC,MAAM,EAAE,CAACZ,IAAI,CAACa,UAAU,CAAC;MAC3B,CAAC;MACL,IAAIb,IAAI,CAACc,oBAAoB,CAAC,CAAC,EAAE;QAC/B,IAAId,IAAI,CAACG,IAAI,CAACY,IAAI,KAAK,KAAK,EAAE;UAC5BT,MAAM,CAACU,KAAK,GAAGhB,IAAI,CAACS,KAAK,CAACC,qBAAqB,CAAE,OAAMR,IAAK,EAAC,CAAC;QAChE,CAAC,MAAM,IAAIF,IAAI,CAACG,IAAI,CAACY,IAAI,KAAK,KAAK,EAAE;UACnCT,MAAM,CAACW,KAAK,GAAGjB,IAAI,CAACS,KAAK,CAACC,qBAAqB,CAAE,OAAMR,IAAK,EAAC,CAAC;QAChE,CAAC,MAAM,IAAIF,IAAI,CAACG,IAAI,CAACY,IAAI,KAAK,QAAQ,EAAE;UACtCT,MAAM,CAACY,QAAQ,GAAGlB,IAAI,CAACS,KAAK,CAACC,qBAAqB,CAACR,IAAI,CAAC;QAC1D;MACF;MACAJ,eAAe,CAACqB,GAAG,CAACjB,IAAI,EAAEI,MAAM,CAAC;IACnC;EACF;EACA,OAAOR,eAAe;AACxB;AAEO,SAASsB,sBAAsBA,CACpCtB,eAAgC,EAChCuB,yBAAkC,EAClCC,sBAA+B,EAC/BC,KAAW,EACX;EACA,MAAMC,SAAwB,GAAG,EAAE;EAEnC,KAAK,MAAM,CAACtB,IAAI,EAAEuB,KAAK,CAAC,IAAI3B,eAAe,EAAE;IAW3C,MAAM;MAAEa,MAAM,EAAEe,QAAQ;MAAEd,MAAM,EAAEe,QAAQ;MAAEX,KAAK;MAAEC;IAAM,CAAC,GAAGQ,KAAK;IAClE,MAAMG,UAAU,GAAGZ,KAAK,IAAIC,KAAK;IACjC,MAAMZ,EAAE,GAAGwB,WAAC,CAACC,SAAS,CAACL,KAAK,CAACpB,EAAE,CAAC;IAEhC,IAAI0B,IAAkB;IAEtB,IAAIV,yBAAyB,EAAE;MAC7BU,IAAI,GAAGF,WAAC,CAACG,cAAc,CAACT,KAAK,CAACU,SAAS,CAAC,2BAA2B,CAAC,EAAE,CACpEJ,WAAC,CAACK,aAAa,CAAChC,IAAI,CAAC,CACtB,CAAC;IACJ,CAAC,MAAM,IAAIoB,sBAAsB,EAAE;MACjCS,IAAI,GAAGF,WAAC,CAACG,cAAc,CAACH,WAAC,CAACM,UAAU,CAAC,QAAQ,CAAC,EAAE,CAACN,WAAC,CAACK,aAAa,CAAChC,IAAI,CAAC,CAAC,CAAC;IAC1E,CAAC,MAAM,IAAI,CAACwB,QAAQ,EAAE;MACpBK,IAAI,GAAGF,WAAC,CAACO,aAAa,CACpBP,WAAC,CAACM,UAAU,CAAC,CAACR,QAAQ,IAAIC,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC,EAC7D,EACF,CAAC;IACH;IAEA,IAAIG,IAAI,EAAE;MACR,IAAAM,6BAAc,EAACN,IAAI,CAAC;MACpBP,SAAS,CAACc,IAAI,CAACC,cAAQ,CAACC,SAAS,CAACC,GAAI,OAAMpC,EAAG,MAAK0B,IAAK,EAAC,CAAC;IAC7D;EACF;EAEA,OAAOP,SAAS;AAClB;AAWA,SAASkB,yBAAyBA,CAChCC,OAA6C,EAC7C;EAGA,MAAMC,aAAa,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAAAC,MAAA,CAAAC,MAAA,KAEvCN,OAAO,GAEZO,iCAAkB,CACnB,CAAC;EAEF,MAAMC,kBAAwD,GAAAH,MAAA,CAAAC,MAAA,KACzDN,OAAO;IAEVS,KAAKA,CAACC,IAAI,EAAE;MACV,MAAM;QAAEvD;MAAgB,CAAC,GAAG,IAAI;MAChC,MAAMwD,IAAI,GAAGD,IAAI,CAAC7C,GAAG,CAAC,WAAW,CAAC;MAElC,MAAM+C,mBAAmB,GAAG,IAAIxD,GAAG,CAACD,eAAe,CAAC;MACpD,MAAM0D,UAAU,GAAG,EAAE;MACrB,KAAK,MAAMxD,IAAI,IAAIsD,IAAI,EAAE;QACvB,IAAI,CAACtD,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;QACvB,MAAM;UAAEC;QAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE;QACjCkD,mBAAmB,CAACE,MAAM,CAACvD,IAAI,CAAC;QAChCsD,UAAU,CAAClB,IAAI,CAACpC,IAAI,CAAC;MACvB;MAIA,IAAI,CAACsD,UAAU,CAACE,MAAM,EAAE;QACtB;MACF;MAKAL,IAAI,CAAC7C,GAAG,CAAC,MAAM,CAAC,CAACqC,QAAQ,CAACD,aAAa,EAAAI,MAAA,CAAAC,MAAA,KAClC,IAAI;QACPO;MAAU,EACX,CAAC;MACFH,IAAI,CAACR,QAAQ,CAACM,kBAAkB,EAAAH,MAAA,CAAAC,MAAA,KAC3B,IAAI;QACPnD,eAAe,EAAEyD;MAAmB,EACrC,CAAC;MAIFF,IAAI,CAACM,OAAO,CAAC,MAAM,CAAC;IACtB;EAAC,EACF;EAED,OAAOR,kBAAkB;AAC3B;AAUA,MAAMA,kBAAkB,GAAGT,yBAAyB,CAElD;EACAkB,WAAWA,CAACP,IAAI,EAAE;IAAEQ;EAAc,CAAC,EAAE;IACnC,MAAM;MAAE/D,eAAe;MAAE0D;IAAW,CAAC,GAAG,IAAI;IAC5C,MAAM;MAAErD,IAAI;MAAE2D;IAAW,CAAC,GAAGT,IAAI;IAEjC,IACE,CAACS,UAAU,CAACC,kBAAkB,CAAC;MAAEC,QAAQ,EAAE7D;IAAK,CAAC,CAAC,IAClD,CAAC2D,UAAU,CAACG,0BAA0B,CAAC;MAAED,QAAQ,EAAE7D;IAAK,CAAC,CAAC,EAC1D;MACA;IACF;IACA,MAAM;MAAED;IAAK,CAAC,GAAGC,IAAI,CAACE,EAAE;IACxB,IAAI,CAACP,eAAe,CAACS,GAAG,CAACL,IAAI,CAAC,EAAE;IAChC,IAAIsD,UAAU,IAAIA,UAAU,CAACU,QAAQ,CAAChE,IAAI,CAAC,EAAE;IAE7C,IAAI,CAACiE,MAAM,CAACL,UAAU,EAAED,aAAa,CAAC;EACxC;AACF,CAAC,CAAC;AAGF,SAASO,QAAQA,CACflE,IAAY,EACZO,KAAY,EACZ4D,YAAsC,EACtC;EAIA,OACE,CAAAC,MAAA,GAAA7D,KAAK,aAAL6D,MAAA,CAAOC,UAAU,CAACrE,IAAI,CAAC,IACvB,CAACO,KAAK,CAAC+D,uBAAuB,CAACtE,IAAI,EAAEmE,YAAY,CAAC,EAClD;IAAA,IAAAC,MAAA;IACA7D,KAAK,CAACgE,MAAM,CAACvE,IAAI,CAAC;IAClBO,KAAK,GAAGA,KAAK,CAACiE,MAAM;EACtB;AACF;AAEO,SAASC,eAAeA,CAC7BC,GAAiB,EACjBC,IAAU,EACVC,aAAuB,EACvB;EACA,IAAIA,aAAa,IAAI,EAACD,IAAI,CAACE,eAAe,YAApBF,IAAI,CAACE,eAAe,CAAG,YAAY,CAAC,GAAE,OAAOH,GAAG;EACtE,OAAO/C,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC2C,GAAG,CAAC,CAAC;AAC9D;AAEA,MAAMI,gBAAgB,GAAGtC,yBAAyB,CAI/C;EACDuC,gBAAgBA,CAAC5B,IAAI,EAAE;IAAEwB;EAAK,CAAC,EAAE;IAC/B,MAAM;MAAEK,QAAQ;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAG/B,IAAI,CAAClD,IAAI;IAC3C,IAAI+E,QAAQ,KAAK,IAAI,EAAE;IACvB,IAAI,CAACrD,WAAC,CAACwD,aAAa,CAACF,IAAI,CAAC,EAAE;IAE5B,MAAM;MAAE9D,yBAAyB;MAAEvB,eAAe;MAAE0D;IAAW,CAAC,GAAG,IAAI;IAEvE,MAAM;MAAEtD;IAAK,CAAC,GAAGiF,IAAI,CAAC9E,EAAE;IAExB,IAAI,CAACP,eAAe,CAACS,GAAG,CAACL,IAAI,CAAC,EAAE;IAChC,IAAIsD,UAAU,IAAIA,UAAU,CAACU,QAAQ,CAAChE,IAAI,CAAC,EAAE;IAI7CkE,QAAQ,CAAC,IAAI,CAACkB,QAAQ,CAACpF,IAAI,EAAEmD,IAAI,CAAC5C,KAAK,EAAE,IAAI,CAAC4D,YAAY,CAAC;IAE3D,IAAIhD,yBAAyB,EAAE;MAC7B,MAAM;QAAEhB;MAAG,CAAC,GAAGP,eAAe,CAACU,GAAG,CAACN,IAAI,CAAC;MACxCmD,IAAI,CAACkC,WAAW,CAAChD,cAAQ,CAACiD,UAAU,CAAC/C,GAAI;AAC/C,+CAA+CkC,eAAe,CACpDS,KAAK,EACLP,IACF,CAAE,KAAIhD,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE;AAC9B,OAAO,CAAC;MACF;IACF;IAEA,MAAM;MAAEA,EAAE;MAAEM,MAAM,EAAEe;IAAS,CAAC,GAAG5B,eAAe,CAACU,GAAG,CAACN,IAAI,CAAC;IAE1D,IAAIwB,QAAQ,EAAE;MACZ2B,IAAI,CAACkC,WAAW,CACdhD,cAAQ,CAACiD,UAAU,CAAC/C,GAAI,GAAEkC,eAAe,CACvCS,KAAK,EACLP,IACF,CAAE,QAAOhD,WAAC,CAACC,SAAS,CAAC,IAAI,CAACwD,QAAQ,CAAE,EACtC,CAAC;MACD;IACF;IAEAjC,IAAI,CAACkC,WAAW,CACdhD,cAAQ,CAACiD,UAAU,CAAC/C,GAAI,GAAEZ,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE,QAAOsE,eAAe,CAC9DS,KAAK,EACLP,IACF,CAAE,GACJ,CAAC;EACH;AACF,CAAC,CAAC;AASF,MAAMY,sBAAuE,GAC3E;EACEC,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACrB,MAAM;MAAEnF;IAAM,CAAC,GAAGkF,MAAM;IACxB,MAAM;MAAEE;IAAO,CAAC,GAAGF,MAAM,CAACxF,IAAgC;IAE1D,MAAM2F,IAAI,GAAGrF,KAAK,CAACsF,qBAAqB,CAACF,MAAM,CAAC;IAChD,IAAI,CAACC,IAAI,EAAE;MACT;IACF;IAEA,IAAI,CAACE,QAAQ,CAAC7E,GAAG,CAAC0E,MAAM,EAAEC,IAAI,EAAEF,KAAK,CAAC;EACxC,CAAC;EAEDK,QAAQA,CAACN,MAAM,EAAE;IACf,MAAM;MAAEE;IAAO,CAAC,GAAGF,MAAM,CAACxF,IAAgC;IAE1D,IAAI,IAAI,CAAC6F,QAAQ,CAACzF,GAAG,CAACsF,MAAM,CAAC,EAAE;MAC7B,OAAOhE,WAAC,CAACC,SAAS,CAAC,IAAI,CAACkE,QAAQ,CAACxF,GAAG,CAACqF,MAAM,CAAC,CAAC;IAC/C;IAEA,OAAOhE,WAAC,CAACC,SAAS,CAAC+D,MAAM,CAAC;EAC5B,CAAC;EAEDrF,GAAGA,CAACmF,MAAM,EAAE;IACV,MAAM;MAAEL,QAAQ;MAAExF,eAAe;MAAE+E,IAAI;MAAER;IAAa,CAAC,GAAG,IAAI;IAC9D,MAAM;MAAEnE;IAAK,CAAC,GAAIyF,MAAM,CAACxF,IAAI,CAAC6D,QAAQ,CAAmB3D,EAAE;IAC3D,MAAM;MACJA,EAAE;MACFM,MAAM,EAAEe,QAAQ;MAChBd,MAAM,EAAEe,QAAQ;MAChBT,QAAQ;MACRF,KAAK;MACLC;IACF,CAAC,GAAGnB,eAAe,CAACU,GAAG,CAACN,IAAI,CAAC;IAC7B,MAAM0B,UAAU,GAAGZ,KAAK,IAAIC,KAAK;IAEjC,IAAIS,QAAQ,EAAE;MACZ,MAAMwE,UAAU,GACdvE,QAAQ,IAAI,CAACC,UAAU,GACnB,6BAA6B,GAC7B,gCAAgC;MAItCwC,QAAQ,CAACkB,QAAQ,CAACpF,IAAI,EAAEyF,MAAM,CAAClF,KAAK,EAAE4D,YAAY,CAAC;MAEnD,OAAOxC,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAACiE,UAAU,CAAC,EAAE,CAClD,IAAI,CAACD,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACwD,QAAQ,CAAC,EACrBzD,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,CAChB,CAAC;IACJ;IAEA,IAAIsB,QAAQ,EAAE;MACZ,IAAIC,UAAU,EAAE;QACd,IAAI,CAACZ,KAAK,IAAIC,KAAK,EAAE;UACnB,IAAI4D,IAAI,CAACE,eAAe,CAAC,gBAAgB,CAAC,EAAE;YAC1C,OAAOlD,WAAC,CAACsE,kBAAkB,CAAC,CAC1B,IAAI,CAACF,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,gBAAgB,CAAC,EAAE,CACjDJ,WAAC,CAACK,aAAa,CAAE,IAAGhC,IAAK,EAAC,CAAC,CAC5B,CAAC,CACH,CAAC;UACJ;UACAkG,OAAO,CAACC,IAAI,CACT,gEACH,CAAC;QACH;QACA,OAAOxE,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACgE,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,CAChB,CAAC;MACJ;MACA,OAAOwB,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,uBAAuB,CAAC,EAAE,CAC/D,IAAI,CAACgE,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,EACfwB,WAAC,CAACC,SAAS,CAACZ,QAAQ,CAAC,CACtB,CAAC;IACJ;IACA,OAAOW,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACgE,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,CAChB,CAAC;EACJ,CAAC;EAEDiG,QAAQA,CAACX,MAAM,EAAE;IACf,IAAI,CAACD,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO9D,WAAC,CAACG,cAAc,CACrBH,WAAC,CAAC0E,gBAAgB,CAAC,IAAI,CAAC/F,GAAG,CAACmF,MAAM,CAAC,EAAE9D,WAAC,CAACM,UAAU,CAAC,MAAM,CAAC,CAAC,EAC1D,CAAC,IAAI,CAAC8D,QAAQ,CAACN,MAAM,CAAC,CACxB,CAAC;EACH,CAAC;EAEDxE,GAAGA,CAACwE,MAAM,EAAElE,KAAK,EAAE;IACjB,MAAM;MAAE6D,QAAQ;MAAExF,eAAe;MAAE+E;IAAK,CAAC,GAAG,IAAI;IAChD,MAAM;MAAE3E;IAAK,CAAC,GAAIyF,MAAM,CAACxF,IAAI,CAAC6D,QAAQ,CAAmB3D,EAAE;IAC3D,MAAM;MACJA,EAAE;MACFM,MAAM,EAAEe,QAAQ;MAChBd,MAAM,EAAEe,QAAQ;MAChBV,KAAK;MACLD;IACF,CAAC,GAAGlB,eAAe,CAACU,GAAG,CAACN,IAAI,CAAC;IAC7B,MAAM0B,UAAU,GAAGZ,KAAK,IAAIC,KAAK;IAEjC,IAAIS,QAAQ,EAAE;MACZ,MAAMwE,UAAU,GACdvE,QAAQ,IAAI,CAACC,UAAU,GACnB,6BAA6B,GAC7B,gCAAgC;MAEtC,OAAOC,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAACiE,UAAU,CAAC,EAAE,CAClD,IAAI,CAACD,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACwD,QAAQ,CAAC,EACrBzD,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,EACfoB,KAAK,CACN,CAAC;IACJ;IACA,IAAIE,QAAQ,EAAE;MACZ,IAAIV,KAAK,EAAE;QACT,OAAOY,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACgE,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,EACfoB,KAAK,CACN,CAAC;MACJ;MACA,OAAOI,WAAC,CAACsE,kBAAkB,CAAC,CAC1B,IAAI,CAACF,QAAQ,CAACN,MAAM,CAAC,EACrBlE,KAAK,EACLI,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,eAAe,CAAC,EAAE,CAChDJ,WAAC,CAACK,aAAa,CAAE,IAAGhC,IAAK,EAAC,CAAC,CAC5B,CAAC,CACH,CAAC;IACJ;IACA,OAAO2B,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,sBAAsB,CAAC,EAAE,CAC9D,IAAI,CAACgE,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,EACfoB,KAAK,CACN,CAAC;EACJ,CAAC;EAED+E,cAAcA,CAACb,MAAM,EAAE;IACrB,MAAM;MAAEL,QAAQ;MAAExF,eAAe;MAAE+E;IAAK,CAAC,GAAG,IAAI;IAChD,MAAM;MAAE3E;IAAK,CAAC,GAAIyF,MAAM,CAACxF,IAAI,CAAC6D,QAAQ,CAAmB3D,EAAE;IAC3D,MAAM;MAAEA,EAAE;MAAEM,MAAM,EAAEe;IAAS,CAAC,GAAG5B,eAAe,CAACU,GAAG,CAACN,IAAI,CAAC;IAC1D,IAAIwB,QAAQ,EAAE;MACZ,IAAI;QAGF,IAAI+E,MAAM,GAAG5B,IAAI,CAAC5C,SAAS,CAAC,uCAAuC,CAAC;MACtE,CAAC,CAAC,OAAAyE,OAAA,EAAM;QACN,MAAM,IAAIC,KAAK,CACb,0EAA0E,GACxE,qDACJ,CAAC;MACH;MACA,OAAO9E,WAAC,CAAC0E,gBAAgB,CACvB1E,WAAC,CAACG,cAAc,CAACyE,MAAM,EAAE,CACvB,IAAI,CAACR,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACwD,QAAQ,CAAC,EACrBzD,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,CAChB,CAAC,EACFwB,WAAC,CAACM,UAAU,CAAC,OAAO,CACtB,CAAC;IACH;IAEA,OAAON,WAAC,CAAC0E,gBAAgB,CACvB1E,WAAC,CAACG,cAAc,CAAC6C,IAAI,CAAC5C,SAAS,CAAC,iCAAiC,CAAC,EAAE,CAClE,IAAI,CAACgE,QAAQ,CAACN,MAAM,CAAC,EACrB9D,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAC,CAChB,CAAC,EACFwB,WAAC,CAACM,UAAU,CAAC,OAAO,CACtB,CAAC;EACH,CAAC;EAEDyE,IAAIA,CAACjB,MAAM,EAAEkB,IAAwC,EAAE;IAErD,IAAI,CAACnB,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO,IAAAmB,qCAAY,EAAC,IAAI,CAACtG,GAAG,CAACmF,MAAM,CAAC,EAAE,IAAI,CAACM,QAAQ,CAACN,MAAM,CAAC,EAAEkB,IAAI,EAAE,KAAK,CAAC;EAC3E,CAAC;EAEDE,YAAYA,CAACpB,MAAM,EAAEkB,IAAwC,EAAE;IAC7D,IAAI,CAACnB,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC;IAEvB,OAAO,IAAAmB,qCAAY,EAAC,IAAI,CAACtG,GAAG,CAACmF,MAAM,CAAC,EAAE,IAAI,CAACM,QAAQ,CAACN,MAAM,CAAC,EAAEkB,IAAI,EAAE,IAAI,CAAC;EAC1E,CAAC;EAEDpD,MAAMA,CAAA,EAAG;IACP,MAAM,IAAIkD,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAEH,MAAMK,uBAAkD,GAAG;EACzDxG,GAAGA,CAACmF,MAAM,EAAE;IACV,MAAM;MAAE7F,eAAe;MAAE+E;IAAK,CAAC,GAAG,IAAI;IACtC,MAAM;MAAEgB;IAAO,CAAC,GAAGF,MAAM,CAACxF,IAAI;IAC9B,MAAM;MAAED;IAAK,CAAC,GAAIyF,MAAM,CAACxF,IAAI,CAAC6D,QAAQ,CAAmB3D,EAAE;IAE3D,OAAOkC,cAAQ,CAACiD,UAAW,uBAAsB,CAAC;MAChDyB,IAAI,EAAEpC,IAAI,CAAC5C,SAAS,CAAC,4BAA4B,CAAC;MAClDiF,GAAG,EAAErF,WAAC,CAACC,SAAS,CAAC+D,MAAM,CAAC;MACxBsB,IAAI,EAAEtF,WAAC,CAACC,SAAS,CAAChC,eAAe,CAACU,GAAG,CAACN,IAAI,CAAC,CAACG,EAAE;IAChD,CAAC,CAAC;EACJ,CAAC;EAEDc,GAAGA,CAAA,EAAG;IAEJ,MAAM,IAAIwF,KAAK,CAAC,yDAAyD,CAAC;EAC5E,CAAC;EAEDL,QAAQA,CAACX,MAAM,EAAE;IACf,OAAO9D,WAAC,CAACG,cAAc,CACrBH,WAAC,CAAC0E,gBAAgB,CAAC,IAAI,CAAC/F,GAAG,CAACmF,MAAM,CAAC,EAAE9D,WAAC,CAACM,UAAU,CAAC,MAAM,CAAC,CAAC,EAE1D,CAACN,WAAC,CAACC,SAAS,CAAC6D,MAAM,CAACxF,IAAI,CAAC0F,MAAsB,CAAC,CAClD,CAAC;EACH,CAAC;EAEDuB,SAASA,CAACzB,MAAM,EAAE;IAChB,OAAO,IAAI,CAACnF,GAAG,CAACmF,MAAM,CAAC;EACzB,CAAC;EAEDa,cAAcA,CAACb,MAAM,EAAE;IACrB,OAAO,IAAI,CAACnF,GAAG,CAACmF,MAAM,CAAC;EACzB,CAAC;EAEDiB,IAAIA,CAACjB,MAAM,EAAEkB,IAAI,EAAE;IACjB,OAAOhF,WAAC,CAACG,cAAc,CAAC,IAAI,CAACxB,GAAG,CAACmF,MAAM,CAAC,EAAEkB,IAAI,CAAC;EACjD,CAAC;EAEDE,YAAYA,CAACpB,MAAM,EAAEkB,IAAI,EAAE;IACzB,OAAOhF,WAAC,CAACwF,sBAAsB,CAAC,IAAI,CAAC7G,GAAG,CAACmF,MAAM,CAAC,EAAEkB,IAAI,EAAE,IAAI,CAAC;EAC/D,CAAC;EAEDpD,MAAMA,CAAA,EAAG;IACP,MAAM,IAAIkD,KAAK,CACb,qEACF,CAAC;EACH;AACF,CAAC;AAEM,SAASW,0BAA0BA,CACxCC,GAAiB,EACjBlE,IAAuB,EACvBvD,eAAgC,EAChC;EACEuB,yBAAyB;EACzBwC,aAAa;EACbQ;AAKF,CAAC,EACD9C,KAAW,EACX;EACA,IAAI,CAACzB,eAAe,CAAC0H,IAAI,EAAE;EAE3B,MAAMlE,IAAI,GAAGD,IAAI,CAAC7C,GAAG,CAAC,MAAM,CAAC;EAC7B,MAAMiH,OAAO,GAAGpG,yBAAyB,GACrC2F,uBAAuB,GACvBvB,sBAAsB;EAE1B,IAAAiC,0CAA2B,EAAmBpE,IAAI,EAAEH,kBAAkB,EAAAH,MAAA,CAAAC,MAAA;IACpEnD,eAAe;IACfwF,QAAQ,EAAEiC,GAAG;IACb1C,IAAI,EAAEtD;EAAK,GACRkG,OAAO;IACV5D,aAAa;IACbQ;EAAY,EACb,CAAC;EACFf,IAAI,CAACT,QAAQ,CAACmC,gBAAgB,EAAE;IAC9BlF,eAAe;IACfwF,QAAQ,EAAEiC,GAAG;IACb1C,IAAI,EAAEtD,KAAK;IACXF,yBAAyB;IACzBgD;EACF,CAAC,CAAC;AACJ;AAEA,SAASsD,0BAA0BA,CACjCJ,GAAiB,EACjBvH,IAAsC,EACtCF,eAAgC,EAChC;EACA,MAAM;IAAEO;EAAG,CAAC,GAAGP,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EACzD,MAAMuB,KAAK,GAAGzB,IAAI,CAACG,IAAI,CAACsB,KAAK,IAAIzB,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAC;EAEhE,OAAOC,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC3B,8BAA8B8E,GAAI,KAAI1F,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE;AACtD;AACA;AACA;AACA,iBAAiBoB,KAAM;AACvB;AACA,KAAK,EACDzB,IACF,CAAC;AACH;AAEA,SAAS8H,iCAAiCA,CACxCP,GAAiB,EACjBvH,IAAsC,EACtCF,eAAgC,EAChCyB,KAAW,EACX;EACA,MAAM;IAAElB;EAAG,CAAC,GAAGP,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EACzD,MAAMuB,KAAK,GAAGzB,IAAI,CAACG,IAAI,CAACsB,KAAK,IAAIzB,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAC;EAE7B;IACjC,IAAI,CAACrG,KAAK,CAACwD,eAAe,CAAC,2BAA2B,CAAC,EAAE;MACvD,OAAO8C,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEZ,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE,QAAOkH,GAAI;AAC5D;AACA;AACA;AACA,mBAAmB9F,KAAM;AACzB,WAAW,EACHzB,IACF,CAAC;IACH;EACF;EAEA,MAAMyG,MAAM,GAAGlF,KAAK,CAACU,SAAS,CAAC,2BAA2B,CAAC;EAC3D,OAAO4F,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEgE,MAAO;AACpC,QAAQ5E,WAAC,CAACkG,cAAc,CAAC,CAAE;AAC3B,QAAQlG,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE;AACxB;AACA;AACA,iBAAiBoB,KAAM;AACvB;AACA,MAAM,EACFzB,IACF,CAAC;AACH;AAEA,SAASgI,+BAA+BA,CACtChI,IAAsC,EACtCF,eAAgC,EAChC;EACA,MAAMmI,WAAW,GAAGnI,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG,EAAE;IAAEW,KAAK;IAAEC,KAAK;IAAEiH;EAAU,CAAC,GAAGD,WAAW;EACnD,MAAMrG,UAAU,GAAGZ,KAAK,IAAIC,KAAK;EAEjC,IAAI,CAACjB,IAAI,CAACa,UAAU,CAAC,CAAC,KAAKqH,SAAS,IAAI,CAACtG,UAAU,CAAC,EAAE;EAEtD,IAAIA,UAAU,EAAE;IACd9B,eAAe,CAACqB,GAAG,CAACnB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAA8C,MAAA,CAAAC,MAAA,KACpCgF,WAAW;MACdC,SAAS,EAAE;IAAI,EAChB,CAAC;IAEF,OAAOL,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,cAAcZ,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE;AAC9B;AACA;AACA;AACA,iBAAiBW,KAAK,GAAGA,KAAK,CAACd,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACtE,iBAAiB3G,KAAK,GAAGA,KAAK,CAACf,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACtE;AACA,OAAO,EACD5H,IACF,CAAC;EACH;EAEA,MAAMyB,KAAK,GAAGzB,IAAI,CAACG,IAAI,CAACsB,KAAK,IAAIzB,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAC;EAChE,OAAOC,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC3B,YAAYZ,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE;AAC5B;AACA;AACA;AACA,iBAAiBoB,KAAM;AACvB;AACA,KAAK,EACDzB,IACF,CAAC;AACH;AAEA,SAASmI,2BAA2BA,CAClCZ,GAAiB,EACjBvH,IAAoC,EACpCF,eAAgC,EAChC;EACA,MAAMmI,WAAW,GAAGnI,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEgB,QAAQ;IAAEb,EAAE;IAAEW,KAAK;IAAEC,KAAK;IAAEiH;EAAU,CAAC,GAAGD,WAAW;EAC7D,IAAIC,SAAS,EAAE;EAEf,IAAIhH,QAAQ,EAAE;IACZ,OAAO2G,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,gCAAgC8E,GAAI,KAAIlH,EAAG;AAC3C;AACA;AACA;AACA,mBAAmBa,QAAQ,CAAChB,IAAK;AACjC;AACA,OAAO,EACDF,IACF,CAAC;EACH;EACA,MAAM4B,UAAU,GAAGZ,KAAK,IAAIC,KAAK;EACjC,IAAIW,UAAU,EAAE;IACd9B,eAAe,CAACqB,GAAG,CAACnB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAA8C,MAAA,CAAAC,MAAA,KACpCgF,WAAW;MACdC,SAAS,EAAE;IAAI,EAChB,CAAC;IAEF,OAAOL,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,gCAAgC8E,GAAI,KAAIlH,EAAG;AAC3C;AACA;AACA;AACA,iBAAiBW,KAAK,GAAGA,KAAK,CAACd,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACtE,iBAAiB3G,KAAK,GAAGA,KAAK,CAACf,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACtE;AACA,OAAO,EACD5H,IACF,CAAC;EACH;AACF;AAEA,SAASoI,kCAAkCA,CACzCb,GAAiB,EACjBvH,IAAoC,EACpCF,eAAgC,EAChCyB,KAAW,EACX;EACA,MAAM0G,WAAW,GAAGnI,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEc,KAAK;IAAEC,KAAK;IAAEiH;EAAU,CAAC,GAAGD,WAAW;EAE/C,IAAIC,SAAS,EAAE;EAEf,MAAMtG,UAAU,GAAGZ,KAAK,IAAIC,KAAK;EACjC,IAAIW,UAAU,EAAE;IACd,OAAOyG,kCAAkC,CACvCd,GAAG,EACHvH,IAAI,EACJF,eAAe,EACfyB,KACF,CAAC;EACH;EAEA,OAAO+G,wCAAwC,CAC7Cf,GAAG,EACHvH,IAAI,EACJF,eAAe,EACfyB,KACF,CAAC;AACH;AAEA,SAAS8G,kCAAkCA,CACzCd,GAAiB,EACjBvH,IAAoC,EACpCF,eAAgC,EAChCyB,KAAW,EACX;EACA,MAAM0G,WAAW,GAAGnI,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG,EAAE;IAAEW,KAAK;IAAEC;EAAM,CAAC,GAAGgH,WAAW;EAExCnI,eAAe,CAACqB,GAAG,CAACnB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAA8C,MAAA,CAAAC,MAAA,KACpCgF,WAAW;IACdC,SAAS,EAAE;EAAI,EAChB,CAAC;EAEiC;IACjC,IAAI,CAAC3G,KAAK,CAACwD,eAAe,CAAC,2BAA2B,CAAC,EAAE;MACvD,OAAO8C,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC/B,YAAYpC,EAAG,QAAOkH,GAAI;AAC1B,mBAAmBvG,KAAK,GAAGA,KAAK,CAACd,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACxE,mBAAmB3G,KAAK,GAAGA,KAAK,CAACf,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACxE;AACA,SAAS,EACD5H,IACF,CAAC;IACH;EACF;EAEA,MAAMyG,MAAM,GAAGlF,KAAK,CAACU,SAAS,CAAC,2BAA2B,CAAC;EAC3D,OAAO4F,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEgE,MAAO;AACpC,QAAQ5E,WAAC,CAACkG,cAAc,CAAC,CAAE;AAC3B,QAAQlG,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE;AACxB;AACA,eAAeW,KAAK,GAAGA,KAAK,CAACd,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACpE,eAAe3G,KAAK,GAAGA,KAAK,CAACf,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACpE;AACA,MAAM,EACF5H,IACF,CAAC;AACH;AAEA,SAASsI,wCAAwCA,CAC/Cf,GAAiB,EACjBvH,IAAoC,EACpCF,eAAgC,EAChCyB,KAAW,EACX;EACA,MAAM0G,WAAW,GAAGnI,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG;EAAG,CAAC,GAAG4H,WAAW;EAES;IACjC,IAAI,CAAC1G,KAAK,CAACwD,eAAe,CAAC,4BAA4B,CAAC,EAAE;MACxD,OAAO8C,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEpC,EAAG,QAAOkH,GAAI,GAAE,EACzCvH,IACF,CAAC;IACH;EACF;EAEA,MAAMyG,MAAM,GAAGlF,KAAK,CAACU,SAAS,CAAC,4BAA4B,CAAC;EAC5D,OAAO4F,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI,GAAEgE,MAAO;AACpC,QAAQ5E,WAAC,CAACkG,cAAc,CAAC,CAAE;AAC3B,QAAQlG,WAAC,CAACC,SAAS,CAACzB,EAAE,CAAE;AACxB,MAAM,EACFL,IACF,CAAC;AACH;AAEA,SAASuI,yBAAyBA,CAChChB,GAAiB,EACjBvH,IAA+B,EAC/B;EACA,MAAM;IAAEI,GAAG;IAAEoI;EAAS,CAAC,GAAGxI,IAAI,CAACG,IAAI;EACnC,MAAMsB,KAAK,GAAGzB,IAAI,CAACG,IAAI,CAACsB,KAAK,IAAIzB,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAC;EAEhE,OAAOC,mBAAmB,CACxBhG,WAAC,CAAC4G,mBAAmB,CACnB5G,WAAC,CAAC6G,oBAAoB,CACpB,GAAG,EACH7G,WAAC,CAAC0E,gBAAgB,CAACgB,GAAG,EAAEnH,GAAG,EAAEoI,QAAQ,IAAI3G,WAAC,CAAC8G,SAAS,CAACvI,GAAG,CAAC,CAAC,EAC1DqB,KACF,CACF,CAAC,EACDzB,IACF,CAAC;AACH;AAEA,SAAS4I,wBAAwBA,CAC/BrB,GAAiB,EACjBvH,IAA+B,EAC/BuB,KAAW,EACX;EACA,MAAM;IAAEnB,GAAG;IAAEoI;EAAS,CAAC,GAAGxI,IAAI,CAACG,IAAI;EACnC,MAAMsB,KAAK,GAAGzB,IAAI,CAACG,IAAI,CAACsB,KAAK,IAAIzB,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAC;EAEhE,OAAOC,mBAAmB,CACxBhG,WAAC,CAAC4G,mBAAmB,CACnB5G,WAAC,CAACG,cAAc,CAACT,KAAK,CAACU,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAClDsF,GAAG,EACHiB,QAAQ,IAAI3G,WAAC,CAAC8G,SAAS,CAACvI,GAAG,CAAC,GACxBA,GAAG,GACHyB,WAAC,CAACK,aAAa,CAAE9B,GAAG,CAAkBF,IAAI,CAAC,EAC/CuB,KAAK,CACN,CACH,CAAC,EACDzB,IACF,CAAC;AACH;AAEA,SAAS6I,iCAAiCA,CACxCtB,GAAiB,EACjBvH,IAAoC,EACpCuB,KAAW,EACXzB,eAAgC,EAChC;EACA,MAAMmI,WAAW,GAAGnI,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IAAEG,EAAE;IAAEa,QAAQ;IAAEF,KAAK;IAAEC,KAAK;IAAEiH;EAAU,CAAC,GAAGD,WAAW;EAE7D,IAAIC,SAAS,EAAE;EAEf,MAAMtG,UAAU,GAAGZ,KAAK,IAAIC,KAAK;EACjC,IAAIW,UAAU,EAAE;IACd9B,eAAe,CAACqB,GAAG,CAACnB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAA8C,MAAA,CAAAC,MAAA,KACpCgF,WAAW;MACdC,SAAS,EAAE;IAAI,EAChB,CAAC;IAEF,OAAOL,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC7B,gCAAgC8E,GAAI,KAAIlH,EAAG;AAC3C;AACA;AACA;AACA,iBAAiBW,KAAK,GAAGA,KAAK,CAACd,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACtE,iBAAiB3G,KAAK,GAAGA,KAAK,CAACf,IAAI,GAAGF,IAAI,CAACS,KAAK,CAACmH,kBAAkB,CAAC,CAAE;AACtE;AACA,OAAO,EACD5H,IACF,CAAC;EACH;EAEA,OAAO6H,mBAAmB,CACxBtF,cAAQ,CAACC,SAAS,CAACC,GAAI;AAC3B,8BAA8B8E,GAAI,KAAIlH,EAAG;AACzC;AACA;AACA;AACA,iBAAiBa,QAAQ,CAAChB,IAAK;AAC/B;AACA,KAAK,EACDF,IACF,CAAC;AACH;AAEA,SAAS8I,6BAA6BA,CACpC9I,IAAoC,EACpCF,eAAgC,EAChCuB,yBAAyB,GAAG,KAAK,EACjC;EACA,MAAM4G,WAAW,GAAGnI,eAAe,CAACU,GAAG,CAACR,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,CAAC;EAC9D,MAAM;IACJG,EAAE;IACFa,QAAQ;IACRF,KAAK;IACLC,KAAK;IACL8H,cAAc;IACdC,cAAc;IACdrI,MAAM,EAAEe;EACV,CAAC,GAAGuG,WAAW;EACf,MAAM;IAAEgB,MAAM;IAAE3F,IAAI;IAAE4F,SAAS;IAAEC;EAAM,CAAC,GAAGnJ,IAAI,CAACG,IAAI;EACpD,MAAMiJ,QAAQ,GAAGpI,KAAK,IAAI,CAAC+H,cAAc,IAAIE,MAAM,CAACvF,MAAM,KAAK,CAAC;EAChE,MAAM2F,QAAQ,GAAGpI,KAAK,IAAI,CAAC+H,cAAc,IAAIC,MAAM,CAACvF,MAAM,GAAG,CAAC;EAE9D,IAAI4F,MAAM,GAAGpI,QAAQ;EAErB,IAAIkI,QAAQ,EAAE;IACZtJ,eAAe,CAACqB,GAAG,CAACnB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAA8C,MAAA,CAAAC,MAAA,KACpCgF,WAAW;MACdc,cAAc,EAAE;IAAI,EACrB,CAAC;IACFO,MAAM,GAAGtI,KAAK;EAChB,CAAC,MAAM,IAAIqI,QAAQ,EAAE;IACnBvJ,eAAe,CAACqB,GAAG,CAACnB,IAAI,CAACG,IAAI,CAACC,GAAG,CAACC,EAAE,CAACH,IAAI,EAAA8C,MAAA,CAAAC,MAAA,KACpCgF,WAAW;MACde,cAAc,EAAE;IAAI,EACrB,CAAC;IACFM,MAAM,GAAGrI,KAAK;EAChB,CAAC,MAAM,IAAIS,QAAQ,IAAI,CAACL,yBAAyB,EAAE;IACjDiI,MAAM,GAAGjJ,EAAE;EACb;EAEA,OAAOwH,mBAAmB,CACxBhG,WAAC,CAAC0H,mBAAmB,CACnB1H,WAAC,CAACC,SAAS,CAACwH,MAAM,CAAC,EAEnBL,MAAM,EACN3F,IAAI,EACJ4F,SAAS,EACTC,KACF,CAAC,EACDnJ,IACF,CAAC;AACH;AAQA,MAAMwJ,kBAAkB,GAAG3G,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAmB,CACnE;EACE0G,cAAcA,CAACpG,IAAI,EAAE9B,KAAK,EAAE;IAE1B,MAAMmD,MAAM,GAAGrB,IAAI,CAACqG,UAAU,CAC5BrG,IAAI,IAAI,CAAC,IAAAsG,iEAAwB,EAACtG,IAAI,CAAClD,IAAI,CAC7C,CAAC;IACD,IAAI0B,WAAC,CAAC+H,iBAAiB,CAAClF,MAAM,CAACvE,IAAI,EAAE;MAAE+E,QAAQ,EAAE;IAAS,CAAC,CAAC,EAAE;MAC5D7B,IAAI,CAACS,UAAU,CAACyB,WAAW,CAAC1D,WAAC,CAACgI,cAAc,CAAC,IAAI,CAAC,CAAC;MACnD;IACF;IAEAtI,KAAK,CAACuI,aAAa,GAAG,IAAI;IAC1BzG,IAAI,CAACkC,WAAW,CAAC1D,WAAC,CAACC,SAAS,CAACP,KAAK,CAAC+D,QAAQ,CAAC,CAAC;EAC/C,CAAC;EACDyE,YAAYA,CAAC1G,IAAI,EAAE;IACjB,MAAM2G,IAAI,GAAG3G,IAAI,CAAC7C,GAAG,CAAC,MAAM,CAAC;IAC7B,MAAMwD,QAAQ,GAAGX,IAAI,CAAC7C,GAAG,CAAC,UAAU,CAAC;IACrC,MAAM;MAAEC;IAAM,CAAC,GAAG4C,IAAI;IAGtB,IACE2G,IAAI,CAACC,YAAY,CAAC;MAAE/J,IAAI,EAAE;IAAM,CAAC,CAAC,IAClC8D,QAAQ,CAACiG,YAAY,CAAC;MAAE/J,IAAI,EAAE;IAAS,CAAC,CAAC,EACzC;MACAmD,IAAI,CAACkC,WAAW,CAAC9E,KAAK,CAACmH,kBAAkB,CAAC,CAAC,CAAC;IAC9C;EACF;AACF,CAAC,EACD1E,iCAAkB,CACnB,CAAC;AAEF,MAAMgH,sBAAiD,GAAG;EACxDC,oBAAoBA,CAAC9G,IAAI,EAAE9B,KAAK,EAAE;IAChC,IACE8B,IAAI,CAAC5C,KAAK,CAAC+D,uBAAuB,CAACnB,IAAI,CAAClD,IAAI,CAACD,IAAI,EAAEqB,KAAK,CAAC8C,YAAY,CAAC,EACtE;MACA9C,KAAK,CAACuI,aAAa,GAAG,IAAI;MAC1BzG,IAAI,CAAClD,IAAI,CAACD,IAAI,GAAGqB,KAAK,CAAC+D,QAAQ,CAACpF,IAAI;IACtC;EACF;AACF,CAAC;AAED,SAASkK,kBAAkBA,CACzB/G,IAAc,EACdkE,GAAiB,EACjB8C,WAA+B,EAC/BxF,IAAU,EACVyF,aAAsB,EACtBC,aAAsB,EACtBC,eAAoC,EACpC;EAAA,IAAAC,eAAA;EACA,MAAMlJ,KAAuB,GAAG;IAC9B+D,QAAQ,EAAEiC,GAAG;IACbuC,aAAa,EAAE,KAAK;IACpBzF,YAAY,EAAEmG;EAChB,CAAC;EAED,MAAME,QAAQ,GAAG,IAAIC,4BAAa,CAAC;IACjCC,UAAU,EAAEvH,IAAI;IAChBkH,aAAa;IACb1F,IAAI;IACJgG,aAAa,EAAEtD,GAAG;IAClB8C,WAAW;IACXS,YAAYA,CAAA,EAAG;MACbvJ,KAAK,CAACuI,aAAa,GAAG,IAAI;MAE1B,OAAOjI,WAAC,CAACyI,aAAa,YAAfzI,WAAC,CAACyI,aAAa,CAAGjH,IAAI,CAAClD,IAAI,CAAC,IAAIkD,IAAI,CAAClD,IAAI,CAACQ,MAAM,GACnD4G,GAAG,GACH1F,WAAC,CAAC0E,gBAAgB,CAACgB,GAAG,EAAE1F,WAAC,CAACM,UAAU,CAAC,WAAW,CAAC,CAAC;IACxD;EACF,CAAC,CAAC;EACFuI,QAAQ,CAACK,OAAO,CAAC,CAAC;EAClB,IAAIT,aAAa,IAAIjH,IAAI,CAACxC,UAAU,CAAC,CAAC,EAAE;IACtCwC,IAAI,CAACR,QAAQ,CAAC2G,kBAAkB,EAAEjI,KAAK,CAAC;EAC1C;EAGA,IACEiJ,eAAe,IAAI,IAAI,KAAAC,eAAA,GACvBlJ,KAAK,CAAC+D,QAAQ,aAAdmF,eAAA,CAAgBvK,IAAI,IACpBqB,KAAK,CAAC+D,QAAQ,CAACpF,IAAI,MAAKsK,eAAe,oBAAfA,eAAe,CAAEtK,IAAI,GAC7C;IACAmD,IAAI,CAACR,QAAQ,CAACqH,sBAAsB,EAAE3I,KAAK,CAAC;EAC9C;EAEA,OAAOA,KAAK,CAACuI,aAAa;AAC5B;AASA,SAASkB,cAAcA,CAAC;EAAE5K,GAAG;EAAEoI;AAA0B,CAAC,EAAE;EAC1D,IAAIpI,GAAG,CAAC6K,IAAI,KAAK,YAAY,EAAE;IAC7B,OAAO,CAACzC,QAAQ,KAAKpI,GAAG,CAACF,IAAI,KAAK,MAAM,IAAIE,GAAG,CAACF,IAAI,KAAK,QAAQ,CAAC;EACpE;EACA,IAAIE,GAAG,CAAC6K,IAAI,KAAK,eAAe,EAAE;IAChC,OAAO7K,GAAG,CAACqB,KAAK,KAAK,MAAM,IAAIrB,GAAG,CAACqB,KAAK,KAAK,QAAQ;EACvD;EACA,OAAO,KAAK;AACd;AAaA,SAASoG,mBAAmBA,CAAmB1H,IAAO,EAAEH,IAAc,EAAE;EACtE6B,WAAC,CAACqJ,sBAAsB,CAAC/K,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAC;EACzC0B,WAAC,CAACsJ,oBAAoB,CAAChL,IAAI,EAAEH,IAAI,CAACG,IAAI,CAAC;EACvC,OAAOA,IAAI;AACb;AAEO,SAASiL,oBAAoBA,CAClC7D,GAAiB,EACjB8D,QAAkC,EAClCxL,KAAiB,EACjBC,eAAgC,EAChCyB,KAAW,EACX+J,oBAA6B,EAC7BjK,yBAAkC,EAClCkJ,aAAsB,EACtBC,eAA6B,EAC7B;EACA,IAAIV,aAAa,GAAG,KAAK;EACzB,IAAIyB,cAA4B;EAChC,MAAMC,WAA0B,GAAG,EAAE;EACrC,MAAMC,aAA4B,GAAG,EAAE;EAEvC,MAAMC,eAAwC,GAAG,EAAE;EAEnD,MAAMrB,WAAW,GAAGxI,WAAC,CAACoI,YAAY,CAACoB,QAAQ,CAAC,GACxC,MAAMA,QAAQ,GACd,MAAM;IAAA,IAAAM,eAAA;IACJ,CAAAA,eAAA,GAAAJ,cAAc,YAAAI,eAAA,GAAdJ,cAAc,GACZ1L,KAAK,CAAC,CAAC,CAAC,CAACY,KAAK,CAACmL,gCAAgC,CAACP,QAAQ,CAAC;IAC3D,OAAOE,cAAc;EACvB,CAAC;EAEL,KAAK,MAAMvL,IAAI,IAAIH,KAAK,EAAE;IACxBG,IAAI,CAAC6L,eAAe,CAAC,CAAC,IAAIlM,EAAE,CAACmM,sBAAsB,CAAC9L,IAAI,CAAC;IAGzD,MAAM0B,QAAQ,GAAG,EAACG,WAAC,CAACyI,aAAa,YAAfzI,WAAC,CAACyI,aAAa,CAAGtK,IAAI,CAACG,IAAI,CAAC,KAAIH,IAAI,CAACG,IAAI,CAACQ,MAAM;IAClE,MAAMoL,UAAU,GAAG,CAACrK,QAAQ;IAC5B,MAAMzB,SAAS,GAAGD,IAAI,CAACC,SAAS,CAAC,CAAC;IAClC,MAAM+L,QAAQ,GAAG,CAAC/L,SAAS;IAC3B,MAAMgM,OAAO,GAAGjM,IAAI,CAACa,UAAU,CAAC,CAAC;IACjC,MAAMc,QAAQ,GAAG,CAACsK,OAAO;IACzB,MAAM3B,aAAa,GAAGtK,IAAI,CAACsK,aAAa,oBAAlBtK,IAAI,CAACsK,aAAa,CAAG,CAAC;IAE5C,IAAI5I,QAAQ,IAAKC,QAAQ,IAAI1B,SAAU,IAAIqK,aAAa,EAAE;MACxD,MAAM4B,QAAQ,GAAG9B,kBAAkB,CACjCpK,IAAI,EACJuH,GAAG,EACH8C,WAAW,EACX9I,KAAK,EACL+I,aAAa,EACbC,aAAa,EACbC,eACF,CAAC;MACDV,aAAa,GAAGA,aAAa,IAAIoC,QAAQ;IAC3C;IAOA,QAAQ,IAAI;MACV,KAAK5B,aAAa;QAAE;UAClB,MAAM6B,SAAS,GAAInM,IAAI,CAACG,IAAI,CAAmBmD,IAAI;UAGnD,IAAI6I,SAAS,CAACzI,MAAM,KAAK,CAAC,IAAI7B,WAAC,CAACuK,qBAAqB,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YACnEX,WAAW,CAAClJ,IAAI,CAACuF,mBAAmB,CAACsE,SAAS,CAAC,CAAC,CAAC,EAAEnM,IAAI,CAAC,CAAC;UAC3D,CAAC,MAAM;YACLwL,WAAW,CAAClJ,IAAI,CACdT,WAAC,CAACwK,gBAAgB,CAChB9J,cAAQ,CAACC,SAAS,CAACC,GAAI,YAAW0J,SAAU,OAAM,EAClDnM,IAAI,CAACG,IACP,CACF,CAAC;UACH;UACA;QACF;MACA,KAAKuB,QAAQ,IAAIzB,SAAS,IAAIgM,OAAO,IAAI5K,yBAAyB;QAChEyI,aAAa,GAAG,IAAI;QACpB0B,WAAW,CAAClJ,IAAI,CAEdqF,0BAA0B,CAAC9F,WAAC,CAACC,SAAS,CAACyF,GAAG,CAAC,EAAEvH,IAAI,EAAEF,eAAe,CACpE,CAAC;QACD;MACF,KAAK4B,QAAQ,IAAIzB,SAAS,IAAIgM,OAAO,IAAI,CAAC5K,yBAAyB;QACjEyI,aAAa,GAAG,IAAI;QACpB0B,WAAW,CAAClJ,IAAI,CAEd0F,+BAA+B,CAAChI,IAAI,EAAEF,eAAe,CACvD,CAAC;QACD;MACF,KAAK4B,QAAQ,IAAIsK,QAAQ,IAAIC,OAAO,IAAIX,oBAAoB;QAO1D,IAAI,CAACN,cAAc,CAAChL,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9B2J,aAAa,GAAG,IAAI;UAEpB0B,WAAW,CAAClJ,IAAI,CAACiG,yBAAyB,CAAC1G,WAAC,CAACC,SAAS,CAACyF,GAAG,CAAC,EAAEvH,IAAI,CAAC,CAAC;UACnE;QACF;MAEF,KAAK0B,QAAQ,IAAIsK,QAAQ,IAAIC,OAAO,IAAI,CAACX,oBAAoB;QAC3DxB,aAAa,GAAG,IAAI;QACpB0B,WAAW,CAAClJ,IAAI,CAEdsG,wBAAwB,CAAC/G,WAAC,CAACC,SAAS,CAACyF,GAAG,CAAC,EAAEvH,IAAI,EAAEuB,KAAK,CACxD,CAAC;QACD;MACF,KAAKwK,UAAU,IAAI9L,SAAS,IAAIgM,OAAO,IAAI5K,yBAAyB;QAClEoK,aAAa,CAACnJ,IAAI,CAEhBqF,0BAA0B,CAAC9F,WAAC,CAACkG,cAAc,CAAC,CAAC,EAAE/H,IAAI,EAAEF,eAAe,CACtE,CAAC;QACD;MACF,KAAKiM,UAAU,IAAI9L,SAAS,IAAIgM,OAAO,IAAI,CAAC5K,yBAAyB;QACnEoK,aAAa,CAACnJ,IAAI,CAChBwF,iCAAiC,CAC/BjG,WAAC,CAACkG,cAAc,CAAC,CAAC,EAElB/H,IAAI,EACJF,eAAe,EACfyB,KACF,CACF,CAAC;QACD;MACF,KAAKwK,UAAU,IAAI9L,SAAS,IAAI0B,QAAQ,IAAIN,yBAAyB;QACnEoK,aAAa,CAACa,OAAO,CACnBnE,2BAA2B,CACzBtG,WAAC,CAACkG,cAAc,CAAC,CAAC,EAElB/H,IAAI,EACJF,eACF,CACF,CAAC;QACD4L,eAAe,CAACpJ,IAAI,CAClBwG,6BAA6B,CAE3B9I,IAAI,EACJF,eAAe,EACfuB,yBACF,CACF,CAAC;QACD;MACF,KAAK0K,UAAU,IAAI9L,SAAS,IAAI0B,QAAQ,IAAI,CAACN,yBAAyB;QACpEoK,aAAa,CAACa,OAAO,CACnBlE,kCAAkC,CAChCvG,WAAC,CAACkG,cAAc,CAAC,CAAC,EAElB/H,IAAI,EACJF,eAAe,EACfyB,KACF,CACF,CAAC;QACDmK,eAAe,CAACpJ,IAAI,CAClBwG,6BAA6B,CAE3B9I,IAAI,EACJF,eAAe,EACfuB,yBACF,CACF,CAAC;QACD;MACF,KAAKK,QAAQ,IAAIzB,SAAS,IAAI0B,QAAQ,IAAI,CAACN,yBAAyB;QAClEyI,aAAa,GAAG,IAAI;QACpB0B,WAAW,CAACc,OAAO,CAEjBtE,+BAA+B,CAAChI,IAAI,EAAEF,eAAe,CACvD,CAAC;QACD4L,eAAe,CAACpJ,IAAI,CAClBwG,6BAA6B,CAE3B9I,IAAI,EACJF,eAAe,EACfuB,yBACF,CACF,CAAC;QACD;MACF,KAAKK,QAAQ,IAAIzB,SAAS,IAAI0B,QAAQ,IAAIN,yBAAyB;QACjEyI,aAAa,GAAG,IAAI;QACpB0B,WAAW,CAACc,OAAO,CACjBzD,iCAAiC,CAC/BhH,WAAC,CAACC,SAAS,CAACyF,GAAG,CAAC,EAEhBvH,IAAI,EACJuB,KAAK,EACLzB,eACF,CACF,CAAC;QACD4L,eAAe,CAACpJ,IAAI,CAClBwG,6BAA6B,CAE3B9I,IAAI,EACJF,eAAe,EACfuB,yBACF,CACF,CAAC;QACD;MACF,KAAK0K,UAAU,IAAIC,QAAQ,IAAIC,OAAO,IAAIX,oBAAoB;QAE5DG,aAAa,CAACnJ,IAAI,CAACiG,yBAAyB,CAAC1G,WAAC,CAACkG,cAAc,CAAC,CAAC,EAAE/H,IAAI,CAAC,CAAC;QACvE;MACF,KAAK+L,UAAU,IAAIC,QAAQ,IAAIC,OAAO,IAAI,CAACX,oBAAoB;QAC7DG,aAAa,CAACnJ,IAAI,CAEhBsG,wBAAwB,CAAC/G,WAAC,CAACkG,cAAc,CAAC,CAAC,EAAE/H,IAAI,EAAEuB,KAAK,CAC1D,CAAC;QACD;MACF;QACE,MAAM,IAAIoF,KAAK,CAAC,cAAc,CAAC;IACnC;EACF;EAEA,OAAO;IACL6E,WAAW,EAAEA,WAAW,CAACe,MAAM,CAACC,OAAO,CAAC;IACxCf,aAAa,EAAEA,aAAa,CAACc,MAAM,CAACC,OAAO,CAAC;IAC5Cd,eAAe,EAAEA,eAAe,CAACa,MAAM,CAACC,OAAO,CAAC;IAChDC,SAASA,CAACpJ,IAAuB,EAAE;MACjC,KAAK,MAAMrD,IAAI,IAAIH,KAAK,EAAE;QAMxBG,IAAI,CAACG,IAAI,CAACuM,eAAe,GAAG,IAAI;QAChC1M,IAAI,CAAC2M,MAAM,CAAC,CAAC;MACf;MAEA,IAAIpB,cAAc,EAAE;QAClBlI,IAAI,CAAC5C,KAAK,CAAC6B,IAAI,CAAC;UAAEjC,EAAE,EAAEwB,WAAC,CAACC,SAAS,CAACyJ,cAAc;QAAE,CAAC,CAAC;QACpDlI,IAAI,CAAClC,GAAG,CACN,YAAY,EACZU,WAAC,CAAC6G,oBAAoB,CAAC,GAAG,EAAE6C,cAAc,EAAElI,IAAI,CAAClD,IAAI,CAACyM,UAAU,CAClE,CAAC;MACH;MAEA,IAAI,CAAC9C,aAAa,EAAE,OAAOzG,IAAI;MAE/B,IAAIA,IAAI,CAACwJ,iBAAiB,CAAC,CAAC,EAAE;QAC5BxJ,IAAI,CAAC5C,KAAK,CAAC6B,IAAI,CAAC;UAAEjC,EAAE,EAAEkH;QAAI,CAAC,CAAC;QAC5BlE,IAAI,CAACkC,WAAW,CACd1D,WAAC,CAAC6G,oBAAoB,CAAC,GAAG,EAAE7G,WAAC,CAACC,SAAS,CAACyF,GAAG,CAAC,EAAElE,IAAI,CAAClD,IAAI,CACzD,CAAC;MACH,CAAC,MAAM,IAAI,CAACkD,IAAI,CAAClD,IAAI,CAACE,EAAE,EAAE;QAExBgD,IAAI,CAAClD,IAAI,CAACE,EAAE,GAAGkH,GAAG;MACpB;MAEA,OAAOlE,IAAI;IACb;EACF,CAAC;AACH"}