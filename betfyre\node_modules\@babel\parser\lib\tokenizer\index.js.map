{"version": 3, "names": ["_location", "require", "_comments", "_identifier", "_types", "_parseError", "_whitespace", "_state", "_helper<PERSON><PERSON><PERSON><PERSON>er", "_excluded", "_excluded2", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "buildPosition", "pos", "lineStart", "curLine", "Position", "VALID_REGEX_FLAGS", "Set", "Token", "constructor", "state", "type", "value", "start", "end", "loc", "SourceLocation", "startLoc", "endLoc", "exports", "Tokenizer", "Comments<PERSON><PERSON><PERSON>", "options", "input", "isLookahead", "tokens", "errorHandlers_readInt", "invalidDigit", "radix", "errorRecovery", "raise", "Errors", "InvalidDigit", "at", "numericSeparatorInEscapeSequence", "errorBuilder", "NumericSeparatorInEscapeSequence", "unexpectedNumericSeparator", "UnexpectedNumericSeparator", "errorHandlers_readCodePoint", "assign", "invalidEscapeSequence", "InvalidEscapeSequence", "invalidCodePoint", "InvalidCodePoint", "errorHandlers_readStringContents_string", "strictNumericEscape", "recordStrictModeErrors", "StrictNumericEscape", "unterminated", "UnterminatedString", "errorHandlers_readStringContents_template", "UnterminatedTemplate", "State", "init", "pushToken", "token", "tokensLength", "push", "next", "checkKeywordEscapes", "lastTokStart", "lastTokEndLoc", "lastTokStartLoc", "nextToken", "eat", "match", "createLookaheadState", "context", "cur<PERSON><PERSON><PERSON><PERSON>", "inType", "curPosition", "<PERSON><PERSON><PERSON>", "old", "curr", "nextTokenStart", "nextTokenStartSince", "skipWhiteSpace", "lastIndex", "test", "lookaheadCharCode", "charCodeAt", "nextTokenInLineStart", "nextTokenInLineStartSince", "skipWhiteSpaceInLine", "lookaheadInLineCharCode", "codePointAtPos", "cp", "trail", "setStrict", "strict", "strictErrors", "for<PERSON>ach", "toParseError", "clear", "skipSpace", "finishToken", "getTokenFromCode", "skip<PERSON><PERSON>Com<PERSON>", "commentEnd", "UnterminatedComment", "lineBreakG", "comment", "slice", "skipLineComment", "startSkip", "ch", "isNewLine", "spaceStart", "comments", "loop", "undefined", "addComment", "attachComment", "isWhitespace", "inModule", "annexB", "commentWhitespace", "leadingNode", "trailingNode", "containingNode", "commentStack", "val", "prevType", "updateContext", "replaceToken", "readToken_numberSign", "readToken_interpreter", "nextPos", "UnexpectedDigitAfterHash", "hasPlugin", "expectPlugin", "getPluginOption", "RecordExpressionHashIncorrectStartSyntaxType", "TupleExpressionHashIncorrectStartSyntaxType", "isIdentifierStart", "readWord1", "finishOp", "readToken_dot", "readNumber", "readToken_slash", "readToken_mult_modulo", "code", "width", "readToken_pipe_amp", "RecordExpressionBarIncorrectEndSyntaxType", "TupleExpressionBarIncorrectEndSyntaxType", "readToken_caret", "proposal", "topicToken", "lookaheadCh", "codePointAt", "unexpected", "readToken_atSign", "readToken_plus_min", "readToken_lt", "readToken_gt", "size", "readToken_eq_excl", "readToken_question", "next2", "TupleExpressionBarIncorrectStartSyntaxType", "RecordExpressionBarIncorrectStartSyntaxType", "readTemplateToken", "readRadixNumber", "readString", "readWord", "InvalidOrUnexpectedToken", "String", "fromCodePoint", "str", "readRegexp", "escaped", "inClass", "UnterminatedRegExp", "createPositionWithColumnOffset", "content", "mods", "char", "fromCharCode", "has", "includes", "IncompatibleRegExpUVFlags", "DuplicateRegExpFlags", "isIdentifierChar", "MalformedRegExpFlags", "pattern", "flags", "readInt", "len", "forceLen", "allowNumSeparator", "n", "isBigInt", "InvalidDecimal", "NumberIdentifier", "index", "replace", "startsWithDot", "isFloat", "isDecimal", "hasExponent", "isOctal", "InvalidNumber", "hasLeadingZero", "integer", "StrictOctalLiteral", "underscorePos", "ZeroDigitNumericSeparator", "InvalidOrMissingExponent", "InvalidBigIntLiteral", "parseInt", "parseFloat", "readCodePoint", "throwOnInvalid", "quote", "readStringContents", "readTemplateContinuation", "opening", "firstInvalidLoc", "firstInvalidTemplateEscapePos", "set", "firstCode", "containsEsc", "word", "chunkStart", "escStart", "identifierCheck", "MissingUnicodeEscape", "esc", "EscapedCharNotAnIdentifier", "keywordTypes", "get", "tokenLabelName", "tokenIsKeyword", "InvalidEscapedReservedWord", "reservedWord", "raiseProperties", "details", "error", "errors", "raiseOverwrite", "UnexpectedToken", "expected", "pluginName", "MissingPlugin", "missingPlugin", "expectOnePlugin", "pluginNames", "some", "name", "MissingOneOfPlugins", "default"], "sources": ["../../src/tokenizer/index.ts"], "sourcesContent": ["/*:: declare var invariant; */\n\nimport type { Options } from \"../options\";\nimport {\n  Position,\n  SourceLocation,\n  createPositionWithColumnOffset,\n} from \"../util/location\";\nimport CommentsParser, { type CommentWhitespace } from \"../parser/comments\";\nimport type * as N from \"../types\";\nimport * as charCodes from \"charcodes\";\nimport { isIdentifierStart, isIdentifierChar } from \"../util/identifier\";\nimport {\n  tokenIsKeyword,\n  tokenLabelName,\n  tt,\n  keywords as keywordTypes,\n  type TokenType,\n} from \"./types\";\nimport type { TokContext } from \"./context\";\nimport {\n  Errors,\n  type ParseError,\n  type ParseErrorConstructor,\n  type RaiseProperties,\n} from \"../parse-error\";\nimport {\n  lineBreakG,\n  isNewLine,\n  isWhitespace,\n  skipWhiteSpace,\n  skipWhiteSpaceInLine,\n} from \"../util/whitespace\";\nimport State from \"./state\";\nimport type { LookaheadState, DeferredStrictError } from \"./state\";\n\nimport {\n  readInt,\n  readCodePoint,\n  readStringContents,\n  type IntErrorHandlers,\n  type CodePointErrorHandlers,\n  type StringContentsErrorHandlers,\n} from \"@babel/helper-string-parser\";\n\nimport type { Plugin } from \"../typings\";\n\nfunction buildPosition(pos: number, lineStart: number, curLine: number) {\n  return new Position(curLine, pos - lineStart, pos);\n}\n\nconst VALID_REGEX_FLAGS = new Set([\n  charCodes.lowercaseG,\n  charCodes.lowercaseM,\n  charCodes.lowercaseS,\n  charCodes.lowercaseI,\n  charCodes.lowercaseY,\n  charCodes.lowercaseU,\n  charCodes.lowercaseD,\n  charCodes.lowercaseV,\n]);\n\n// Object type used to represent tokens. Note that normally, tokens\n// simply exist as properties on the parser object. This is only\n// used for the onToken callback and the external tokenizer.\n\nexport class Token {\n  constructor(state: State) {\n    this.type = state.type;\n    this.value = state.value;\n    this.start = state.start;\n    this.end = state.end;\n    this.loc = new SourceLocation(state.startLoc, state.endLoc);\n  }\n\n  declare type: TokenType;\n  declare value: any;\n  declare start: number;\n  declare end: number;\n  declare loc: SourceLocation;\n}\n\n// ## Tokenizer\n\nexport default abstract class Tokenizer extends CommentsParser {\n  isLookahead: boolean;\n\n  // Token store.\n  tokens: Array<Token | N.Comment> = [];\n\n  constructor(options: Options, input: string) {\n    super();\n    this.state = new State();\n    this.state.init(options);\n    this.input = input;\n    this.length = input.length;\n    this.isLookahead = false;\n  }\n\n  pushToken(token: Token | N.Comment) {\n    // Pop out invalid tokens trapped by try-catch parsing.\n    // Those parsing branches are mainly created by typescript and flow plugins.\n    this.tokens.length = this.state.tokensLength;\n    this.tokens.push(token);\n    ++this.state.tokensLength;\n  }\n\n  // Move to the next token\n\n  next(): void {\n    this.checkKeywordEscapes();\n    if (this.options.tokens) {\n      this.pushToken(new Token(this.state));\n    }\n\n    this.state.lastTokStart = this.state.start;\n    this.state.lastTokEndLoc = this.state.endLoc;\n    this.state.lastTokStartLoc = this.state.startLoc;\n    this.nextToken();\n  }\n\n  eat(type: TokenType): boolean {\n    if (this.match(type)) {\n      this.next();\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /**\n   * Whether current token matches given type\n   */\n  match(type: TokenType): boolean {\n    return this.state.type === type;\n  }\n\n  /**\n   * Create a LookaheadState from current parser state\n   */\n  createLookaheadState(state: State): LookaheadState {\n    return {\n      pos: state.pos,\n      value: null,\n      type: state.type,\n      start: state.start,\n      end: state.end,\n      context: [this.curContext()],\n      inType: state.inType,\n      startLoc: state.startLoc,\n      lastTokEndLoc: state.lastTokEndLoc,\n      curLine: state.curLine,\n      lineStart: state.lineStart,\n      curPosition: state.curPosition,\n    };\n  }\n\n  /**\n   * lookahead peeks the next token, skipping changes to token context and\n   * comment stack. For performance it returns a limited LookaheadState\n   * instead of full parser state.\n   *\n   * The { column, line } Loc info is not included in lookahead since such usage\n   * is rare. Although it may return other location properties e.g. `curLine` and\n   * `lineStart`, these properties are not listed in the LookaheadState interface\n   * and thus the returned value is _NOT_ reliable.\n   *\n   * The tokenizer should make best efforts to avoid using any parser state\n   * other than those defined in LookaheadState\n   */\n  lookahead(): LookaheadState {\n    const old = this.state;\n    // @ts-expect-error For performance we use a simplified tokenizer state structure\n    this.state = this.createLookaheadState(old);\n\n    this.isLookahead = true;\n    this.nextToken();\n    this.isLookahead = false;\n\n    const curr = this.state;\n    this.state = old;\n    return curr;\n  }\n\n  nextTokenStart(): number {\n    return this.nextTokenStartSince(this.state.pos);\n  }\n\n  nextTokenStartSince(pos: number): number {\n    skipWhiteSpace.lastIndex = pos;\n    return skipWhiteSpace.test(this.input) ? skipWhiteSpace.lastIndex : pos;\n  }\n\n  lookaheadCharCode(): number {\n    return this.input.charCodeAt(this.nextTokenStart());\n  }\n\n  /**\n   * Similar to nextToken, but it will stop at line break when it is seen before the next token\n   *\n   * @returns {number} position of the next token start or line break, whichever is seen first.\n   * @memberof Tokenizer\n   */\n  nextTokenInLineStart(): number {\n    return this.nextTokenInLineStartSince(this.state.pos);\n  }\n\n  nextTokenInLineStartSince(pos: number): number {\n    skipWhiteSpaceInLine.lastIndex = pos;\n    return skipWhiteSpaceInLine.test(this.input)\n      ? skipWhiteSpaceInLine.lastIndex\n      : pos;\n  }\n\n  /**\n   * Similar to lookaheadCharCode, but it will return the char code of line break if it is\n   * seen before the next token\n   *\n   * @returns {number} char code of the next token start or line break, whichever is seen first.\n   * @memberof Tokenizer\n   */\n  lookaheadInLineCharCode(): number {\n    return this.input.charCodeAt(this.nextTokenInLineStart());\n  }\n\n  codePointAtPos(pos: number): number {\n    // The implementation is based on\n    // https://source.chromium.org/chromium/chromium/src/+/master:v8/src/builtins/builtins-string-gen.cc;l=1455;drc=221e331b49dfefadbc6fa40b0c68e6f97606d0b3;bpv=0;bpt=1\n    // We reimplement `codePointAt` because `codePointAt` is a V8 builtin which is not inlined by TurboFan (as of M91)\n    // since `input` is mostly ASCII, an inlined `charCodeAt` wins here\n    let cp = this.input.charCodeAt(pos);\n    if ((cp & 0xfc00) === 0xd800 && ++pos < this.input.length) {\n      const trail = this.input.charCodeAt(pos);\n      if ((trail & 0xfc00) === 0xdc00) {\n        cp = 0x10000 + ((cp & 0x3ff) << 10) + (trail & 0x3ff);\n      }\n    }\n    return cp;\n  }\n\n  // Toggle strict mode. Re-reads the next number or string to please\n  // pedantic tests (`\"use strict\"; 010;` should fail).\n\n  setStrict(strict: boolean): void {\n    this.state.strict = strict;\n    if (strict) {\n      // Throw an error for any string decimal escape found before/immediately\n      // after a \"use strict\" directive. Strict mode will be set at parse\n      // time for any literals that occur after the next node of the strict\n      // directive.\n      this.state.strictErrors.forEach(([toParseError, at]) =>\n        this.raise(toParseError, { at }),\n      );\n      this.state.strictErrors.clear();\n    }\n  }\n\n  curContext(): TokContext {\n    return this.state.context[this.state.context.length - 1];\n  }\n\n  // Read a single token, updating the parser object's token-related properties.\n  nextToken(): void {\n    this.skipSpace();\n    this.state.start = this.state.pos;\n    if (!this.isLookahead) this.state.startLoc = this.state.curPosition();\n    if (this.state.pos >= this.length) {\n      this.finishToken(tt.eof);\n      return;\n    }\n\n    this.getTokenFromCode(this.codePointAtPos(this.state.pos));\n  }\n\n  // Skips a block comment, whose end is marked by commentEnd.\n  // *-/ is used by the Flow plugin, when parsing block comments nested\n  // inside Flow comments.\n  skipBlockComment(commentEnd: \"*/\" | \"*-/\"): N.CommentBlock | undefined {\n    let startLoc;\n    if (!this.isLookahead) startLoc = this.state.curPosition();\n    const start = this.state.pos;\n    const end = this.input.indexOf(commentEnd, start + 2);\n    if (end === -1) {\n      // We have to call this again here because startLoc may not be set...\n      // This seems to be for performance reasons:\n      // https://github.com/babel/babel/commit/acf2a10899f696a8aaf34df78bf9725b5ea7f2da\n      throw this.raise(Errors.UnterminatedComment, {\n        at: this.state.curPosition(),\n      });\n    }\n\n    this.state.pos = end + commentEnd.length;\n    lineBreakG.lastIndex = start + 2;\n    while (lineBreakG.test(this.input) && lineBreakG.lastIndex <= end) {\n      ++this.state.curLine;\n      this.state.lineStart = lineBreakG.lastIndex;\n    }\n\n    // If we are doing a lookahead right now we need to advance the position (above code)\n    // but we do not want to push the comment to the state.\n    if (this.isLookahead) return;\n    /*:: invariant(startLoc) */\n\n    const comment: N.CommentBlock = {\n      type: \"CommentBlock\",\n      value: this.input.slice(start + 2, end),\n      start,\n      end: end + commentEnd.length,\n      loc: new SourceLocation(startLoc, this.state.curPosition()),\n    };\n    if (this.options.tokens) this.pushToken(comment);\n    return comment;\n  }\n\n  skipLineComment(startSkip: number): N.CommentLine | undefined {\n    const start = this.state.pos;\n    let startLoc;\n    if (!this.isLookahead) startLoc = this.state.curPosition();\n    let ch = this.input.charCodeAt((this.state.pos += startSkip));\n    if (this.state.pos < this.length) {\n      while (!isNewLine(ch) && ++this.state.pos < this.length) {\n        ch = this.input.charCodeAt(this.state.pos);\n      }\n    }\n\n    // If we are doing a lookahead right now we need to advance the position (above code)\n    // but we do not want to push the comment to the state.\n    if (this.isLookahead) return;\n    /*:: invariant(startLoc) */\n\n    const end = this.state.pos;\n    const value = this.input.slice(start + startSkip, end);\n\n    const comment: N.CommentLine = {\n      type: \"CommentLine\",\n      value,\n      start,\n      end,\n      loc: new SourceLocation(startLoc, this.state.curPosition()),\n    };\n    if (this.options.tokens) this.pushToken(comment);\n    return comment;\n  }\n\n  // Called at the start of the parse and after every token. Skips\n  // whitespace and comments, and.\n\n  skipSpace(): void {\n    const spaceStart = this.state.pos;\n    const comments = [];\n    loop: while (this.state.pos < this.length) {\n      const ch = this.input.charCodeAt(this.state.pos);\n      switch (ch) {\n        case charCodes.space:\n        case charCodes.nonBreakingSpace:\n        case charCodes.tab:\n          ++this.state.pos;\n          break;\n        case charCodes.carriageReturn:\n          if (\n            this.input.charCodeAt(this.state.pos + 1) === charCodes.lineFeed\n          ) {\n            ++this.state.pos;\n          }\n        // fall through\n        case charCodes.lineFeed:\n        case charCodes.lineSeparator:\n        case charCodes.paragraphSeparator:\n          ++this.state.pos;\n          ++this.state.curLine;\n          this.state.lineStart = this.state.pos;\n          break;\n\n        case charCodes.slash:\n          switch (this.input.charCodeAt(this.state.pos + 1)) {\n            case charCodes.asterisk: {\n              const comment = this.skipBlockComment(\"*/\");\n              if (comment !== undefined) {\n                this.addComment(comment);\n                if (this.options.attachComment) comments.push(comment);\n              }\n              break;\n            }\n\n            case charCodes.slash: {\n              const comment = this.skipLineComment(2);\n              if (comment !== undefined) {\n                this.addComment(comment);\n                if (this.options.attachComment) comments.push(comment);\n              }\n              break;\n            }\n\n            default:\n              break loop;\n          }\n          break;\n\n        default:\n          if (isWhitespace(ch)) {\n            ++this.state.pos;\n          } else if (\n            ch === charCodes.dash &&\n            !this.inModule &&\n            this.options.annexB\n          ) {\n            const pos = this.state.pos;\n            if (\n              this.input.charCodeAt(pos + 1) === charCodes.dash &&\n              this.input.charCodeAt(pos + 2) === charCodes.greaterThan &&\n              (spaceStart === 0 || this.state.lineStart > spaceStart)\n            ) {\n              // A `-->` line comment\n              const comment = this.skipLineComment(3);\n              if (comment !== undefined) {\n                this.addComment(comment);\n                if (this.options.attachComment) comments.push(comment);\n              }\n            } else {\n              break loop;\n            }\n          } else if (\n            ch === charCodes.lessThan &&\n            !this.inModule &&\n            this.options.annexB\n          ) {\n            const pos = this.state.pos;\n            if (\n              this.input.charCodeAt(pos + 1) === charCodes.exclamationMark &&\n              this.input.charCodeAt(pos + 2) === charCodes.dash &&\n              this.input.charCodeAt(pos + 3) === charCodes.dash\n            ) {\n              // `<!--`, an XML-style comment that should be interpreted as a line comment\n              const comment = this.skipLineComment(4);\n              if (comment !== undefined) {\n                this.addComment(comment);\n                if (this.options.attachComment) comments.push(comment);\n              }\n            } else {\n              break loop;\n            }\n          } else {\n            break loop;\n          }\n      }\n    }\n\n    if (comments.length > 0) {\n      const end = this.state.pos;\n      const commentWhitespace: CommentWhitespace = {\n        start: spaceStart,\n        end,\n        comments,\n        leadingNode: null,\n        trailingNode: null,\n        containingNode: null,\n      };\n      this.state.commentStack.push(commentWhitespace);\n    }\n  }\n\n  // Called at the end of every token. Sets `end`, `val`, and\n  // maintains `context` and `canStartJSXElement`, and skips the space after\n  // the token, so that the next one's `start` will point at the\n  // right position.\n\n  finishToken(type: TokenType, val?: any): void {\n    this.state.end = this.state.pos;\n    this.state.endLoc = this.state.curPosition();\n    const prevType = this.state.type;\n    this.state.type = type;\n    this.state.value = val;\n\n    if (!this.isLookahead) {\n      this.updateContext(prevType);\n    }\n  }\n\n  replaceToken(type: TokenType): void {\n    this.state.type = type;\n    // @ts-expect-error the prevType of updateContext is required\n    // only when the new type is tt.slash/tt.jsxTagEnd\n    this.updateContext();\n  }\n\n  // ### Token reading\n\n  // This is the function that is called to fetch the next token. It\n  // is somewhat obscure, because it works in character codes rather\n  // than characters, and because operator parsing has been inlined\n  // into it.\n  //\n  // All in the name of speed.\n\n  // number sign is \"#\"\n  readToken_numberSign(): void {\n    if (this.state.pos === 0 && this.readToken_interpreter()) {\n      return;\n    }\n\n    const nextPos = this.state.pos + 1;\n    const next = this.codePointAtPos(nextPos);\n    if (next >= charCodes.digit0 && next <= charCodes.digit9) {\n      throw this.raise(Errors.UnexpectedDigitAfterHash, {\n        at: this.state.curPosition(),\n      });\n    }\n\n    if (\n      next === charCodes.leftCurlyBrace ||\n      (next === charCodes.leftSquareBracket && this.hasPlugin(\"recordAndTuple\"))\n    ) {\n      // When we see `#{`, it is likely to be a hash record.\n      // However we don't yell at `#[` since users may intend to use \"computed private fields\",\n      // which is not allowed in the spec. Throwing expecting recordAndTuple is\n      // misleading\n      this.expectPlugin(\"recordAndTuple\");\n      if (this.getPluginOption(\"recordAndTuple\", \"syntaxType\") === \"bar\") {\n        throw this.raise(\n          next === charCodes.leftCurlyBrace\n            ? Errors.RecordExpressionHashIncorrectStartSyntaxType\n            : Errors.TupleExpressionHashIncorrectStartSyntaxType,\n          { at: this.state.curPosition() },\n        );\n      }\n\n      this.state.pos += 2;\n      if (next === charCodes.leftCurlyBrace) {\n        // #{\n        this.finishToken(tt.braceHashL);\n      } else {\n        // #[\n        this.finishToken(tt.bracketHashL);\n      }\n    } else if (isIdentifierStart(next)) {\n      ++this.state.pos;\n      this.finishToken(tt.privateName, this.readWord1(next));\n    } else if (next === charCodes.backslash) {\n      ++this.state.pos;\n      this.finishToken(tt.privateName, this.readWord1());\n    } else {\n      this.finishOp(tt.hash, 1);\n    }\n  }\n\n  readToken_dot(): void {\n    const next = this.input.charCodeAt(this.state.pos + 1);\n    if (next >= charCodes.digit0 && next <= charCodes.digit9) {\n      this.readNumber(true);\n      return;\n    }\n\n    if (\n      next === charCodes.dot &&\n      this.input.charCodeAt(this.state.pos + 2) === charCodes.dot\n    ) {\n      this.state.pos += 3;\n      this.finishToken(tt.ellipsis);\n    } else {\n      ++this.state.pos;\n      this.finishToken(tt.dot);\n    }\n  }\n\n  readToken_slash(): void {\n    const next = this.input.charCodeAt(this.state.pos + 1);\n    if (next === charCodes.equalsTo) {\n      this.finishOp(tt.slashAssign, 2);\n    } else {\n      this.finishOp(tt.slash, 1);\n    }\n  }\n\n  readToken_interpreter(): boolean {\n    if (this.state.pos !== 0 || this.length < 2) return false;\n\n    let ch = this.input.charCodeAt(this.state.pos + 1);\n    if (ch !== charCodes.exclamationMark) return false;\n\n    const start = this.state.pos;\n    this.state.pos += 1;\n\n    while (!isNewLine(ch) && ++this.state.pos < this.length) {\n      ch = this.input.charCodeAt(this.state.pos);\n    }\n\n    const value = this.input.slice(start + 2, this.state.pos);\n\n    this.finishToken(tt.interpreterDirective, value);\n\n    return true;\n  }\n\n  readToken_mult_modulo(code: number): void {\n    // '%' or '*'\n    let type = code === charCodes.asterisk ? tt.star : tt.modulo;\n    let width = 1;\n    let next = this.input.charCodeAt(this.state.pos + 1);\n\n    // Exponentiation operator '**'\n    if (code === charCodes.asterisk && next === charCodes.asterisk) {\n      width++;\n      next = this.input.charCodeAt(this.state.pos + 2);\n      type = tt.exponent;\n    }\n\n    // '%=' or '*='\n    if (next === charCodes.equalsTo && !this.state.inType) {\n      width++;\n      // `tt.moduloAssign` is only needed to support % as a Hack-pipe topic token.\n      // If the proposal ends up choosing a different token,\n      // it can be merged with tt.assign.\n      type = code === charCodes.percentSign ? tt.moduloAssign : tt.assign;\n    }\n\n    this.finishOp(type, width);\n  }\n\n  readToken_pipe_amp(code: number): void {\n    // '||' '&&' '||=' '&&='\n    const next = this.input.charCodeAt(this.state.pos + 1);\n\n    if (next === code) {\n      if (this.input.charCodeAt(this.state.pos + 2) === charCodes.equalsTo) {\n        this.finishOp(tt.assign, 3);\n      } else {\n        this.finishOp(\n          code === charCodes.verticalBar ? tt.logicalOR : tt.logicalAND,\n          2,\n        );\n      }\n      return;\n    }\n\n    if (code === charCodes.verticalBar) {\n      // '|>'\n      if (next === charCodes.greaterThan) {\n        this.finishOp(tt.pipeline, 2);\n        return;\n      }\n      // '|}'\n      if (\n        this.hasPlugin(\"recordAndTuple\") &&\n        next === charCodes.rightCurlyBrace\n      ) {\n        if (this.getPluginOption(\"recordAndTuple\", \"syntaxType\") !== \"bar\") {\n          throw this.raise(Errors.RecordExpressionBarIncorrectEndSyntaxType, {\n            at: this.state.curPosition(),\n          });\n        }\n        this.state.pos += 2;\n        this.finishToken(tt.braceBarR);\n        return;\n      }\n\n      // '|]'\n      if (\n        this.hasPlugin(\"recordAndTuple\") &&\n        next === charCodes.rightSquareBracket\n      ) {\n        if (this.getPluginOption(\"recordAndTuple\", \"syntaxType\") !== \"bar\") {\n          throw this.raise(Errors.TupleExpressionBarIncorrectEndSyntaxType, {\n            at: this.state.curPosition(),\n          });\n        }\n        this.state.pos += 2;\n        this.finishToken(tt.bracketBarR);\n        return;\n      }\n    }\n\n    if (next === charCodes.equalsTo) {\n      this.finishOp(tt.assign, 2);\n      return;\n    }\n\n    this.finishOp(\n      code === charCodes.verticalBar ? tt.bitwiseOR : tt.bitwiseAND,\n      1,\n    );\n  }\n\n  readToken_caret(): void {\n    const next = this.input.charCodeAt(this.state.pos + 1);\n\n    // '^='\n    if (next === charCodes.equalsTo && !this.state.inType) {\n      // `tt.xorAssign` is only needed to support ^ as a Hack-pipe topic token.\n      // If the proposal ends up choosing a different token,\n      // it can be merged with tt.assign.\n      this.finishOp(tt.xorAssign, 2);\n    }\n    // '^^'\n    else if (\n      next === charCodes.caret &&\n      // If the ^^ token is not enabled, we don't throw but parse two single ^s\n      // because it could be a ^ hack token followed by a ^ binary operator.\n      this.hasPlugin([\n        \"pipelineOperator\",\n        { proposal: \"hack\", topicToken: \"^^\" },\n      ])\n    ) {\n      this.finishOp(tt.doubleCaret, 2);\n\n      // `^^^` is forbidden and must be separated by a space.\n      const lookaheadCh = this.input.codePointAt(this.state.pos);\n      if (lookaheadCh === charCodes.caret) {\n        this.unexpected();\n      }\n    }\n    // '^'\n    else {\n      this.finishOp(tt.bitwiseXOR, 1);\n    }\n  }\n\n  readToken_atSign(): void {\n    const next = this.input.charCodeAt(this.state.pos + 1);\n\n    // '@@'\n    if (\n      next === charCodes.atSign &&\n      this.hasPlugin([\n        \"pipelineOperator\",\n        { proposal: \"hack\", topicToken: \"@@\" },\n      ])\n    ) {\n      this.finishOp(tt.doubleAt, 2);\n    }\n    // '@'\n    else {\n      this.finishOp(tt.at, 1);\n    }\n  }\n\n  readToken_plus_min(code: number): void {\n    // '+-'\n    const next = this.input.charCodeAt(this.state.pos + 1);\n\n    if (next === code) {\n      this.finishOp(tt.incDec, 2);\n      return;\n    }\n\n    if (next === charCodes.equalsTo) {\n      this.finishOp(tt.assign, 2);\n    } else {\n      this.finishOp(tt.plusMin, 1);\n    }\n  }\n\n  readToken_lt(): void {\n    // '<'\n    const { pos } = this.state;\n    const next = this.input.charCodeAt(pos + 1);\n\n    if (next === charCodes.lessThan) {\n      if (this.input.charCodeAt(pos + 2) === charCodes.equalsTo) {\n        this.finishOp(tt.assign, 3);\n        return;\n      }\n      this.finishOp(tt.bitShiftL, 2);\n      return;\n    }\n\n    if (next === charCodes.equalsTo) {\n      // <=\n      this.finishOp(tt.relational, 2);\n      return;\n    }\n\n    this.finishOp(tt.lt, 1);\n  }\n\n  readToken_gt(): void {\n    // '>'\n    const { pos } = this.state;\n    const next = this.input.charCodeAt(pos + 1);\n\n    if (next === charCodes.greaterThan) {\n      const size =\n        this.input.charCodeAt(pos + 2) === charCodes.greaterThan ? 3 : 2;\n      if (this.input.charCodeAt(pos + size) === charCodes.equalsTo) {\n        this.finishOp(tt.assign, size + 1);\n        return;\n      }\n      this.finishOp(tt.bitShiftR, size);\n      return;\n    }\n\n    if (next === charCodes.equalsTo) {\n      // <= | >=\n      this.finishOp(tt.relational, 2);\n      return;\n    }\n\n    this.finishOp(tt.gt, 1);\n  }\n\n  readToken_eq_excl(code: number): void {\n    // '=!'\n    const next = this.input.charCodeAt(this.state.pos + 1);\n    if (next === charCodes.equalsTo) {\n      this.finishOp(\n        tt.equality,\n        this.input.charCodeAt(this.state.pos + 2) === charCodes.equalsTo\n          ? 3\n          : 2,\n      );\n      return;\n    }\n    if (code === charCodes.equalsTo && next === charCodes.greaterThan) {\n      // '=>'\n      this.state.pos += 2;\n      this.finishToken(tt.arrow);\n      return;\n    }\n    this.finishOp(code === charCodes.equalsTo ? tt.eq : tt.bang, 1);\n  }\n\n  readToken_question(): void {\n    // '?'\n    const next = this.input.charCodeAt(this.state.pos + 1);\n    const next2 = this.input.charCodeAt(this.state.pos + 2);\n    if (next === charCodes.questionMark) {\n      if (next2 === charCodes.equalsTo) {\n        // '??='\n        this.finishOp(tt.assign, 3);\n      } else {\n        // '??'\n        this.finishOp(tt.nullishCoalescing, 2);\n      }\n    } else if (\n      next === charCodes.dot &&\n      !(next2 >= charCodes.digit0 && next2 <= charCodes.digit9)\n    ) {\n      // '.' not followed by a number\n      this.state.pos += 2;\n      this.finishToken(tt.questionDot);\n    } else {\n      ++this.state.pos;\n      this.finishToken(tt.question);\n    }\n  }\n\n  getTokenFromCode(code: number): void {\n    switch (code) {\n      // The interpretation of a dot depends on whether it is followed\n      // by a digit or another two dots.\n\n      case charCodes.dot:\n        this.readToken_dot();\n        return;\n      // Punctuation tokens.\n      case charCodes.leftParenthesis:\n        ++this.state.pos;\n        this.finishToken(tt.parenL);\n        return;\n      case charCodes.rightParenthesis:\n        ++this.state.pos;\n        this.finishToken(tt.parenR);\n        return;\n      case charCodes.semicolon:\n        ++this.state.pos;\n        this.finishToken(tt.semi);\n        return;\n      case charCodes.comma:\n        ++this.state.pos;\n        this.finishToken(tt.comma);\n        return;\n      case charCodes.leftSquareBracket:\n        if (\n          this.hasPlugin(\"recordAndTuple\") &&\n          this.input.charCodeAt(this.state.pos + 1) === charCodes.verticalBar\n        ) {\n          if (this.getPluginOption(\"recordAndTuple\", \"syntaxType\") !== \"bar\") {\n            throw this.raise(\n              Errors.TupleExpressionBarIncorrectStartSyntaxType,\n              { at: this.state.curPosition() },\n            );\n          }\n\n          // [|\n          this.state.pos += 2;\n          this.finishToken(tt.bracketBarL);\n        } else {\n          ++this.state.pos;\n          this.finishToken(tt.bracketL);\n        }\n        return;\n      case charCodes.rightSquareBracket:\n        ++this.state.pos;\n        this.finishToken(tt.bracketR);\n        return;\n      case charCodes.leftCurlyBrace:\n        if (\n          this.hasPlugin(\"recordAndTuple\") &&\n          this.input.charCodeAt(this.state.pos + 1) === charCodes.verticalBar\n        ) {\n          if (this.getPluginOption(\"recordAndTuple\", \"syntaxType\") !== \"bar\") {\n            throw this.raise(\n              Errors.RecordExpressionBarIncorrectStartSyntaxType,\n              { at: this.state.curPosition() },\n            );\n          }\n\n          // {|\n          this.state.pos += 2;\n          this.finishToken(tt.braceBarL);\n        } else {\n          ++this.state.pos;\n          this.finishToken(tt.braceL);\n        }\n        return;\n      case charCodes.rightCurlyBrace:\n        ++this.state.pos;\n        this.finishToken(tt.braceR);\n        return;\n\n      case charCodes.colon:\n        if (\n          this.hasPlugin(\"functionBind\") &&\n          this.input.charCodeAt(this.state.pos + 1) === charCodes.colon\n        ) {\n          this.finishOp(tt.doubleColon, 2);\n        } else {\n          ++this.state.pos;\n          this.finishToken(tt.colon);\n        }\n        return;\n\n      case charCodes.questionMark:\n        this.readToken_question();\n        return;\n\n      case charCodes.graveAccent:\n        this.readTemplateToken();\n        return;\n\n      case charCodes.digit0: {\n        const next = this.input.charCodeAt(this.state.pos + 1);\n        // '0x', '0X' - hex number\n        if (next === charCodes.lowercaseX || next === charCodes.uppercaseX) {\n          this.readRadixNumber(16);\n          return;\n        }\n        // '0o', '0O' - octal number\n        if (next === charCodes.lowercaseO || next === charCodes.uppercaseO) {\n          this.readRadixNumber(8);\n          return;\n        }\n        // '0b', '0B' - binary number\n        if (next === charCodes.lowercaseB || next === charCodes.uppercaseB) {\n          this.readRadixNumber(2);\n          return;\n        }\n      }\n      // Anything else beginning with a digit is an integer, octal\n      // number, or float. (fall through)\n      case charCodes.digit1:\n      case charCodes.digit2:\n      case charCodes.digit3:\n      case charCodes.digit4:\n      case charCodes.digit5:\n      case charCodes.digit6:\n      case charCodes.digit7:\n      case charCodes.digit8:\n      case charCodes.digit9:\n        this.readNumber(false);\n        return;\n\n      // Quotes produce strings.\n      case charCodes.quotationMark:\n      case charCodes.apostrophe:\n        this.readString(code);\n        return;\n\n      // Operators are parsed inline in tiny state machines. '=' (charCodes.equalsTo) is\n      // often referred to. `finishOp` simply skips the amount of\n      // characters it is given as second argument, and returns a token\n      // of the type given by its first argument.\n\n      case charCodes.slash:\n        this.readToken_slash();\n        return;\n\n      case charCodes.percentSign:\n      case charCodes.asterisk:\n        this.readToken_mult_modulo(code);\n        return;\n\n      case charCodes.verticalBar:\n      case charCodes.ampersand:\n        this.readToken_pipe_amp(code);\n        return;\n\n      case charCodes.caret:\n        this.readToken_caret();\n        return;\n\n      case charCodes.plusSign:\n      case charCodes.dash:\n        this.readToken_plus_min(code);\n        return;\n\n      case charCodes.lessThan:\n        this.readToken_lt();\n        return;\n\n      case charCodes.greaterThan:\n        this.readToken_gt();\n        return;\n\n      case charCodes.equalsTo:\n      case charCodes.exclamationMark:\n        this.readToken_eq_excl(code);\n        return;\n\n      case charCodes.tilde:\n        this.finishOp(tt.tilde, 1);\n        return;\n\n      case charCodes.atSign:\n        this.readToken_atSign();\n        return;\n\n      case charCodes.numberSign:\n        this.readToken_numberSign();\n        return;\n\n      case charCodes.backslash:\n        this.readWord();\n        return;\n\n      default:\n        if (isIdentifierStart(code)) {\n          this.readWord(code);\n          return;\n        }\n    }\n\n    throw this.raise(Errors.InvalidOrUnexpectedToken, {\n      at: this.state.curPosition(),\n      unexpected: String.fromCodePoint(code),\n    });\n  }\n\n  finishOp(type: TokenType, size: number): void {\n    const str = this.input.slice(this.state.pos, this.state.pos + size);\n    this.state.pos += size;\n    this.finishToken(type, str);\n  }\n\n  readRegexp(): void {\n    const startLoc = this.state.startLoc;\n    const start = this.state.start + 1;\n    let escaped, inClass;\n    let { pos } = this.state;\n    for (; ; ++pos) {\n      if (pos >= this.length) {\n        // FIXME: explain\n        throw this.raise(Errors.UnterminatedRegExp, {\n          at: createPositionWithColumnOffset(startLoc, 1),\n        });\n      }\n      const ch = this.input.charCodeAt(pos);\n      if (isNewLine(ch)) {\n        throw this.raise(Errors.UnterminatedRegExp, {\n          at: createPositionWithColumnOffset(startLoc, 1),\n        });\n      }\n      if (escaped) {\n        escaped = false;\n      } else {\n        if (ch === charCodes.leftSquareBracket) {\n          inClass = true;\n        } else if (ch === charCodes.rightSquareBracket && inClass) {\n          inClass = false;\n        } else if (ch === charCodes.slash && !inClass) {\n          break;\n        }\n        escaped = ch === charCodes.backslash;\n      }\n    }\n    const content = this.input.slice(start, pos);\n    ++pos;\n\n    let mods = \"\";\n\n    const nextPos = () =>\n      // (pos + 1) + 1 - start\n      createPositionWithColumnOffset(startLoc, pos + 2 - start);\n\n    while (pos < this.length) {\n      const cp = this.codePointAtPos(pos);\n      // It doesn't matter if cp > 0xffff, the loop will either throw or break because we check on cp\n      const char = String.fromCharCode(cp);\n\n      // @ts-expect-error VALID_REGEX_FLAGS.has should accept expanded type: number\n      if (VALID_REGEX_FLAGS.has(cp)) {\n        if (cp === charCodes.lowercaseV) {\n          if (mods.includes(\"u\")) {\n            this.raise(Errors.IncompatibleRegExpUVFlags, { at: nextPos() });\n          }\n        } else if (cp === charCodes.lowercaseU) {\n          if (mods.includes(\"v\")) {\n            this.raise(Errors.IncompatibleRegExpUVFlags, { at: nextPos() });\n          }\n        }\n        if (mods.includes(char)) {\n          this.raise(Errors.DuplicateRegExpFlags, { at: nextPos() });\n        }\n      } else if (isIdentifierChar(cp) || cp === charCodes.backslash) {\n        this.raise(Errors.MalformedRegExpFlags, { at: nextPos() });\n      } else {\n        break;\n      }\n\n      ++pos;\n      mods += char;\n    }\n    this.state.pos = pos;\n\n    this.finishToken(tt.regexp, {\n      pattern: content,\n      flags: mods,\n    });\n  }\n\n  // Read an integer in the given radix. Return null if zero digits\n  // were read, the integer value otherwise. When `len` is given, this\n  // will return `null` unless the integer has exactly `len` digits.\n  // When `forceLen` is `true`, it means that we already know that in case\n  // of a malformed number we have to skip `len` characters anyway, instead\n  // of bailing out early. For example, in \"\\u{123Z}\" we want to read up to }\n  // anyway, while in \"\\u00Z\" we will stop at Z instead of consuming four\n  // characters (and thus the closing quote).\n\n  readInt(\n    radix: number,\n    len?: number,\n    forceLen: boolean = false,\n    allowNumSeparator: boolean | \"bail\" = true,\n  ): number | null {\n    const { n, pos } = readInt(\n      this.input,\n      this.state.pos,\n      this.state.lineStart,\n      this.state.curLine,\n      radix,\n      len,\n      forceLen,\n      allowNumSeparator,\n      this.errorHandlers_readInt,\n      /* bailOnError */ false,\n    );\n    this.state.pos = pos;\n    return n;\n  }\n\n  readRadixNumber(radix: number): void {\n    const startLoc = this.state.curPosition();\n    let isBigInt = false;\n\n    this.state.pos += 2; // 0x\n    const val = this.readInt(radix);\n    if (val == null) {\n      this.raise(Errors.InvalidDigit, {\n        // Numeric literals can't have newlines, so this is safe to do.\n        at: createPositionWithColumnOffset(startLoc, 2),\n        radix,\n      });\n    }\n    const next = this.input.charCodeAt(this.state.pos);\n\n    if (next === charCodes.lowercaseN) {\n      ++this.state.pos;\n      isBigInt = true;\n    } else if (next === charCodes.lowercaseM) {\n      throw this.raise(Errors.InvalidDecimal, { at: startLoc });\n    }\n\n    if (isIdentifierStart(this.codePointAtPos(this.state.pos))) {\n      throw this.raise(Errors.NumberIdentifier, {\n        at: this.state.curPosition(),\n      });\n    }\n\n    if (isBigInt) {\n      const str = this.input\n        .slice(startLoc.index, this.state.pos)\n        .replace(/[_n]/g, \"\");\n      this.finishToken(tt.bigint, str);\n      return;\n    }\n\n    this.finishToken(tt.num, val);\n  }\n\n  // Read an integer, octal integer, or floating-point number.\n\n  readNumber(startsWithDot: boolean): void {\n    const start = this.state.pos;\n    const startLoc = this.state.curPosition();\n    let isFloat = false;\n    let isBigInt = false;\n    let isDecimal = false;\n    let hasExponent = false;\n    let isOctal = false;\n\n    if (!startsWithDot && this.readInt(10) === null) {\n      this.raise(Errors.InvalidNumber, { at: this.state.curPosition() });\n    }\n    const hasLeadingZero =\n      this.state.pos - start >= 2 &&\n      this.input.charCodeAt(start) === charCodes.digit0;\n\n    if (hasLeadingZero) {\n      const integer = this.input.slice(start, this.state.pos);\n      this.recordStrictModeErrors(Errors.StrictOctalLiteral, { at: startLoc });\n      if (!this.state.strict) {\n        // disallow numeric separators in non octal decimals and legacy octal likes\n        const underscorePos = integer.indexOf(\"_\");\n        if (underscorePos > 0) {\n          // Numeric literals can't have newlines, so this is safe to do.\n          this.raise(Errors.ZeroDigitNumericSeparator, {\n            at: createPositionWithColumnOffset(startLoc, underscorePos),\n          });\n        }\n      }\n      isOctal = hasLeadingZero && !/[89]/.test(integer);\n    }\n\n    let next = this.input.charCodeAt(this.state.pos);\n    if (next === charCodes.dot && !isOctal) {\n      ++this.state.pos;\n      this.readInt(10);\n      isFloat = true;\n      next = this.input.charCodeAt(this.state.pos);\n    }\n\n    if (\n      (next === charCodes.uppercaseE || next === charCodes.lowercaseE) &&\n      !isOctal\n    ) {\n      next = this.input.charCodeAt(++this.state.pos);\n      if (next === charCodes.plusSign || next === charCodes.dash) {\n        ++this.state.pos;\n      }\n      if (this.readInt(10) === null) {\n        this.raise(Errors.InvalidOrMissingExponent, { at: startLoc });\n      }\n      isFloat = true;\n      hasExponent = true;\n      next = this.input.charCodeAt(this.state.pos);\n    }\n\n    if (next === charCodes.lowercaseN) {\n      // disallow floats, legacy octal syntax and non octal decimals\n      // new style octal (\"0o\") is handled in this.readRadixNumber\n      if (isFloat || hasLeadingZero) {\n        this.raise(Errors.InvalidBigIntLiteral, { at: startLoc });\n      }\n      ++this.state.pos;\n      isBigInt = true;\n    }\n\n    if (next === charCodes.lowercaseM) {\n      this.expectPlugin(\"decimal\", this.state.curPosition());\n      if (hasExponent || hasLeadingZero) {\n        this.raise(Errors.InvalidDecimal, { at: startLoc });\n      }\n      ++this.state.pos;\n      isDecimal = true;\n    }\n\n    if (isIdentifierStart(this.codePointAtPos(this.state.pos))) {\n      throw this.raise(Errors.NumberIdentifier, {\n        at: this.state.curPosition(),\n      });\n    }\n\n    // remove \"_\" for numeric literal separator, and trailing `m` or `n`\n    const str = this.input.slice(start, this.state.pos).replace(/[_mn]/g, \"\");\n\n    if (isBigInt) {\n      this.finishToken(tt.bigint, str);\n      return;\n    }\n\n    if (isDecimal) {\n      this.finishToken(tt.decimal, str);\n      return;\n    }\n\n    const val = isOctal ? parseInt(str, 8) : parseFloat(str);\n    this.finishToken(tt.num, val);\n  }\n\n  // Read a string value, interpreting backslash-escapes.\n\n  readCodePoint(throwOnInvalid: boolean): number | null {\n    const { code, pos } = readCodePoint(\n      this.input,\n      this.state.pos,\n      this.state.lineStart,\n      this.state.curLine,\n      throwOnInvalid,\n      this.errorHandlers_readCodePoint,\n    );\n    this.state.pos = pos;\n    return code;\n  }\n\n  readString(quote: number): void {\n    const { str, pos, curLine, lineStart } = readStringContents(\n      quote === charCodes.quotationMark ? \"double\" : \"single\",\n      this.input,\n      this.state.pos + 1, // skip the quote\n      this.state.lineStart,\n      this.state.curLine,\n      this.errorHandlers_readStringContents_string,\n    );\n    this.state.pos = pos + 1; // skip the quote\n    this.state.lineStart = lineStart;\n    this.state.curLine = curLine;\n    this.finishToken(tt.string, str);\n  }\n\n  // Reads template continuation `}...`\n  readTemplateContinuation(): void {\n    if (!this.match(tt.braceR)) {\n      this.unexpected(null, tt.braceR);\n    }\n    // rewind pos to `}`\n    this.state.pos--;\n    this.readTemplateToken();\n  }\n\n  // Reads template string tokens.\n  readTemplateToken(): void {\n    const opening = this.input[this.state.pos];\n    const { str, firstInvalidLoc, pos, curLine, lineStart } =\n      readStringContents(\n        \"template\",\n        this.input,\n        this.state.pos + 1, // skip '`' or `}`\n        this.state.lineStart,\n        this.state.curLine,\n        this.errorHandlers_readStringContents_template,\n      );\n    this.state.pos = pos + 1; // skip '`' or `$`\n    this.state.lineStart = lineStart;\n    this.state.curLine = curLine;\n\n    if (firstInvalidLoc) {\n      this.state.firstInvalidTemplateEscapePos = new Position(\n        firstInvalidLoc.curLine,\n        firstInvalidLoc.pos - firstInvalidLoc.lineStart,\n        firstInvalidLoc.pos,\n      );\n    }\n\n    if (this.input.codePointAt(pos) === charCodes.graveAccent) {\n      this.finishToken(\n        tt.templateTail,\n        firstInvalidLoc ? null : opening + str + \"`\",\n      );\n    } else {\n      this.state.pos++; // skip '{'\n      this.finishToken(\n        tt.templateNonTail,\n        firstInvalidLoc ? null : opening + str + \"${\",\n      );\n    }\n  }\n\n  recordStrictModeErrors(\n    toParseError: DeferredStrictError,\n    { at }: { at: Position },\n  ) {\n    const index = at.index;\n\n    if (this.state.strict && !this.state.strictErrors.has(index)) {\n      this.raise(toParseError, { at });\n    } else {\n      this.state.strictErrors.set(index, [toParseError, at]);\n    }\n  }\n\n  // Read an identifier, and return it as a string. Sets `this.state.containsEsc`\n  // to whether the word contained a '\\u' escape.\n  //\n  // Incrementally adds only escaped chars, adding other chunks as-is\n  // as a micro-optimization.\n  //\n  // When `firstCode` is given, it assumes it is always an identifier start and\n  // will skip reading start position again\n\n  readWord1(firstCode?: number): string {\n    this.state.containsEsc = false;\n    let word = \"\";\n    const start = this.state.pos;\n    let chunkStart = this.state.pos;\n    if (firstCode !== undefined) {\n      this.state.pos += firstCode <= 0xffff ? 1 : 2;\n    }\n\n    while (this.state.pos < this.length) {\n      const ch = this.codePointAtPos(this.state.pos);\n      if (isIdentifierChar(ch)) {\n        this.state.pos += ch <= 0xffff ? 1 : 2;\n      } else if (ch === charCodes.backslash) {\n        this.state.containsEsc = true;\n\n        word += this.input.slice(chunkStart, this.state.pos);\n        const escStart = this.state.curPosition();\n        const identifierCheck =\n          this.state.pos === start ? isIdentifierStart : isIdentifierChar;\n\n        if (this.input.charCodeAt(++this.state.pos) !== charCodes.lowercaseU) {\n          this.raise(Errors.MissingUnicodeEscape, {\n            at: this.state.curPosition(),\n          });\n          chunkStart = this.state.pos - 1;\n          continue;\n        }\n\n        ++this.state.pos;\n        const esc = this.readCodePoint(true);\n        if (esc !== null) {\n          if (!identifierCheck(esc)) {\n            this.raise(Errors.EscapedCharNotAnIdentifier, { at: escStart });\n          }\n\n          word += String.fromCodePoint(esc);\n        }\n        chunkStart = this.state.pos;\n      } else {\n        break;\n      }\n    }\n    return word + this.input.slice(chunkStart, this.state.pos);\n  }\n\n  // Read an identifier or keyword token. Will check for reserved\n  // words when necessary.\n\n  readWord(firstCode?: number): void {\n    const word = this.readWord1(firstCode);\n    const type = keywordTypes.get(word);\n    if (type !== undefined) {\n      // We don't use word as state.value here because word is a dynamic string\n      // while token label is a shared constant string\n      this.finishToken(type, tokenLabelName(type));\n    } else {\n      this.finishToken(tt.name, word);\n    }\n  }\n\n  checkKeywordEscapes(): void {\n    const { type } = this.state;\n    if (tokenIsKeyword(type) && this.state.containsEsc) {\n      this.raise(Errors.InvalidEscapedReservedWord, {\n        at: this.state.startLoc,\n        reservedWord: tokenLabelName(type),\n      });\n    }\n  }\n\n  /**\n   * Raise a `ParseError` given the appropriate properties. If passed a\n   * `Position` for the `at` property, raises the `ParseError` at that location.\n   * Otherwise, if passed a `Node`, raises the `ParseError` at the start\n   * location of that `Node`.\n   *\n   * If `errorRecovery` is `true`, the error is pushed to the errors array and\n   * returned. If `errorRecovery` is `false`, the error is instead thrown.\n   */\n  raise<ErrorDetails>(\n    toParseError: ParseErrorConstructor<ErrorDetails>,\n    raiseProperties: RaiseProperties<ErrorDetails>,\n  ): ParseError<ErrorDetails> {\n    const { at, ...details } = raiseProperties;\n    const loc = at instanceof Position ? at : at.loc.start;\n    // @ts-expect-error: refine details typing\n    const error = toParseError({ loc, details });\n\n    if (!this.options.errorRecovery) throw error;\n    if (!this.isLookahead) this.state.errors.push(error);\n\n    return error;\n  }\n\n  /**\n   * If `errorRecovery` is `false`, this method behaves identically to `raise`.\n   * If `errorRecovery` is `true`, this method will first see if there is\n   * already an error stored at the same `Position`, and replaces it with the\n   * one generated here.\n   */\n  raiseOverwrite<ErrorDetails>(\n    toParseError: ParseErrorConstructor<ErrorDetails>,\n    raiseProperties: RaiseProperties<ErrorDetails>,\n  ): ParseError<ErrorDetails> | never {\n    const { at, ...details } = raiseProperties;\n    const loc = at instanceof Position ? at : at.loc.start;\n    const pos = loc.index;\n    const errors = this.state.errors;\n\n    for (let i = errors.length - 1; i >= 0; i--) {\n      const error = errors[i];\n      if (error.loc.index === pos) {\n        // @ts-expect-error: refine details typing\n        return (errors[i] = toParseError({ loc, details }));\n      }\n      if (error.loc.index < pos) break;\n    }\n\n    return this.raise(toParseError, raiseProperties);\n  }\n\n  // updateContext is used by the jsx plugin\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  updateContext(prevType: TokenType): void {}\n\n  // Raise an unexpected token error. Can take the expected token type.\n  unexpected(loc?: Position | null, type?: TokenType): void {\n    throw this.raise(Errors.UnexpectedToken, {\n      expected: type ? tokenLabelName(type) : null,\n      at: loc != null ? loc : this.state.startLoc,\n    });\n  }\n\n  expectPlugin(pluginName: Plugin, loc?: Position): true {\n    if (this.hasPlugin(pluginName)) {\n      return true;\n    }\n\n    throw this.raise(Errors.MissingPlugin, {\n      at: loc != null ? loc : this.state.startLoc,\n      missingPlugin: [pluginName],\n    });\n  }\n\n  expectOnePlugin(pluginNames: Plugin[]): void {\n    if (!pluginNames.some(name => this.hasPlugin(name))) {\n      throw this.raise(Errors.MissingOneOfPlugins, {\n        at: this.state.startLoc,\n        missingPlugin: pluginNames,\n      });\n    }\n  }\n\n  errorBuilder(error: ParseErrorConstructor<{}>) {\n    return (pos: number, lineStart: number, curLine: number) => {\n      this.raise(error, {\n        at: buildPosition(pos, lineStart, curLine),\n      });\n    };\n  }\n\n  errorHandlers_readInt: IntErrorHandlers = {\n    invalidDigit: (pos, lineStart, curLine, radix) => {\n      if (!this.options.errorRecovery) return false;\n\n      this.raise(Errors.InvalidDigit, {\n        at: buildPosition(pos, lineStart, curLine),\n        radix,\n      });\n      // Continue parsing the number as if there was no invalid digit.\n      return true;\n    },\n    numericSeparatorInEscapeSequence: this.errorBuilder(\n      Errors.NumericSeparatorInEscapeSequence,\n    ),\n    unexpectedNumericSeparator: this.errorBuilder(\n      Errors.UnexpectedNumericSeparator,\n    ),\n  };\n\n  errorHandlers_readCodePoint: CodePointErrorHandlers = {\n    ...this.errorHandlers_readInt,\n    invalidEscapeSequence: this.errorBuilder(Errors.InvalidEscapeSequence),\n    invalidCodePoint: this.errorBuilder(Errors.InvalidCodePoint),\n  };\n\n  errorHandlers_readStringContents_string: StringContentsErrorHandlers = {\n    ...this.errorHandlers_readCodePoint,\n    strictNumericEscape: (pos, lineStart, curLine) => {\n      this.recordStrictModeErrors(Errors.StrictNumericEscape, {\n        at: buildPosition(pos, lineStart, curLine),\n      });\n    },\n    unterminated: (pos, lineStart, curLine) => {\n      throw this.raise(Errors.UnterminatedString, {\n        // Report the error at the string quote\n        at: buildPosition(pos - 1, lineStart, curLine),\n      });\n    },\n  };\n\n  errorHandlers_readStringContents_template: StringContentsErrorHandlers = {\n    ...this.errorHandlers_readCodePoint,\n    strictNumericEscape: this.errorBuilder(Errors.StrictNumericEscape),\n    unterminated: (pos, lineStart, curLine) => {\n      throw this.raise(Errors.UnterminatedTemplate, {\n        at: buildPosition(pos, lineStart, curLine),\n      });\n    },\n  };\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AAKA,IAAAC,SAAA,GAAAD,OAAA;AAGA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAQA,IAAAI,WAAA,GAAAJ,OAAA;AAMA,IAAAK,WAAA,GAAAL,OAAA;AAOA,IAAAM,MAAA,GAAAN,OAAA;AAGA,IAAAO,mBAAA,GAAAP,OAAA;AAOqC,MAAAQ,SAAA;EAAAC,UAAA;AAAA,SAAAC,8BAAAC,MAAA,EAAAC,QAAA,QAAAD,MAAA,yBAAAE,MAAA,WAAAC,UAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAL,MAAA,OAAAM,GAAA,EAAAC,CAAA,OAAAA,CAAA,MAAAA,CAAA,GAAAJ,UAAA,CAAAK,MAAA,EAAAD,CAAA,MAAAD,GAAA,GAAAH,UAAA,CAAAI,CAAA,OAAAN,QAAA,CAAAQ,OAAA,CAAAH,GAAA,kBAAAJ,MAAA,CAAAI,GAAA,IAAAN,MAAA,CAAAM,GAAA,YAAAJ,MAAA;AAIrC,SAASQ,aAAaA,CAACC,GAAW,EAAEC,SAAiB,EAAEC,OAAe,EAAE;EACtE,OAAO,IAAIC,kBAAQ,CAACD,OAAO,EAAEF,GAAG,GAAGC,SAAS,EAAED,GAAG,CAAC;AACpD;AAEA,MAAMI,iBAAiB,GAAG,IAAIC,GAAG,CAAC,wCASjC,CAAC;AAMK,MAAMC,KAAK,CAAC;EACjBC,WAAWA,CAACC,KAAY,EAAE;IACxB,IAAI,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACtB,IAAI,CAACC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACxB,IAAI,CAACC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACxB,IAAI,CAACC,GAAG,GAAGJ,KAAK,CAACI,GAAG;IACpB,IAAI,CAACC,GAAG,GAAG,IAAIC,wBAAc,CAACN,KAAK,CAACO,QAAQ,EAAEP,KAAK,CAACQ,MAAM,CAAC;EAC7D;AAOF;AAACC,OAAA,CAAAX,KAAA,GAAAA,KAAA;AAIc,MAAeY,SAAS,SAASC,iBAAc,CAAC;EAM7DZ,WAAWA,CAACa,OAAgB,EAAEC,KAAa,EAAE;IAC3C,KAAK,CAAC,CAAC;IAAC,KANVC,WAAW;IAAA,KAGXC,MAAM,GAA6B,EAAE;IAAA,KAi8CrCC,qBAAqB,GAAqB;MACxCC,YAAY,EAAEA,CAACzB,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAEwB,KAAK,KAAK;QAChD,IAAI,CAAC,IAAI,CAACN,OAAO,CAACO,aAAa,EAAE,OAAO,KAAK;QAE7C,IAAI,CAACC,KAAK,CAACC,kBAAM,CAACC,YAAY,EAAE;UAC9BC,EAAE,EAAEhC,aAAa,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,CAAC;UAC1CwB;QACF,CAAC,CAAC;QAEF,OAAO,IAAI;MACb,CAAC;MACDM,gCAAgC,EAAE,IAAI,CAACC,YAAY,CACjDJ,kBAAM,CAACK,gCACT,CAAC;MACDC,0BAA0B,EAAE,IAAI,CAACF,YAAY,CAC3CJ,kBAAM,CAACO,0BACT;IACF,CAAC;IAAA,KAEDC,2BAA2B,GAAA5C,MAAA,CAAA6C,MAAA,KACtB,IAAI,CAACd,qBAAqB;MAC7Be,qBAAqB,EAAE,IAAI,CAACN,YAAY,CAACJ,kBAAM,CAACW,qBAAqB,CAAC;MACtEC,gBAAgB,EAAE,IAAI,CAACR,YAAY,CAACJ,kBAAM,CAACa,gBAAgB;IAAC;IAAA,KAG9DC,uCAAuC,GAAAlD,MAAA,CAAA6C,MAAA,KAClC,IAAI,CAACD,2BAA2B;MACnCO,mBAAmB,EAAEA,CAAC5C,GAAG,EAAEC,SAAS,EAAEC,OAAO,KAAK;QAChD,IAAI,CAAC2C,sBAAsB,CAAChB,kBAAM,CAACiB,mBAAmB,EAAE;UACtDf,EAAE,EAAEhC,aAAa,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO;QAC3C,CAAC,CAAC;MACJ,CAAC;MACD6C,YAAY,EAAEA,CAAC/C,GAAG,EAAEC,SAAS,EAAEC,OAAO,KAAK;QACzC,MAAM,IAAI,CAAC0B,KAAK,CAACC,kBAAM,CAACmB,kBAAkB,EAAE;UAE1CjB,EAAE,EAAEhC,aAAa,CAACC,GAAG,GAAG,CAAC,EAAEC,SAAS,EAAEC,OAAO;QAC/C,CAAC,CAAC;MACJ;IAAC;IAAA,KAGH+C,yCAAyC,GAAAxD,MAAA,CAAA6C,MAAA,KACpC,IAAI,CAACD,2BAA2B;MACnCO,mBAAmB,EAAE,IAAI,CAACX,YAAY,CAACJ,kBAAM,CAACiB,mBAAmB,CAAC;MAClEC,YAAY,EAAEA,CAAC/C,GAAG,EAAEC,SAAS,EAAEC,OAAO,KAAK;QACzC,MAAM,IAAI,CAAC0B,KAAK,CAACC,kBAAM,CAACqB,oBAAoB,EAAE;UAC5CnB,EAAE,EAAEhC,aAAa,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO;QAC3C,CAAC,CAAC;MACJ;IAAC;IA5+CD,IAAI,CAACM,KAAK,GAAG,IAAI2C,cAAK,CAAC,CAAC;IACxB,IAAI,CAAC3C,KAAK,CAAC4C,IAAI,CAAChC,OAAO,CAAC;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACxB,MAAM,GAAGwB,KAAK,CAACxB,MAAM;IAC1B,IAAI,CAACyB,WAAW,GAAG,KAAK;EAC1B;EAEA+B,SAASA,CAACC,KAAwB,EAAE;IAGlC,IAAI,CAAC/B,MAAM,CAAC1B,MAAM,GAAG,IAAI,CAACW,KAAK,CAAC+C,YAAY;IAC5C,IAAI,CAAChC,MAAM,CAACiC,IAAI,CAACF,KAAK,CAAC;IACvB,EAAE,IAAI,CAAC9C,KAAK,CAAC+C,YAAY;EAC3B;EAIAE,IAAIA,CAAA,EAAS;IACX,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACtC,OAAO,CAACG,MAAM,EAAE;MACvB,IAAI,CAAC8B,SAAS,CAAC,IAAI/C,KAAK,CAAC,IAAI,CAACE,KAAK,CAAC,CAAC;IACvC;IAEA,IAAI,CAACA,KAAK,CAACmD,YAAY,GAAG,IAAI,CAACnD,KAAK,CAACG,KAAK;IAC1C,IAAI,CAACH,KAAK,CAACoD,aAAa,GAAG,IAAI,CAACpD,KAAK,CAACQ,MAAM;IAC5C,IAAI,CAACR,KAAK,CAACqD,eAAe,GAAG,IAAI,CAACrD,KAAK,CAACO,QAAQ;IAChD,IAAI,CAAC+C,SAAS,CAAC,CAAC;EAClB;EAEAC,GAAGA,CAACtD,IAAe,EAAW;IAC5B,IAAI,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAAC,EAAE;MACpB,IAAI,CAACgD,IAAI,CAAC,CAAC;MACX,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAKAO,KAAKA,CAACvD,IAAe,EAAW;IAC9B,OAAO,IAAI,CAACD,KAAK,CAACC,IAAI,KAAKA,IAAI;EACjC;EAKAwD,oBAAoBA,CAACzD,KAAY,EAAkB;IACjD,OAAO;MACLR,GAAG,EAAEQ,KAAK,CAACR,GAAG;MACdU,KAAK,EAAE,IAAI;MACXD,IAAI,EAAED,KAAK,CAACC,IAAI;MAChBE,KAAK,EAAEH,KAAK,CAACG,KAAK;MAClBC,GAAG,EAAEJ,KAAK,CAACI,GAAG;MACdsD,OAAO,EAAE,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;MAC5BC,MAAM,EAAE5D,KAAK,CAAC4D,MAAM;MACpBrD,QAAQ,EAAEP,KAAK,CAACO,QAAQ;MACxB6C,aAAa,EAAEpD,KAAK,CAACoD,aAAa;MAClC1D,OAAO,EAAEM,KAAK,CAACN,OAAO;MACtBD,SAAS,EAAEO,KAAK,CAACP,SAAS;MAC1BoE,WAAW,EAAE7D,KAAK,CAAC6D;IACrB,CAAC;EACH;EAeAC,SAASA,CAAA,EAAmB;IAC1B,MAAMC,GAAG,GAAG,IAAI,CAAC/D,KAAK;IAEtB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACyD,oBAAoB,CAACM,GAAG,CAAC;IAE3C,IAAI,CAACjD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACwC,SAAS,CAAC,CAAC;IAChB,IAAI,CAACxC,WAAW,GAAG,KAAK;IAExB,MAAMkD,IAAI,GAAG,IAAI,CAAChE,KAAK;IACvB,IAAI,CAACA,KAAK,GAAG+D,GAAG;IAChB,OAAOC,IAAI;EACb;EAEAC,cAAcA,CAAA,EAAW;IACvB,OAAO,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAClE,KAAK,CAACR,GAAG,CAAC;EACjD;EAEA0E,mBAAmBA,CAAC1E,GAAW,EAAU;IACvC2E,0BAAc,CAACC,SAAS,GAAG5E,GAAG;IAC9B,OAAO2E,0BAAc,CAACE,IAAI,CAAC,IAAI,CAACxD,KAAK,CAAC,GAAGsD,0BAAc,CAACC,SAAS,GAAG5E,GAAG;EACzE;EAEA8E,iBAAiBA,CAAA,EAAW;IAC1B,OAAO,IAAI,CAACzD,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACN,cAAc,CAAC,CAAC,CAAC;EACrD;EAQAO,oBAAoBA,CAAA,EAAW;IAC7B,OAAO,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAACzE,KAAK,CAACR,GAAG,CAAC;EACvD;EAEAiF,yBAAyBA,CAACjF,GAAW,EAAU;IAC7CkF,gCAAoB,CAACN,SAAS,GAAG5E,GAAG;IACpC,OAAOkF,gCAAoB,CAACL,IAAI,CAAC,IAAI,CAACxD,KAAK,CAAC,GACxC6D,gCAAoB,CAACN,SAAS,GAC9B5E,GAAG;EACT;EASAmF,uBAAuBA,CAAA,EAAW;IAChC,OAAO,IAAI,CAAC9D,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;EAC3D;EAEAI,cAAcA,CAACpF,GAAW,EAAU;IAKlC,IAAIqF,EAAE,GAAG,IAAI,CAAChE,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,CAAC;IACnC,IAAI,CAACqF,EAAE,GAAG,MAAM,MAAM,MAAM,IAAI,EAAErF,GAAG,GAAG,IAAI,CAACqB,KAAK,CAACxB,MAAM,EAAE;MACzD,MAAMyF,KAAK,GAAG,IAAI,CAACjE,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,CAAC;MACxC,IAAI,CAACsF,KAAK,GAAG,MAAM,MAAM,MAAM,EAAE;QAC/BD,EAAE,GAAG,OAAO,IAAI,CAACA,EAAE,GAAG,KAAK,KAAK,EAAE,CAAC,IAAIC,KAAK,GAAG,KAAK,CAAC;MACvD;IACF;IACA,OAAOD,EAAE;EACX;EAKAE,SAASA,CAACC,MAAe,EAAQ;IAC/B,IAAI,CAAChF,KAAK,CAACgF,MAAM,GAAGA,MAAM;IAC1B,IAAIA,MAAM,EAAE;MAKV,IAAI,CAAChF,KAAK,CAACiF,YAAY,CAACC,OAAO,CAAC,CAAC,CAACC,YAAY,EAAE5D,EAAE,CAAC,KACjD,IAAI,CAACH,KAAK,CAAC+D,YAAY,EAAE;QAAE5D;MAAG,CAAC,CACjC,CAAC;MACD,IAAI,CAACvB,KAAK,CAACiF,YAAY,CAACG,KAAK,CAAC,CAAC;IACjC;EACF;EAEAzB,UAAUA,CAAA,EAAe;IACvB,OAAO,IAAI,CAAC3D,KAAK,CAAC0D,OAAO,CAAC,IAAI,CAAC1D,KAAK,CAAC0D,OAAO,CAACrE,MAAM,GAAG,CAAC,CAAC;EAC1D;EAGAiE,SAASA,CAAA,EAAS;IAChB,IAAI,CAAC+B,SAAS,CAAC,CAAC;IAChB,IAAI,CAACrF,KAAK,CAACG,KAAK,GAAG,IAAI,CAACH,KAAK,CAACR,GAAG;IACjC,IAAI,CAAC,IAAI,CAACsB,WAAW,EAAE,IAAI,CAACd,KAAK,CAACO,QAAQ,GAAG,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC;IACrE,IAAI,IAAI,CAAC7D,KAAK,CAACR,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;MACjC,IAAI,CAACiG,WAAW,IAAO,CAAC;MACxB;IACF;IAEA,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACX,cAAc,CAAC,IAAI,CAAC5E,KAAK,CAACR,GAAG,CAAC,CAAC;EAC5D;EAKAgG,gBAAgBA,CAACC,UAAwB,EAA8B;IACrE,IAAIlF,QAAQ;IACZ,IAAI,CAAC,IAAI,CAACO,WAAW,EAAEP,QAAQ,GAAG,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC;IAC1D,MAAM1D,KAAK,GAAG,IAAI,CAACH,KAAK,CAACR,GAAG;IAC5B,MAAMY,GAAG,GAAG,IAAI,CAACS,KAAK,CAACvB,OAAO,CAACmG,UAAU,EAAEtF,KAAK,GAAG,CAAC,CAAC;IACrD,IAAIC,GAAG,KAAK,CAAC,CAAC,EAAE;MAId,MAAM,IAAI,CAACgB,KAAK,CAACC,kBAAM,CAACqE,mBAAmB,EAAE;QAC3CnE,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;MAC7B,CAAC,CAAC;IACJ;IAEA,IAAI,CAAC7D,KAAK,CAACR,GAAG,GAAGY,GAAG,GAAGqF,UAAU,CAACpG,MAAM;IACxCsG,sBAAU,CAACvB,SAAS,GAAGjE,KAAK,GAAG,CAAC;IAChC,OAAOwF,sBAAU,CAACtB,IAAI,CAAC,IAAI,CAACxD,KAAK,CAAC,IAAI8E,sBAAU,CAACvB,SAAS,IAAIhE,GAAG,EAAE;MACjE,EAAE,IAAI,CAACJ,KAAK,CAACN,OAAO;MACpB,IAAI,CAACM,KAAK,CAACP,SAAS,GAAGkG,sBAAU,CAACvB,SAAS;IAC7C;IAIA,IAAI,IAAI,CAACtD,WAAW,EAAE;IAGtB,MAAM8E,OAAuB,GAAG;MAC9B3F,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,IAAI,CAACW,KAAK,CAACgF,KAAK,CAAC1F,KAAK,GAAG,CAAC,EAAEC,GAAG,CAAC;MACvCD,KAAK;MACLC,GAAG,EAAEA,GAAG,GAAGqF,UAAU,CAACpG,MAAM;MAC5BgB,GAAG,EAAE,IAAIC,wBAAc,CAACC,QAAQ,EAAE,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC;IAC5D,CAAC;IACD,IAAI,IAAI,CAACjD,OAAO,CAACG,MAAM,EAAE,IAAI,CAAC8B,SAAS,CAAC+C,OAAO,CAAC;IAChD,OAAOA,OAAO;EAChB;EAEAE,eAAeA,CAACC,SAAiB,EAA6B;IAC5D,MAAM5F,KAAK,GAAG,IAAI,CAACH,KAAK,CAACR,GAAG;IAC5B,IAAIe,QAAQ;IACZ,IAAI,CAAC,IAAI,CAACO,WAAW,EAAEP,QAAQ,GAAG,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC;IAC1D,IAAImC,EAAE,GAAG,IAAI,CAACnF,KAAK,CAAC0D,UAAU,CAAE,IAAI,CAACvE,KAAK,CAACR,GAAG,IAAIuG,SAAU,CAAC;IAC7D,IAAI,IAAI,CAAC/F,KAAK,CAACR,GAAG,GAAG,IAAI,CAACH,MAAM,EAAE;MAChC,OAAO,CAAC,IAAA4G,qBAAS,EAACD,EAAE,CAAC,IAAI,EAAE,IAAI,CAAChG,KAAK,CAACR,GAAG,GAAG,IAAI,CAACH,MAAM,EAAE;QACvD2G,EAAE,GAAG,IAAI,CAACnF,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;MAC5C;IACF;IAIA,IAAI,IAAI,CAACsB,WAAW,EAAE;IAGtB,MAAMV,GAAG,GAAG,IAAI,CAACJ,KAAK,CAACR,GAAG;IAC1B,MAAMU,KAAK,GAAG,IAAI,CAACW,KAAK,CAACgF,KAAK,CAAC1F,KAAK,GAAG4F,SAAS,EAAE3F,GAAG,CAAC;IAEtD,MAAMwF,OAAsB,GAAG;MAC7B3F,IAAI,EAAE,aAAa;MACnBC,KAAK;MACLC,KAAK;MACLC,GAAG;MACHC,GAAG,EAAE,IAAIC,wBAAc,CAACC,QAAQ,EAAE,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC;IAC5D,CAAC;IACD,IAAI,IAAI,CAACjD,OAAO,CAACG,MAAM,EAAE,IAAI,CAAC8B,SAAS,CAAC+C,OAAO,CAAC;IAChD,OAAOA,OAAO;EAChB;EAKAP,SAASA,CAAA,EAAS;IAChB,MAAMa,UAAU,GAAG,IAAI,CAAClG,KAAK,CAACR,GAAG;IACjC,MAAM2G,QAAQ,GAAG,EAAE;IACnBC,IAAI,EAAE,OAAO,IAAI,CAACpG,KAAK,CAACR,GAAG,GAAG,IAAI,CAACH,MAAM,EAAE;MACzC,MAAM2G,EAAE,GAAG,IAAI,CAACnF,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;MAChD,QAAQwG,EAAE;QACR;QACA;QACA;UACE,EAAE,IAAI,CAAChG,KAAK,CAACR,GAAG;UAChB;QACF;UACE,IACE,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,OAAuB,EAChE;YACA,EAAE,IAAI,CAACQ,KAAK,CAACR,GAAG;UAClB;QAEF;QACA;QACA;UACE,EAAE,IAAI,CAACQ,KAAK,CAACR,GAAG;UAChB,EAAE,IAAI,CAACQ,KAAK,CAACN,OAAO;UACpB,IAAI,CAACM,KAAK,CAACP,SAAS,GAAG,IAAI,CAACO,KAAK,CAACR,GAAG;UACrC;QAEF;UACE,QAAQ,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;YAC/C;cAAyB;gBACvB,MAAMoG,OAAO,GAAG,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAAC;gBAC3C,IAAII,OAAO,KAAKS,SAAS,EAAE;kBACzB,IAAI,CAACC,UAAU,CAACV,OAAO,CAAC;kBACxB,IAAI,IAAI,CAAChF,OAAO,CAAC2F,aAAa,EAAEJ,QAAQ,CAACnD,IAAI,CAAC4C,OAAO,CAAC;gBACxD;gBACA;cACF;YAEA;cAAsB;gBACpB,MAAMA,OAAO,GAAG,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;gBACvC,IAAIF,OAAO,KAAKS,SAAS,EAAE;kBACzB,IAAI,CAACC,UAAU,CAACV,OAAO,CAAC;kBACxB,IAAI,IAAI,CAAChF,OAAO,CAAC2F,aAAa,EAAEJ,QAAQ,CAACnD,IAAI,CAAC4C,OAAO,CAAC;gBACxD;gBACA;cACF;YAEA;cACE,MAAMQ,IAAI;UACd;UACA;QAEF;UACE,IAAI,IAAAI,wBAAY,EAACR,EAAE,CAAC,EAAE;YACpB,EAAE,IAAI,CAAChG,KAAK,CAACR,GAAG;UAClB,CAAC,MAAM,IACLwG,EAAE,OAAmB,IACrB,CAAC,IAAI,CAACS,QAAQ,IACd,IAAI,CAAC7F,OAAO,CAAC8F,MAAM,EACnB;YACA,MAAMlH,GAAG,GAAG,IAAI,CAACQ,KAAK,CAACR,GAAG;YAC1B,IACE,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC,OAAmB,IACjD,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC,OAA0B,KACvD0G,UAAU,KAAK,CAAC,IAAI,IAAI,CAAClG,KAAK,CAACP,SAAS,GAAGyG,UAAU,CAAC,EACvD;cAEA,MAAMN,OAAO,GAAG,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;cACvC,IAAIF,OAAO,KAAKS,SAAS,EAAE;gBACzB,IAAI,CAACC,UAAU,CAACV,OAAO,CAAC;gBACxB,IAAI,IAAI,CAAChF,OAAO,CAAC2F,aAAa,EAAEJ,QAAQ,CAACnD,IAAI,CAAC4C,OAAO,CAAC;cACxD;YACF,CAAC,MAAM;cACL,MAAMQ,IAAI;YACZ;UACF,CAAC,MAAM,IACLJ,EAAE,OAAuB,IACzB,CAAC,IAAI,CAACS,QAAQ,IACd,IAAI,CAAC7F,OAAO,CAAC8F,MAAM,EACnB;YACA,MAAMlH,GAAG,GAAG,IAAI,CAACQ,KAAK,CAACR,GAAG;YAC1B,IACE,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC,OAA8B,IAC5D,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC,OAAmB,IACjD,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC,OAAmB,EACjD;cAEA,MAAMoG,OAAO,GAAG,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;cACvC,IAAIF,OAAO,KAAKS,SAAS,EAAE;gBACzB,IAAI,CAACC,UAAU,CAACV,OAAO,CAAC;gBACxB,IAAI,IAAI,CAAChF,OAAO,CAAC2F,aAAa,EAAEJ,QAAQ,CAACnD,IAAI,CAAC4C,OAAO,CAAC;cACxD;YACF,CAAC,MAAM;cACL,MAAMQ,IAAI;YACZ;UACF,CAAC,MAAM;YACL,MAAMA,IAAI;UACZ;MACJ;IACF;IAEA,IAAID,QAAQ,CAAC9G,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMe,GAAG,GAAG,IAAI,CAACJ,KAAK,CAACR,GAAG;MAC1B,MAAMmH,iBAAoC,GAAG;QAC3CxG,KAAK,EAAE+F,UAAU;QACjB9F,GAAG;QACH+F,QAAQ;QACRS,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAE,IAAI;QAClBC,cAAc,EAAE;MAClB,CAAC;MACD,IAAI,CAAC9G,KAAK,CAAC+G,YAAY,CAAC/D,IAAI,CAAC2D,iBAAiB,CAAC;IACjD;EACF;EAOArB,WAAWA,CAACrF,IAAe,EAAE+G,GAAS,EAAQ;IAC5C,IAAI,CAAChH,KAAK,CAACI,GAAG,GAAG,IAAI,CAACJ,KAAK,CAACR,GAAG;IAC/B,IAAI,CAACQ,KAAK,CAACQ,MAAM,GAAG,IAAI,CAACR,KAAK,CAAC6D,WAAW,CAAC,CAAC;IAC5C,MAAMoD,QAAQ,GAAG,IAAI,CAACjH,KAAK,CAACC,IAAI;IAChC,IAAI,CAACD,KAAK,CAACC,IAAI,GAAGA,IAAI;IACtB,IAAI,CAACD,KAAK,CAACE,KAAK,GAAG8G,GAAG;IAEtB,IAAI,CAAC,IAAI,CAAClG,WAAW,EAAE;MACrB,IAAI,CAACoG,aAAa,CAACD,QAAQ,CAAC;IAC9B;EACF;EAEAE,YAAYA,CAAClH,IAAe,EAAQ;IAClC,IAAI,CAACD,KAAK,CAACC,IAAI,GAAGA,IAAI;IAGtB,IAAI,CAACiH,aAAa,CAAC,CAAC;EACtB;EAYAE,oBAAoBA,CAAA,EAAS;IAC3B,IAAI,IAAI,CAACpH,KAAK,CAACR,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC6H,qBAAqB,CAAC,CAAC,EAAE;MACxD;IACF;IAEA,MAAMC,OAAO,GAAG,IAAI,CAACtH,KAAK,CAACR,GAAG,GAAG,CAAC;IAClC,MAAMyD,IAAI,GAAG,IAAI,CAAC2B,cAAc,CAAC0C,OAAO,CAAC;IACzC,IAAIrE,IAAI,MAAoB,IAAIA,IAAI,MAAoB,EAAE;MACxD,MAAM,IAAI,CAAC7B,KAAK,CAACC,kBAAM,CAACkG,wBAAwB,EAAE;QAChDhG,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;MAC7B,CAAC,CAAC;IACJ;IAEA,IACEZ,IAAI,QAA6B,IAChCA,IAAI,OAAgC,IAAI,IAAI,CAACuE,SAAS,CAAC,gBAAgB,CAAE,EAC1E;MAKA,IAAI,CAACC,YAAY,CAAC,gBAAgB,CAAC;MACnC,IAAI,IAAI,CAACC,eAAe,CAAC,gBAAgB,EAAE,YAAY,CAAC,KAAK,KAAK,EAAE;QAClE,MAAM,IAAI,CAACtG,KAAK,CACd6B,IAAI,QAA6B,GAC7B5B,kBAAM,CAACsG,4CAA4C,GACnDtG,kBAAM,CAACuG,2CAA2C,EACtD;UAAErG,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;QAAE,CACjC,CAAC;MACH;MAEA,IAAI,CAAC7D,KAAK,CAACR,GAAG,IAAI,CAAC;MACnB,IAAIyD,IAAI,QAA6B,EAAE;QAErC,IAAI,CAACqC,WAAW,EAAc,CAAC;MACjC,CAAC,MAAM;QAEL,IAAI,CAACA,WAAW,EAAgB,CAAC;MACnC;IACF,CAAC,MAAM,IAAI,IAAAuC,6BAAiB,EAAC5E,IAAI,CAAC,EAAE;MAClC,EAAE,IAAI,CAACjD,KAAK,CAACR,GAAG;MAChB,IAAI,CAAC8F,WAAW,MAAiB,IAAI,CAACwC,SAAS,CAAC7E,IAAI,CAAC,CAAC;IACxD,CAAC,MAAM,IAAIA,IAAI,OAAwB,EAAE;MACvC,EAAE,IAAI,CAACjD,KAAK,CAACR,GAAG;MAChB,IAAI,CAAC8F,WAAW,MAAiB,IAAI,CAACwC,SAAS,CAAC,CAAC,CAAC;IACpD,CAAC,MAAM;MACL,IAAI,CAACC,QAAQ,KAAU,CAAC,CAAC;IAC3B;EACF;EAEAC,aAAaA,CAAA,EAAS;IACpB,MAAM/E,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IACtD,IAAIyD,IAAI,MAAoB,IAAIA,IAAI,MAAoB,EAAE;MACxD,IAAI,CAACgF,UAAU,CAAC,IAAI,CAAC;MACrB;IACF;IAEA,IACEhF,IAAI,OAAkB,IACtB,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,OAAkB,EAC3D;MACA,IAAI,CAACQ,KAAK,CAACR,GAAG,IAAI,CAAC;MACnB,IAAI,CAAC8F,WAAW,GAAY,CAAC;IAC/B,CAAC,MAAM;MACL,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;MAChB,IAAI,CAAC8F,WAAW,GAAO,CAAC;IAC1B;EACF;EAEA4C,eAAeA,CAAA,EAAS;IACtB,MAAMjF,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IACtD,IAAIyD,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAAC8E,QAAQ,KAAiB,CAAC,CAAC;IAClC,CAAC,MAAM;MACL,IAAI,CAACA,QAAQ,KAAW,CAAC,CAAC;IAC5B;EACF;EAEAV,qBAAqBA,CAAA,EAAY;IAC/B,IAAI,IAAI,CAACrH,KAAK,CAACR,GAAG,KAAK,CAAC,IAAI,IAAI,CAACH,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK;IAEzD,IAAI2G,EAAE,GAAG,IAAI,CAACnF,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IAClD,IAAIwG,EAAE,OAA8B,EAAE,OAAO,KAAK;IAElD,MAAM7F,KAAK,GAAG,IAAI,CAACH,KAAK,CAACR,GAAG;IAC5B,IAAI,CAACQ,KAAK,CAACR,GAAG,IAAI,CAAC;IAEnB,OAAO,CAAC,IAAAyG,qBAAS,EAACD,EAAE,CAAC,IAAI,EAAE,IAAI,CAAChG,KAAK,CAACR,GAAG,GAAG,IAAI,CAACH,MAAM,EAAE;MACvD2G,EAAE,GAAG,IAAI,CAACnF,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;IAC5C;IAEA,MAAMU,KAAK,GAAG,IAAI,CAACW,KAAK,CAACgF,KAAK,CAAC1F,KAAK,GAAG,CAAC,EAAE,IAAI,CAACH,KAAK,CAACR,GAAG,CAAC;IAEzD,IAAI,CAAC8F,WAAW,KAA0BpF,KAAK,CAAC;IAEhD,OAAO,IAAI;EACb;EAEAiI,qBAAqBA,CAACC,IAAY,EAAQ;IAExC,IAAInI,IAAI,GAAGmI,IAAI,OAAuB,UAAsB;IAC5D,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIpF,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IAGpD,IAAI4I,IAAI,OAAuB,IAAInF,IAAI,OAAuB,EAAE;MAC9DoF,KAAK,EAAE;MACPpF,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;MAChDS,IAAI,KAAc;IACpB;IAGA,IAAIgD,IAAI,OAAuB,IAAI,CAAC,IAAI,CAACjD,KAAK,CAAC4D,MAAM,EAAE;MACrDyE,KAAK,EAAE;MAIPpI,IAAI,GAAGmI,IAAI,OAA0B,UAA8B;IACrE;IAEA,IAAI,CAACL,QAAQ,CAAC9H,IAAI,EAAEoI,KAAK,CAAC;EAC5B;EAEAC,kBAAkBA,CAACF,IAAY,EAAQ;IAErC,MAAMnF,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IAEtD,IAAIyD,IAAI,KAAKmF,IAAI,EAAE;MACjB,IAAI,IAAI,CAACvH,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,OAAuB,EAAE;QACpE,IAAI,CAACuI,QAAQ,KAAY,CAAC,CAAC;MAC7B,CAAC,MAAM;QACL,IAAI,CAACA,QAAQ,CACXK,IAAI,QAA0B,UAA+B,EAC7D,CACF,CAAC;MACH;MACA;IACF;IAEA,IAAIA,IAAI,QAA0B,EAAE;MAElC,IAAInF,IAAI,OAA0B,EAAE;QAClC,IAAI,CAAC8E,QAAQ,KAAc,CAAC,CAAC;QAC7B;MACF;MAEA,IACE,IAAI,CAACP,SAAS,CAAC,gBAAgB,CAAC,IAChCvE,IAAI,QAA8B,EAClC;QACA,IAAI,IAAI,CAACyE,eAAe,CAAC,gBAAgB,EAAE,YAAY,CAAC,KAAK,KAAK,EAAE;UAClE,MAAM,IAAI,CAACtG,KAAK,CAACC,kBAAM,CAACkH,yCAAyC,EAAE;YACjEhH,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;UAC7B,CAAC,CAAC;QACJ;QACA,IAAI,CAAC7D,KAAK,CAACR,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC8F,WAAW,EAAa,CAAC;QAC9B;MACF;MAGA,IACE,IAAI,CAACkC,SAAS,CAAC,gBAAgB,CAAC,IAChCvE,IAAI,OAAiC,EACrC;QACA,IAAI,IAAI,CAACyE,eAAe,CAAC,gBAAgB,EAAE,YAAY,CAAC,KAAK,KAAK,EAAE;UAClE,MAAM,IAAI,CAACtG,KAAK,CAACC,kBAAM,CAACmH,wCAAwC,EAAE;YAChEjH,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;UAC7B,CAAC,CAAC;QACJ;QACA,IAAI,CAAC7D,KAAK,CAACR,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC8F,WAAW,EAAe,CAAC;QAChC;MACF;IACF;IAEA,IAAIrC,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAAC8E,QAAQ,KAAY,CAAC,CAAC;MAC3B;IACF;IAEA,IAAI,CAACA,QAAQ,CACXK,IAAI,QAA0B,UAA+B,EAC7D,CACF,CAAC;EACH;EAEAK,eAAeA,CAAA,EAAS;IACtB,MAAMxF,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IAGtD,IAAIyD,IAAI,OAAuB,IAAI,CAAC,IAAI,CAACjD,KAAK,CAAC4D,MAAM,EAAE;MAIrD,IAAI,CAACmE,QAAQ,KAAe,CAAC,CAAC;IAChC,CAAC,MAEI,IACH9E,IAAI,OAAoB,IAGxB,IAAI,CAACuE,SAAS,CAAC,CACb,kBAAkB,EAClB;MAAEkB,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAK,CAAC,CACvC,CAAC,EACF;MACA,IAAI,CAACZ,QAAQ,KAAiB,CAAC,CAAC;MAGhC,MAAMa,WAAW,GAAG,IAAI,CAAC/H,KAAK,CAACgI,WAAW,CAAC,IAAI,CAAC7I,KAAK,CAACR,GAAG,CAAC;MAC1D,IAAIoJ,WAAW,OAAoB,EAAE;QACnC,IAAI,CAACE,UAAU,CAAC,CAAC;MACnB;IACF,CAAC,MAEI;MACH,IAAI,CAACf,QAAQ,KAAgB,CAAC,CAAC;IACjC;EACF;EAEAgB,gBAAgBA,CAAA,EAAS;IACvB,MAAM9F,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IAGtD,IACEyD,IAAI,OAAqB,IACzB,IAAI,CAACuE,SAAS,CAAC,CACb,kBAAkB,EAClB;MAAEkB,QAAQ,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAK,CAAC,CACvC,CAAC,EACF;MACA,IAAI,CAACZ,QAAQ,KAAc,CAAC,CAAC;IAC/B,CAAC,MAEI;MACH,IAAI,CAACA,QAAQ,KAAQ,CAAC,CAAC;IACzB;EACF;EAEAiB,kBAAkBA,CAACZ,IAAY,EAAQ;IAErC,MAAMnF,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IAEtD,IAAIyD,IAAI,KAAKmF,IAAI,EAAE;MACjB,IAAI,CAACL,QAAQ,KAAY,CAAC,CAAC;MAC3B;IACF;IAEA,IAAI9E,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAAC8E,QAAQ,KAAY,CAAC,CAAC;IAC7B,CAAC,MAAM;MACL,IAAI,CAACA,QAAQ,KAAa,CAAC,CAAC;IAC9B;EACF;EAEAkB,YAAYA,CAAA,EAAS;IAEnB,MAAM;MAAEzJ;IAAI,CAAC,GAAG,IAAI,CAACQ,KAAK;IAC1B,MAAMiD,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC;IAE3C,IAAIyD,IAAI,OAAuB,EAAE;MAC/B,IAAI,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC,OAAuB,EAAE;QACzD,IAAI,CAACuI,QAAQ,KAAY,CAAC,CAAC;QAC3B;MACF;MACA,IAAI,CAACA,QAAQ,KAAe,CAAC,CAAC;MAC9B;IACF;IAEA,IAAI9E,IAAI,OAAuB,EAAE;MAE/B,IAAI,CAAC8E,QAAQ,KAAgB,CAAC,CAAC;MAC/B;IACF;IAEA,IAAI,CAACA,QAAQ,KAAQ,CAAC,CAAC;EACzB;EAEAmB,YAAYA,CAAA,EAAS;IAEnB,MAAM;MAAE1J;IAAI,CAAC,GAAG,IAAI,CAACQ,KAAK;IAC1B,MAAMiD,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC;IAE3C,IAAIyD,IAAI,OAA0B,EAAE;MAClC,MAAMkG,IAAI,GACR,IAAI,CAACtI,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG,CAAC,CAAC,OAA0B,GAAG,CAAC,GAAG,CAAC;MAClE,IAAI,IAAI,CAACqB,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,GAAG2J,IAAI,CAAC,OAAuB,EAAE;QAC5D,IAAI,CAACpB,QAAQ,KAAYoB,IAAI,GAAG,CAAC,CAAC;QAClC;MACF;MACA,IAAI,CAACpB,QAAQ,KAAeoB,IAAI,CAAC;MACjC;IACF;IAEA,IAAIlG,IAAI,OAAuB,EAAE;MAE/B,IAAI,CAAC8E,QAAQ,KAAgB,CAAC,CAAC;MAC/B;IACF;IAEA,IAAI,CAACA,QAAQ,KAAQ,CAAC,CAAC;EACzB;EAEAqB,iBAAiBA,CAAChB,IAAY,EAAQ;IAEpC,MAAMnF,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IACtD,IAAIyD,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAAC8E,QAAQ,KAEX,IAAI,CAAClH,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,OAAuB,GAC5D,CAAC,GACD,CACN,CAAC;MACD;IACF;IACA,IAAI4I,IAAI,OAAuB,IAAInF,IAAI,OAA0B,EAAE;MAEjE,IAAI,CAACjD,KAAK,CAACR,GAAG,IAAI,CAAC;MACnB,IAAI,CAAC8F,WAAW,GAAS,CAAC;MAC1B;IACF;IACA,IAAI,CAACyC,QAAQ,CAACK,IAAI,OAAuB,UAAkB,EAAE,CAAC,CAAC;EACjE;EAEAiB,kBAAkBA,CAAA,EAAS;IAEzB,MAAMpG,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IACtD,MAAM8J,KAAK,GAAG,IAAI,CAACzI,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;IACvD,IAAIyD,IAAI,OAA2B,EAAE;MACnC,IAAIqG,KAAK,OAAuB,EAAE;QAEhC,IAAI,CAACvB,QAAQ,KAAY,CAAC,CAAC;MAC7B,CAAC,MAAM;QAEL,IAAI,CAACA,QAAQ,KAAuB,CAAC,CAAC;MACxC;IACF,CAAC,MAAM,IACL9E,IAAI,OAAkB,IACtB,EAAEqG,KAAK,MAAoB,IAAIA,KAAK,MAAoB,CAAC,EACzD;MAEA,IAAI,CAACtJ,KAAK,CAACR,GAAG,IAAI,CAAC;MACnB,IAAI,CAAC8F,WAAW,GAAe,CAAC;IAClC,CAAC,MAAM;MACL,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;MAChB,IAAI,CAAC8F,WAAW,GAAY,CAAC;IAC/B;EACF;EAEAC,gBAAgBA,CAAC6C,IAAY,EAAQ;IACnC,QAAQA,IAAI;MAIV;QACE,IAAI,CAACJ,aAAa,CAAC,CAAC;QACpB;MAEF;QACE,EAAE,IAAI,CAAChI,KAAK,CAACR,GAAG;QAChB,IAAI,CAAC8F,WAAW,GAAU,CAAC;QAC3B;MACF;QACE,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;QAChB,IAAI,CAAC8F,WAAW,GAAU,CAAC;QAC3B;MACF;QACE,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;QAChB,IAAI,CAAC8F,WAAW,GAAQ,CAAC;QACzB;MACF;QACE,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;QAChB,IAAI,CAAC8F,WAAW,GAAS,CAAC;QAC1B;MACF;QACE,IACE,IAAI,CAACkC,SAAS,CAAC,gBAAgB,CAAC,IAChC,IAAI,CAAC3G,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,QAA0B,EACnE;UACA,IAAI,IAAI,CAACkI,eAAe,CAAC,gBAAgB,EAAE,YAAY,CAAC,KAAK,KAAK,EAAE;YAClE,MAAM,IAAI,CAACtG,KAAK,CACdC,kBAAM,CAACkI,0CAA0C,EACjD;cAAEhI,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;YAAE,CACjC,CAAC;UACH;UAGA,IAAI,CAAC7D,KAAK,CAACR,GAAG,IAAI,CAAC;UACnB,IAAI,CAAC8F,WAAW,EAAe,CAAC;QAClC,CAAC,MAAM;UACL,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;UAChB,IAAI,CAAC8F,WAAW,EAAY,CAAC;QAC/B;QACA;MACF;QACE,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;QAChB,IAAI,CAAC8F,WAAW,EAAY,CAAC;QAC7B;MACF;QACE,IACE,IAAI,CAACkC,SAAS,CAAC,gBAAgB,CAAC,IAChC,IAAI,CAAC3G,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,QAA0B,EACnE;UACA,IAAI,IAAI,CAACkI,eAAe,CAAC,gBAAgB,EAAE,YAAY,CAAC,KAAK,KAAK,EAAE;YAClE,MAAM,IAAI,CAACtG,KAAK,CACdC,kBAAM,CAACmI,2CAA2C,EAClD;cAAEjI,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;YAAE,CACjC,CAAC;UACH;UAGA,IAAI,CAAC7D,KAAK,CAACR,GAAG,IAAI,CAAC;UACnB,IAAI,CAAC8F,WAAW,EAAa,CAAC;QAChC,CAAC,MAAM;UACL,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;UAChB,IAAI,CAAC8F,WAAW,EAAU,CAAC;QAC7B;QACA;MACF;QACE,EAAE,IAAI,CAACtF,KAAK,CAACR,GAAG;QAChB,IAAI,CAAC8F,WAAW,EAAU,CAAC;QAC3B;MAEF;QACE,IACE,IAAI,CAACkC,SAAS,CAAC,cAAc,CAAC,IAC9B,IAAI,CAAC3G,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,OAAoB,EAC7D;UACA,IAAI,CAACuI,QAAQ,KAAiB,CAAC,CAAC;QAClC,CAAC,MAAM;UACL,EAAE,IAAI,CAAC/H,KAAK,CAACR,GAAG;UAChB,IAAI,CAAC8F,WAAW,GAAS,CAAC;QAC5B;QACA;MAEF;QACE,IAAI,CAAC+D,kBAAkB,CAAC,CAAC;QACzB;MAEF;QACE,IAAI,CAACI,iBAAiB,CAAC,CAAC;QACxB;MAEF;QAAuB;UACrB,MAAMxG,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC;UAEtD,IAAIyD,IAAI,QAAyB,IAAIA,IAAI,OAAyB,EAAE;YAClE,IAAI,CAACyG,eAAe,CAAC,EAAE,CAAC;YACxB;UACF;UAEA,IAAIzG,IAAI,QAAyB,IAAIA,IAAI,OAAyB,EAAE;YAClE,IAAI,CAACyG,eAAe,CAAC,CAAC,CAAC;YACvB;UACF;UAEA,IAAIzG,IAAI,OAAyB,IAAIA,IAAI,OAAyB,EAAE;YAClE,IAAI,CAACyG,eAAe,CAAC,CAAC,CAAC;YACvB;UACF;QACF;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACE,IAAI,CAACzB,UAAU,CAAC,KAAK,CAAC;QACtB;MAGF;MACA;QACE,IAAI,CAAC0B,UAAU,CAACvB,IAAI,CAAC;QACrB;MAOF;QACE,IAAI,CAACF,eAAe,CAAC,CAAC;QACtB;MAEF;MACA;QACE,IAAI,CAACC,qBAAqB,CAACC,IAAI,CAAC;QAChC;MAEF;MACA;QACE,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC;QAC7B;MAEF;QACE,IAAI,CAACK,eAAe,CAAC,CAAC;QACtB;MAEF;MACA;QACE,IAAI,CAACO,kBAAkB,CAACZ,IAAI,CAAC;QAC7B;MAEF;QACE,IAAI,CAACa,YAAY,CAAC,CAAC;QACnB;MAEF;QACE,IAAI,CAACC,YAAY,CAAC,CAAC;QACnB;MAEF;MACA;QACE,IAAI,CAACE,iBAAiB,CAAChB,IAAI,CAAC;QAC5B;MAEF;QACE,IAAI,CAACL,QAAQ,KAAW,CAAC,CAAC;QAC1B;MAEF;QACE,IAAI,CAACgB,gBAAgB,CAAC,CAAC;QACvB;MAEF;QACE,IAAI,CAAC3B,oBAAoB,CAAC,CAAC;QAC3B;MAEF;QACE,IAAI,CAACwC,QAAQ,CAAC,CAAC;QACf;MAEF;QACE,IAAI,IAAA/B,6BAAiB,EAACO,IAAI,CAAC,EAAE;UAC3B,IAAI,CAACwB,QAAQ,CAACxB,IAAI,CAAC;UACnB;QACF;IACJ;IAEA,MAAM,IAAI,CAAChH,KAAK,CAACC,kBAAM,CAACwI,wBAAwB,EAAE;MAChDtI,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC,CAAC;MAC5BiF,UAAU,EAAEgB,MAAM,CAACC,aAAa,CAAC3B,IAAI;IACvC,CAAC,CAAC;EACJ;EAEAL,QAAQA,CAAC9H,IAAe,EAAEkJ,IAAY,EAAQ;IAC5C,MAAMa,GAAG,GAAG,IAAI,CAACnJ,KAAK,CAACgF,KAAK,CAAC,IAAI,CAAC7F,KAAK,CAACR,GAAG,EAAE,IAAI,CAACQ,KAAK,CAACR,GAAG,GAAG2J,IAAI,CAAC;IACnE,IAAI,CAACnJ,KAAK,CAACR,GAAG,IAAI2J,IAAI;IACtB,IAAI,CAAC7D,WAAW,CAACrF,IAAI,EAAE+J,GAAG,CAAC;EAC7B;EAEAC,UAAUA,CAAA,EAAS;IACjB,MAAM1J,QAAQ,GAAG,IAAI,CAACP,KAAK,CAACO,QAAQ;IACpC,MAAMJ,KAAK,GAAG,IAAI,CAACH,KAAK,CAACG,KAAK,GAAG,CAAC;IAClC,IAAI+J,OAAO,EAAEC,OAAO;IACpB,IAAI;MAAE3K;IAAI,CAAC,GAAG,IAAI,CAACQ,KAAK;IACxB,QAAS,EAAER,GAAG,EAAE;MACd,IAAIA,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAEtB,MAAM,IAAI,CAAC+B,KAAK,CAACC,kBAAM,CAAC+I,kBAAkB,EAAE;UAC1C7I,EAAE,EAAE,IAAA8I,wCAA8B,EAAC9J,QAAQ,EAAE,CAAC;QAChD,CAAC,CAAC;MACJ;MACA,MAAMyF,EAAE,GAAG,IAAI,CAACnF,KAAK,CAAC0D,UAAU,CAAC/E,GAAG,CAAC;MACrC,IAAI,IAAAyG,qBAAS,EAACD,EAAE,CAAC,EAAE;QACjB,MAAM,IAAI,CAAC5E,KAAK,CAACC,kBAAM,CAAC+I,kBAAkB,EAAE;UAC1C7I,EAAE,EAAE,IAAA8I,wCAA8B,EAAC9J,QAAQ,EAAE,CAAC;QAChD,CAAC,CAAC;MACJ;MACA,IAAI2J,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACL,IAAIlE,EAAE,OAAgC,EAAE;UACtCmE,OAAO,GAAG,IAAI;QAChB,CAAC,MAAM,IAAInE,EAAE,OAAiC,IAAImE,OAAO,EAAE;UACzDA,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM,IAAInE,EAAE,OAAoB,IAAI,CAACmE,OAAO,EAAE;UAC7C;QACF;QACAD,OAAO,GAAGlE,EAAE,OAAwB;MACtC;IACF;IACA,MAAMsE,OAAO,GAAG,IAAI,CAACzJ,KAAK,CAACgF,KAAK,CAAC1F,KAAK,EAAEX,GAAG,CAAC;IAC5C,EAAEA,GAAG;IAEL,IAAI+K,IAAI,GAAG,EAAE;IAEb,MAAMjD,OAAO,GAAGA,CAAA,KAEd,IAAA+C,wCAA8B,EAAC9J,QAAQ,EAAEf,GAAG,GAAG,CAAC,GAAGW,KAAK,CAAC;IAE3D,OAAOX,GAAG,GAAG,IAAI,CAACH,MAAM,EAAE;MACxB,MAAMwF,EAAE,GAAG,IAAI,CAACD,cAAc,CAACpF,GAAG,CAAC;MAEnC,MAAMgL,IAAI,GAAGV,MAAM,CAACW,YAAY,CAAC5F,EAAE,CAAC;MAGpC,IAAIjF,iBAAiB,CAAC8K,GAAG,CAAC7F,EAAE,CAAC,EAAE;QAC7B,IAAIA,EAAE,QAAyB,EAAE;UAC/B,IAAI0F,IAAI,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACtB,IAAI,CAACvJ,KAAK,CAACC,kBAAM,CAACuJ,yBAAyB,EAAE;cAAErJ,EAAE,EAAE+F,OAAO,CAAC;YAAE,CAAC,CAAC;UACjE;QACF,CAAC,MAAM,IAAIzC,EAAE,QAAyB,EAAE;UACtC,IAAI0F,IAAI,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACtB,IAAI,CAACvJ,KAAK,CAACC,kBAAM,CAACuJ,yBAAyB,EAAE;cAAErJ,EAAE,EAAE+F,OAAO,CAAC;YAAE,CAAC,CAAC;UACjE;QACF;QACA,IAAIiD,IAAI,CAACI,QAAQ,CAACH,IAAI,CAAC,EAAE;UACvB,IAAI,CAACpJ,KAAK,CAACC,kBAAM,CAACwJ,oBAAoB,EAAE;YAAEtJ,EAAE,EAAE+F,OAAO,CAAC;UAAE,CAAC,CAAC;QAC5D;MACF,CAAC,MAAM,IAAI,IAAAwD,4BAAgB,EAACjG,EAAE,CAAC,IAAIA,EAAE,OAAwB,EAAE;QAC7D,IAAI,CAACzD,KAAK,CAACC,kBAAM,CAAC0J,oBAAoB,EAAE;UAAExJ,EAAE,EAAE+F,OAAO,CAAC;QAAE,CAAC,CAAC;MAC5D,CAAC,MAAM;QACL;MACF;MAEA,EAAE9H,GAAG;MACL+K,IAAI,IAAIC,IAAI;IACd;IACA,IAAI,CAACxK,KAAK,CAACR,GAAG,GAAGA,GAAG;IAEpB,IAAI,CAAC8F,WAAW,MAAY;MAC1B0F,OAAO,EAAEV,OAAO;MAChBW,KAAK,EAAEV;IACT,CAAC,CAAC;EACJ;EAWAW,OAAOA,CACLhK,KAAa,EACbiK,GAAY,EACZC,QAAiB,GAAG,KAAK,EACzBC,iBAAmC,GAAG,IAAI,EAC3B;IACf,MAAM;MAAEC,CAAC;MAAE9L;IAAI,CAAC,GAAG,IAAA0L,2BAAO,EACxB,IAAI,CAACrK,KAAK,EACV,IAAI,CAACb,KAAK,CAACR,GAAG,EACd,IAAI,CAACQ,KAAK,CAACP,SAAS,EACpB,IAAI,CAACO,KAAK,CAACN,OAAO,EAClBwB,KAAK,EACLiK,GAAG,EACHC,QAAQ,EACRC,iBAAiB,EACjB,IAAI,CAACrK,qBAAqB,EACR,KACpB,CAAC;IACD,IAAI,CAAChB,KAAK,CAACR,GAAG,GAAGA,GAAG;IACpB,OAAO8L,CAAC;EACV;EAEA5B,eAAeA,CAACxI,KAAa,EAAQ;IACnC,MAAMX,QAAQ,GAAG,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC;IACzC,IAAI0H,QAAQ,GAAG,KAAK;IAEpB,IAAI,CAACvL,KAAK,CAACR,GAAG,IAAI,CAAC;IACnB,MAAMwH,GAAG,GAAG,IAAI,CAACkE,OAAO,CAAChK,KAAK,CAAC;IAC/B,IAAI8F,GAAG,IAAI,IAAI,EAAE;MACf,IAAI,CAAC5F,KAAK,CAACC,kBAAM,CAACC,YAAY,EAAE;QAE9BC,EAAE,EAAE,IAAA8I,wCAA8B,EAAC9J,QAAQ,EAAE,CAAC,CAAC;QAC/CW;MACF,CAAC,CAAC;IACJ;IACA,MAAM+B,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;IAElD,IAAIyD,IAAI,QAAyB,EAAE;MACjC,EAAE,IAAI,CAACjD,KAAK,CAACR,GAAG;MAChB+L,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM,IAAItI,IAAI,QAAyB,EAAE;MACxC,MAAM,IAAI,CAAC7B,KAAK,CAACC,kBAAM,CAACmK,cAAc,EAAE;QAAEjK,EAAE,EAAEhB;MAAS,CAAC,CAAC;IAC3D;IAEA,IAAI,IAAAsH,6BAAiB,EAAC,IAAI,CAACjD,cAAc,CAAC,IAAI,CAAC5E,KAAK,CAACR,GAAG,CAAC,CAAC,EAAE;MAC1D,MAAM,IAAI,CAAC4B,KAAK,CAACC,kBAAM,CAACoK,gBAAgB,EAAE;QACxClK,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;MAC7B,CAAC,CAAC;IACJ;IAEA,IAAI0H,QAAQ,EAAE;MACZ,MAAMvB,GAAG,GAAG,IAAI,CAACnJ,KAAK,CACnBgF,KAAK,CAACtF,QAAQ,CAACmL,KAAK,EAAE,IAAI,CAAC1L,KAAK,CAACR,GAAG,CAAC,CACrCmM,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MACvB,IAAI,CAACrG,WAAW,MAAY0E,GAAG,CAAC;MAChC;IACF;IAEA,IAAI,CAAC1E,WAAW,MAAS0B,GAAG,CAAC;EAC/B;EAIAiB,UAAUA,CAAC2D,aAAsB,EAAQ;IACvC,MAAMzL,KAAK,GAAG,IAAI,CAACH,KAAK,CAACR,GAAG;IAC5B,MAAMe,QAAQ,GAAG,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC;IACzC,IAAIgI,OAAO,GAAG,KAAK;IACnB,IAAIN,QAAQ,GAAG,KAAK;IACpB,IAAIO,SAAS,GAAG,KAAK;IACrB,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAIC,OAAO,GAAG,KAAK;IAEnB,IAAI,CAACJ,aAAa,IAAI,IAAI,CAACV,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE;MAC/C,IAAI,CAAC9J,KAAK,CAACC,kBAAM,CAAC4K,aAAa,EAAE;QAAE1K,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;MAAE,CAAC,CAAC;IACpE;IACA,MAAMqI,cAAc,GAClB,IAAI,CAAClM,KAAK,CAACR,GAAG,GAAGW,KAAK,IAAI,CAAC,IAC3B,IAAI,CAACU,KAAK,CAAC0D,UAAU,CAACpE,KAAK,CAAC,OAAqB;IAEnD,IAAI+L,cAAc,EAAE;MAClB,MAAMC,OAAO,GAAG,IAAI,CAACtL,KAAK,CAACgF,KAAK,CAAC1F,KAAK,EAAE,IAAI,CAACH,KAAK,CAACR,GAAG,CAAC;MACvD,IAAI,CAAC6C,sBAAsB,CAAChB,kBAAM,CAAC+K,kBAAkB,EAAE;QAAE7K,EAAE,EAAEhB;MAAS,CAAC,CAAC;MACxE,IAAI,CAAC,IAAI,CAACP,KAAK,CAACgF,MAAM,EAAE;QAEtB,MAAMqH,aAAa,GAAGF,OAAO,CAAC7M,OAAO,CAAC,GAAG,CAAC;QAC1C,IAAI+M,aAAa,GAAG,CAAC,EAAE;UAErB,IAAI,CAACjL,KAAK,CAACC,kBAAM,CAACiL,yBAAyB,EAAE;YAC3C/K,EAAE,EAAE,IAAA8I,wCAA8B,EAAC9J,QAAQ,EAAE8L,aAAa;UAC5D,CAAC,CAAC;QACJ;MACF;MACAL,OAAO,GAAGE,cAAc,IAAI,CAAC,MAAM,CAAC7H,IAAI,CAAC8H,OAAO,CAAC;IACnD;IAEA,IAAIlJ,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;IAChD,IAAIyD,IAAI,OAAkB,IAAI,CAAC+I,OAAO,EAAE;MACtC,EAAE,IAAI,CAAChM,KAAK,CAACR,GAAG;MAChB,IAAI,CAAC0L,OAAO,CAAC,EAAE,CAAC;MAChBW,OAAO,GAAG,IAAI;MACd5I,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;IAC9C;IAEA,IACE,CAACyD,IAAI,OAAyB,IAAIA,IAAI,QAAyB,KAC/D,CAAC+I,OAAO,EACR;MACA/I,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,EAAE,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;MAC9C,IAAIyD,IAAI,OAAuB,IAAIA,IAAI,OAAmB,EAAE;QAC1D,EAAE,IAAI,CAACjD,KAAK,CAACR,GAAG;MAClB;MACA,IAAI,IAAI,CAAC0L,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE;QAC7B,IAAI,CAAC9J,KAAK,CAACC,kBAAM,CAACkL,wBAAwB,EAAE;UAAEhL,EAAE,EAAEhB;QAAS,CAAC,CAAC;MAC/D;MACAsL,OAAO,GAAG,IAAI;MACdE,WAAW,GAAG,IAAI;MAClB9I,IAAI,GAAG,IAAI,CAACpC,KAAK,CAAC0D,UAAU,CAAC,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC;IAC9C;IAEA,IAAIyD,IAAI,QAAyB,EAAE;MAGjC,IAAI4I,OAAO,IAAIK,cAAc,EAAE;QAC7B,IAAI,CAAC9K,KAAK,CAACC,kBAAM,CAACmL,oBAAoB,EAAE;UAAEjL,EAAE,EAAEhB;QAAS,CAAC,CAAC;MAC3D;MACA,EAAE,IAAI,CAACP,KAAK,CAACR,GAAG;MAChB+L,QAAQ,GAAG,IAAI;IACjB;IAEA,IAAItI,IAAI,QAAyB,EAAE;MACjC,IAAI,CAACwE,YAAY,CAAC,SAAS,EAAE,IAAI,CAACzH,KAAK,CAAC6D,WAAW,CAAC,CAAC,CAAC;MACtD,IAAIkI,WAAW,IAAIG,cAAc,EAAE;QACjC,IAAI,CAAC9K,KAAK,CAACC,kBAAM,CAACmK,cAAc,EAAE;UAAEjK,EAAE,EAAEhB;QAAS,CAAC,CAAC;MACrD;MACA,EAAE,IAAI,CAACP,KAAK,CAACR,GAAG;MAChBsM,SAAS,GAAG,IAAI;IAClB;IAEA,IAAI,IAAAjE,6BAAiB,EAAC,IAAI,CAACjD,cAAc,CAAC,IAAI,CAAC5E,KAAK,CAACR,GAAG,CAAC,CAAC,EAAE;MAC1D,MAAM,IAAI,CAAC4B,KAAK,CAACC,kBAAM,CAACoK,gBAAgB,EAAE;QACxClK,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;MAC7B,CAAC,CAAC;IACJ;IAGA,MAAMmG,GAAG,GAAG,IAAI,CAACnJ,KAAK,CAACgF,KAAK,CAAC1F,KAAK,EAAE,IAAI,CAACH,KAAK,CAACR,GAAG,CAAC,CAACmM,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IAEzE,IAAIJ,QAAQ,EAAE;MACZ,IAAI,CAACjG,WAAW,MAAY0E,GAAG,CAAC;MAChC;IACF;IAEA,IAAI8B,SAAS,EAAE;MACb,IAAI,CAACxG,WAAW,MAAa0E,GAAG,CAAC;MACjC;IACF;IAEA,MAAMhD,GAAG,GAAGgF,OAAO,GAAGS,QAAQ,CAACzC,GAAG,EAAE,CAAC,CAAC,GAAG0C,UAAU,CAAC1C,GAAG,CAAC;IACxD,IAAI,CAAC1E,WAAW,MAAS0B,GAAG,CAAC;EAC/B;EAIA2F,aAAaA,CAACC,cAAuB,EAAiB;IACpD,MAAM;MAAExE,IAAI;MAAE5I;IAAI,CAAC,GAAG,IAAAmN,iCAAa,EACjC,IAAI,CAAC9L,KAAK,EACV,IAAI,CAACb,KAAK,CAACR,GAAG,EACd,IAAI,CAACQ,KAAK,CAACP,SAAS,EACpB,IAAI,CAACO,KAAK,CAACN,OAAO,EAClBkN,cAAc,EACd,IAAI,CAAC/K,2BACP,CAAC;IACD,IAAI,CAAC7B,KAAK,CAACR,GAAG,GAAGA,GAAG;IACpB,OAAO4I,IAAI;EACb;EAEAuB,UAAUA,CAACkD,KAAa,EAAQ;IAC9B,MAAM;MAAE7C,GAAG;MAAExK,GAAG;MAAEE,OAAO;MAAED;IAAU,CAAC,GAAG,IAAAqN,sCAAkB,EACzDD,KAAK,OAA4B,GAAG,QAAQ,GAAG,QAAQ,EACvD,IAAI,CAAChM,KAAK,EACV,IAAI,CAACb,KAAK,CAACR,GAAG,GAAG,CAAC,EAClB,IAAI,CAACQ,KAAK,CAACP,SAAS,EACpB,IAAI,CAACO,KAAK,CAACN,OAAO,EAClB,IAAI,CAACyC,uCACP,CAAC;IACD,IAAI,CAACnC,KAAK,CAACR,GAAG,GAAGA,GAAG,GAAG,CAAC;IACxB,IAAI,CAACQ,KAAK,CAACP,SAAS,GAAGA,SAAS;IAChC,IAAI,CAACO,KAAK,CAACN,OAAO,GAAGA,OAAO;IAC5B,IAAI,CAAC4F,WAAW,MAAY0E,GAAG,CAAC;EAClC;EAGA+C,wBAAwBA,CAAA,EAAS;IAC/B,IAAI,CAAC,IAAI,CAACvJ,KAAK,EAAU,CAAC,EAAE;MAC1B,IAAI,CAACsF,UAAU,CAAC,IAAI,GAAW,CAAC;IAClC;IAEA,IAAI,CAAC9I,KAAK,CAACR,GAAG,EAAE;IAChB,IAAI,CAACiK,iBAAiB,CAAC,CAAC;EAC1B;EAGAA,iBAAiBA,CAAA,EAAS;IACxB,MAAMuD,OAAO,GAAG,IAAI,CAACnM,KAAK,CAAC,IAAI,CAACb,KAAK,CAACR,GAAG,CAAC;IAC1C,MAAM;MAAEwK,GAAG;MAAEiD,eAAe;MAAEzN,GAAG;MAAEE,OAAO;MAAED;IAAU,CAAC,GACrD,IAAAqN,sCAAkB,EAChB,UAAU,EACV,IAAI,CAACjM,KAAK,EACV,IAAI,CAACb,KAAK,CAACR,GAAG,GAAG,CAAC,EAClB,IAAI,CAACQ,KAAK,CAACP,SAAS,EACpB,IAAI,CAACO,KAAK,CAACN,OAAO,EAClB,IAAI,CAAC+C,yCACP,CAAC;IACH,IAAI,CAACzC,KAAK,CAACR,GAAG,GAAGA,GAAG,GAAG,CAAC;IACxB,IAAI,CAACQ,KAAK,CAACP,SAAS,GAAGA,SAAS;IAChC,IAAI,CAACO,KAAK,CAACN,OAAO,GAAGA,OAAO;IAE5B,IAAIuN,eAAe,EAAE;MACnB,IAAI,CAACjN,KAAK,CAACkN,6BAA6B,GAAG,IAAIvN,kBAAQ,CACrDsN,eAAe,CAACvN,OAAO,EACvBuN,eAAe,CAACzN,GAAG,GAAGyN,eAAe,CAACxN,SAAS,EAC/CwN,eAAe,CAACzN,GAClB,CAAC;IACH;IAEA,IAAI,IAAI,CAACqB,KAAK,CAACgI,WAAW,CAACrJ,GAAG,CAAC,OAA0B,EAAE;MACzD,IAAI,CAAC8F,WAAW,KAEd2H,eAAe,GAAG,IAAI,GAAGD,OAAO,GAAGhD,GAAG,GAAG,GAC3C,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAAChK,KAAK,CAACR,GAAG,EAAE;MAChB,IAAI,CAAC8F,WAAW,KAEd2H,eAAe,GAAG,IAAI,GAAGD,OAAO,GAAGhD,GAAG,GAAG,IAC3C,CAAC;IACH;EACF;EAEA3H,sBAAsBA,CACpB8C,YAAiC,EACjC;IAAE5D;EAAqB,CAAC,EACxB;IACA,MAAMmK,KAAK,GAAGnK,EAAE,CAACmK,KAAK;IAEtB,IAAI,IAAI,CAAC1L,KAAK,CAACgF,MAAM,IAAI,CAAC,IAAI,CAAChF,KAAK,CAACiF,YAAY,CAACyF,GAAG,CAACgB,KAAK,CAAC,EAAE;MAC5D,IAAI,CAACtK,KAAK,CAAC+D,YAAY,EAAE;QAAE5D;MAAG,CAAC,CAAC;IAClC,CAAC,MAAM;MACL,IAAI,CAACvB,KAAK,CAACiF,YAAY,CAACkI,GAAG,CAACzB,KAAK,EAAE,CAACvG,YAAY,EAAE5D,EAAE,CAAC,CAAC;IACxD;EACF;EAWAuG,SAASA,CAACsF,SAAkB,EAAU;IACpC,IAAI,CAACpN,KAAK,CAACqN,WAAW,GAAG,KAAK;IAC9B,IAAIC,IAAI,GAAG,EAAE;IACb,MAAMnN,KAAK,GAAG,IAAI,CAACH,KAAK,CAACR,GAAG;IAC5B,IAAI+N,UAAU,GAAG,IAAI,CAACvN,KAAK,CAACR,GAAG;IAC/B,IAAI4N,SAAS,KAAK/G,SAAS,EAAE;MAC3B,IAAI,CAACrG,KAAK,CAACR,GAAG,IAAI4N,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC;IAC/C;IAEA,OAAO,IAAI,CAACpN,KAAK,CAACR,GAAG,GAAG,IAAI,CAACH,MAAM,EAAE;MACnC,MAAM2G,EAAE,GAAG,IAAI,CAACpB,cAAc,CAAC,IAAI,CAAC5E,KAAK,CAACR,GAAG,CAAC;MAC9C,IAAI,IAAAsL,4BAAgB,EAAC9E,EAAE,CAAC,EAAE;QACxB,IAAI,CAAChG,KAAK,CAACR,GAAG,IAAIwG,EAAE,IAAI,MAAM,GAAG,CAAC,GAAG,CAAC;MACxC,CAAC,MAAM,IAAIA,EAAE,OAAwB,EAAE;QACrC,IAAI,CAAChG,KAAK,CAACqN,WAAW,GAAG,IAAI;QAE7BC,IAAI,IAAI,IAAI,CAACzM,KAAK,CAACgF,KAAK,CAAC0H,UAAU,EAAE,IAAI,CAACvN,KAAK,CAACR,GAAG,CAAC;QACpD,MAAMgO,QAAQ,GAAG,IAAI,CAACxN,KAAK,CAAC6D,WAAW,CAAC,CAAC;QACzC,MAAM4J,eAAe,GACnB,IAAI,CAACzN,KAAK,CAACR,GAAG,KAAKW,KAAK,GAAG0H,6BAAiB,GAAGiD,4BAAgB;QAEjE,IAAI,IAAI,CAACjK,KAAK,CAAC0D,UAAU,CAAC,EAAE,IAAI,CAACvE,KAAK,CAACR,GAAG,CAAC,QAAyB,EAAE;UACpE,IAAI,CAAC4B,KAAK,CAACC,kBAAM,CAACqM,oBAAoB,EAAE;YACtCnM,EAAE,EAAE,IAAI,CAACvB,KAAK,CAAC6D,WAAW,CAAC;UAC7B,CAAC,CAAC;UACF0J,UAAU,GAAG,IAAI,CAACvN,KAAK,CAACR,GAAG,GAAG,CAAC;UAC/B;QACF;QAEA,EAAE,IAAI,CAACQ,KAAK,CAACR,GAAG;QAChB,MAAMmO,GAAG,GAAG,IAAI,CAAChB,aAAa,CAAC,IAAI,CAAC;QACpC,IAAIgB,GAAG,KAAK,IAAI,EAAE;UAChB,IAAI,CAACF,eAAe,CAACE,GAAG,CAAC,EAAE;YACzB,IAAI,CAACvM,KAAK,CAACC,kBAAM,CAACuM,0BAA0B,EAAE;cAAErM,EAAE,EAAEiM;YAAS,CAAC,CAAC;UACjE;UAEAF,IAAI,IAAIxD,MAAM,CAACC,aAAa,CAAC4D,GAAG,CAAC;QACnC;QACAJ,UAAU,GAAG,IAAI,CAACvN,KAAK,CAACR,GAAG;MAC7B,CAAC,MAAM;QACL;MACF;IACF;IACA,OAAO8N,IAAI,GAAG,IAAI,CAACzM,KAAK,CAACgF,KAAK,CAAC0H,UAAU,EAAE,IAAI,CAACvN,KAAK,CAACR,GAAG,CAAC;EAC5D;EAKAoK,QAAQA,CAACwD,SAAkB,EAAQ;IACjC,MAAME,IAAI,GAAG,IAAI,CAACxF,SAAS,CAACsF,SAAS,CAAC;IACtC,MAAMnN,IAAI,GAAG4N,eAAY,CAACC,GAAG,CAACR,IAAI,CAAC;IACnC,IAAIrN,IAAI,KAAKoG,SAAS,EAAE;MAGtB,IAAI,CAACf,WAAW,CAACrF,IAAI,EAAE,IAAA8N,qBAAc,EAAC9N,IAAI,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACqF,WAAW,MAAUgI,IAAI,CAAC;IACjC;EACF;EAEApK,mBAAmBA,CAAA,EAAS;IAC1B,MAAM;MAAEjD;IAAK,CAAC,GAAG,IAAI,CAACD,KAAK;IAC3B,IAAI,IAAAgO,qBAAc,EAAC/N,IAAI,CAAC,IAAI,IAAI,CAACD,KAAK,CAACqN,WAAW,EAAE;MAClD,IAAI,CAACjM,KAAK,CAACC,kBAAM,CAAC4M,0BAA0B,EAAE;QAC5C1M,EAAE,EAAE,IAAI,CAACvB,KAAK,CAACO,QAAQ;QACvB2N,YAAY,EAAE,IAAAH,qBAAc,EAAC9N,IAAI;MACnC,CAAC,CAAC;IACJ;EACF;EAWAmB,KAAKA,CACH+D,YAAiD,EACjDgJ,eAA8C,EACpB;IAC1B,MAAM;QAAE5M;MAAe,CAAC,GAAG4M,eAAe;MAA3BC,OAAO,GAAAxP,6BAAA,CAAKuP,eAAe,EAAAzP,SAAA;IAC1C,MAAM2B,GAAG,GAAGkB,EAAE,YAAY5B,kBAAQ,GAAG4B,EAAE,GAAGA,EAAE,CAAClB,GAAG,CAACF,KAAK;IAEtD,MAAMkO,KAAK,GAAGlJ,YAAY,CAAC;MAAE9E,GAAG;MAAE+N;IAAQ,CAAC,CAAC;IAE5C,IAAI,CAAC,IAAI,CAACxN,OAAO,CAACO,aAAa,EAAE,MAAMkN,KAAK;IAC5C,IAAI,CAAC,IAAI,CAACvN,WAAW,EAAE,IAAI,CAACd,KAAK,CAACsO,MAAM,CAACtL,IAAI,CAACqL,KAAK,CAAC;IAEpD,OAAOA,KAAK;EACd;EAQAE,cAAcA,CACZpJ,YAAiD,EACjDgJ,eAA8C,EACZ;IAClC,MAAM;QAAE5M;MAAe,CAAC,GAAG4M,eAAe;MAA3BC,OAAO,GAAAxP,6BAAA,CAAKuP,eAAe,EAAAxP,UAAA;IAC1C,MAAM0B,GAAG,GAAGkB,EAAE,YAAY5B,kBAAQ,GAAG4B,EAAE,GAAGA,EAAE,CAAClB,GAAG,CAACF,KAAK;IACtD,MAAMX,GAAG,GAAGa,GAAG,CAACqL,KAAK;IACrB,MAAM4C,MAAM,GAAG,IAAI,CAACtO,KAAK,CAACsO,MAAM;IAEhC,KAAK,IAAIlP,CAAC,GAAGkP,MAAM,CAACjP,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMiP,KAAK,GAAGC,MAAM,CAAClP,CAAC,CAAC;MACvB,IAAIiP,KAAK,CAAChO,GAAG,CAACqL,KAAK,KAAKlM,GAAG,EAAE;QAE3B,OAAQ8O,MAAM,CAAClP,CAAC,CAAC,GAAG+F,YAAY,CAAC;UAAE9E,GAAG;UAAE+N;QAAQ,CAAC,CAAC;MACpD;MACA,IAAIC,KAAK,CAAChO,GAAG,CAACqL,KAAK,GAAGlM,GAAG,EAAE;IAC7B;IAEA,OAAO,IAAI,CAAC4B,KAAK,CAAC+D,YAAY,EAAEgJ,eAAe,CAAC;EAClD;EAIAjH,aAAaA,CAACD,QAAmB,EAAQ,CAAC;EAG1C6B,UAAUA,CAACzI,GAAqB,EAAEJ,IAAgB,EAAQ;IACxD,MAAM,IAAI,CAACmB,KAAK,CAACC,kBAAM,CAACmN,eAAe,EAAE;MACvCC,QAAQ,EAAExO,IAAI,GAAG,IAAA8N,qBAAc,EAAC9N,IAAI,CAAC,GAAG,IAAI;MAC5CsB,EAAE,EAAElB,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,IAAI,CAACL,KAAK,CAACO;IACrC,CAAC,CAAC;EACJ;EAEAkH,YAAYA,CAACiH,UAAkB,EAAErO,GAAc,EAAQ;IACrD,IAAI,IAAI,CAACmH,SAAS,CAACkH,UAAU,CAAC,EAAE;MAC9B,OAAO,IAAI;IACb;IAEA,MAAM,IAAI,CAACtN,KAAK,CAACC,kBAAM,CAACsN,aAAa,EAAE;MACrCpN,EAAE,EAAElB,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,IAAI,CAACL,KAAK,CAACO,QAAQ;MAC3CqO,aAAa,EAAE,CAACF,UAAU;IAC5B,CAAC,CAAC;EACJ;EAEAG,eAAeA,CAACC,WAAqB,EAAQ;IAC3C,IAAI,CAACA,WAAW,CAACC,IAAI,CAACC,IAAI,IAAI,IAAI,CAACxH,SAAS,CAACwH,IAAI,CAAC,CAAC,EAAE;MACnD,MAAM,IAAI,CAAC5N,KAAK,CAACC,kBAAM,CAAC4N,mBAAmB,EAAE;QAC3C1N,EAAE,EAAE,IAAI,CAACvB,KAAK,CAACO,QAAQ;QACvBqO,aAAa,EAAEE;MACjB,CAAC,CAAC;IACJ;EACF;EAEArN,YAAYA,CAAC4M,KAAgC,EAAE;IAC7C,OAAO,CAAC7O,GAAW,EAAEC,SAAiB,EAAEC,OAAe,KAAK;MAC1D,IAAI,CAAC0B,KAAK,CAACiN,KAAK,EAAE;QAChB9M,EAAE,EAAEhC,aAAa,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO;MAC3C,CAAC,CAAC;IACJ,CAAC;EACH;AAmDF;AAACe,OAAA,CAAAyO,OAAA,GAAAxO,SAAA"}