{"version": 3, "names": ["_core", "require", "_helperEnvironmentVisitor", "findBareSupers", "traverse", "visitors", "merge", "Super", "path", "node", "parentPath", "isCallExpression", "callee", "push", "environmentVisitor", "referenceVisitor", "TSTypeAnnotation|TypeAnnotation", "skip", "ReferencedIdentifier", "scope", "hasOwnBinding", "name", "rename", "handleClassTDZ", "state", "classBinding", "getBinding", "classNameTDZError", "file", "addHelper", "throwNode", "t", "callExpression", "stringLiteral", "replaceWith", "sequenceExpression", "classFieldDefinitionEvaluationTDZVisitor", "injectInitialization", "constructor", "nodes", "renamer", "length", "isDerived", "superClass", "newConstructor", "classMethod", "identifier", "blockStatement", "params", "restElement", "body", "template", "statement", "ast", "get", "unshiftContainer", "bareSupers", "<PERSON><PERSON><PERSON><PERSON>", "bareSuper", "insertAfter", "map", "n", "cloneNode", "extractComputedKeys", "computedPaths", "declarations", "id", "computedPath", "computedKey", "isReferencedIdentifier", "computedNode", "isConstantExpression", "ident", "generateUidIdentifierBasedOnNode", "key", "kind", "expressionStatement", "assignmentExpression"], "sources": ["../src/misc.ts"], "sourcesContent": ["import { template, traverse, types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { NodePath, Scope, Visitor, Binding } from \"@babel/traverse\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\n\nconst findBareSupers = traverse.visitors.merge<NodePath<t.CallExpression>[]>([\n  {\n    Super(path) {\n      const { node, parentPath } = path;\n      if (parentPath.isCallExpression({ callee: node })) {\n        this.push(parentPath);\n      }\n    },\n  },\n  environmentVisitor,\n]);\n\nconst referenceVisitor: Visitor<{ scope: Scope }> = {\n  \"TSTypeAnnotation|TypeAnnotation\"(\n    path: NodePath<t.TSTypeAnnotation | t.TypeAnnotation>,\n  ) {\n    path.skip();\n  },\n\n  ReferencedIdentifier(path: NodePath<t.Identifier>, { scope }) {\n    if (scope.hasOwnBinding(path.node.name)) {\n      scope.rename(path.node.name);\n      path.skip();\n    }\n  },\n};\n\ntype HandleClassTDZState = {\n  classBinding: Binding;\n  file: File;\n};\n\nfunction handleClassTDZ(\n  path: NodePath<t.Identifier>,\n  state: HandleClassTDZState,\n) {\n  if (\n    state.classBinding &&\n    state.classBinding === path.scope.getBinding(path.node.name)\n  ) {\n    const classNameTDZError = state.file.addHelper(\"classNameTDZError\");\n    const throwNode = t.callExpression(classNameTDZError, [\n      t.stringLiteral(path.node.name),\n    ]);\n\n    path.replaceWith(t.sequenceExpression([throwNode, path.node]));\n    path.skip();\n  }\n}\n\nconst classFieldDefinitionEvaluationTDZVisitor: Visitor<HandleClassTDZState> = {\n  ReferencedIdentifier: handleClassTDZ,\n};\n\ninterface RenamerState {\n  scope: Scope;\n}\n\nexport function injectInitialization(\n  path: NodePath<t.Class>,\n  constructor: NodePath<t.ClassMethod> | undefined,\n  nodes: t.Statement[],\n  renamer?: (visitor: Visitor<RenamerState>, state: RenamerState) => void,\n) {\n  if (!nodes.length) return;\n\n  const isDerived = !!path.node.superClass;\n\n  if (!constructor) {\n    const newConstructor = t.classMethod(\n      \"constructor\",\n      t.identifier(\"constructor\"),\n      [],\n      t.blockStatement([]),\n    );\n\n    if (isDerived) {\n      newConstructor.params = [t.restElement(t.identifier(\"args\"))];\n      newConstructor.body.body.push(template.statement.ast`super(...args)`);\n    }\n\n    [constructor] = path\n      .get(\"body\")\n      .unshiftContainer(\"body\", newConstructor) as NodePath<t.ClassMethod>[];\n  }\n\n  if (renamer) {\n    renamer(referenceVisitor, { scope: constructor.scope });\n  }\n\n  if (isDerived) {\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    constructor.traverse(findBareSupers, bareSupers);\n    let isFirst = true;\n    for (const bareSuper of bareSupers) {\n      if (isFirst) {\n        bareSuper.insertAfter(nodes);\n        isFirst = false;\n      } else {\n        bareSuper.insertAfter(nodes.map(n => t.cloneNode(n)));\n      }\n    }\n  } else {\n    constructor.get(\"body\").unshiftContainer(\"body\", nodes);\n  }\n}\n\nexport function extractComputedKeys(\n  path: NodePath<t.Class>,\n  computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[],\n  file: File,\n) {\n  const declarations: t.ExpressionStatement[] = [];\n  const state = {\n    classBinding: path.node.id && path.scope.getBinding(path.node.id.name),\n    file,\n  };\n  for (const computedPath of computedPaths) {\n    const computedKey = computedPath.get(\"key\");\n    if (computedKey.isReferencedIdentifier()) {\n      handleClassTDZ(computedKey, state);\n    } else {\n      computedKey.traverse(classFieldDefinitionEvaluationTDZVisitor, state);\n    }\n\n    const computedNode = computedPath.node;\n    // Make sure computed property names are only evaluated once (upon class definition)\n    // and in the right order in combination with static properties\n    if (!computedKey.isConstantExpression()) {\n      const ident = path.scope.generateUidIdentifierBasedOnNode(\n        computedNode.key,\n      );\n      // Declaring in the same block scope\n      // Ref: https://github.com/babel/babel/pull/10029/files#diff-fbbdd83e7a9c998721c1484529c2ce92\n      path.scope.push({\n        id: ident,\n        kind: \"let\",\n      });\n      declarations.push(\n        t.expressionStatement(\n          t.assignmentExpression(\"=\", t.cloneNode(ident), computedNode.key),\n        ),\n      );\n      computedNode.key = t.cloneNode(ident);\n    }\n  }\n\n  return declarations;\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,yBAAA,GAAAD,OAAA;AAEA,MAAME,cAAc,GAAGC,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAA+B,CAC3E;EACEC,KAAKA,CAACC,IAAI,EAAE;IACV,MAAM;MAAEC,IAAI;MAAEC;IAAW,CAAC,GAAGF,IAAI;IACjC,IAAIE,UAAU,CAACC,gBAAgB,CAAC;MAAEC,MAAM,EAAEH;IAAK,CAAC,CAAC,EAAE;MACjD,IAAI,CAACI,IAAI,CAACH,UAAU,CAAC;IACvB;EACF;AACF,CAAC,EACDI,iCAAkB,CACnB,CAAC;AAEF,MAAMC,gBAA2C,GAAG;EAClD,iCAAiCC,CAC/BR,IAAqD,EACrD;IACAA,IAAI,CAACS,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,oBAAoBA,CAACV,IAA4B,EAAE;IAAEW;EAAM,CAAC,EAAE;IAC5D,IAAIA,KAAK,CAACC,aAAa,CAACZ,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,EAAE;MACvCF,KAAK,CAACG,MAAM,CAACd,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC;MAC5Bb,IAAI,CAACS,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAOD,SAASM,cAAcA,CACrBf,IAA4B,EAC5BgB,KAA0B,EAC1B;EACA,IACEA,KAAK,CAACC,YAAY,IAClBD,KAAK,CAACC,YAAY,KAAKjB,IAAI,CAACW,KAAK,CAACO,UAAU,CAAClB,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,EAC5D;IACA,MAAMM,iBAAiB,GAAGH,KAAK,CAACI,IAAI,CAACC,SAAS,CAAC,mBAAmB,CAAC;IACnE,MAAMC,SAAS,GAAGC,WAAC,CAACC,cAAc,CAACL,iBAAiB,EAAE,CACpDI,WAAC,CAACE,aAAa,CAACzB,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,CAChC,CAAC;IAEFb,IAAI,CAAC0B,WAAW,CAACH,WAAC,CAACI,kBAAkB,CAAC,CAACL,SAAS,EAAEtB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9DD,IAAI,CAACS,IAAI,CAAC,CAAC;EACb;AACF;AAEA,MAAMmB,wCAAsE,GAAG;EAC7ElB,oBAAoB,EAAEK;AACxB,CAAC;AAMM,SAASc,oBAAoBA,CAClC7B,IAAuB,EACvB8B,WAAgD,EAChDC,KAAoB,EACpBC,OAAuE,EACvE;EACA,IAAI,CAACD,KAAK,CAACE,MAAM,EAAE;EAEnB,MAAMC,SAAS,GAAG,CAAC,CAAClC,IAAI,CAACC,IAAI,CAACkC,UAAU;EAExC,IAAI,CAACL,WAAW,EAAE;IAChB,MAAMM,cAAc,GAAGb,WAAC,CAACc,WAAW,CAClC,aAAa,EACbd,WAAC,CAACe,UAAU,CAAC,aAAa,CAAC,EAC3B,EAAE,EACFf,WAAC,CAACgB,cAAc,CAAC,EAAE,CACrB,CAAC;IAED,IAAIL,SAAS,EAAE;MACbE,cAAc,CAACI,MAAM,GAAG,CAACjB,WAAC,CAACkB,WAAW,CAAClB,WAAC,CAACe,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;MAC7DF,cAAc,CAACM,IAAI,CAACA,IAAI,CAACrC,IAAI,CAACsC,cAAQ,CAACC,SAAS,CAACC,GAAI,gBAAe,CAAC;IACvE;IAEA,CAACf,WAAW,CAAC,GAAG9B,IAAI,CACjB8C,GAAG,CAAC,MAAM,CAAC,CACXC,gBAAgB,CAAC,MAAM,EAAEX,cAAc,CAA8B;EAC1E;EAEA,IAAIJ,OAAO,EAAE;IACXA,OAAO,CAACzB,gBAAgB,EAAE;MAAEI,KAAK,EAAEmB,WAAW,CAACnB;IAAM,CAAC,CAAC;EACzD;EAEA,IAAIuB,SAAS,EAAE;IACb,MAAMc,UAAwC,GAAG,EAAE;IACnDlB,WAAW,CAAClC,QAAQ,CAACD,cAAc,EAAEqD,UAAU,CAAC;IAChD,IAAIC,OAAO,GAAG,IAAI;IAClB,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;MAClC,IAAIC,OAAO,EAAE;QACXC,SAAS,CAACC,WAAW,CAACpB,KAAK,CAAC;QAC5BkB,OAAO,GAAG,KAAK;MACjB,CAAC,MAAM;QACLC,SAAS,CAACC,WAAW,CAACpB,KAAK,CAACqB,GAAG,CAACC,CAAC,IAAI9B,WAAC,CAAC+B,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC;MACvD;IACF;EACF,CAAC,MAAM;IACLvB,WAAW,CAACgB,GAAG,CAAC,MAAM,CAAC,CAACC,gBAAgB,CAAC,MAAM,EAAEhB,KAAK,CAAC;EACzD;AACF;AAEO,SAASwB,mBAAmBA,CACjCvD,IAAuB,EACvBwD,aAA0D,EAC1DpC,IAAU,EACV;EACA,MAAMqC,YAAqC,GAAG,EAAE;EAChD,MAAMzC,KAAK,GAAG;IACZC,YAAY,EAAEjB,IAAI,CAACC,IAAI,CAACyD,EAAE,IAAI1D,IAAI,CAACW,KAAK,CAACO,UAAU,CAAClB,IAAI,CAACC,IAAI,CAACyD,EAAE,CAAC7C,IAAI,CAAC;IACtEO;EACF,CAAC;EACD,KAAK,MAAMuC,YAAY,IAAIH,aAAa,EAAE;IACxC,MAAMI,WAAW,GAAGD,YAAY,CAACb,GAAG,CAAC,KAAK,CAAC;IAC3C,IAAIc,WAAW,CAACC,sBAAsB,CAAC,CAAC,EAAE;MACxC9C,cAAc,CAAC6C,WAAW,EAAE5C,KAAK,CAAC;IACpC,CAAC,MAAM;MACL4C,WAAW,CAAChE,QAAQ,CAACgC,wCAAwC,EAAEZ,KAAK,CAAC;IACvE;IAEA,MAAM8C,YAAY,GAAGH,YAAY,CAAC1D,IAAI;IAGtC,IAAI,CAAC2D,WAAW,CAACG,oBAAoB,CAAC,CAAC,EAAE;MACvC,MAAMC,KAAK,GAAGhE,IAAI,CAACW,KAAK,CAACsD,gCAAgC,CACvDH,YAAY,CAACI,GACf,CAAC;MAGDlE,IAAI,CAACW,KAAK,CAACN,IAAI,CAAC;QACdqD,EAAE,EAAEM,KAAK;QACTG,IAAI,EAAE;MACR,CAAC,CAAC;MACFV,YAAY,CAACpD,IAAI,CACfkB,WAAC,CAAC6C,mBAAmB,CACnB7C,WAAC,CAAC8C,oBAAoB,CAAC,GAAG,EAAE9C,WAAC,CAAC+B,SAAS,CAACU,KAAK,CAAC,EAAEF,YAAY,CAACI,GAAG,CAClE,CACF,CAAC;MACDJ,YAAY,CAACI,GAAG,GAAG3C,WAAC,CAAC+B,SAAS,CAACU,KAAK,CAAC;IACvC;EACF;EAEA,OAAOP,YAAY;AACrB"}