{"version": 3, "names": ["_checkInRHS", "require", "applyDecs2301Factory", "createAddInitializerMethod", "initializers", "decoratorFinishedRef", "addInitializer", "initializer", "assertNotFinished", "assertCallable", "push", "assertInstanceIfPrivate", "has", "target", "TypeError", "memberDec", "dec", "name", "desc", "kind", "isStatic", "isPrivate", "value", "hasPrivateBrand", "kindStr", "ctx", "static", "private", "v", "get", "set", "t", "call", "bind", "access", "fnName", "Error", "fn", "hint", "assertValidReturnValue", "type", "undefined", "init", "curryThis1", "curryThis2", "applyMemberDec", "ret", "base", "decInfo", "decs", "Object", "getOwnPropertyDescriptor", "newValue", "i", "length", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "applyMemberDecs", "Class", "decInfos", "instanceBrand", "protoInitializers", "staticInitializers", "staticBrand", "existingProtoNonFields", "Map", "existingStaticNonFields", "Array", "isArray", "_", "checkInRHS", "prototype", "existingNonFields", "existingKind", "pushInitializers", "applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs2301", "memberDecs", "e", "c", "exports", "default"], "sources": ["../../src/helpers/applyDecs2301.js"], "sourcesContent": ["/* @minVersion 7.21.0 */\n\nimport checkInRHS from \"checkInRHS\";\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction applyDecs2301Factory() {\n  function createAddInitializerMethod(initializers, decoratorFinishedRef) {\n    return function addInitializer(initializer) {\n      assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n      assertCallable(initializer, \"An initializer\");\n      initializers.push(initializer);\n    };\n  }\n\n  function assertInstanceIfPrivate(has, target) {\n    if (!has(target)) {\n      throw new TypeError(\n        \"Attempted to access private element on non-instance\"\n      );\n    }\n  }\n\n  function memberDec(\n    dec,\n    name,\n    desc,\n    initializers,\n    kind,\n    isStatic,\n    isPrivate,\n    value,\n    hasPrivateBrand\n  ) {\n    var kindStr;\n\n    switch (kind) {\n      case 1 /* ACCESSOR */:\n        kindStr = \"accessor\";\n        break;\n      case 2 /* METHOD */:\n        kindStr = \"method\";\n        break;\n      case 3 /* GETTER */:\n        kindStr = \"getter\";\n        break;\n      case 4 /* SETTER */:\n        kindStr = \"setter\";\n        break;\n      default:\n        kindStr = \"field\";\n    }\n\n    var ctx = {\n      kind: kindStr,\n      name: isPrivate ? \"#\" + name : name,\n      static: isStatic,\n      private: isPrivate,\n    };\n\n    var decoratorFinishedRef = { v: false };\n\n    if (kind !== 0 /* FIELD */) {\n      ctx.addInitializer = createAddInitializerMethod(\n        initializers,\n        decoratorFinishedRef\n      );\n    }\n\n    var get, set;\n    if (!isPrivate && (kind === 0 /* FIELD */ || kind === 2) /* METHOD */) {\n      get = function (target) {\n        return target[name];\n      };\n      if (kind === 0 /* FIELD */) {\n        set = function (target, v) {\n          target[name] = v;\n        };\n      }\n    } else if (kind === 2 /* METHOD */) {\n      // Assert: isPrivate is true.\n      get = function (target) {\n        assertInstanceIfPrivate(hasPrivateBrand, target);\n        return desc.value;\n      };\n    } else {\n      // Assert: If kind === 0, then isPrivate is true.\n      var t = kind === 0 /* FIELD */ || kind === 1; /* ACCESSOR */\n      if (t || kind === 3 /* GETTER */) {\n        if (isPrivate) {\n          get = function (target) {\n            assertInstanceIfPrivate(hasPrivateBrand, target);\n            return desc.get.call(target);\n          };\n        } else {\n          get = function (target) {\n            return desc.get.call(target);\n          };\n        }\n      }\n      if (t || kind === 4 /* SETTER */) {\n        if (isPrivate) {\n          set = function (target, value) {\n            assertInstanceIfPrivate(hasPrivateBrand, target);\n            desc.set.call(target, value);\n          };\n        } else {\n          set = function (target, value) {\n            desc.set.call(target, value);\n          };\n        }\n      }\n    }\n    var has = isPrivate\n      ? hasPrivateBrand.bind()\n      : function (target) {\n          return name in target;\n        };\n    ctx.access =\n      get && set\n        ? { get: get, set: set, has: has }\n        : get\n        ? { get: get, has: has }\n        : { set: set, has: has };\n\n    try {\n      return dec(value, ctx);\n    } finally {\n      decoratorFinishedRef.v = true;\n    }\n  }\n\n  function assertNotFinished(decoratorFinishedRef, fnName) {\n    if (decoratorFinishedRef.v) {\n      throw new Error(\n        \"attempted to call \" + fnName + \" after decoration was finished\"\n      );\n    }\n  }\n\n  function assertCallable(fn, hint) {\n    if (typeof fn !== \"function\") {\n      throw new TypeError(hint + \" must be a function\");\n    }\n  }\n\n  function assertValidReturnValue(kind, value) {\n    var type = typeof value;\n\n    if (kind === 1 /* ACCESSOR */) {\n      if (type !== \"object\" || value === null) {\n        throw new TypeError(\n          \"accessor decorators must return an object with get, set, or init properties or void 0\"\n        );\n      }\n      if (value.get !== undefined) {\n        assertCallable(value.get, \"accessor.get\");\n      }\n      if (value.set !== undefined) {\n        assertCallable(value.set, \"accessor.set\");\n      }\n      if (value.init !== undefined) {\n        assertCallable(value.init, \"accessor.init\");\n      }\n    } else if (type !== \"function\") {\n      var hint;\n      if (kind === 0 /* FIELD */) {\n        hint = \"field\";\n      } else if (kind === 10 /* CLASS */) {\n        hint = \"class\";\n      } else {\n        hint = \"method\";\n      }\n      throw new TypeError(\n        hint + \" decorators must return a function or void 0\"\n      );\n    }\n  }\n\n  function curryThis1(fn) {\n    return function () {\n      return fn(this);\n    };\n  }\n  function curryThis2(fn) {\n    return function (value) {\n      fn(this, value);\n    };\n  }\n\n  function applyMemberDec(\n    ret,\n    base,\n    decInfo,\n    name,\n    kind,\n    isStatic,\n    isPrivate,\n    initializers,\n    hasPrivateBrand\n  ) {\n    var decs = decInfo[0];\n\n    var desc, init, value;\n\n    if (isPrivate) {\n      if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n        desc = {\n          get: curryThis1(decInfo[3]),\n          set: curryThis2(decInfo[4]),\n        };\n      } else {\n        if (kind === 3 /* GETTER */) {\n          desc = {\n            get: decInfo[3],\n          };\n        } else if (kind === 4 /* SETTER */) {\n          desc = {\n            set: decInfo[3],\n          };\n        } else {\n          desc = {\n            value: decInfo[3],\n          };\n        }\n      }\n    } else if (kind !== 0 /* FIELD */) {\n      desc = Object.getOwnPropertyDescriptor(base, name);\n    }\n\n    if (kind === 1 /* ACCESSOR */) {\n      value = {\n        get: desc.get,\n        set: desc.set,\n      };\n    } else if (kind === 2 /* METHOD */) {\n      value = desc.value;\n    } else if (kind === 3 /* GETTER */) {\n      value = desc.get;\n    } else if (kind === 4 /* SETTER */) {\n      value = desc.set;\n    }\n\n    var newValue, get, set;\n\n    if (typeof decs === \"function\") {\n      newValue = memberDec(\n        decs,\n        name,\n        desc,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value,\n        hasPrivateBrand\n      );\n\n      if (newValue !== void 0) {\n        assertValidReturnValue(kind, newValue);\n\n        if (kind === 0 /* FIELD */) {\n          init = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          init = newValue.init;\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n      }\n    } else {\n      for (var i = decs.length - 1; i >= 0; i--) {\n        var dec = decs[i];\n\n        newValue = memberDec(\n          dec,\n          name,\n          desc,\n          initializers,\n          kind,\n          isStatic,\n          isPrivate,\n          value,\n          hasPrivateBrand\n        );\n\n        if (newValue !== void 0) {\n          assertValidReturnValue(kind, newValue);\n          var newInit;\n\n          if (kind === 0 /* FIELD */) {\n            newInit = newValue;\n          } else if (kind === 1 /* ACCESSOR */) {\n            newInit = newValue.init;\n            get = newValue.get || value.get;\n            set = newValue.set || value.set;\n\n            value = { get: get, set: set };\n          } else {\n            value = newValue;\n          }\n\n          if (newInit !== void 0) {\n            if (init === void 0) {\n              init = newInit;\n            } else if (typeof init === \"function\") {\n              init = [init, newInit];\n            } else {\n              init.push(newInit);\n            }\n          }\n        }\n      }\n    }\n\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      if (init === void 0) {\n        // If the initializer was void 0, sub in a dummy initializer\n        init = function (instance, init) {\n          return init;\n        };\n      } else if (typeof init !== \"function\") {\n        var ownInitializers = init;\n\n        init = function (instance, init) {\n          var value = init;\n\n          for (var i = 0; i < ownInitializers.length; i++) {\n            value = ownInitializers[i].call(instance, value);\n          }\n\n          return value;\n        };\n      } else {\n        var originalInitializer = init;\n\n        init = function (instance, init) {\n          return originalInitializer.call(instance, init);\n        };\n      }\n\n      ret.push(init);\n    }\n\n    if (kind !== 0 /* FIELD */) {\n      if (kind === 1 /* ACCESSOR */) {\n        desc.get = value.get;\n        desc.set = value.set;\n      } else if (kind === 2 /* METHOD */) {\n        desc.value = value;\n      } else if (kind === 3 /* GETTER */) {\n        desc.get = value;\n      } else if (kind === 4 /* SETTER */) {\n        desc.set = value;\n      }\n\n      if (isPrivate) {\n        if (kind === 1 /* ACCESSOR */) {\n          ret.push(function (instance, args) {\n            return value.get.call(instance, args);\n          });\n          ret.push(function (instance, args) {\n            return value.set.call(instance, args);\n          });\n        } else if (kind === 2 /* METHOD */) {\n          ret.push(value);\n        } else {\n          ret.push(function (instance, args) {\n            return value.call(instance, args);\n          });\n        }\n      } else {\n        Object.defineProperty(base, name, desc);\n      }\n    }\n  }\n\n  function applyMemberDecs(Class, decInfos, instanceBrand) {\n    var ret = [];\n    var protoInitializers;\n    var staticInitializers;\n    var staticBrand;\n\n    var existingProtoNonFields = new Map();\n    var existingStaticNonFields = new Map();\n\n    for (var i = 0; i < decInfos.length; i++) {\n      var decInfo = decInfos[i];\n\n      // skip computed property names\n      if (!Array.isArray(decInfo)) continue;\n\n      var kind = decInfo[1];\n      var name = decInfo[2];\n      var isPrivate = decInfo.length > 3;\n\n      var isStatic = kind >= 5; /* STATIC */\n      var base;\n      var initializers;\n      var hasPrivateBrand = instanceBrand;\n\n      if (isStatic) {\n        base = Class;\n        kind = kind - 5 /* STATIC */;\n        // initialize staticInitializers when we see a non-field static member\n        if (kind !== 0 /* FIELD */) {\n          staticInitializers = staticInitializers || [];\n          initializers = staticInitializers;\n        }\n        if (isPrivate && !staticBrand) {\n          staticBrand = function (_) {\n            return checkInRHS(_) === Class;\n          };\n        }\n        hasPrivateBrand = staticBrand;\n      } else {\n        base = Class.prototype;\n        // initialize protoInitializers when we see a non-field member\n        if (kind !== 0 /* FIELD */) {\n          protoInitializers = protoInitializers || [];\n          initializers = protoInitializers;\n        }\n      }\n\n      if (kind !== 0 /* FIELD */ && !isPrivate) {\n        var existingNonFields = isStatic\n          ? existingStaticNonFields\n          : existingProtoNonFields;\n\n        var existingKind = existingNonFields.get(name) || 0;\n\n        if (\n          existingKind === true ||\n          (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n          (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n        ) {\n          throw new Error(\n            \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n              name\n          );\n        } else if (!existingKind && kind > 2 /* METHOD */) {\n          existingNonFields.set(name, kind);\n        } else {\n          existingNonFields.set(name, true);\n        }\n      }\n\n      applyMemberDec(\n        ret,\n        base,\n        decInfo,\n        name,\n        kind,\n        isStatic,\n        isPrivate,\n        initializers,\n        hasPrivateBrand\n      );\n    }\n\n    pushInitializers(ret, protoInitializers);\n    pushInitializers(ret, staticInitializers);\n    return ret;\n  }\n\n  function pushInitializers(ret, initializers) {\n    if (initializers) {\n      ret.push(function (instance) {\n        for (var i = 0; i < initializers.length; i++) {\n          initializers[i].call(instance);\n        }\n        return instance;\n      });\n    }\n  }\n\n  function applyClassDecs(targetClass, classDecs) {\n    if (classDecs.length > 0) {\n      var initializers = [];\n      var newClass = targetClass;\n      var name = targetClass.name;\n\n      for (var i = classDecs.length - 1; i >= 0; i--) {\n        var decoratorFinishedRef = { v: false };\n\n        try {\n          var nextNewClass = classDecs[i](newClass, {\n            kind: \"class\",\n            name: name,\n            addInitializer: createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef\n            ),\n          });\n        } finally {\n          decoratorFinishedRef.v = true;\n        }\n\n        if (nextNewClass !== undefined) {\n          assertValidReturnValue(10 /* CLASS */, nextNewClass);\n          newClass = nextNewClass;\n        }\n      }\n\n      return [\n        newClass,\n        function () {\n          for (var i = 0; i < initializers.length; i++) {\n            initializers[i].call(newClass);\n          }\n        },\n      ];\n    }\n    // The transformer will not emit assignment when there are no class decorators,\n    // so we don't have to return an empty array here.\n  }\n\n  /**\n    Basic usage:\n\n    applyDecs(\n      Class,\n      [\n        // member decorators\n        [\n          dec,                // dec or array of decs\n          0,                  // kind of value being decorated\n          'prop',             // name of public prop on class containing the value being decorated,\n          '#p',               // the name of the private property (if is private, void 0 otherwise),\n        ]\n      ],\n      [\n        // class decorators\n        dec1, dec2\n      ]\n    )\n    ```\n\n    Fully transpiled example:\n\n    ```js\n    @dec\n    class Class {\n      @dec\n      a = 123;\n\n      @dec\n      #a = 123;\n\n      @dec\n      @dec2\n      accessor b = 123;\n\n      @dec\n      accessor #b = 123;\n\n      @dec\n      c() { console.log('c'); }\n\n      @dec\n      #c() { console.log('privC'); }\n\n      @dec\n      get d() { console.log('d'); }\n\n      @dec\n      get #d() { console.log('privD'); }\n\n      @dec\n      set e(v) { console.log('e'); }\n\n      @dec\n      set #e(v) { console.log('privE'); }\n    }\n\n\n    // becomes\n    let initializeInstance;\n    let initializeClass;\n\n    let initA;\n    let initPrivA;\n\n    let initB;\n    let initPrivB, getPrivB, setPrivB;\n\n    let privC;\n    let privD;\n    let privE;\n\n    let Class;\n    class _Class {\n      static {\n        let ret = applyDecs(\n          this,\n          [\n            [dec, 0, 'a'],\n            [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n            [[dec, dec2], 1, 'b'],\n            [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n            [dec, 2, 'c'],\n            [dec, 2, 'c', () => console.log('privC')],\n            [dec, 3, 'd'],\n            [dec, 3, 'd', () => console.log('privD')],\n            [dec, 4, 'e'],\n            [dec, 4, 'e', () => console.log('privE')],\n          ],\n          [\n            dec\n          ]\n        )\n\n        initA = ret[0];\n\n        initPrivA = ret[1];\n\n        initB = ret[2];\n\n        initPrivB = ret[3];\n        getPrivB = ret[4];\n        setPrivB = ret[5];\n\n        privC = ret[6];\n\n        privD = ret[7];\n\n        privE = ret[8];\n\n        initializeInstance = ret[9];\n\n        Class = ret[10]\n\n        initializeClass = ret[11];\n      }\n\n      a = (initializeInstance(this), initA(this, 123));\n\n      #a = initPrivA(this, 123);\n\n      #bData = initB(this, 123);\n      get b() { return this.#bData }\n      set b(v) { this.#bData = v }\n\n      #privBData = initPrivB(this, 123);\n      get #b() { return getPrivB(this); }\n      set #b(v) { setPrivB(this, v); }\n\n      c() { console.log('c'); }\n\n      #c(...args) { return privC(this, ...args) }\n\n      get d() { console.log('d'); }\n\n      get #d() { return privD(this); }\n\n      set e(v) { console.log('e'); }\n\n      set #e(v) { privE(this, v); }\n    }\n\n    initializeClass(Class);\n  */\n  return function applyDecs2301(\n    targetClass,\n    memberDecs,\n    classDecs,\n    instanceBrand\n  ) {\n    return {\n      e: applyMemberDecs(targetClass, memberDecs, instanceBrand),\n      // Lazily apply class decorations so that member init locals can be properly bound.\n      get c() {\n        return applyClassDecs(targetClass, classDecs);\n      },\n    };\n  };\n}\n\nexport default function applyDecs2301(\n  targetClass,\n  memberDecs,\n  classDecs,\n  instanceBrand\n) {\n  return (applyDecs2301 = applyDecs2301Factory())(\n    targetClass,\n    memberDecs,\n    classDecs,\n    instanceBrand\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,WAAA,GAAAC,OAAA;AAoBA,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,SAASC,0BAA0BA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;IACtE,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAE;MAC1CC,iBAAiB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;MACzDI,cAAc,CAACF,WAAW,EAAE,gBAAgB,CAAC;MAC7CH,YAAY,CAACM,IAAI,CAACH,WAAW,CAAC;IAChC,CAAC;EACH;EAEA,SAASI,uBAAuBA,CAACC,GAAG,EAAEC,MAAM,EAAE;IAC5C,IAAI,CAACD,GAAG,CAACC,MAAM,CAAC,EAAE;MAChB,MAAM,IAAIC,SAAS,CACjB,qDACF,CAAC;IACH;EACF;EAEA,SAASC,SAASA,CAChBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eAAe,EACf;IACA,IAAIC,OAAO;IAEX,QAAQL,IAAI;MACV,KAAK,CAAC;QACJK,OAAO,GAAG,UAAU;QACpB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF;QACEA,OAAO,GAAG,OAAO;IACrB;IAEA,IAAIC,GAAG,GAAG;MACRN,IAAI,EAAEK,OAAO;MACbP,IAAI,EAAEI,SAAS,GAAG,GAAG,GAAGJ,IAAI,GAAGA,IAAI;MACnCS,MAAM,EAAEN,QAAQ;MAChBO,OAAO,EAAEN;IACX,CAAC;IAED,IAAIhB,oBAAoB,GAAG;MAAEuB,CAAC,EAAE;IAAM,CAAC;IAEvC,IAAIT,IAAI,KAAK,CAAC,EAAc;MAC1BM,GAAG,CAACnB,cAAc,GAAGH,0BAA0B,CAC7CC,YAAY,EACZC,oBACF,CAAC;IACH;IAEA,IAAIwB,GAAG,EAAEC,GAAG;IACZ,IAAI,CAACT,SAAS,KAAKF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,CAAC,EAAe;MACrEU,GAAG,GAAG,SAAAA,CAAUhB,MAAM,EAAE;QACtB,OAAOA,MAAM,CAACI,IAAI,CAAC;MACrB,CAAC;MACD,IAAIE,IAAI,KAAK,CAAC,EAAc;QAC1BW,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAEe,CAAC,EAAE;UACzBf,MAAM,CAACI,IAAI,CAAC,GAAGW,CAAC;QAClB,CAAC;MACH;IACF,CAAC,MAAM,IAAIT,IAAI,KAAK,CAAC,EAAe;MAElCU,GAAG,GAAG,SAAAA,CAAUhB,MAAM,EAAE;QACtBF,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;QAChD,OAAOK,IAAI,CAACI,KAAK;MACnB,CAAC;IACH,CAAC,MAAM;MAEL,IAAIS,CAAC,GAAGZ,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC;MAC5C,IAAIY,CAAC,IAAIZ,IAAI,KAAK,CAAC,EAAe;QAChC,IAAIE,SAAS,EAAE;UACbQ,GAAG,GAAG,SAAAA,CAAUhB,MAAM,EAAE;YACtBF,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;YAChD,OAAOK,IAAI,CAACW,GAAG,CAACG,IAAI,CAACnB,MAAM,CAAC;UAC9B,CAAC;QACH,CAAC,MAAM;UACLgB,GAAG,GAAG,SAAAA,CAAUhB,MAAM,EAAE;YACtB,OAAOK,IAAI,CAACW,GAAG,CAACG,IAAI,CAACnB,MAAM,CAAC;UAC9B,CAAC;QACH;MACF;MACA,IAAIkB,CAAC,IAAIZ,IAAI,KAAK,CAAC,EAAe;QAChC,IAAIE,SAAS,EAAE;UACbS,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAES,KAAK,EAAE;YAC7BX,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;YAChDK,IAAI,CAACY,GAAG,CAACE,IAAI,CAACnB,MAAM,EAAES,KAAK,CAAC;UAC9B,CAAC;QACH,CAAC,MAAM;UACLQ,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAES,KAAK,EAAE;YAC7BJ,IAAI,CAACY,GAAG,CAACE,IAAI,CAACnB,MAAM,EAAES,KAAK,CAAC;UAC9B,CAAC;QACH;MACF;IACF;IACA,IAAIV,GAAG,GAAGS,SAAS,GACfE,eAAe,CAACU,IAAI,CAAC,CAAC,GACtB,UAAUpB,MAAM,EAAE;MAChB,OAAOI,IAAI,IAAIJ,MAAM;IACvB,CAAC;IACLY,GAAG,CAACS,MAAM,GACRL,GAAG,IAAIC,GAAG,GACN;MAAED,GAAG,EAAEA,GAAG;MAAEC,GAAG,EAAEA,GAAG;MAAElB,GAAG,EAAEA;IAAI,CAAC,GAChCiB,GAAG,GACH;MAAEA,GAAG,EAAEA,GAAG;MAAEjB,GAAG,EAAEA;IAAI,CAAC,GACtB;MAAEkB,GAAG,EAAEA,GAAG;MAAElB,GAAG,EAAEA;IAAI,CAAC;IAE5B,IAAI;MACF,OAAOI,GAAG,CAACM,KAAK,EAAEG,GAAG,CAAC;IACxB,CAAC,SAAS;MACRpB,oBAAoB,CAACuB,CAAC,GAAG,IAAI;IAC/B;EACF;EAEA,SAASpB,iBAAiBA,CAACH,oBAAoB,EAAE8B,MAAM,EAAE;IACvD,IAAI9B,oBAAoB,CAACuB,CAAC,EAAE;MAC1B,MAAM,IAAIQ,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAClC,CAAC;IACH;EACF;EAEA,SAAS1B,cAAcA,CAAC4B,EAAE,EAAEC,IAAI,EAAE;IAChC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;MAC5B,MAAM,IAAIvB,SAAS,CAACwB,IAAI,GAAG,qBAAqB,CAAC;IACnD;EACF;EAEA,SAASC,sBAAsBA,CAACpB,IAAI,EAAEG,KAAK,EAAE;IAC3C,IAAIkB,IAAI,GAAG,OAAOlB,KAAK;IAEvB,IAAIH,IAAI,KAAK,CAAC,EAAiB;MAC7B,IAAIqB,IAAI,KAAK,QAAQ,IAAIlB,KAAK,KAAK,IAAI,EAAE;QACvC,MAAM,IAAIR,SAAS,CACjB,uFACF,CAAC;MACH;MACA,IAAIQ,KAAK,CAACO,GAAG,KAAKY,SAAS,EAAE;QAC3BhC,cAAc,CAACa,KAAK,CAACO,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIP,KAAK,CAACQ,GAAG,KAAKW,SAAS,EAAE;QAC3BhC,cAAc,CAACa,KAAK,CAACQ,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIR,KAAK,CAACoB,IAAI,KAAKD,SAAS,EAAE;QAC5BhC,cAAc,CAACa,KAAK,CAACoB,IAAI,EAAE,eAAe,CAAC;MAC7C;IACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAIF,IAAI;MACR,IAAInB,IAAI,KAAK,CAAC,EAAc;QAC1BmB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM,IAAInB,IAAI,KAAK,EAAE,EAAc;QAClCmB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM;QACLA,IAAI,GAAG,QAAQ;MACjB;MACA,MAAM,IAAIxB,SAAS,CACjBwB,IAAI,GAAG,8CACT,CAAC;IACH;EACF;EAEA,SAASK,UAAUA,CAACN,EAAE,EAAE;IACtB,OAAO,YAAY;MACjB,OAAOA,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC;EACH;EACA,SAASO,UAAUA,CAACP,EAAE,EAAE;IACtB,OAAO,UAAUf,KAAK,EAAE;MACtBe,EAAE,CAAC,IAAI,EAAEf,KAAK,CAAC;IACjB,CAAC;EACH;EAEA,SAASuB,cAAcA,CACrBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP/B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTjB,YAAY,EACZmB,eAAe,EACf;IACA,IAAI0B,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;IAErB,IAAI9B,IAAI,EAAEwB,IAAI,EAAEpB,KAAK;IAErB,IAAID,SAAS,EAAE;MACb,IAAIF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;QACvDD,IAAI,GAAG;UACLW,GAAG,EAAEc,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;UAC3BlB,GAAG,EAAEc,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC;QAC5B,CAAC;MACH,CAAC,MAAM;QACL,IAAI7B,IAAI,KAAK,CAAC,EAAe;UAC3BD,IAAI,GAAG;YACLW,GAAG,EAAEmB,OAAO,CAAC,CAAC;UAChB,CAAC;QACH,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAe;UAClCD,IAAI,GAAG;YACLY,GAAG,EAAEkB,OAAO,CAAC,CAAC;UAChB,CAAC;QACH,CAAC,MAAM;UACL9B,IAAI,GAAG;YACLI,KAAK,EAAE0B,OAAO,CAAC,CAAC;UAClB,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAc;MACjCD,IAAI,GAAGgC,MAAM,CAACC,wBAAwB,CAACJ,IAAI,EAAE9B,IAAI,CAAC;IACpD;IAEA,IAAIE,IAAI,KAAK,CAAC,EAAiB;MAC7BG,KAAK,GAAG;QACNO,GAAG,EAAEX,IAAI,CAACW,GAAG;QACbC,GAAG,EAAEZ,IAAI,CAACY;MACZ,CAAC;IACH,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACW,GAAG;IAClB,CAAC,MAAM,IAAIV,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACY,GAAG;IAClB;IAEA,IAAIsB,QAAQ,EAAEvB,GAAG,EAAEC,GAAG;IAEtB,IAAI,OAAOmB,IAAI,KAAK,UAAU,EAAE;MAC9BG,QAAQ,GAAGrC,SAAS,CAClBkC,IAAI,EACJhC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eACF,CAAC;MAED,IAAI6B,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBb,sBAAsB,CAACpB,IAAI,EAAEiC,QAAQ,CAAC;QAEtC,IAAIjC,IAAI,KAAK,CAAC,EAAc;UAC1BuB,IAAI,GAAGU,QAAQ;QACjB,CAAC,MAAM,IAAIjC,IAAI,KAAK,CAAC,EAAiB;UACpCuB,IAAI,GAAGU,QAAQ,CAACV,IAAI;UACpBb,GAAG,GAAGuB,QAAQ,CAACvB,GAAG,IAAIP,KAAK,CAACO,GAAG;UAC/BC,GAAG,GAAGsB,QAAQ,CAACtB,GAAG,IAAIR,KAAK,CAACQ,GAAG;UAE/BR,KAAK,GAAG;YAAEO,GAAG,EAAEA,GAAG;YAAEC,GAAG,EAAEA;UAAI,CAAC;QAChC,CAAC,MAAM;UACLR,KAAK,GAAG8B,QAAQ;QAClB;MACF;IACF,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzC,IAAIrC,GAAG,GAAGiC,IAAI,CAACI,CAAC,CAAC;QAEjBD,QAAQ,GAAGrC,SAAS,CAClBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eACF,CAAC;QAED,IAAI6B,QAAQ,KAAK,KAAK,CAAC,EAAE;UACvBb,sBAAsB,CAACpB,IAAI,EAAEiC,QAAQ,CAAC;UACtC,IAAIG,OAAO;UAEX,IAAIpC,IAAI,KAAK,CAAC,EAAc;YAC1BoC,OAAO,GAAGH,QAAQ;UACpB,CAAC,MAAM,IAAIjC,IAAI,KAAK,CAAC,EAAiB;YACpCoC,OAAO,GAAGH,QAAQ,CAACV,IAAI;YACvBb,GAAG,GAAGuB,QAAQ,CAACvB,GAAG,IAAIP,KAAK,CAACO,GAAG;YAC/BC,GAAG,GAAGsB,QAAQ,CAACtB,GAAG,IAAIR,KAAK,CAACQ,GAAG;YAE/BR,KAAK,GAAG;cAAEO,GAAG,EAAEA,GAAG;cAAEC,GAAG,EAAEA;YAAI,CAAC;UAChC,CAAC,MAAM;YACLR,KAAK,GAAG8B,QAAQ;UAClB;UAEA,IAAIG,OAAO,KAAK,KAAK,CAAC,EAAE;YACtB,IAAIb,IAAI,KAAK,KAAK,CAAC,EAAE;cACnBA,IAAI,GAAGa,OAAO;YAChB,CAAC,MAAM,IAAI,OAAOb,IAAI,KAAK,UAAU,EAAE;cACrCA,IAAI,GAAG,CAACA,IAAI,EAAEa,OAAO,CAAC;YACxB,CAAC,MAAM;cACLb,IAAI,CAAChC,IAAI,CAAC6C,OAAO,CAAC;YACpB;UACF;QACF;MACF;IACF;IAEA,IAAIpC,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvD,IAAIuB,IAAI,KAAK,KAAK,CAAC,EAAE;QAEnBA,IAAI,GAAG,SAAAA,CAAUc,QAAQ,EAAEd,IAAI,EAAE;UAC/B,OAAOA,IAAI;QACb,CAAC;MACH,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QACrC,IAAIe,eAAe,GAAGf,IAAI;QAE1BA,IAAI,GAAG,SAAAA,CAAUc,QAAQ,EAAEd,IAAI,EAAE;UAC/B,IAAIpB,KAAK,GAAGoB,IAAI;UAEhB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,eAAe,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;YAC/C/B,KAAK,GAAGmC,eAAe,CAACJ,CAAC,CAAC,CAACrB,IAAI,CAACwB,QAAQ,EAAElC,KAAK,CAAC;UAClD;UAEA,OAAOA,KAAK;QACd,CAAC;MACH,CAAC,MAAM;QACL,IAAIoC,mBAAmB,GAAGhB,IAAI;QAE9BA,IAAI,GAAG,SAAAA,CAAUc,QAAQ,EAAEd,IAAI,EAAE;UAC/B,OAAOgB,mBAAmB,CAAC1B,IAAI,CAACwB,QAAQ,EAAEd,IAAI,CAAC;QACjD,CAAC;MACH;MAEAI,GAAG,CAACpC,IAAI,CAACgC,IAAI,CAAC;IAChB;IAEA,IAAIvB,IAAI,KAAK,CAAC,EAAc;MAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;QAC7BD,IAAI,CAACW,GAAG,GAAGP,KAAK,CAACO,GAAG;QACpBX,IAAI,CAACY,GAAG,GAAGR,KAAK,CAACQ,GAAG;MACtB,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACI,KAAK,GAAGA,KAAK;MACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACW,GAAG,GAAGP,KAAK;MAClB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACY,GAAG,GAAGR,KAAK;MAClB;MAEA,IAAID,SAAS,EAAE;QACb,IAAIF,IAAI,KAAK,CAAC,EAAiB;UAC7B2B,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOrC,KAAK,CAACO,GAAG,CAACG,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;UACFb,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOrC,KAAK,CAACQ,GAAG,CAACE,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIxC,IAAI,KAAK,CAAC,EAAe;UAClC2B,GAAG,CAACpC,IAAI,CAACY,KAAK,CAAC;QACjB,CAAC,MAAM;UACLwB,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOrC,KAAK,CAACU,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;UACnC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLT,MAAM,CAACU,cAAc,CAACb,IAAI,EAAE9B,IAAI,EAAEC,IAAI,CAAC;MACzC;IACF;EACF;EAEA,SAAS2C,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IACvD,IAAIlB,GAAG,GAAG,EAAE;IACZ,IAAImB,iBAAiB;IACrB,IAAIC,kBAAkB;IACtB,IAAIC,WAAW;IAEf,IAAIC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC,IAAIC,uBAAuB,GAAG,IAAID,GAAG,CAAC,CAAC;IAEvC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,QAAQ,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIL,OAAO,GAAGe,QAAQ,CAACV,CAAC,CAAC;MAGzB,IAAI,CAACkB,KAAK,CAACC,OAAO,CAACxB,OAAO,CAAC,EAAE;MAE7B,IAAI7B,IAAI,GAAG6B,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI/B,IAAI,GAAG+B,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI3B,SAAS,GAAG2B,OAAO,CAACM,MAAM,GAAG,CAAC;MAElC,IAAIlC,QAAQ,GAAGD,IAAI,IAAI,CAAC;MACxB,IAAI4B,IAAI;MACR,IAAI3C,YAAY;MAChB,IAAImB,eAAe,GAAGyC,aAAa;MAEnC,IAAI5C,QAAQ,EAAE;QACZ2B,IAAI,GAAGe,KAAK;QACZ3C,IAAI,GAAGA,IAAI,GAAG,CAAC;QAEf,IAAIA,IAAI,KAAK,CAAC,EAAc;UAC1B+C,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;UAC7C9D,YAAY,GAAG8D,kBAAkB;QACnC;QACA,IAAI7C,SAAS,IAAI,CAAC8C,WAAW,EAAE;UAC7BA,WAAW,GAAG,SAAAA,CAAUM,CAAC,EAAE;YACzB,OAAOC,WAAU,CAACD,CAAC,CAAC,KAAKX,KAAK;UAChC,CAAC;QACH;QACAvC,eAAe,GAAG4C,WAAW;MAC/B,CAAC,MAAM;QACLpB,IAAI,GAAGe,KAAK,CAACa,SAAS;QAEtB,IAAIxD,IAAI,KAAK,CAAC,EAAc;UAC1B8C,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;UAC3C7D,YAAY,GAAG6D,iBAAiB;QAClC;MACF;MAEA,IAAI9C,IAAI,KAAK,CAAC,IAAgB,CAACE,SAAS,EAAE;QACxC,IAAIuD,iBAAiB,GAAGxD,QAAQ,GAC5BkD,uBAAuB,GACvBF,sBAAsB;QAE1B,IAAIS,YAAY,GAAGD,iBAAiB,CAAC/C,GAAG,CAACZ,IAAI,CAAC,IAAI,CAAC;QAEnD,IACE4D,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiB1D,IAAI,KAAK,CAAE,IAC9C0D,YAAY,KAAK,CAAC,IAAiB1D,IAAI,KAAK,CAAE,EAC/C;UACA,MAAM,IAAIiB,KAAK,CACb,uMAAuM,GACrMnB,IACJ,CAAC;QACH,CAAC,MAAM,IAAI,CAAC4D,YAAY,IAAI1D,IAAI,GAAG,CAAC,EAAe;UACjDyD,iBAAiB,CAAC9C,GAAG,CAACb,IAAI,EAAEE,IAAI,CAAC;QACnC,CAAC,MAAM;UACLyD,iBAAiB,CAAC9C,GAAG,CAACb,IAAI,EAAE,IAAI,CAAC;QACnC;MACF;MAEA4B,cAAc,CACZC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP/B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTjB,YAAY,EACZmB,eACF,CAAC;IACH;IAEAuD,gBAAgB,CAAChC,GAAG,EAAEmB,iBAAiB,CAAC;IACxCa,gBAAgB,CAAChC,GAAG,EAAEoB,kBAAkB,CAAC;IACzC,OAAOpB,GAAG;EACZ;EAEA,SAASgC,gBAAgBA,CAAChC,GAAG,EAAE1C,YAAY,EAAE;IAC3C,IAAIA,YAAY,EAAE;MAChB0C,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAE;QAC3B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjD,YAAY,CAACkD,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5CjD,YAAY,CAACiD,CAAC,CAAC,CAACrB,IAAI,CAACwB,QAAQ,CAAC;QAChC;QACA,OAAOA,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF;EAEA,SAASuB,cAAcA,CAACC,WAAW,EAAEC,SAAS,EAAE;IAC9C,IAAIA,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAIlD,YAAY,GAAG,EAAE;MACrB,IAAI8E,QAAQ,GAAGF,WAAW;MAC1B,IAAI/D,IAAI,GAAG+D,WAAW,CAAC/D,IAAI;MAE3B,KAAK,IAAIoC,CAAC,GAAG4B,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9C,IAAIhD,oBAAoB,GAAG;UAAEuB,CAAC,EAAE;QAAM,CAAC;QAEvC,IAAI;UACF,IAAIuD,YAAY,GAAGF,SAAS,CAAC5B,CAAC,CAAC,CAAC6B,QAAQ,EAAE;YACxC/D,IAAI,EAAE,OAAO;YACbF,IAAI,EAAEA,IAAI;YACVX,cAAc,EAAEH,0BAA0B,CACxCC,YAAY,EACZC,oBACF;UACF,CAAC,CAAC;QACJ,CAAC,SAAS;UACRA,oBAAoB,CAACuB,CAAC,GAAG,IAAI;QAC/B;QAEA,IAAIuD,YAAY,KAAK1C,SAAS,EAAE;UAC9BF,sBAAsB,CAAC,EAAE,EAAc4C,YAAY,CAAC;UACpDD,QAAQ,GAAGC,YAAY;QACzB;MACF;MAEA,OAAO,CACLD,QAAQ,EACR,YAAY;QACV,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjD,YAAY,CAACkD,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5CjD,YAAY,CAACiD,CAAC,CAAC,CAACrB,IAAI,CAACkD,QAAQ,CAAC;QAChC;MACF,CAAC,CACF;IACH;EAGF;EAmJA,OAAO,SAASE,aAAaA,CAC3BJ,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTjB,aAAa,EACb;IACA,OAAO;MACLsB,CAAC,EAAEzB,eAAe,CAACmB,WAAW,EAAEK,UAAU,EAAErB,aAAa,CAAC;MAE1D,IAAIuB,CAACA,CAAA,EAAG;QACN,OAAOR,cAAc,CAACC,WAAW,EAAEC,SAAS,CAAC;MAC/C;IACF,CAAC;EACH,CAAC;AACH;AAEe,SAASG,aAAaA,CACnCJ,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTjB,aAAa,EACb;EACA,OAAO,CAAAwB,OAAA,CAAAC,OAAA,GAACL,aAAa,GAAGlF,oBAAoB,CAAC,CAAC,EAC5C8E,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTjB,aACF,CAAC;AACH"}