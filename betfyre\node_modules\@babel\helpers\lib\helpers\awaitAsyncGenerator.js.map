{"version": 3, "names": ["_OverloadYield", "require", "_awaitAsyncGenerator", "value", "OverloadYield"], "sources": ["../../src/helpers/awaitAsyncGenerator.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport OverloadYield from \"OverloadYield\";\n\nexport default function _awaitAsyncGenerator(value) {\n  return new OverloadYield(value, /* kind: await */ 0);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAEe,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAClD,OAAO,IAAIC,cAAa,CAACD,KAAK,EAAoB,CAAC,CAAC;AACtD"}