{"version": 3, "names": ["willPathCastToBoolean", "path", "maybeWrapped", "node", "parentPath", "isLogicalExpression", "operator", "right", "isSequenceExpression", "expressions", "length", "isConditional", "test", "isUnaryExpression", "isLoop"], "sources": ["../src/util.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\n\n/**\n * Test if a NodePath will be cast to boolean when evaluated.\n *\n * @example\n * // returns true\n * const nodePathAQDotB = NodePath(\"if (a?.#b) {}\").get(\"test\"); // a?.#b\n * willPathCastToBoolean(nodePathAQDotB)\n * @example\n * // returns false\n * willPathCastToBoolean(NodePath(\"a?.#b\"))\n * @todo Respect transparent expression wrappers\n * @see {@link packages/babel-plugin-transform-optional-chaining/src/util.js}\n * @param {NodePath} path\n * @returns {boolean}\n */\nexport function willPathCastToBoolean(path: NodePath): boolean {\n  const maybeWrapped = path;\n  const { node, parentPath } = maybeWrapped;\n  if (parentPath.isLogicalExpression()) {\n    const { operator, right } = parentPath.node;\n    if (\n      operator === \"&&\" ||\n      operator === \"||\" ||\n      (operator === \"??\" && node === right)\n    ) {\n      return willPathCastToBoolean(parentPath);\n    }\n  }\n  if (parentPath.isSequenceExpression()) {\n    const { expressions } = parentPath.node;\n    if (expressions[expressions.length - 1] === node) {\n      return willPathCastToBoolean(parentPath);\n    } else {\n      // if it is in the middle of a sequence expression, we don't\n      // care the return value so just cast to boolean for smaller\n      // output\n      return true;\n    }\n  }\n  return (\n    parentPath.isConditional({ test: node }) ||\n    parentPath.isUnaryExpression({ operator: \"!\" }) ||\n    parentPath.isLoop({ test: node })\n  );\n}\n"], "mappings": ";;;;;;AAiBO,SAASA,qBAAqBA,CAACC,IAAc,EAAW;EAC7D,MAAMC,YAAY,GAAGD,IAAI;EACzB,MAAM;IAAEE,IAAI;IAAEC;EAAW,CAAC,GAAGF,YAAY;EACzC,IAAIE,UAAU,CAACC,mBAAmB,CAAC,CAAC,EAAE;IACpC,MAAM;MAAEC,QAAQ;MAAEC;IAAM,CAAC,GAAGH,UAAU,CAACD,IAAI;IAC3C,IACEG,QAAQ,KAAK,IAAI,IACjBA,QAAQ,KAAK,IAAI,IAChBA,QAAQ,KAAK,IAAI,IAAIH,IAAI,KAAKI,KAAM,EACrC;MACA,OAAOP,qBAAqB,CAACI,UAAU,CAAC;IAC1C;EACF;EACA,IAAIA,UAAU,CAACI,oBAAoB,CAAC,CAAC,EAAE;IACrC,MAAM;MAAEC;IAAY,CAAC,GAAGL,UAAU,CAACD,IAAI;IACvC,IAAIM,WAAW,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKP,IAAI,EAAE;MAChD,OAAOH,qBAAqB,CAACI,UAAU,CAAC;IAC1C,CAAC,MAAM;MAIL,OAAO,IAAI;IACb;EACF;EACA,OACEA,UAAU,CAACO,aAAa,CAAC;IAAEC,IAAI,EAAET;EAAK,CAAC,CAAC,IACxCC,UAAU,CAACS,iBAAiB,CAAC;IAAEP,QAAQ,EAAE;EAAI,CAAC,CAAC,IAC/CF,UAAU,CAACU,MAAM,CAAC;IAAEF,IAAI,EAAET;EAAK,CAAC,CAAC;AAErC"}