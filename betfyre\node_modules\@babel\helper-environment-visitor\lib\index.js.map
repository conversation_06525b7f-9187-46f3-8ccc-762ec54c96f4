{"version": 3, "names": ["exports", "skipAllButComputedKey", "path", "skip", "node", "computed", "context", "maybeQueue", "get", "requeueComputedKeyAndDecorators", "decorators", "decorator", "visitor", "FunctionParent", "isArrowFunctionExpression", "isMethod", "Property", "isObjectProperty", "_default", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { Node<PERSON><PERSON>, Visitor } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nif (!process.env.BABEL_8_BREAKING) {\n  if (!USE_ESM) {\n    if (!IS_STANDALONE) {\n      // eslint-disable-next-line no-restricted-globals\n      exports.skipAllButComputedKey = function skipAllButComputedKey(\n        path: NodePath<t.Method | t.ClassProperty>,\n      ) {\n        path.skip();\n        if (path.node.computed) {\n          // requeue the computed key\n          path.context.maybeQueue(path.get(\"key\"));\n        }\n      };\n    }\n  }\n}\n\nexport function requeueComputedKeyAndDecorators(\n  path: NodePath<t.Method | t.Property>,\n) {\n  const { context, node } = path;\n  // @ts-expect-error ClassPrivateProperty does not have computed\n  if (node.computed) {\n    // requeue the computed key\n    context.maybeQueue(path.get(\"key\"));\n  }\n  if (node.decorators) {\n    for (const decorator of path.get(\"decorators\")) {\n      // requeue the decorators\n      context.maybeQueue(decorator);\n    }\n  }\n}\n\n// environmentVisitor should be used when traversing the whole class and not for specific class elements/methods.\n// For perf reasons, the environmentVisitor might be traversed with `{ noScope: true }`, which means `path.scope` is undefined.\n// Avoid using `path.scope` here\nconst visitor: Visitor = {\n  FunctionParent(path) {\n    if (path.isArrowFunctionExpression()) {\n      // arrows are not skipped because they inherit the context.\n      return;\n    } else {\n      path.skip();\n      if (path.isMethod()) {\n        requeueComputedKeyAndDecorators(path);\n      }\n    }\n  },\n  Property(path) {\n    if (path.isObjectProperty()) {\n      return;\n    }\n    path.skip();\n    requeueComputedKeyAndDecorators(path);\n  },\n};\n\nexport default visitor;\n"], "mappings": ";;;;;;;AAGmC;EACnB;IACQ;MAElBA,OAAO,CAACC,qBAAqB,GAAG,SAASA,qBAAqBA,CAC5DC,IAA0C,EAC1C;QACAA,IAAI,CAACC,IAAI,CAAC,CAAC;QACX,IAAID,IAAI,CAACE,IAAI,CAACC,QAAQ,EAAE;UAEtBH,IAAI,CAACI,OAAO,CAACC,UAAU,CAACL,IAAI,CAACM,GAAG,CAAC,KAAK,CAAC,CAAC;QAC1C;MACF,CAAC;IACH;EACF;AACF;AAEO,SAASC,+BAA+BA,CAC7CP,IAAqC,EACrC;EACA,MAAM;IAAEI,OAAO;IAAEF;EAAK,CAAC,GAAGF,IAAI;EAE9B,IAAIE,IAAI,CAACC,QAAQ,EAAE;IAEjBC,OAAO,CAACC,UAAU,CAACL,IAAI,CAACM,GAAG,CAAC,KAAK,CAAC,CAAC;EACrC;EACA,IAAIJ,IAAI,CAACM,UAAU,EAAE;IACnB,KAAK,MAAMC,SAAS,IAAIT,IAAI,CAACM,GAAG,CAAC,YAAY,CAAC,EAAE;MAE9CF,OAAO,CAACC,UAAU,CAACI,SAAS,CAAC;IAC/B;EACF;AACF;AAKA,MAAMC,OAAgB,GAAG;EACvBC,cAAcA,CAACX,IAAI,EAAE;IACnB,IAAIA,IAAI,CAACY,yBAAyB,CAAC,CAAC,EAAE;MAEpC;IACF,CAAC,MAAM;MACLZ,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAID,IAAI,CAACa,QAAQ,CAAC,CAAC,EAAE;QACnBN,+BAA+B,CAACP,IAAI,CAAC;MACvC;IACF;EACF,CAAC;EACDc,QAAQA,CAACd,IAAI,EAAE;IACb,IAAIA,IAAI,CAACe,gBAAgB,CAAC,CAAC,EAAE;MAC3B;IACF;IACAf,IAAI,CAACC,IAAI,CAAC,CAAC;IACXM,+BAA+B,CAACP,IAAI,CAAC;EACvC;AACF,CAAC;AAAC,IAAAgB,QAAA,GAEaN,OAAO;AAAAZ,OAAA,CAAAmB,OAAA,GAAAD,QAAA"}