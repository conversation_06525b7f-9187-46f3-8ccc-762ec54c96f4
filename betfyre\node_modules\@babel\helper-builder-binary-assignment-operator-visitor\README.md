# @babel/helper-builder-binary-assignment-operator-visitor

> Helper function to build binary assignment operator visitors

See our website [@babel/helper-builder-binary-assignment-operator-visitor](https://babeljs.io/docs/en/babel-helper-builder-binary-assignment-operator-visitor) for more information.

## Install

Using npm:

```sh
npm install --save @babel/helper-builder-binary-assignment-operator-visitor
```

or using yarn:

```sh
yarn add @babel/helper-builder-binary-assignment-operator-visitor
```
