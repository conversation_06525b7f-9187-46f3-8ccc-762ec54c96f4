{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "_helperFunctionName", "hasOwnDecorators", "node", "decorators", "length", "hasDecorators", "body", "some", "prop", "key", "value", "t", "objectProperty", "identifier", "method", "objectMethod", "blockStatement", "takeDecorators", "result", "arrayExpression", "map", "decorator", "expression", "undefined", "<PERSON><PERSON><PERSON>", "computed", "isIdentifier", "stringLiteral", "name", "String", "extractElementDescriptor", "file", "classRef", "superRef", "path", "isMethod", "isClassMethod", "isPrivate", "buildCodeFrameError", "type", "scope", "isTSDeclareMethod", "ReplaceSupers", "methodPath", "objectRef", "refToPreserve", "replace", "properties", "kind", "static", "booleanLiteral", "filter", "Boolean", "id", "transformed", "toExpression", "push", "nameFunction", "isClassProperty", "template", "statements", "ast", "buildUndefinedNode", "remove", "objectExpression", "addDecorateHelper", "addHelper", "buildDecoratedClass", "ref", "elements", "initializeId", "generateUidIdentifier", "isDeclaration", "isStrict", "isInStrictMode", "superClass", "cloneNode", "superId", "generateUidIdentifierBasedOnNode", "classDecorators", "definitions", "element", "abstract", "wrapperCall", "nullLiteral", "arguments", "directives", "directive", "directiveLiteral", "replacement", "classPathDesc", "statement", "instanceNodes", "wrapClass", "replaceWith", "get"], "sources": ["../src/decorators.ts"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n\nimport { types as t, template } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport nameFunction from \"@babel/helper-function-name\";\n\ntype Decoratable = Extract<t.Node, { decorators?: t.Decorator[] | null }>;\n\nexport function hasOwnDecorators(node: t.Node) {\n  // @ts-expect-error(flow->ts) TODO: maybe we could add t.isDecoratable to make ts happy\n  return !!(node.decorators && node.decorators.length);\n}\n\nexport function hasDecorators(node: t.Class) {\n  return hasOwnDecorators(node) || node.body.body.some(hasOwnDecorators);\n}\n\nfunction prop(key: string, value?: t.Expression) {\n  if (!value) return null;\n  return t.objectProperty(t.identifier(key), value);\n}\n\nfunction method(key: string, body: t.Statement[]) {\n  return t.objectMethod(\n    \"method\",\n    t.identifier(key),\n    [],\n    t.blockStatement(body),\n  );\n}\n\nfunction takeDecorators(node: Decoratable) {\n  let result: t.ArrayExpression | undefined;\n  if (node.decorators && node.decorators.length > 0) {\n    result = t.arrayExpression(\n      node.decorators.map(decorator => decorator.expression),\n    );\n  }\n  node.decorators = undefined;\n  return result;\n}\n\ntype AcceptedElement = Exclude<ClassElement, t.TSIndexSignature>;\ntype SupportedElement = Exclude<\n  AcceptedElement,\n  | t.ClassPrivateMethod\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty\n  | t.StaticBlock\n>;\n\nfunction getKey(node: SupportedElement) {\n  if (node.computed) {\n    return node.key;\n  } else if (t.isIdentifier(node.key)) {\n    return t.stringLiteral(node.key.name);\n  } else {\n    return t.stringLiteral(\n      String(\n        // A non-identifier non-computed key\n        (node.key as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n          .value,\n      ),\n    );\n  }\n}\n\nfunction extractElementDescriptor(\n  file: File,\n  classRef: t.Identifier,\n  superRef: t.Identifier,\n  path: NodePath<AcceptedElement>,\n) {\n  const isMethod = path.isClassMethod();\n  if (path.isPrivate()) {\n    throw path.buildCodeFrameError(\n      `Private ${\n        isMethod ? \"methods\" : \"fields\"\n      } in decorated classes are not supported yet.`,\n    );\n  }\n  if (path.node.type === \"ClassAccessorProperty\") {\n    throw path.buildCodeFrameError(\n      `Accessor properties are not supported in 2018-09 decorator transform, please specify { \"version\": \"2021-12\" } instead.`,\n    );\n  }\n  if (path.node.type === \"StaticBlock\") {\n    throw path.buildCodeFrameError(\n      `Static blocks are not supported in 2018-09 decorator transform, please specify { \"version\": \"2021-12\" } instead.`,\n    );\n  }\n\n  const { node, scope } = path as NodePath<SupportedElement>;\n\n  if (!path.isTSDeclareMethod()) {\n    new ReplaceSupers({\n      methodPath: path as NodePath<\n        Exclude<SupportedElement, t.TSDeclareMethod>\n      >,\n      objectRef: classRef,\n      superRef,\n      file,\n      refToPreserve: classRef,\n    }).replace();\n  }\n\n  const properties: t.ObjectExpression[\"properties\"] = [\n    prop(\"kind\", t.stringLiteral(t.isClassMethod(node) ? node.kind : \"field\")),\n    prop(\"decorators\", takeDecorators(node as Decoratable)),\n    prop(\"static\", node.static && t.booleanLiteral(true)),\n    prop(\"key\", getKey(node)),\n  ].filter(Boolean);\n\n  if (t.isClassMethod(node)) {\n    const id = node.computed\n      ? null\n      : (node.key as\n          | t.Identifier\n          | t.StringLiteral\n          | t.NumericLiteral\n          | t.BigIntLiteral);\n    const transformed = t.toExpression(node);\n    properties.push(\n      prop(\n        \"value\",\n        nameFunction({ node: transformed, id, scope }) || transformed,\n      ),\n    );\n  } else if (t.isClassProperty(node) && node.value) {\n    properties.push(\n      method(\"value\", template.statements.ast`return ${node.value}`),\n    );\n  } else {\n    properties.push(prop(\"value\", scope.buildUndefinedNode()));\n  }\n\n  path.remove();\n\n  return t.objectExpression(properties);\n}\n\nfunction addDecorateHelper(file: File) {\n  return file.addHelper(\"decorate\");\n}\n\ntype ClassElement = t.Class[\"body\"][\"body\"][number];\ntype ClassElementPath = NodePath<ClassElement>;\n\nexport function buildDecoratedClass(\n  ref: t.Identifier,\n  path: NodePath<t.Class>,\n  elements: ClassElementPath[],\n  file: File,\n) {\n  const { node, scope } = path;\n  const initializeId = scope.generateUidIdentifier(\"initialize\");\n  const isDeclaration = node.id && path.isDeclaration();\n  const isStrict = path.isInStrictMode();\n  const { superClass } = node;\n\n  node.type = \"ClassDeclaration\";\n  if (!node.id) node.id = t.cloneNode(ref);\n\n  let superId: t.Identifier;\n  if (superClass) {\n    superId = scope.generateUidIdentifierBasedOnNode(node.superClass, \"super\");\n    node.superClass = superId;\n  }\n\n  const classDecorators = takeDecorators(node);\n  const definitions = t.arrayExpression(\n    elements\n      .filter(\n        element =>\n          // @ts-expect-error Ignore TypeScript's abstract methods (see #10514)\n          !element.node.abstract && element.node.type !== \"TSIndexSignature\",\n      )\n      .map(path =>\n        extractElementDescriptor(\n          file,\n          node.id,\n          superId,\n          // @ts-expect-error TS can not exclude TSIndexSignature\n          path,\n        ),\n      ),\n  );\n\n  const wrapperCall = template.expression.ast`\n    ${addDecorateHelper(file)}(\n      ${classDecorators || t.nullLiteral()},\n      function (${initializeId}, ${superClass ? t.cloneNode(superId) : null}) {\n        ${node}\n        return { F: ${t.cloneNode(node.id)}, d: ${definitions} };\n      },\n      ${superClass}\n    )\n  ` as t.CallExpression & { arguments: [unknown, t.FunctionExpression] };\n\n  if (!isStrict) {\n    wrapperCall.arguments[1].body.directives.push(\n      t.directive(t.directiveLiteral(\"use strict\")),\n    );\n  }\n\n  let replacement: t.Node = wrapperCall;\n  let classPathDesc = \"arguments.1.body.body.0\";\n  if (isDeclaration) {\n    replacement = template.statement.ast`let ${ref} = ${wrapperCall}`;\n    classPathDesc = \"declarations.0.init.\" + classPathDesc;\n  }\n\n  return {\n    instanceNodes: [template.statement.ast`${t.cloneNode(initializeId)}(this)`],\n    wrapClass(path: NodePath<t.Class>) {\n      path.replaceWith(replacement);\n      return path.get(classPathDesc) as NodePath;\n    },\n  };\n}\n"], "mappings": ";;;;;;;;AAEA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AAIO,SAASG,gBAAgBA,CAACC,IAAY,EAAE;EAE7C,OAAO,CAAC,EAAEA,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAACC,MAAM,CAAC;AACtD;AAEO,SAASC,aAAaA,CAACH,IAAa,EAAE;EAC3C,OAAOD,gBAAgB,CAACC,IAAI,CAAC,IAAIA,IAAI,CAACI,IAAI,CAACA,IAAI,CAACC,IAAI,CAACN,gBAAgB,CAAC;AACxE;AAEA,SAASO,IAAIA,CAACC,GAAW,EAAEC,KAAoB,EAAE;EAC/C,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EACvB,OAAOC,WAAC,CAACC,cAAc,CAACD,WAAC,CAACE,UAAU,CAACJ,GAAG,CAAC,EAAEC,KAAK,CAAC;AACnD;AAEA,SAASI,MAAMA,CAACL,GAAW,EAAEH,IAAmB,EAAE;EAChD,OAAOK,WAAC,CAACI,YAAY,CACnB,QAAQ,EACRJ,WAAC,CAACE,UAAU,CAACJ,GAAG,CAAC,EACjB,EAAE,EACFE,WAAC,CAACK,cAAc,CAACV,IAAI,CACvB,CAAC;AACH;AAEA,SAASW,cAAcA,CAACf,IAAiB,EAAE;EACzC,IAAIgB,MAAqC;EACzC,IAAIhB,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;IACjDc,MAAM,GAAGP,WAAC,CAACQ,eAAe,CACxBjB,IAAI,CAACC,UAAU,CAACiB,GAAG,CAACC,SAAS,IAAIA,SAAS,CAACC,UAAU,CACvD,CAAC;EACH;EACApB,IAAI,CAACC,UAAU,GAAGoB,SAAS;EAC3B,OAAOL,MAAM;AACf;AAWA,SAASM,MAAMA,CAACtB,IAAsB,EAAE;EACtC,IAAIA,IAAI,CAACuB,QAAQ,EAAE;IACjB,OAAOvB,IAAI,CAACO,GAAG;EACjB,CAAC,MAAM,IAAIE,WAAC,CAACe,YAAY,CAACxB,IAAI,CAACO,GAAG,CAAC,EAAE;IACnC,OAAOE,WAAC,CAACgB,aAAa,CAACzB,IAAI,CAACO,GAAG,CAACmB,IAAI,CAAC;EACvC,CAAC,MAAM;IACL,OAAOjB,WAAC,CAACgB,aAAa,CACpBE,MAAM,CAEH3B,IAAI,CAACO,GAAG,CACNC,KACL,CACF,CAAC;EACH;AACF;AAEA,SAASoB,wBAAwBA,CAC/BC,IAAU,EACVC,QAAsB,EACtBC,QAAsB,EACtBC,IAA+B,EAC/B;EACA,MAAMC,QAAQ,GAAGD,IAAI,CAACE,aAAa,CAAC,CAAC;EACrC,IAAIF,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE;IACpB,MAAMH,IAAI,CAACI,mBAAmB,CAC3B,WACCH,QAAQ,GAAG,SAAS,GAAG,QACxB,8CACH,CAAC;EACH;EACA,IAAID,IAAI,CAAChC,IAAI,CAACqC,IAAI,KAAK,uBAAuB,EAAE;IAC9C,MAAML,IAAI,CAACI,mBAAmB,CAC3B,wHACH,CAAC;EACH;EACA,IAAIJ,IAAI,CAAChC,IAAI,CAACqC,IAAI,KAAK,aAAa,EAAE;IACpC,MAAML,IAAI,CAACI,mBAAmB,CAC3B,kHACH,CAAC;EACH;EAEA,MAAM;IAAEpC,IAAI;IAAEsC;EAAM,CAAC,GAAGN,IAAkC;EAE1D,IAAI,CAACA,IAAI,CAACO,iBAAiB,CAAC,CAAC,EAAE;IAC7B,IAAIC,4BAAa,CAAC;MAChBC,UAAU,EAAET,IAEX;MACDU,SAAS,EAAEZ,QAAQ;MACnBC,QAAQ;MACRF,IAAI;MACJc,aAAa,EAAEb;IACjB,CAAC,CAAC,CAACc,OAAO,CAAC,CAAC;EACd;EAEA,MAAMC,UAA4C,GAAG,CACnDvC,IAAI,CAAC,MAAM,EAAEG,WAAC,CAACgB,aAAa,CAAChB,WAAC,CAACyB,aAAa,CAAClC,IAAI,CAAC,GAAGA,IAAI,CAAC8C,IAAI,GAAG,OAAO,CAAC,CAAC,EAC1ExC,IAAI,CAAC,YAAY,EAAES,cAAc,CAACf,IAAmB,CAAC,CAAC,EACvDM,IAAI,CAAC,QAAQ,EAAEN,IAAI,CAAC+C,MAAM,IAAItC,WAAC,CAACuC,cAAc,CAAC,IAAI,CAAC,CAAC,EACrD1C,IAAI,CAAC,KAAK,EAAEgB,MAAM,CAACtB,IAAI,CAAC,CAAC,CAC1B,CAACiD,MAAM,CAACC,OAAO,CAAC;EAEjB,IAAIzC,WAAC,CAACyB,aAAa,CAAClC,IAAI,CAAC,EAAE;IACzB,MAAMmD,EAAE,GAAGnD,IAAI,CAACuB,QAAQ,GACpB,IAAI,GACHvB,IAAI,CAACO,GAIc;IACxB,MAAM6C,WAAW,GAAG3C,WAAC,CAAC4C,YAAY,CAACrD,IAAI,CAAC;IACxC6C,UAAU,CAACS,IAAI,CACbhD,IAAI,CACF,OAAO,EACP,IAAAiD,2BAAY,EAAC;MAAEvD,IAAI,EAAEoD,WAAW;MAAED,EAAE;MAAEb;IAAM,CAAC,CAAC,IAAIc,WACpD,CACF,CAAC;EACH,CAAC,MAAM,IAAI3C,WAAC,CAAC+C,eAAe,CAACxD,IAAI,CAAC,IAAIA,IAAI,CAACQ,KAAK,EAAE;IAChDqC,UAAU,CAACS,IAAI,CACb1C,MAAM,CAAC,OAAO,EAAE6C,cAAQ,CAACC,UAAU,CAACC,GAAI,UAAS3D,IAAI,CAACQ,KAAM,EAAC,CAC/D,CAAC;EACH,CAAC,MAAM;IACLqC,UAAU,CAACS,IAAI,CAAChD,IAAI,CAAC,OAAO,EAAEgC,KAAK,CAACsB,kBAAkB,CAAC,CAAC,CAAC,CAAC;EAC5D;EAEA5B,IAAI,CAAC6B,MAAM,CAAC,CAAC;EAEb,OAAOpD,WAAC,CAACqD,gBAAgB,CAACjB,UAAU,CAAC;AACvC;AAEA,SAASkB,iBAAiBA,CAAClC,IAAU,EAAE;EACrC,OAAOA,IAAI,CAACmC,SAAS,CAAC,UAAU,CAAC;AACnC;AAKO,SAASC,mBAAmBA,CACjCC,GAAiB,EACjBlC,IAAuB,EACvBmC,QAA4B,EAC5BtC,IAAU,EACV;EACA,MAAM;IAAE7B,IAAI;IAAEsC;EAAM,CAAC,GAAGN,IAAI;EAC5B,MAAMoC,YAAY,GAAG9B,KAAK,CAAC+B,qBAAqB,CAAC,YAAY,CAAC;EAC9D,MAAMC,aAAa,GAAGtE,IAAI,CAACmD,EAAE,IAAInB,IAAI,CAACsC,aAAa,CAAC,CAAC;EACrD,MAAMC,QAAQ,GAAGvC,IAAI,CAACwC,cAAc,CAAC,CAAC;EACtC,MAAM;IAAEC;EAAW,CAAC,GAAGzE,IAAI;EAE3BA,IAAI,CAACqC,IAAI,GAAG,kBAAkB;EAC9B,IAAI,CAACrC,IAAI,CAACmD,EAAE,EAAEnD,IAAI,CAACmD,EAAE,GAAG1C,WAAC,CAACiE,SAAS,CAACR,GAAG,CAAC;EAExC,IAAIS,OAAqB;EACzB,IAAIF,UAAU,EAAE;IACdE,OAAO,GAAGrC,KAAK,CAACsC,gCAAgC,CAAC5E,IAAI,CAACyE,UAAU,EAAE,OAAO,CAAC;IAC1EzE,IAAI,CAACyE,UAAU,GAAGE,OAAO;EAC3B;EAEA,MAAME,eAAe,GAAG9D,cAAc,CAACf,IAAI,CAAC;EAC5C,MAAM8E,WAAW,GAAGrE,WAAC,CAACQ,eAAe,CACnCkD,QAAQ,CACLlB,MAAM,CACL8B,OAAO,IAEL,CAACA,OAAO,CAAC/E,IAAI,CAACgF,QAAQ,IAAID,OAAO,CAAC/E,IAAI,CAACqC,IAAI,KAAK,kBACpD,CAAC,CACAnB,GAAG,CAACc,IAAI,IACPJ,wBAAwB,CACtBC,IAAI,EACJ7B,IAAI,CAACmD,EAAE,EACPwB,OAAO,EAEP3C,IACF,CACF,CACJ,CAAC;EAED,MAAMiD,WAAW,GAAGxB,cAAQ,CAACrC,UAAU,CAACuC,GAAI;AAC9C,MAAMI,iBAAiB,CAAClC,IAAI,CAAE;AAC9B,QAAQgD,eAAe,IAAIpE,WAAC,CAACyE,WAAW,CAAC,CAAE;AAC3C,kBAAkBd,YAAa,KAAIK,UAAU,GAAGhE,WAAC,CAACiE,SAAS,CAACC,OAAO,CAAC,GAAG,IAAK;AAC5E,UAAU3E,IAAK;AACf,sBAAsBS,WAAC,CAACiE,SAAS,CAAC1E,IAAI,CAACmD,EAAE,CAAE,QAAO2B,WAAY;AAC9D;AACA,QAAQL,UAAW;AACnB;AACA,GAAwE;EAEtE,IAAI,CAACF,QAAQ,EAAE;IACbU,WAAW,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC/E,IAAI,CAACgF,UAAU,CAAC9B,IAAI,CAC3C7C,WAAC,CAAC4E,SAAS,CAAC5E,WAAC,CAAC6E,gBAAgB,CAAC,YAAY,CAAC,CAC9C,CAAC;EACH;EAEA,IAAIC,WAAmB,GAAGN,WAAW;EACrC,IAAIO,aAAa,GAAG,yBAAyB;EAC7C,IAAIlB,aAAa,EAAE;IACjBiB,WAAW,GAAG9B,cAAQ,CAACgC,SAAS,CAAC9B,GAAI,OAAMO,GAAI,MAAKe,WAAY,EAAC;IACjEO,aAAa,GAAG,sBAAsB,GAAGA,aAAa;EACxD;EAEA,OAAO;IACLE,aAAa,EAAE,CAACjC,cAAQ,CAACgC,SAAS,CAAC9B,GAAI,GAAElD,WAAC,CAACiE,SAAS,CAACN,YAAY,CAAE,QAAO,CAAC;IAC3EuB,SAASA,CAAC3D,IAAuB,EAAE;MACjCA,IAAI,CAAC4D,WAAW,CAACL,WAAW,CAAC;MAC7B,OAAOvD,IAAI,CAAC6D,GAAG,CAACL,aAAa,CAAC;IAChC;EACF,CAAC;AACH"}