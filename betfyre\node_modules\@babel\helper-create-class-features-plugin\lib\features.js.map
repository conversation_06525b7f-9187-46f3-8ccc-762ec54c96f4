{"version": 3, "names": ["_decorators", "require", "FEATURES", "Object", "freeze", "fields", "privateMethods", "decorators", "privateIn", "staticBlocks", "exports", "featuresSameLoose", "Map", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "looseLowPriorityKey", "enableFeature", "file", "feature", "loose", "hasFeature", "canIgnoreLoose", "set", "get", "setL<PERSON>e", "resolvedLoose", "higherPriorityPluginName", "mask", "name", "isLoose", "Error", "undefined", "console", "warn", "shouldTransform", "path", "decoratorPath", "publicFieldPath", "privateFieldPath", "privateMethodPath", "staticBlockPath", "hasOwnDecorators", "node", "el", "isClassProperty", "isClassPrivateProperty", "isClassPrivateMethod", "isStaticBlock", "buildCodeFrameError"], "sources": ["../src/features.ts"], "sourcesContent": ["import type { File, types as t } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport { hasOwnDecorators } from \"./decorators\";\n\nexport const FEATURES = Object.freeze(\n  process.env.BABEL_8_BREAKING\n    ? {\n        //classes: 1 << 0,\n        fields: 1 << 1,\n        privateMethods: 1 << 2,\n        privateIn: 1 << 3,\n        staticBlocks: 1 << 4,\n      }\n    : {\n        //classes: 1 << 0,\n        fields: 1 << 1,\n        privateMethods: 1 << 2,\n        decorators: 1 << 3,\n        privateIn: 1 << 4,\n        staticBlocks: 1 << 5,\n      },\n);\n\nconst featuresSameLoose = new Map([\n  [FEATURES.fields, \"@babel/plugin-transform-class-properties\"],\n  [FEATURES.privateMethods, \"@babel/plugin-transform-private-methods\"],\n  [FEATURES.privateIn, \"@babel/plugin-transform-private-property-in-object\"],\n]);\n\n// We can't use a symbol because this needs to always be the same, even if\n// this package isn't deduped by npm. e.g.\n//  - node_modules/\n//    - @babel/plugin-class-features\n//    - @babel/plugin-proposal-decorators\n//      - node_modules\n//        - @babel-plugin-class-features\nconst featuresKey = \"@babel/plugin-class-features/featuresKey\";\nconst looseKey = \"@babel/plugin-class-features/looseKey\";\n\n// See https://github.com/babel/babel/issues/11622.\n// Since preset-env sets loose for the fields and private methods plugins, it can\n// cause conflicts with the loose mode set by an explicit plugin in the config.\n// To solve this problem, we ignore preset-env's loose mode if another plugin\n// explicitly sets it\n// The code to handle this logic doesn't check that \"low priority loose\" is always\n// the same. However, it is only set by the preset and not directly by users:\n// unless someone _wants_ to break it, it shouldn't be a problem.\nconst looseLowPriorityKey =\n  \"@babel/plugin-class-features/looseLowPriorityKey/#__internal__@babel/preset-env__please-overwrite-loose-instead-of-throwing\";\n\nexport function enableFeature(file: File, feature: number, loose: boolean) {\n  // We can't blindly enable the feature because, if it was already set,\n  // \"loose\" can't be changed, so that\n  //   @babel/plugin-class-properties { loose: true }\n  //   @babel/plugin-class-properties { loose: false }\n  // is transformed in loose mode.\n  // We only enabled the feature if it was previously disabled.\n  if (!hasFeature(file, feature) || canIgnoreLoose(file, feature)) {\n    file.set(featuresKey, file.get(featuresKey) | feature);\n    if (\n      // @ts-expect-error comparing loose with internal private magic string\n      loose ===\n      \"#__internal__@babel/preset-env__prefer-true-but-false-is-ok-if-it-prevents-an-error\"\n    ) {\n      setLoose(file, feature, true);\n      file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) | feature);\n    } else if (\n      // @ts-expect-error comparing loose with internal private magic string\n      loose ===\n      \"#__internal__@babel/preset-env__prefer-false-but-true-is-ok-if-it-prevents-an-error\"\n    ) {\n      setLoose(file, feature, false);\n      file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) | feature);\n    } else {\n      setLoose(file, feature, loose);\n    }\n  }\n\n  let resolvedLoose: boolean | undefined;\n  let higherPriorityPluginName: string | undefined;\n\n  for (const [mask, name] of featuresSameLoose) {\n    if (!hasFeature(file, mask)) continue;\n\n    const loose = isLoose(file, mask);\n\n    if (canIgnoreLoose(file, mask)) {\n      continue;\n    } else if (resolvedLoose === !loose) {\n      throw new Error(\n        \"'loose' mode configuration must be the same for @babel/plugin-transform-class-properties, \" +\n          \"@babel/plugin-transform-private-methods and \" +\n          \"@babel/plugin-transform-private-property-in-object (when they are enabled).\",\n      );\n    } else {\n      resolvedLoose = loose;\n      higherPriorityPluginName = name;\n    }\n  }\n\n  if (resolvedLoose !== undefined) {\n    for (const [mask, name] of featuresSameLoose) {\n      if (hasFeature(file, mask) && isLoose(file, mask) !== resolvedLoose) {\n        setLoose(file, mask, resolvedLoose);\n        console.warn(\n          `Though the \"loose\" option was set to \"${!resolvedLoose}\" in your @babel/preset-env ` +\n            `config, it will not be used for ${name} since the \"loose\" mode option was set to ` +\n            `\"${resolvedLoose}\" for ${higherPriorityPluginName}.\\nThe \"loose\" option must be the ` +\n            `same for @babel/plugin-transform-class-properties, @babel/plugin-transform-private-methods ` +\n            `and @babel/plugin-transform-private-property-in-object (when they are enabled): you can ` +\n            `silence this warning by explicitly adding\\n` +\n            `\\t[\"${name}\", { \"loose\": ${resolvedLoose} }]\\n` +\n            `to the \"plugins\" section of your Babel config.`,\n        );\n      }\n    }\n  }\n}\n\nfunction hasFeature(file: File, feature: number) {\n  return !!(file.get(featuresKey) & feature);\n}\n\nexport function isLoose(file: File, feature: number) {\n  return !!(file.get(looseKey) & feature);\n}\n\nfunction setLoose(file: File, feature: number, loose: boolean) {\n  if (loose) file.set(looseKey, file.get(looseKey) | feature);\n  else file.set(looseKey, file.get(looseKey) & ~feature);\n\n  file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) & ~feature);\n}\n\nfunction canIgnoreLoose(file: File, feature: number) {\n  return !!(file.get(looseLowPriorityKey) & feature);\n}\n\nexport function shouldTransform(path: NodePath<t.Class>, file: File): boolean {\n  let decoratorPath: NodePath<t.Decorator> | null = null;\n  let publicFieldPath: NodePath<t.ClassProperty> | null = null;\n  let privateFieldPath: NodePath<t.ClassPrivateProperty> | null = null;\n  let privateMethodPath: NodePath<t.ClassPrivateMethod> | null = null;\n  let staticBlockPath: NodePath<t.StaticBlock> | null = null;\n\n  if (hasOwnDecorators(path.node)) {\n    decoratorPath = path.get(\"decorators.0\");\n  }\n  for (const el of path.get(\"body.body\")) {\n    if (!decoratorPath && hasOwnDecorators(el.node)) {\n      decoratorPath = el.get(\"decorators.0\");\n    }\n    if (!publicFieldPath && el.isClassProperty()) {\n      publicFieldPath = el;\n    }\n    if (!privateFieldPath && el.isClassPrivateProperty()) {\n      privateFieldPath = el;\n    }\n    // NOTE: path.isClassPrivateMethod() it isn't supported in <7.2.0\n    if (!privateMethodPath && el.isClassPrivateMethod?.()) {\n      privateMethodPath = el;\n    }\n    if (!staticBlockPath && el.isStaticBlock?.()) {\n      staticBlockPath = el;\n    }\n  }\n\n  if (decoratorPath && privateFieldPath) {\n    throw privateFieldPath.buildCodeFrameError(\n      \"Private fields in decorated classes are not supported yet.\",\n    );\n  }\n  if (decoratorPath && privateMethodPath) {\n    throw privateMethodPath.buildCodeFrameError(\n      \"Private methods in decorated classes are not supported yet.\",\n    );\n  }\n\n  if (decoratorPath && !hasFeature(file, FEATURES.decorators)) {\n    throw path.buildCodeFrameError(\n      \"Decorators are not enabled.\" +\n        \"\\nIf you are using \" +\n        '[\"@babel/plugin-proposal-decorators\", { \"version\": \"legacy\" }], ' +\n        'make sure it comes *before* \"@babel/plugin-transform-class-properties\" ' +\n        \"and enable loose mode, like so:\\n\" +\n        '\\t[\"@babel/plugin-proposal-decorators\", { \"version\": \"legacy\" }]\\n' +\n        '\\t[\"@babel/plugin-transform-class-properties\", { \"loose\": true }]',\n    );\n  }\n\n  if (privateMethodPath && !hasFeature(file, FEATURES.privateMethods)) {\n    throw privateMethodPath.buildCodeFrameError(\n      \"Class private methods are not enabled. \" +\n        \"Please add `@babel/plugin-transform-private-methods` to your configuration.\",\n    );\n  }\n\n  if (\n    (publicFieldPath || privateFieldPath) &&\n    !hasFeature(file, FEATURES.fields) &&\n    // We want to allow enabling the private-methods plugin even without enabling\n    // the class-properties plugin. Class fields will still be compiled in classes\n    // that contain private methods.\n    // This is already allowed with the other various class features plugins, but\n    // it's because they can fallback to a transform separated from this helper.\n    !hasFeature(file, FEATURES.privateMethods)\n  ) {\n    throw path.buildCodeFrameError(\n      \"Class fields are not enabled. \" +\n        \"Please add `@babel/plugin-transform-class-properties` to your configuration.\",\n    );\n  }\n\n  if (staticBlockPath && !hasFeature(file, FEATURES.staticBlocks)) {\n    throw path.buildCodeFrameError(\n      \"Static class blocks are not enabled. \" +\n        \"Please add `@babel/plugin-transform-class-static-block` to your configuration.\",\n    );\n  }\n\n  if (decoratorPath || privateMethodPath || staticBlockPath) {\n    // If one of those feature is used we know that its transform is\n    // enabled, otherwise the previous checks throw.\n    return true;\n  }\n  if (\n    (publicFieldPath || privateFieldPath) &&\n    hasFeature(file, FEATURES.fields)\n  ) {\n    return true;\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;AAEA,IAAAA,WAAA,GAAAC,OAAA;AAEO,MAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAS/B;EAEEC,MAAM,EAAE,CAAC,IAAI,CAAC;EACdC,cAAc,EAAE,CAAC,IAAI,CAAC;EACtBC,UAAU,EAAE,CAAC,IAAI,CAAC;EAClBC,SAAS,EAAE,CAAC,IAAI,CAAC;EACjBC,YAAY,EAAE,CAAC,IAAI;AACrB,CACN,CAAC;AAACC,OAAA,CAAAR,QAAA,GAAAA,QAAA;AAEF,MAAMS,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAChC,CAACV,QAAQ,CAACG,MAAM,EAAE,0CAA0C,CAAC,EAC7D,CAACH,QAAQ,CAACI,cAAc,EAAE,yCAAyC,CAAC,EACpE,CAACJ,QAAQ,CAACM,SAAS,EAAE,oDAAoD,CAAC,CAC3E,CAAC;AASF,MAAMK,WAAW,GAAG,0CAA0C;AAC9D,MAAMC,QAAQ,GAAG,uCAAuC;AAUxD,MAAMC,mBAAmB,GACvB,6HAA6H;AAExH,SAASC,aAAaA,CAACC,IAAU,EAAEC,OAAe,EAAEC,KAAc,EAAE;EAOzE,IAAI,CAACC,UAAU,CAACH,IAAI,EAAEC,OAAO,CAAC,IAAIG,cAAc,CAACJ,IAAI,EAAEC,OAAO,CAAC,EAAE;IAC/DD,IAAI,CAACK,GAAG,CAACT,WAAW,EAAEI,IAAI,CAACM,GAAG,CAACV,WAAW,CAAC,GAAGK,OAAO,CAAC;IACtD,IAEEC,KAAK,KACL,qFAAqF,EACrF;MACAK,QAAQ,CAACP,IAAI,EAAEC,OAAO,EAAE,IAAI,CAAC;MAC7BD,IAAI,CAACK,GAAG,CAACP,mBAAmB,EAAEE,IAAI,CAACM,GAAG,CAACR,mBAAmB,CAAC,GAAGG,OAAO,CAAC;IACxE,CAAC,MAAM,IAELC,KAAK,KACL,qFAAqF,EACrF;MACAK,QAAQ,CAACP,IAAI,EAAEC,OAAO,EAAE,KAAK,CAAC;MAC9BD,IAAI,CAACK,GAAG,CAACP,mBAAmB,EAAEE,IAAI,CAACM,GAAG,CAACR,mBAAmB,CAAC,GAAGG,OAAO,CAAC;IACxE,CAAC,MAAM;MACLM,QAAQ,CAACP,IAAI,EAAEC,OAAO,EAAEC,KAAK,CAAC;IAChC;EACF;EAEA,IAAIM,aAAkC;EACtC,IAAIC,wBAA4C;EAEhD,KAAK,MAAM,CAACC,IAAI,EAAEC,IAAI,CAAC,IAAIjB,iBAAiB,EAAE;IAC5C,IAAI,CAACS,UAAU,CAACH,IAAI,EAAEU,IAAI,CAAC,EAAE;IAE7B,MAAMR,KAAK,GAAGU,OAAO,CAACZ,IAAI,EAAEU,IAAI,CAAC;IAEjC,IAAIN,cAAc,CAACJ,IAAI,EAAEU,IAAI,CAAC,EAAE;MAC9B;IACF,CAAC,MAAM,IAAIF,aAAa,KAAK,CAACN,KAAK,EAAE;MACnC,MAAM,IAAIW,KAAK,CACb,4FAA4F,GAC1F,8CAA8C,GAC9C,6EACJ,CAAC;IACH,CAAC,MAAM;MACLL,aAAa,GAAGN,KAAK;MACrBO,wBAAwB,GAAGE,IAAI;IACjC;EACF;EAEA,IAAIH,aAAa,KAAKM,SAAS,EAAE;IAC/B,KAAK,MAAM,CAACJ,IAAI,EAAEC,IAAI,CAAC,IAAIjB,iBAAiB,EAAE;MAC5C,IAAIS,UAAU,CAACH,IAAI,EAAEU,IAAI,CAAC,IAAIE,OAAO,CAACZ,IAAI,EAAEU,IAAI,CAAC,KAAKF,aAAa,EAAE;QACnED,QAAQ,CAACP,IAAI,EAAEU,IAAI,EAAEF,aAAa,CAAC;QACnCO,OAAO,CAACC,IAAI,CACT,yCAAwC,CAACR,aAAc,8BAA6B,GAClF,mCAAkCG,IAAK,4CAA2C,GAClF,IAAGH,aAAc,SAAQC,wBAAyB,oCAAmC,GACrF,6FAA4F,GAC5F,0FAAyF,GACzF,6CAA4C,GAC5C,OAAME,IAAK,iBAAgBH,aAAc,OAAM,GAC/C,gDACL,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASL,UAAUA,CAACH,IAAU,EAAEC,OAAe,EAAE;EAC/C,OAAO,CAAC,EAAED,IAAI,CAACM,GAAG,CAACV,WAAW,CAAC,GAAGK,OAAO,CAAC;AAC5C;AAEO,SAASW,OAAOA,CAACZ,IAAU,EAAEC,OAAe,EAAE;EACnD,OAAO,CAAC,EAAED,IAAI,CAACM,GAAG,CAACT,QAAQ,CAAC,GAAGI,OAAO,CAAC;AACzC;AAEA,SAASM,QAAQA,CAACP,IAAU,EAAEC,OAAe,EAAEC,KAAc,EAAE;EAC7D,IAAIA,KAAK,EAAEF,IAAI,CAACK,GAAG,CAACR,QAAQ,EAAEG,IAAI,CAACM,GAAG,CAACT,QAAQ,CAAC,GAAGI,OAAO,CAAC,CAAC,KACvDD,IAAI,CAACK,GAAG,CAACR,QAAQ,EAAEG,IAAI,CAACM,GAAG,CAACT,QAAQ,CAAC,GAAG,CAACI,OAAO,CAAC;EAEtDD,IAAI,CAACK,GAAG,CAACP,mBAAmB,EAAEE,IAAI,CAACM,GAAG,CAACR,mBAAmB,CAAC,GAAG,CAACG,OAAO,CAAC;AACzE;AAEA,SAASG,cAAcA,CAACJ,IAAU,EAAEC,OAAe,EAAE;EACnD,OAAO,CAAC,EAAED,IAAI,CAACM,GAAG,CAACR,mBAAmB,CAAC,GAAGG,OAAO,CAAC;AACpD;AAEO,SAASgB,eAAeA,CAACC,IAAuB,EAAElB,IAAU,EAAW;EAC5E,IAAImB,aAA2C,GAAG,IAAI;EACtD,IAAIC,eAAiD,GAAG,IAAI;EAC5D,IAAIC,gBAAyD,GAAG,IAAI;EACpE,IAAIC,iBAAwD,GAAG,IAAI;EACnE,IAAIC,eAA+C,GAAG,IAAI;EAE1D,IAAI,IAAAC,4BAAgB,EAACN,IAAI,CAACO,IAAI,CAAC,EAAE;IAC/BN,aAAa,GAAGD,IAAI,CAACZ,GAAG,CAAC,cAAc,CAAC;EAC1C;EACA,KAAK,MAAMoB,EAAE,IAAIR,IAAI,CAACZ,GAAG,CAAC,WAAW,CAAC,EAAE;IACtC,IAAI,CAACa,aAAa,IAAI,IAAAK,4BAAgB,EAACE,EAAE,CAACD,IAAI,CAAC,EAAE;MAC/CN,aAAa,GAAGO,EAAE,CAACpB,GAAG,CAAC,cAAc,CAAC;IACxC;IACA,IAAI,CAACc,eAAe,IAAIM,EAAE,CAACC,eAAe,CAAC,CAAC,EAAE;MAC5CP,eAAe,GAAGM,EAAE;IACtB;IACA,IAAI,CAACL,gBAAgB,IAAIK,EAAE,CAACE,sBAAsB,CAAC,CAAC,EAAE;MACpDP,gBAAgB,GAAGK,EAAE;IACvB;IAEA,IAAI,CAACJ,iBAAiB,IAAII,EAAE,CAACG,oBAAoB,YAAvBH,EAAE,CAACG,oBAAoB,CAAG,CAAC,EAAE;MACrDP,iBAAiB,GAAGI,EAAE;IACxB;IACA,IAAI,CAACH,eAAe,IAAIG,EAAE,CAACI,aAAa,YAAhBJ,EAAE,CAACI,aAAa,CAAG,CAAC,EAAE;MAC5CP,eAAe,GAAGG,EAAE;IACtB;EACF;EAEA,IAAIP,aAAa,IAAIE,gBAAgB,EAAE;IACrC,MAAMA,gBAAgB,CAACU,mBAAmB,CACxC,4DACF,CAAC;EACH;EACA,IAAIZ,aAAa,IAAIG,iBAAiB,EAAE;IACtC,MAAMA,iBAAiB,CAACS,mBAAmB,CACzC,6DACF,CAAC;EACH;EAEA,IAAIZ,aAAa,IAAI,CAAChB,UAAU,CAACH,IAAI,EAAEf,QAAQ,CAACK,UAAU,CAAC,EAAE;IAC3D,MAAM4B,IAAI,CAACa,mBAAmB,CAC5B,6BAA6B,GAC3B,qBAAqB,GACrB,kEAAkE,GAClE,yEAAyE,GACzE,mCAAmC,GACnC,oEAAoE,GACpE,mEACJ,CAAC;EACH;EAEA,IAAIT,iBAAiB,IAAI,CAACnB,UAAU,CAACH,IAAI,EAAEf,QAAQ,CAACI,cAAc,CAAC,EAAE;IACnE,MAAMiC,iBAAiB,CAACS,mBAAmB,CACzC,yCAAyC,GACvC,6EACJ,CAAC;EACH;EAEA,IACE,CAACX,eAAe,IAAIC,gBAAgB,KACpC,CAAClB,UAAU,CAACH,IAAI,EAAEf,QAAQ,CAACG,MAAM,CAAC,IAMlC,CAACe,UAAU,CAACH,IAAI,EAAEf,QAAQ,CAACI,cAAc,CAAC,EAC1C;IACA,MAAM6B,IAAI,CAACa,mBAAmB,CAC5B,gCAAgC,GAC9B,8EACJ,CAAC;EACH;EAEA,IAAIR,eAAe,IAAI,CAACpB,UAAU,CAACH,IAAI,EAAEf,QAAQ,CAACO,YAAY,CAAC,EAAE;IAC/D,MAAM0B,IAAI,CAACa,mBAAmB,CAC5B,uCAAuC,GACrC,gFACJ,CAAC;EACH;EAEA,IAAIZ,aAAa,IAAIG,iBAAiB,IAAIC,eAAe,EAAE;IAGzD,OAAO,IAAI;EACb;EACA,IACE,CAACH,eAAe,IAAIC,gBAAgB,KACpClB,UAAU,CAACH,IAAI,EAAEf,QAAQ,CAACG,MAAM,CAAC,EACjC;IACA,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd"}