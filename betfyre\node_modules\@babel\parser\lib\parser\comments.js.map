{"version": 3, "names": ["_base", "require", "setTrailingComments", "node", "comments", "trailingComments", "undefined", "unshift", "setLeadingComments", "leadingComments", "setInnerComments", "innerComments", "adjustInnerComments", "elements", "commentWS", "lastElement", "i", "length", "start", "Comments<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "addComment", "comment", "filename", "loc", "state", "push", "processComment", "commentStack", "commentStackLength", "lastCommentWS", "end", "leadingNode", "nodeStart", "commentEnd", "containingNode", "finalizeComment", "splice", "trailingNode", "commentStart", "input", "charCodeAt", "type", "properties", "arguments", "params", "specifiers", "finalizeRemainingComments", "resetPreviousNodeTrailingComments", "resetPreviousIdentifierLeadingComments", "takeSurroundingComments", "exports", "default"], "sources": ["../../src/parser/comments.ts"], "sourcesContent": ["/*:: declare var invariant; */\n\nimport BaseParser from \"./base\";\nimport type { Comment, Node, Identifier } from \"../types\";\nimport * as charCodes from \"charcodes\";\nimport type { Undone } from \"./node\";\n\n/**\n * A whitespace token containing comments\n */\nexport type CommentWhitespace = {\n  /**\n   * the start of the whitespace token.\n   */\n  start: number;\n  /**\n   * the end of the whitespace token.\n   */\n  end: number;\n  /**\n   * the containing comments\n   */\n  comments: Array<Comment>;\n  /**\n   * the immediately preceding AST node of the whitespace token\n   */\n  leadingNode: Node | null;\n  /**\n   * the immediately following AST node of the whitespace token\n   */\n  trailingNode: Node | null;\n  /**\n   * the innermost AST node containing the whitespace with minimal size (|end - start|)\n   */\n  containingNode: Node | null;\n};\n\n/**\n * Merge comments with node's trailingComments or assign comments to be\n * trailingComments. New comments will be placed before old comments\n * because the commentStack is enumerated reversely.\n */\nfunction setTrailingComments(node: Undone<Node>, comments: Array<Comment>) {\n  if (node.trailingComments === undefined) {\n    node.trailingComments = comments;\n  } else {\n    node.trailingComments.unshift(...comments);\n  }\n}\n\n/**\n * Merge comments with node's leadingComments or assign comments to be\n * leadingComments. New comments will be placed before old comments\n * because the commentStack is enumerated reversely.\n */\nfunction setLeadingComments(node: Undone<Node>, comments: Array<Comment>) {\n  if (node.leadingComments === undefined) {\n    node.leadingComments = comments;\n  } else {\n    node.leadingComments.unshift(...comments);\n  }\n}\n\n/**\n * Merge comments with node's innerComments or assign comments to be\n * innerComments. New comments will be placed before old comments\n * because the commentStack is enumerated reversely.\n */\nexport function setInnerComments(\n  node: Undone<Node>,\n  comments?: Array<Comment>,\n) {\n  if (node.innerComments === undefined) {\n    node.innerComments = comments;\n  } else {\n    node.innerComments.unshift(...comments);\n  }\n}\n\n/**\n * Given node and elements array, if elements has non-null element,\n * merge comments to its trailingComments, otherwise merge comments\n * to node's innerComments\n */\nfunction adjustInnerComments(\n  node: Undone<Node>,\n  elements: Array<Node>,\n  commentWS: CommentWhitespace,\n) {\n  let lastElement = null;\n  let i = elements.length;\n  while (lastElement === null && i > 0) {\n    lastElement = elements[--i];\n  }\n  if (lastElement === null || lastElement.start > commentWS.start) {\n    setInnerComments(node, commentWS.comments);\n  } else {\n    setTrailingComments(lastElement, commentWS.comments);\n  }\n}\n\nexport default class CommentsParser extends BaseParser {\n  addComment(comment: Comment): void {\n    if (this.filename) comment.loc.filename = this.filename;\n    this.state.comments.push(comment);\n  }\n\n  /**\n   * Given a newly created AST node _n_, attach _n_ to a comment whitespace _w_ if applicable\n   * {@see {@link CommentWhitespace}}\n   */\n  processComment(node: Node): void {\n    const { commentStack } = this.state;\n    const commentStackLength = commentStack.length;\n    if (commentStackLength === 0) return;\n    let i = commentStackLength - 1;\n    const lastCommentWS = commentStack[i];\n\n    if (lastCommentWS.start === node.end) {\n      lastCommentWS.leadingNode = node;\n      i--;\n    }\n\n    const { start: nodeStart } = node;\n    // invariant: for all 0 <= j <= i, let c = commentStack[j], c must satisfy c.end < node.end\n    for (; i >= 0; i--) {\n      const commentWS = commentStack[i];\n      const commentEnd = commentWS.end;\n      if (commentEnd > nodeStart) {\n        // by definition of commentWhiteSpace, this implies commentWS.start > nodeStart\n        // so node can be a containingNode candidate. At this time we can finalize the comment\n        // whitespace, because\n        // 1) its leadingNode or trailingNode, if exists, will not change\n        // 2) its containingNode have been assigned and will not change because it is the\n        //    innermost minimal-sized AST node\n        commentWS.containingNode = node;\n        this.finalizeComment(commentWS);\n        commentStack.splice(i, 1);\n      } else {\n        if (commentEnd === nodeStart) {\n          commentWS.trailingNode = node;\n        }\n        // stop the loop when commentEnd <= nodeStart\n        break;\n      }\n    }\n  }\n\n  /**\n   * Assign the comments of comment whitespaces to related AST nodes.\n   * Also adjust innerComments following trailing comma.\n   */\n  finalizeComment(commentWS: CommentWhitespace) {\n    const { comments } = commentWS;\n    if (commentWS.leadingNode !== null || commentWS.trailingNode !== null) {\n      if (commentWS.leadingNode !== null) {\n        setTrailingComments(commentWS.leadingNode, comments);\n      }\n      if (commentWS.trailingNode !== null) {\n        setLeadingComments(commentWS.trailingNode, comments);\n      }\n    } else {\n      /*:: invariant(commentWS.containingNode !== null) */\n      const { containingNode: node, start: commentStart } = commentWS;\n      if (this.input.charCodeAt(commentStart - 1) === charCodes.comma) {\n        // If a commentWhitespace follows a comma and the containingNode allows\n        // list structures with trailing comma, merge it to the trailingComment\n        // of the last non-null list element\n        switch (node.type) {\n          case \"ObjectExpression\":\n          case \"ObjectPattern\":\n          case \"RecordExpression\":\n            adjustInnerComments(node, node.properties, commentWS);\n            break;\n          case \"CallExpression\":\n          case \"OptionalCallExpression\":\n            adjustInnerComments(node, node.arguments, commentWS);\n            break;\n          case \"FunctionDeclaration\":\n          case \"FunctionExpression\":\n          case \"ArrowFunctionExpression\":\n          case \"ObjectMethod\":\n          case \"ClassMethod\":\n          case \"ClassPrivateMethod\":\n            adjustInnerComments(node, node.params, commentWS);\n            break;\n          case \"ArrayExpression\":\n          case \"ArrayPattern\":\n          case \"TupleExpression\":\n            adjustInnerComments(node, node.elements, commentWS);\n            break;\n          case \"ExportNamedDeclaration\":\n          case \"ImportDeclaration\":\n            adjustInnerComments(node, node.specifiers, commentWS);\n            break;\n          default: {\n            setInnerComments(node, comments);\n          }\n        }\n      } else {\n        setInnerComments(node, comments);\n      }\n    }\n  }\n\n  /**\n   * Drains remaining commentStack and applies finalizeComment\n   * to each comment whitespace. Used only in parseExpression\n   * where the top level AST node is _not_ Program\n   * {@see {@link CommentsParser#finalizeComment}}\n   */\n  finalizeRemainingComments() {\n    const { commentStack } = this.state;\n    for (let i = commentStack.length - 1; i >= 0; i--) {\n      this.finalizeComment(commentStack[i]);\n    }\n    this.state.commentStack = [];\n  }\n\n  /* eslint-disable no-irregular-whitespace */\n  /**\n   * Reset previous node trailing comments. Used in object / class\n   * property parsing. We parse `async`, `static`, `set` and `get`\n   * as an identifier but may reinterpret it into an async/static/accessor\n   * method later. In this case the identifier is not part of the AST and we\n   * should sync the knowledge to commentStacks\n   *\n   * For example, when parsing\n   * ```\n   * async /* 1 *​/ function f() {}\n   * ```\n   * the comment whitespace `/* 1 *​/` has leading node Identifier(async). When\n   * we see the function token, we create a Function node and mark `/* 1 *​/` as\n   * inner comments. So `/* 1 *​/` should be detached from the Identifier node.\n   *\n   * @param node the last finished AST node _before_ current token\n   */\n  /* eslint-enable no-irregular-whitespace */\n  resetPreviousNodeTrailingComments(node: Node) {\n    const { commentStack } = this.state;\n    const { length } = commentStack;\n    if (length === 0) return;\n    const commentWS = commentStack[length - 1];\n    if (commentWS.leadingNode === node) {\n      commentWS.leadingNode = null;\n    }\n  }\n\n  /* eslint-disable no-irregular-whitespace */\n  /**\n   * Reset previous node leading comments, assuming that `node` is a\n   * single-token node. Used in import phase modifiers parsing. We parse\n   * `module` in `import module foo from ...` as an identifier but may\n   * reinterpret it into a phase modifier later. In this case the identifier is\n   * not part of the AST and we should sync the knowledge to commentStacks\n   *\n   * For example, when parsing\n   * ```\n   * import /* 1 *​/ module a from \"a\";\n   * ```\n   * the comment whitespace `/* 1 *​/` has trailing node Identifier(module). When\n   * we see that `module` is not a default import binding, we mark `/* 1 *​/` as\n   * inner comments of the ImportDeclaration. So `/* 1 *​/` should be detached from\n   * the Identifier node.\n   *\n   * @param node the last finished AST node _before_ current token\n   */\n  /* eslint-enable no-irregular-whitespace */\n  resetPreviousIdentifierLeadingComments(node: Identifier) {\n    const { commentStack } = this.state;\n    const { length } = commentStack;\n    if (length === 0) return;\n\n    if (commentStack[length - 1].trailingNode === node) {\n      commentStack[length - 1].trailingNode = null;\n    } else if (length >= 2 && commentStack[length - 2].trailingNode === node) {\n      commentStack[length - 2].trailingNode = null;\n    }\n  }\n\n  /**\n   * Attach a node to the comment whitespaces right before/after\n   * the given range.\n   *\n   * This is used to properly attach comments around parenthesized\n   * expressions as leading/trailing comments of the inner expression.\n   */\n  takeSurroundingComments(node: Node, start: number, end: number) {\n    const { commentStack } = this.state;\n    const commentStackLength = commentStack.length;\n    if (commentStackLength === 0) return;\n    let i = commentStackLength - 1;\n\n    for (; i >= 0; i--) {\n      const commentWS = commentStack[i];\n      const commentEnd = commentWS.end;\n      const commentStart = commentWS.start;\n\n      if (commentStart === end) {\n        commentWS.leadingNode = node;\n      } else if (commentEnd === start) {\n        commentWS.trailingNode = node;\n      } else if (commentEnd < start) {\n        break;\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAEA,IAAAA,KAAA,GAAAC,OAAA;AAwCA,SAASC,mBAAmBA,CAACC,IAAkB,EAAEC,QAAwB,EAAE;EACzE,IAAID,IAAI,CAACE,gBAAgB,KAAKC,SAAS,EAAE;IACvCH,IAAI,CAACE,gBAAgB,GAAGD,QAAQ;EAClC,CAAC,MAAM;IACLD,IAAI,CAACE,gBAAgB,CAACE,OAAO,CAAC,GAAGH,QAAQ,CAAC;EAC5C;AACF;AAOA,SAASI,kBAAkBA,CAACL,IAAkB,EAAEC,QAAwB,EAAE;EACxE,IAAID,IAAI,CAACM,eAAe,KAAKH,SAAS,EAAE;IACtCH,IAAI,CAACM,eAAe,GAAGL,QAAQ;EACjC,CAAC,MAAM;IACLD,IAAI,CAACM,eAAe,CAACF,OAAO,CAAC,GAAGH,QAAQ,CAAC;EAC3C;AACF;AAOO,SAASM,gBAAgBA,CAC9BP,IAAkB,EAClBC,QAAyB,EACzB;EACA,IAAID,IAAI,CAACQ,aAAa,KAAKL,SAAS,EAAE;IACpCH,IAAI,CAACQ,aAAa,GAAGP,QAAQ;EAC/B,CAAC,MAAM;IACLD,IAAI,CAACQ,aAAa,CAACJ,OAAO,CAAC,GAAGH,QAAQ,CAAC;EACzC;AACF;AAOA,SAASQ,mBAAmBA,CAC1BT,IAAkB,EAClBU,QAAqB,EACrBC,SAA4B,EAC5B;EACA,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,CAAC,GAAGH,QAAQ,CAACI,MAAM;EACvB,OAAOF,WAAW,KAAK,IAAI,IAAIC,CAAC,GAAG,CAAC,EAAE;IACpCD,WAAW,GAAGF,QAAQ,CAAC,EAAEG,CAAC,CAAC;EAC7B;EACA,IAAID,WAAW,KAAK,IAAI,IAAIA,WAAW,CAACG,KAAK,GAAGJ,SAAS,CAACI,KAAK,EAAE;IAC/DR,gBAAgB,CAACP,IAAI,EAAEW,SAAS,CAACV,QAAQ,CAAC;EAC5C,CAAC,MAAM;IACLF,mBAAmB,CAACa,WAAW,EAAED,SAAS,CAACV,QAAQ,CAAC;EACtD;AACF;AAEe,MAAMe,cAAc,SAASC,aAAU,CAAC;EACrDC,UAAUA,CAACC,OAAgB,EAAQ;IACjC,IAAI,IAAI,CAACC,QAAQ,EAAED,OAAO,CAACE,GAAG,CAACD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACvD,IAAI,CAACE,KAAK,CAACrB,QAAQ,CAACsB,IAAI,CAACJ,OAAO,CAAC;EACnC;EAMAK,cAAcA,CAACxB,IAAU,EAAQ;IAC/B,MAAM;MAAEyB;IAAa,CAAC,GAAG,IAAI,CAACH,KAAK;IACnC,MAAMI,kBAAkB,GAAGD,YAAY,CAACX,MAAM;IAC9C,IAAIY,kBAAkB,KAAK,CAAC,EAAE;IAC9B,IAAIb,CAAC,GAAGa,kBAAkB,GAAG,CAAC;IAC9B,MAAMC,aAAa,GAAGF,YAAY,CAACZ,CAAC,CAAC;IAErC,IAAIc,aAAa,CAACZ,KAAK,KAAKf,IAAI,CAAC4B,GAAG,EAAE;MACpCD,aAAa,CAACE,WAAW,GAAG7B,IAAI;MAChCa,CAAC,EAAE;IACL;IAEA,MAAM;MAAEE,KAAK,EAAEe;IAAU,CAAC,GAAG9B,IAAI;IAEjC,OAAOa,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAClB,MAAMF,SAAS,GAAGc,YAAY,CAACZ,CAAC,CAAC;MACjC,MAAMkB,UAAU,GAAGpB,SAAS,CAACiB,GAAG;MAChC,IAAIG,UAAU,GAAGD,SAAS,EAAE;QAO1BnB,SAAS,CAACqB,cAAc,GAAGhC,IAAI;QAC/B,IAAI,CAACiC,eAAe,CAACtB,SAAS,CAAC;QAC/Bc,YAAY,CAACS,MAAM,CAACrB,CAAC,EAAE,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL,IAAIkB,UAAU,KAAKD,SAAS,EAAE;UAC5BnB,SAAS,CAACwB,YAAY,GAAGnC,IAAI;QAC/B;QAEA;MACF;IACF;EACF;EAMAiC,eAAeA,CAACtB,SAA4B,EAAE;IAC5C,MAAM;MAAEV;IAAS,CAAC,GAAGU,SAAS;IAC9B,IAAIA,SAAS,CAACkB,WAAW,KAAK,IAAI,IAAIlB,SAAS,CAACwB,YAAY,KAAK,IAAI,EAAE;MACrE,IAAIxB,SAAS,CAACkB,WAAW,KAAK,IAAI,EAAE;QAClC9B,mBAAmB,CAACY,SAAS,CAACkB,WAAW,EAAE5B,QAAQ,CAAC;MACtD;MACA,IAAIU,SAAS,CAACwB,YAAY,KAAK,IAAI,EAAE;QACnC9B,kBAAkB,CAACM,SAAS,CAACwB,YAAY,EAAElC,QAAQ,CAAC;MACtD;IACF,CAAC,MAAM;MAEL,MAAM;QAAE+B,cAAc,EAAEhC,IAAI;QAAEe,KAAK,EAAEqB;MAAa,CAAC,GAAGzB,SAAS;MAC/D,IAAI,IAAI,CAAC0B,KAAK,CAACC,UAAU,CAACF,YAAY,GAAG,CAAC,CAAC,OAAoB,EAAE;QAI/D,QAAQpC,IAAI,CAACuC,IAAI;UACf,KAAK,kBAAkB;UACvB,KAAK,eAAe;UACpB,KAAK,kBAAkB;YACrB9B,mBAAmB,CAACT,IAAI,EAAEA,IAAI,CAACwC,UAAU,EAAE7B,SAAS,CAAC;YACrD;UACF,KAAK,gBAAgB;UACrB,KAAK,wBAAwB;YAC3BF,mBAAmB,CAACT,IAAI,EAAEA,IAAI,CAACyC,SAAS,EAAE9B,SAAS,CAAC;YACpD;UACF,KAAK,qBAAqB;UAC1B,KAAK,oBAAoB;UACzB,KAAK,yBAAyB;UAC9B,KAAK,cAAc;UACnB,KAAK,aAAa;UAClB,KAAK,oBAAoB;YACvBF,mBAAmB,CAACT,IAAI,EAAEA,IAAI,CAAC0C,MAAM,EAAE/B,SAAS,CAAC;YACjD;UACF,KAAK,iBAAiB;UACtB,KAAK,cAAc;UACnB,KAAK,iBAAiB;YACpBF,mBAAmB,CAACT,IAAI,EAAEA,IAAI,CAACU,QAAQ,EAAEC,SAAS,CAAC;YACnD;UACF,KAAK,wBAAwB;UAC7B,KAAK,mBAAmB;YACtBF,mBAAmB,CAACT,IAAI,EAAEA,IAAI,CAAC2C,UAAU,EAAEhC,SAAS,CAAC;YACrD;UACF;YAAS;cACPJ,gBAAgB,CAACP,IAAI,EAAEC,QAAQ,CAAC;YAClC;QACF;MACF,CAAC,MAAM;QACLM,gBAAgB,CAACP,IAAI,EAAEC,QAAQ,CAAC;MAClC;IACF;EACF;EAQA2C,yBAAyBA,CAAA,EAAG;IAC1B,MAAM;MAAEnB;IAAa,CAAC,GAAG,IAAI,CAACH,KAAK;IACnC,KAAK,IAAIT,CAAC,GAAGY,YAAY,CAACX,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjD,IAAI,CAACoB,eAAe,CAACR,YAAY,CAACZ,CAAC,CAAC,CAAC;IACvC;IACA,IAAI,CAACS,KAAK,CAACG,YAAY,GAAG,EAAE;EAC9B;EAqBAoB,iCAAiCA,CAAC7C,IAAU,EAAE;IAC5C,MAAM;MAAEyB;IAAa,CAAC,GAAG,IAAI,CAACH,KAAK;IACnC,MAAM;MAAER;IAAO,CAAC,GAAGW,YAAY;IAC/B,IAAIX,MAAM,KAAK,CAAC,EAAE;IAClB,MAAMH,SAAS,GAAGc,YAAY,CAACX,MAAM,GAAG,CAAC,CAAC;IAC1C,IAAIH,SAAS,CAACkB,WAAW,KAAK7B,IAAI,EAAE;MAClCW,SAAS,CAACkB,WAAW,GAAG,IAAI;IAC9B;EACF;EAsBAiB,sCAAsCA,CAAC9C,IAAgB,EAAE;IACvD,MAAM;MAAEyB;IAAa,CAAC,GAAG,IAAI,CAACH,KAAK;IACnC,MAAM;MAAER;IAAO,CAAC,GAAGW,YAAY;IAC/B,IAAIX,MAAM,KAAK,CAAC,EAAE;IAElB,IAAIW,YAAY,CAACX,MAAM,GAAG,CAAC,CAAC,CAACqB,YAAY,KAAKnC,IAAI,EAAE;MAClDyB,YAAY,CAACX,MAAM,GAAG,CAAC,CAAC,CAACqB,YAAY,GAAG,IAAI;IAC9C,CAAC,MAAM,IAAIrB,MAAM,IAAI,CAAC,IAAIW,YAAY,CAACX,MAAM,GAAG,CAAC,CAAC,CAACqB,YAAY,KAAKnC,IAAI,EAAE;MACxEyB,YAAY,CAACX,MAAM,GAAG,CAAC,CAAC,CAACqB,YAAY,GAAG,IAAI;IAC9C;EACF;EASAY,uBAAuBA,CAAC/C,IAAU,EAAEe,KAAa,EAAEa,GAAW,EAAE;IAC9D,MAAM;MAAEH;IAAa,CAAC,GAAG,IAAI,CAACH,KAAK;IACnC,MAAMI,kBAAkB,GAAGD,YAAY,CAACX,MAAM;IAC9C,IAAIY,kBAAkB,KAAK,CAAC,EAAE;IAC9B,IAAIb,CAAC,GAAGa,kBAAkB,GAAG,CAAC;IAE9B,OAAOb,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAClB,MAAMF,SAAS,GAAGc,YAAY,CAACZ,CAAC,CAAC;MACjC,MAAMkB,UAAU,GAAGpB,SAAS,CAACiB,GAAG;MAChC,MAAMQ,YAAY,GAAGzB,SAAS,CAACI,KAAK;MAEpC,IAAIqB,YAAY,KAAKR,GAAG,EAAE;QACxBjB,SAAS,CAACkB,WAAW,GAAG7B,IAAI;MAC9B,CAAC,MAAM,IAAI+B,UAAU,KAAKhB,KAAK,EAAE;QAC/BJ,SAAS,CAACwB,YAAY,GAAGnC,IAAI;MAC/B,CAAC,MAAM,IAAI+B,UAAU,GAAGhB,KAAK,EAAE;QAC7B;MACF;IACF;EACF;AACF;AAACiC,OAAA,CAAAC,OAAA,GAAAjC,cAAA"}