{"version": 3, "names": ["PARAM", "PARAM_YIELD", "PARAM_AWAIT", "PARAM_RETURN", "PARAM_IN", "exports", "ProductionParameterHandler", "constructor", "stacks", "enter", "flags", "push", "exit", "pop", "currentFlags", "length", "hasAwait", "<PERSON><PERSON><PERSON>", "hasReturn", "hasIn", "default", "functionFlags", "isAsync", "isGenerator"], "sources": ["../../src/util/production-parameter.ts"], "sourcesContent": ["export const // Initial Parameter flags\n  PARAM = 0b0000,\n  // track [Yield] production parameter\n  PARAM_YIELD = 0b0001,\n  // track [Await] production parameter\n  PARAM_AWAIT = 0b0010,\n  // track [Return] production parameter\n  PARAM_RETURN = 0b0100,\n  PARAM_IN = 0b1000; // track [In] production parameter\n\n// ProductionParameterHandler is a stack fashioned production parameter tracker\n// https://tc39.es/ecma262/#sec-grammar-notation\n// The tracked parameters are defined above.\n//\n// Whenever [+Await]/[+Yield] appears in the right-hand sides of a production,\n// we must enter a new tracking stack. For example when parsing\n//\n// AsyncFunctionDeclaration [Yield, Await]:\n//   async [no LineTerminator here] function BindingIdentifier[?Yield, ?Await]\n//     ( FormalParameters[~Yield, +Await] ) { AsyncFunctionBody }\n//\n// we must follow such process:\n//\n// 1. parse async keyword\n// 2. parse function keyword\n// 3. parse bindingIdentifier <= inherit current parameters: [?Await]\n// 4. enter new stack with (PARAM_AWAIT)\n// 5. parse formal parameters <= must have [Await] parameter [+Await]\n// 6. parse function body\n// 7. exit current stack\n\nexport type ParamKind = number;\n\n// todo(flow->ts) - check if more granular type can be used,\n//  type below is not good because things like PARAM_AWAIT|PARAM_YIELD are not included\n// export type ParamKind =\n//   | typeof PARAM\n//   | typeof PARAM_AWAIT\n//   | typeof PARAM_IN\n//   | typeof PARAM_RETURN\n//   | typeof PARAM_YIELD;\n\nexport default class ProductionParameterHandler {\n  stacks: Array<number> = [];\n  enter(flags: number) {\n    this.stacks.push(flags);\n  }\n\n  exit() {\n    this.stacks.pop();\n  }\n\n  currentFlags(): number {\n    return this.stacks[this.stacks.length - 1];\n  }\n\n  get hasAwait(): boolean {\n    return (this.currentFlags() & PARAM_AWAIT) > 0;\n  }\n\n  get hasYield(): boolean {\n    return (this.currentFlags() & PARAM_YIELD) > 0;\n  }\n\n  get hasReturn(): boolean {\n    return (this.currentFlags() & PARAM_RETURN) > 0;\n  }\n\n  get hasIn(): boolean {\n    return (this.currentFlags() & PARAM_IN) > 0;\n  }\n}\n\nexport function functionFlags(\n  isAsync: boolean,\n  isGenerator: boolean,\n): ParamKind {\n  return (isAsync ? PARAM_AWAIT : 0) | (isGenerator ? PARAM_YIELD : 0);\n}\n"], "mappings": ";;;;;;;AAAO,MACLA,KAAK,GAAG,MAAM;EAEdC,WAAW,GAAG,MAAM;EAEpBC,WAAW,GAAG,MAAM;EAEpBC,YAAY,GAAG,MAAM;EACrBC,QAAQ,GAAG,MAAM;AAACC,OAAA,CAAAD,QAAA,GAAAA,QAAA;AAAAC,OAAA,CAAAF,YAAA,GAAAA,YAAA;AAAAE,OAAA,CAAAH,WAAA,GAAAA,WAAA;AAAAG,OAAA,CAAAJ,WAAA,GAAAA,WAAA;AAAAI,OAAA,CAAAL,KAAA,GAAAA,KAAA;AAkCL,MAAMM,0BAA0B,CAAC;EAAAC,YAAA;IAAA,KAC9CC,MAAM,GAAkB,EAAE;EAAA;EAC1BC,KAAKA,CAACC,KAAa,EAAE;IACnB,IAAI,CAACF,MAAM,CAACG,IAAI,CAACD,KAAK,CAAC;EACzB;EAEAE,IAAIA,CAAA,EAAG;IACL,IAAI,CAACJ,MAAM,CAACK,GAAG,CAAC,CAAC;EACnB;EAEAC,YAAYA,CAAA,EAAW;IACrB,OAAO,IAAI,CAACN,MAAM,CAAC,IAAI,CAACA,MAAM,CAACO,MAAM,GAAG,CAAC,CAAC;EAC5C;EAEA,IAAIC,QAAQA,CAAA,EAAY;IACtB,OAAO,CAAC,IAAI,CAACF,YAAY,CAAC,CAAC,GAAGZ,WAAW,IAAI,CAAC;EAChD;EAEA,IAAIe,QAAQA,CAAA,EAAY;IACtB,OAAO,CAAC,IAAI,CAACH,YAAY,CAAC,CAAC,GAAGb,WAAW,IAAI,CAAC;EAChD;EAEA,IAAIiB,SAASA,CAAA,EAAY;IACvB,OAAO,CAAC,IAAI,CAACJ,YAAY,CAAC,CAAC,GAAGX,YAAY,IAAI,CAAC;EACjD;EAEA,IAAIgB,KAAKA,CAAA,EAAY;IACnB,OAAO,CAAC,IAAI,CAACL,YAAY,CAAC,CAAC,GAAGV,QAAQ,IAAI,CAAC;EAC7C;AACF;AAACC,OAAA,CAAAe,OAAA,GAAAd,0BAAA;AAEM,SAASe,aAAaA,CAC3BC,OAAgB,EAChBC,WAAoB,EACT;EACX,OAAO,CAACD,OAAO,GAAGpB,WAAW,GAAG,CAAC,KAAKqB,WAAW,GAAGtB,WAAW,GAAG,CAAC,CAAC;AACtE"}